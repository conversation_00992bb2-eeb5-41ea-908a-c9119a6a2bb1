package com.desaysv.workserver.mapper.roi;

import com.desaysv.workserver.model.roi.CoordinatesRoi;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 18:34
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
public interface CoordinatesRoiMapper {
    /**
     * 清空Roi
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 插入Roi
     *
     * @param CoordinatesRoi 坐标Roi
     * @return 受影响的行数
     */
    Integer insert(CoordinatesRoi CoordinatesRoi);


    /**
     * 根据严格条件查询Roi
     *
     * @param CoordinatesRoi 坐标Roi
     * @return 指定严格条件的Roi
     */
    CoordinatesRoi selectByStrictCondition(CoordinatesRoi CoordinatesRoi);


    /**
     * 查询所有Roi
     *
     * @return Roi列表
     */
    List<CoordinatesRoi> selectAll();

    /**
     * 查询记录总数量
     *
     * @param CoordinatesRoi 坐标Roi
     * @return 受影响的行数
     */
    Long selectTotalPage(CoordinatesRoi CoordinatesRoi);


    /**
     * 条件查询
     *
     * @param CoordinatesRoi 坐标Roi
     * @return Roi列表
     */
    List<CoordinatesRoi> selectByCondition(CoordinatesRoi CoordinatesRoi);

    /**
     * 根据坐标UUID查询
     *
     * @param coordinatesUUID 坐标UUID
     * @return
     */
    CoordinatesRoi selectByCoordinatesUUID(@Param("coordinatesUUID") String coordinatesUUID);

    /**
     * 根据坐标Roi删除
     *
     * @param coordinatesUUID 坐标UUID
     * @return
     */
    Integer deleteByCoordinatesUUID(@Param("coordinatesUUID") String coordinatesUUID);

    /**
     * 根据坐标UUID更新
     *
     * @param coordinatesRoi 坐标Roi
     * @return
     */
    Integer updateByCoordinatesUUID(CoordinatesRoi coordinatesRoi);
}
