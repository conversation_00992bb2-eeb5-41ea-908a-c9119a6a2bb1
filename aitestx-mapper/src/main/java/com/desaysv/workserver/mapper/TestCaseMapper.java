package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestCase;
import com.desaysv.workserver.vo.testcase.TestCaseVo;

import java.util.List;

/**
 * 测试用例Mapper
 */
public interface TestCaseMapper {

    /**
     * 清空测试用例
     */
    int truncate();

    /**
     * 删除测试用例
     *
     * @param testCaseId 测试用例id
     * @return 是否删除成功
     */
    int deleteByPrimaryKey(Integer testCaseId);

    /**
     * 插入测试用例
     *
     * @param testCase 测试用例
     * @return 是否成功生成主键
     */
    int insert(TestCase testCase);

    /**
     * 根据id查询测试用例
     *
     * @param testCaseId 测试用例id
     * @return 指定id的测试用例
     */
    TestCase selectByPrimaryKey(Integer testCaseId);

    TestCase selectByUniqueRecord(String testSuiteUUID, String moduleName, String testCaseName);

    /**
     * 根据testcase uuid查找最新测试记录
     *
     * @param testcaseUUID 测试用例UUID
     * @return 指定testcase最新测试记录
     */
    TestCase selectByTestCaseUUID(String testcaseUUID);

    TestCase selectByTestCaseId(Integer testCaseId, String testSuiteUUID);

    Integer updateByPrimaryKey(TestCase testCase);

    List<TestCaseVo> selectAllTestCaseVoByTestSuiteUUID(String testSuiteUUID);

    int selectTotalTestCycles(String testProjectName, String moduleName, String testCaseName);
}
