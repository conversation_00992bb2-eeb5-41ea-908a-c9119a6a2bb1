package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestScriptFile;
import com.desaysv.workserver.bo.TestScriptFileSelector;
import com.desaysv.workserver.vo.testcase.TestcaseFileVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-3 10:41
 * @description : 测试用例文件
 * @modified By :
 * @since : 2022-8-3
 */
public interface TestcaseFileMapper {

    Integer insert(TestScriptFile testScriptFile);

    Integer updateByTestScriptFileUUID(TestScriptFile testScriptFile);

    Integer updateAllSelected(TestScriptFileSelector testScriptFileSelector);

    TestScriptFile selectByTestCaseFileUUID(@Param("uuid") String testCaseFileUUID);

    TestScriptFile selectByCondition(TestcaseFileVo testcaseFileVo);

    List<TestScriptFile> selectAll(TestcaseFileVo testcaseFileVo);

    Integer deleteByTestCaseFileUUID(@Param("uuid") String testCaseFileUUID);

    Integer deleteAll(TestcaseFileVo testcaseFileVo);
}
