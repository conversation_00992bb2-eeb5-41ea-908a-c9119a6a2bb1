package com.desaysv.workserver.mapper.roi;

import com.desaysv.workserver.model.roi.PercentTemplateRoi;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 18:34
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
public interface PercentTemplateRoiMapper {
    /**
     * 清空Roi
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除Roi
     *
     * @param templatePictureUUID 模板UUID
     * @return 受影响的行数
     */
    Integer deleteByTemplatePictureUUID(@Param("templatePictureUUID") String templatePictureUUID);

    /**
     * 插入Roi
     *
     * @param PercentTemplateRoi Roi
     * @return 受影响的行数
     */
    Integer insert(PercentTemplateRoi PercentTemplateRoi);

    /**
     * 根据UUID查询Roi
     *
     * @param templateUUID 模板UUID
     * @return 指定UUID的Roi
     */
    PercentTemplateRoi selectByTemplatePictureUUID(@Param("templatePictureUUID") String templateUUID);


    /**
     * 条件查询
     *
     * @param PercentTemplateRoi Roi
     * @return Roi列表
     */
    List<PercentTemplateRoi> selectByCondition(PercentTemplateRoi PercentTemplateRoi);

    /**
     * 查询所有Roi
     *
     * @return Roi列表
     */
    List<PercentTemplateRoi> selectAll();

    /**
     * 根据模板和设备查询所有Roi
     *
     * @return Roi列表
     */
    List<PercentTemplateRoi> selectAllByProjectAndDevice(String projectName, String deviceUniqueCode);

    /**
     * 查询记录总数量
     *
     * @param PercentTemplateRoi Roi
     * @return 受影响的行数
     */
    Long selectTotalPage(PercentTemplateRoi PercentTemplateRoi);

    /**
     * 更新Roi
     *
     * @param PercentTemplateRoi Roi
     * @return 受影响的行数
     */
    Integer updateByTemplatePictureUUID(PercentTemplateRoi PercentTemplateRoi);


}
