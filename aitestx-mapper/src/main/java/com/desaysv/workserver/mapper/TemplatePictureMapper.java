package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TemplatePicture;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-20 14:07
 * @description : 图像模板Mapper接口
 * @modified By :
 * @since : 2022-4-20
 */
public interface TemplatePictureMapper {

    /**
     * 清空图像模板
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除图像模板
     *
     * @param templatePictureUUID 图像模板UUID
     * @return 受影响的行数
     */
    Integer deleteByTemplatePictureUUID(@Param("templatePictureUUID") String templatePictureUUID);

    /**
     * 插入图像模板
     *
     * @param templatePicture 图像模板
     * @return 受影响的行数
     */
    Integer insert(TemplatePicture templatePicture);

    /**
     * 根据UUID查询图像模板
     *
     * @param templatePictureUUID 图像模板UUID
     * @return 指定UUID的图像模板
     */
    TemplatePicture selectByTemplatePictureUUID(@Param("templatePictureUUID") String templatePictureUUID);

    /**
     * 根据name查询图像模板
     *
     * @param templateName 图像模板名
     * @return 指定name的图像模板
     */
    TemplatePicture selectByName(@Param("pictureTemplateName") String templateName,
                                 @Param("deviceUniqueCode") String deviceUniqueCode,
                                 @Param("projectName") String projectName);

    /**
     * 查询所有图像模板
     *
     * @return 图像模板列表
     */
    List<TemplatePicture> selectAll();

    /**
     * 查询记录总数量
     *
     * @param templatePicture 图像模板
     * @return 受影响的行数
     */
    Long selectTotalPage(TemplatePicture templatePicture);


    /**
     * 条件查询
     *
     * @param templatePicture 图像模板
     * @return 图像模板列表
     */
    List<TemplatePicture> selectByCondition(TemplatePicture templatePicture);

    /**
     * 更新图像模板
     *
     * @param templatePicture 图像模板
     * @return 受影响的行数
     */
    Integer updateByTemplatePictureUUID(TemplatePicture templatePicture);
}
