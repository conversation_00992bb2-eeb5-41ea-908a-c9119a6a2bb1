package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.Department;
import com.desaysv.workserver.model.TestProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-26 11:35
 * @description : 测试项目mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface TestProjectMapper {

    /**
     * 清空测试项目
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试项目
     *
     * @param projectId 测试项目id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer projectId);

    /**
     * 插入测试项目
     *
     * @param testProject 测试项目
     * @return 受影响的行数
     */
    Integer insert(TestProject testProject);

    /**
     * 根据id查询测试项目
     *
     * @param projectId 测试项目id
     * @return 指定id的测试项目
     */
    TestProject selectByPrimaryKey(@Param("id") Integer projectId);


    /**
     * 根据测试项目名字段查询测试项目
     *
     * @param projectName 测试项目名
     * @return 测试项目
     */
    TestProject selectByName(@Param("name") String projectName);


    /**
     * 获取公共项目
     *
     * @return
     */
    TestProject selectPublicProject();


    /**
     * 查找所有关联的事业单元
     *
     * @param testProject 测试项目
     * @return 所有关联的事业单元
     */
    List<Department> selectDepartments(TestProject testProject);

    /**
     * 查询所有测试项目
     *
     * @return 测试项目列表
     */
    List<TestProject> selectAll();

    /**
     * 查询记录总数量
     *
     * @param testProject 测试项目
     * @return 受影响的行数
     */
    Integer selectTotalPage(TestProject testProject);

    /**
     * 更新测试项目
     *
     * @param testProject 测试项目
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(TestProject testProject);

    /**
     * 条件查询
     *
     * @param testProject 测试项目
     * @return 测试项目列表
     */
    List<TestProject> selectByCondition(TestProject testProject);
}
