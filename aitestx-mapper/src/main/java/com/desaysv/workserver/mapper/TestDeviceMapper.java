package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-27 10:12
 * @description : 测试设备mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface TestDeviceMapper {

    /**
     * 清空测试设备
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试设备
     *
     * @param deviceId 测试设备id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer deviceId);

    /**
     * 插入测试设备
     *
     * @param device 测试设备
     * @return 受影响的行数
     */
    Integer insert(TestDevice device);

    /**
     * 根据id查询测试设备
     *
     * @param deviceId 测试设备id
     * @return 指定id的测试设备
     */
    TestDevice selectByPrimaryKey(@Param("id") Integer deviceId);

    /**
     * 根据唯一编码查询测试设备
     *
     * @param deviceUniqueCode 测试设备唯一编码
     * @return 指定唯一编码的测试设备
     */
    TestDevice selectByUniqueCode(@Param("uniqueCode") String deviceUniqueCode);

    /**
     * 查询所有测试设备
     *
     * @return 测试设备列表
     */
    List<TestDevice> selectAll();

    /**
     * 查询记录总数量
     *
     * @param device 测试设备
     * @return 受影响的行数
     */
    Integer selectTotalPage(TestDevice device);

    /**
     * 更新测试设备
     *
     * @param device 测试设备
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(TestDevice device);

    /**
     * 条件查询
     *
     * @param device 测试设备
     * @return 测试设备列表
     */
    List<TestDevice> selectByCondition(TestDevice device);
}
