package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.relay.ElectricRelayEntity;

import java.util.List;
import java.util.Map;

public interface ElectricRelayMapper {

    /**
     * 更新电继电器实体中的文本信息
     */
    Integer updateText(ElectricRelayEntity electricRelayEntity);

    /**
     * 插入电继电器实体中的文本信息
     */
    Integer insertText(ElectricRelayEntity electricRelayEntity);

    /**
     * 获取电继电器数据 根据项目名称以及别名
     */
    List<ElectricRelayEntity> selectByAliasAndProject(Map<String, String> paramsMap);

    /**
     * 更新电继电器的状态
     */
    void updateStatus(ElectricRelayEntity entity);

    /**
     * 设备断开 修改连接、开关状态
     */
    void updateConnectSwitchStatus(ElectricRelayEntity entity);
}
