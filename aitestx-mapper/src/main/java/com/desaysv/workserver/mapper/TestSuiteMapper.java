package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestSuite;
import com.desaysv.workserver.vo.testcase.TestSuiteStatisticVo;

/**
 * 测试套件Mapper
 */
public interface TestSuiteMapper {

    /**
     * 清空测试套件
     */
    int truncate();

    /**
     * 删除测试套件
     *
     * @param testSuiteId 测试套件id
     * @return 是否删除成功
     */
    int deleteByPrimaryKey(Integer testSuiteId);

    /**
     * 插入测试套件
     *
     * @param testSuite 测试套件
     * @return 是否成功生成主键
     */
    int insert(TestSuite testSuite);

    /**
     * 根据id查询测试套件
     *
     * @param testSuiteId 测试套件id
     * @return 指定id的测试套件
     */
    TestSuite selectByPrimaryKey(Integer testSuiteId);

    /**
     * 根据套件名和终端id查找测试套件
     *
     * @param testSuiteName 测试套件名
     * @return 测试套件
     */
    TestSuite selectByUniqueRecord(String testSuiteName);

    /**
     * 通过uuid查找测试套件
     *
     * @param uuid 唯一编码
     * @return 测试套件
     */
    TestSuite selectByUUID(String uuid);

    /**
     * 更新测试套件
     *
     * @param testSuite 测试套件
     * @return 是否更新成功
     */
    Integer updateByPrimaryKey(TestSuite testSuite);

    TestSuiteStatisticVo selectAllStatisticsByTestSuiteUUID(String testSuiteUUID);

}
