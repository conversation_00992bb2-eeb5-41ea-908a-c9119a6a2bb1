package com.desaysv.workserver.mapper.roi;

import com.desaysv.workserver.model.roi.RoiType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-27 9:51
 * @description : Roi类型Mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface RoiTypeMapper {

    /**
     * 清空Roi类型
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除Roi类型
     *
     * @param roiTypeId Roi类型id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer roiTypeId);

    /**
     * 插入Roi类型
     *
     * @param roiType Roi类型
     * @return 受影响的行数
     */
    Integer insert(RoiType roiType);

    /**
     * 根据id查询Roi类型
     *
     * @param roiTypeId Roi类型id
     * @return 指定id的Roi类型
     */
    RoiType selectByPrimaryKey(@Param("id") Integer roiTypeId);

    /**
     * 根据name查询Roi类型
     *
     * @param roiTypeName Roi类型名
     * @return 指定name的Roi类型
     */
    RoiType selectByName(@Param("name") String roiTypeName);

    /**
     * 查询所有Roi类型
     *
     * @return Roi类型列表
     */
    List<RoiType> selectAll();

    /**
     * 查询记录总数量
     *
     * @param roiType Roi类型
     * @return 受影响的行数
     */
    Integer selectTotalPage(RoiType roiType);

    /**
     * 更新Roi类型
     *
     * @param roiType Roi类型
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(RoiType roiType);

    /**
     * 条件查询
     *
     * @param roiType Roi类型
     * @return Roi类型列表
     */
    List<RoiType> selectByCondition(RoiType roiType);
}
