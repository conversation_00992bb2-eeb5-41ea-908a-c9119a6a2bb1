package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestResult;
import org.apache.ibatis.annotations.Param;

/**
 * 测试结果Mapper
 */
public interface TestResultMapper {

    /**
     * 清空测试结果
     */
    int truncate();

    /**
     * 删除测试结果
     *
     * @param testResultUUID 测试结果UUID
     * @return 是否删除成功
     */
    int deleteByTestResultUUID(@Param("uuid") String testResultUUID);

    /**
     * 插入测试结果
     *
     * @param testResult 测试结果
     * @return 影响的行数
     */
    int insert(TestResult testResult);

    /**
     * 根据UUID查询测试结果
     *
     * @param testResultUUID 测试结果UUID
     * @return 指定UUID的测试结果
     */
    TestResult selectByTestResultUUID(@Param("uuid") String testResultUUID);

    /**
     * 更新测试结果
     *
     * @param testResult 测试结果
     * @return 影响的行数
     */
    Integer updateByTestResultUUID(TestResult testResult);
}
