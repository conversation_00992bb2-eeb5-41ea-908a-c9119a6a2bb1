package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.RobotCoordinates;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 13:36
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
public interface RobotCoordinatesMapper {
    /**
     * 清空机械臂坐标
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除机械臂坐标
     *
     * @param coordinatesUUID 机械臂坐标UUID
     * @return 受影响的行数
     */
    Integer deleteByCoordinatesUUID(@Param("uuid") String coordinatesUUID);

    /**
     * 插入机械臂坐标
     *
     * @param coordinates 机械臂坐标
     * @return 受影响的行数
     */
    Integer insert(RobotCoordinates coordinates);

    /**
     * 根据UUID查询机械臂坐标
     *
     * @param coordinatesUUID 机械臂坐标UUID
     * @return 指定UUID的机械臂坐标
     */
    RobotCoordinates selectByCoordinatesUUID(@Param("uuid") Integer coordinatesUUID);

    /**
     * 根据指定条件查询坐标
     *
     * @param coordinates 机械臂坐标
     * @return 指定条件的机械臂坐标
     */
    RobotCoordinates selectByCondition(RobotCoordinates coordinates);

    /**
     * 根据指定条件删除坐标
     *
     * @param coordinates 机械臂坐标
     * @return 受影响的行数
     */
    Integer deleteByCondition(RobotCoordinates coordinates);

    /**
     * 查询所有机械臂坐标
     *
     * @return 机械臂坐标列表
     */
    List<RobotCoordinates> selectAll();

    /**
     * 根据条件查询所有机械臂坐标
     *
     * @param projectId 测试项目id
     * @param deviceId  设备id
     * @return 机械臂坐标列表
     */
    List<RobotCoordinates> selectAllByCondition(Integer projectId, Integer deviceId);

    /**
     * 根据条件清除所有机械臂坐标
     *
     * @param projectId 测试项目id
     * @param deviceId  设备id
     * @return 受影响的行数
     */
    Integer clearAllByCondition(Integer projectId, Integer deviceId);

    /**
     * 更新机械臂坐标
     *
     * @param coordinates 机械臂坐标
     * @return 受影响的行数
     */
    Integer updateByCoordinatesUUID(RobotCoordinates coordinates);
}
