package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.OriginalPicture;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 11:37
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
public interface OriginalPictureMapper {
    /**
     * 清空原始图片
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除原始图片
     *
     * @param originalPictureUUID 原始图片UUID
     * @return 受影响的行数
     */
    Integer deleteByOriginalPictureUUID(@Param("uuid") String originalPictureUUID);

    /**
     * 插入原始图片
     *
     * @param originalPicture 原始图片
     * @return 受影响的行数
     */
    Integer insert(OriginalPicture originalPicture);

    /**
     * 根据UUID查询原始图片
     *
     * @param originalPictureUUID 原始图片UUID
     * @return 指定UUID的原始图片
     */
    OriginalPicture selectByOriginalPictureUUID(@Param("uuid") String originalPictureUUID);

    OriginalPicture selectByDimension(OriginalPicture originalPicture);

    /**
     * 查询所有原始图片
     *
     * @return 原始图片列表
     */
    List<OriginalPicture> selectAll();

    /**
     * 查询记录总数量
     *
     * @param originalPicture 原始图片
     * @return 受影响的行数
     */
    Long selectTotalPage(OriginalPicture originalPicture);

    /**
     * 条件查询
     *
     * @param originalPicture 原始图片
     * @return 原始图片列表
     */
    List<OriginalPicture> selectByCondition(OriginalPicture originalPicture);

    /**
     * 更新原始图片
     *
     * @param originalPicture 原始图片
     * @return 受影响的行数
     */
    Integer updateByOriginalPictureUUID(OriginalPicture originalPicture);

}
