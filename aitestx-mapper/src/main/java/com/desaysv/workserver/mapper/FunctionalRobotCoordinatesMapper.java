package com.desaysv.workserver.mapper;


import com.desaysv.workserver.model.FunctionalRobotCoordinates;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FunctionalRobotCoordinatesMapper {
    /**
     * 清空机械臂功能坐标
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除机械臂功能坐标
     *
     * @param funcCoordinatesUUID 机械臂功能坐标UUID
     * @return 受影响的行数
     */
    Integer deleteByFunctionCoordinatesUUID(@Param("uuid") String funcCoordinatesUUID);

    /**
     * 插入机械臂功能坐标
     *
     * @param coordinates 机械臂功能坐标
     * @return 受影响的行数
     */
    Integer insert(FunctionalRobotCoordinates coordinates);

    /**
     * 根据UUID查询机械臂功能坐标
     *
     * @param funcCoordinatesUUID 机械臂功能坐标UUID
     * @return 指定UUID的机械臂功能坐标
     */
    FunctionalRobotCoordinates selectByFunctionCoordinatesUUID(@Param("uuid") String funcCoordinatesUUID);

    /**
     * 根据指定条件查询功能坐标
     *
     * @param coordinatesQuery 机械臂功能坐标
     * @return 指定条件的机械臂功能坐标
     */
    List<FunctionalRobotCoordinates> selectByCondition(FunctionalRobotCoordinatesQueryVo coordinatesQuery);

    /**
     * 根据指定条件删除功能坐标
     *
     * @param coordinates 机械臂功能坐标
     * @return 受影响的行数
     */
    Integer deleteByCondition(FunctionalRobotCoordinates coordinates);


    /**
     * 查询所有机械臂功能坐标
     *
     * @return 机械臂功能坐标列表
     */
    List<FunctionalRobotCoordinates> selectAll();

    /**
     * 更新机械臂功能坐标
     *
     * @param coordinates 机械臂功能坐标
     * @return 受影响的行数
     */
    Integer updateByFunctionCoordinatesUUID(FunctionalRobotCoordinates coordinates);

    /**
     * 根据条件查询所有机械臂功能坐标
     *
     * @param projectId 测试项目id
     * @param deviceId  设备id
     * @return 机械臂功能坐标列表
     */
    List<FunctionalRobotCoordinates> selectAllByCondition(Integer projectId, Integer deviceId);

    /**
     * 根据条件清除所有机械臂功能坐标
     *
     * @param projectId 测试项目id
     * @param deviceId  设备id
     * @return 受影响的行数
     */
    Integer clearAllByCondition(Integer projectId, Integer deviceId);
}
