package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.Tester;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-25 16:35
 * @description : 测试人员mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface TesterMapper {

    /**
     * 清空测试人员
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试人员
     *
     * @param testerId 测试人员id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer testerId);

    /**
     * 插入测试人员
     *
     * @param tester 测试人员
     * @return 受影响的行数
     */
    Integer insert(Tester tester);

    /**
     * 根据id查询测试人员
     *
     * @param testerId 测试人员id
     * @return 指定id的测试人员
     */
    Tester selectByPrimaryKey(@Param("id") Integer testerId);

    /**
     * 根据姓名字段查询测试人员
     *
     * @param testerName 测试人员姓名
     * @return 测试人员
     */
    Tester selectByName(@Param("name") String testerName);

    /**
     * 查询所有测试人员
     *
     * @return 测试人员列表
     */
    List<Tester> selectAll();

    /**
     * 更新测试人员
     *
     * @param tester 测试人员
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(Tester tester);

    /**
     * 查询记录总数量
     *
     * @param tester 测试人员
     * @return 受影响的行数
     */
    Integer selectTotalPage(Tester tester);

    /**
     * 条件查询
     *
     * @param tester 测试人员
     * @return 测试人员列表
     */
    List<Tester> selectByCondition(Tester tester);
}
