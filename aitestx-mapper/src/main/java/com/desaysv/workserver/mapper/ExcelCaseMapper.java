package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.ExcelCaseModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExcelCaseMapper {
    Integer insertExcelCaseTable(List<ExcelCaseModel> excelCaseModelList);

    Integer insertExcelCase(ExcelCaseModel excelCaseModel);

    Integer updateExcelCase(ExcelCaseModel excelCaseModel);

    Integer clearMapColumnData(String tableName);

    Integer batchUpdateExcelCase(List<ExcelCaseModel> excelCaseModelList);

    List<ExcelCaseModel> findExcelCaseByTableName(@Param("tableName") String tableName);

    ExcelCaseModel findRowCaseInfoByUuid(@Param("uuid") String uuid);

    Integer deleteByTableName(@Param("tableName") String tableName);

}
