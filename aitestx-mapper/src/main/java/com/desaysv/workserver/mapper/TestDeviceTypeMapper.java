package com.desaysv.workserver.mapper;

import com.desaysv.workserver.model.TestDeviceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-27 9:58
 * @description : 测试设备类型mapper接口
 * @modified By :
 * @since : 2022-4-27
 */
public interface TestDeviceTypeMapper {

    /**
     * 清空测试设备类型
     *
     * @return 受影响的行数
     */
    Integer truncate();

    /**
     * 删除测试设备类型
     *
     * @param deviceTypeId 测试设备类型id
     * @return 受影响的行数
     */
    Integer deleteByPrimaryKey(@Param("id") Integer deviceTypeId);

    /**
     * 插入测试设备类型
     *
     * @param deviceType 测试设备类型
     * @return 受影响的行数
     */
    Integer insert(TestDeviceType deviceType);

    /**
     * 根据id查询测试设备类型
     *
     * @param deviceTypeId 测试设备类型id
     * @return 指定id的测试设备类型
     */
    TestDeviceType selectByPrimaryKey(@Param("id") Integer deviceTypeId);


    /**
     * 根据name查询测试设备类型
     *
     * @param deviceTypeName 测试设备类型名
     * @return 指定名字的测试设备类型
     */
    TestDeviceType selectByName(@Param("name") String deviceTypeName);

    /**
     * 查询所有测试设备类型
     *
     * @return 测试设备类型列表
     */
    List<TestDeviceType> selectAll();

    /**
     * 查询记录总数量
     *
     * @param deviceType 测试设备类型
     * @return 受影响的行数
     */
    Integer selectTotalPage(TestDeviceType deviceType);

    /**
     * 更新测试设备类型
     *
     * @param deviceType 测试设备类型
     * @return 受影响的行数
     */
    Integer updateByPrimaryKey(TestDeviceType deviceType);

    /**
     * 条件查询
     *
     * @param deviceType 测试设备类型
     * @return 测试设备类型列表
     */
    List<TestDeviceType> selectByCondition(TestDeviceType deviceType);
}
