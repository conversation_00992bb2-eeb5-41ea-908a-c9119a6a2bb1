<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestSuiteMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestSuite">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="test_project_name" jdbcType="VARCHAR" property="testProjectName"/>
        <result column="testsuite_name" jdbcType="VARCHAR" property="testSuiteName"/>
        <result column="hardware_version" jdbcType="VARCHAR" property="hardwareVersion"/>
        <result column="software_version" jdbcType="VARCHAR" property="softwareVersion"/>
        <result column="begin_test_time" jdbcType="VARCHAR" property="beginTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="end_test_time" jdbcType="VARCHAR" property="endTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <resultMap id="TestSuiteVoResultMap" type="com.desaysv.workserver.vo.testcase.TestSuiteStatisticVo">
        <result column="begin_test_time" jdbcType="VARCHAR" property="beginTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="end_test_time" jdbcType="VARCHAR" property="endTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="consumed_time" jdbcType="INTEGER" property="consumedTime"/>
    </resultMap>

    <sql id="tableName">
        test_suite
    </sql>

    <sql id="allColumns">
        id, uuid, test_project_name, testsuite_name, hardware_version, software_version,
        begin_test_time, end_test_time, create_time, update_time
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="tableName"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="tableName"/>
        where id = #{id}
    </delete>

    <insert id="insert"
            useGeneratedKeys="true"
            keyColumn="id"
            keyProperty="id"
            parameterType="com.desaysv.workserver.model.TestSuite">
        insert into
        <include refid="tableName"/>
        (uuid, test_project_name, testsuite_name, hardware_version, software_version, begin_test_time, end_test_time,
        create_time, update_time)
        values
        (#{uuid}, #{testProjectName}, #{testSuiteName}, #{hardwareVersion}, #{softwareVersion},
        #{beginTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        #{endTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.TestSuite">
        update
        <include refid="tableName"/>
        set end_test_time = #{endTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        update_time = datetime('now','localtime')
        where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where id = #{id}
    </select>

    <select id="selectByUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where uuid = #{uuid}
    </select>

    <select id="selectByUniqueRecord" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where testsuite_name = #{testSuiteName}
    </select>

    <select id="selectAllStatisticsByTestSuiteUUID"
            parameterType="java.lang.String"
            resultMap="TestSuiteVoResultMap">
        select
        begin_test_time, end_test_time, timestampdiff(second, begin_test_time, end_test_time) as consumed_time
        from test_suite where uuid = #{testSuiteUUID}
    </select>

</mapper>