<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.ExcelCaseMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.ExcelCaseModel">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="tableName" jdbcType="VARCHAR" property="tableName"/>
        <result column="testCaseID" jdbcType="VARCHAR" property="testCaseID"/>
        <result column="testKey" jdbcType="VARCHAR" property="testKey"/>
        <result column="initialCondition" jdbcType="VARCHAR" property="initialCondition"/>
        <result column="action" jdbcType="VARCHAR" property="action"/>
        <result column="expectedResult" jdbcType="VARCHAR" property="expectedResult"/>
        <result column="initTestSequences" jdbcType="VARCHAR" property="initTestSequences"/>
        <result column="actionTestSequences" jdbcType="VARCHAR" property="actionTestSequences"/>
        <result column="expectedTestSequences" jdbcType="VARCHAR" property="expectedTestSequences"/>
        <result column="actualResult" jdbcType="VARCHAR" property="actualResult"/>
        <result column="testResult" jdbcType="VARCHAR" property="testResult"/>
        <result column="tester" jdbcType="VARCHAR" property="tester"/>
        <result column="testTime" jdbcType="VARCHAR" property="testTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="targetTestTimes" jdbcType="VARCHAR" property="targetTestTimes"/>
        <result column="testedTimes" jdbcType="VARCHAR" property="testedTimes"/>
        <result column="testedPassTimes" jdbcType="VARCHAR" property="testedPassTimes"/>
    </resultMap>

    <sql id="table_name">excel_test_case</sql>

    <sql id="all_columns">
        uuid, tableName,testCaseID, testKey, initialCondition, action, expectedResult,
        initTestSequences, actionTestSequences, expectedTestSequences, actualResult,
        testResult, tester, testTime, remark, targetTestTimes, testedTimes, testedPassTimes
    </sql>

    <insert id="insertExcelCaseTable" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into
        <include refid="table_name"/>
        (uuid, tableName, testCaseID, testKey, initialCondition, action, expectedResult,
        initTestSequences, actionTestSequences, expectedTestSequences, actualResult, testResult,
        tester, testTime, remark, targetTestTimes, testedTimes, testedPassTimes)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.uuid}, #{item.tableName}, #{item.testCaseID}, #{item.testKey}, #{item.initialCondition},
            #{item.action}, #{item.expectedResult},#{item.initTestSequences}, #{item.actionTestSequences},
            #{item.expectedTestSequences},#{item.actualResult}, #{item.testResult}, #{item.tester}, #{item.testTime},
            #{item.remark}, #{item.targetTestTimes}, #{item.testedTimes}, #{item.testedPassTimes})
        </foreach>
    </insert>

    <update id="updateExcelCase" parameterType="com.desaysv.workserver.model.ExcelCaseModel">
        update
        <include refid="table_name"/>
        set
        testCaseID=#{testCaseID},
        testKey=#{testKey},
        initialCondition=#{initialCondition},
        action=#{action},
        expectedResult=#{expectedResult},
        initTestSequences=#{initTestSequences},
        actionTestSequences=#{actionTestSequences},
        expectedTestSequences=#{expectedTestSequences},
        actualResult=#{actualResult},
        testResult=#{testResult},
        tester=#{tester},
        testTime=#{testTime},
        remark=#{remark},
        targetTestTimes=#{targetTestTimes},
        testedTimes=#{testedTimes},
        testedPassTimes=#{testedPassTimes}
        where uuid = #{uuid}
    </update>

    <update id="clearMapColumnData" parameterType="java.lang.String">
        update
        <include refid="table_name"/>
        set
        actualResult=NULL,
        testResult=NULL,
        tester=NULL,
        testTime=NULL
        where tableName = #{tableName}
    </update>

    <update id="batchUpdateExcelCase" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update
            <include refid="table_name"/>
            set
            testCaseID=#{item.testCaseID},
            testKey=#{item.testKey},
            initialCondition=#{item.initialCondition},
            action=#{item.action},
            expectedResult=#{item.expectedResult},
            initTestSequences=#{item.initTestSequences},
            actionTestSequences=#{item.actionTestSequences},
            expectedTestSequences=#{item.expectedTestSequences},
            actualResult=#{item.actualResult},
            testResult=#{item.testResult},
            tester=#{item.tester},
            testTime=#{item.testTime}
            where uuid = #{item.uuid}
        </foreach>
    </update>

    <select id="findExcelCaseByTableName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where tableName = #{tableName}
    </select>

    <select id="findRowCaseInfoByUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </select>

    <delete id="deleteByTableName" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where tableName = #{tableName}
    </delete>
</mapper>