<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestCaseMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestCase">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="testcase_uuid" jdbcType="VARCHAR" property="testcaseUUID"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="testcase_name" jdbcType="VARCHAR" property="testCaseName"/>
        <result column="precondition" jdbcType="VARCHAR" property="preCondition"/>
        <result column="operational_step" jdbcType="VARCHAR" property="operationalStep"/>
        <result column="expectation_result" jdbcType="VARCHAR" property="expectationResult"/>
        <result column="testsuite_uuid" jdbcType="VARCHAR" property="testSuiteUUID"/>
        <result column="testresult_uuid" jdbcType="VARCHAR" property="testResultUUID"/>
        <result column="is_pass" jdbcType="BOOLEAN" property="pass"/>
        <result column="testing" jdbcType="BOOLEAN" property="testing"/>
        <result column="begin_test_time" jdbcType="VARCHAR" property="beginTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="end_test_time" jdbcType="VARCHAR" property="endTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <resultMap id="TestCaseVoResultMap" type="com.desaysv.workserver.vo.testcase.TestCaseVo">
        <result column="testcase_uuid" jdbcType="VARCHAR" property="testcaseUUID"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="testcase_name" jdbcType="VARCHAR" property="testCaseName"/>
        <result column="precondition" jdbcType="VARCHAR" property="preCondition"/>
        <result column="operational_step" jdbcType="VARCHAR" property="operationalStep"/>
        <result column="expectation_result" jdbcType="VARCHAR" property="expectationResult"/>
        <result column="test_cycle" jdbcType="INTEGER" property="testCycle"/>
        <result column="fail_cycle" jdbcType="INTEGER" property="failCycle"/>
        <result column="sum_cycle" jdbcType="INTEGER" property="sumTestCycle"/>
        <result column="fail_rate" jdbcType="FLOAT" property="failTestRate"/>
        <result column="is_pass" jdbcType="BOOLEAN" property="pass"/>
        <result column="begin_test_time" jdbcType="VARCHAR" property="beginTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="end_test_time" jdbcType="VARCHAR" property="endTestTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="consumed_time" jdbcType="INTEGER" property="consumedTime"/>
        <result column="testing" jdbcType="BOOLEAN" property="testing"/>
    </resultMap>

    <sql id="tableName">
        test_case
    </sql>

    <sql id="allColumns">
        id, testcase_uuid, module_name, testcase_name, precondition, operational_step, expectation_result,
        testsuite_uuid, testresult_uuid, is_pass, testing, begin_test_time, end_test_time,
        create_time, update_time
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="tableName"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="tableName"/>
        where id = #{id}
    </delete>

    <insert id="insert"
            useGeneratedKeys="true"
            keyColumn="id"
            keyProperty="id"
            parameterType="com.desaysv.workserver.model.TestCase">
        insert into
        <include refid="tableName"/>
        (testcase_uuid, module_name, testcase_name, precondition, operational_step, expectation_result, testsuite_uuid,
        testresult_uuid,
        is_pass, testing, begin_test_time, end_test_time, create_time, update_time)
        values
        (#{testcaseUUID}, #{moduleName}, #{testCaseName}, #{preCondition}, #{operationalStep}, #{expectationResult},
        #{testSuiteUUID}, #{testResultUUID}, #{pass}, #{testing},
        #{beginTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        #{endTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.TestCase">
        update
        <include refid="tableName"/>
        set
        is_pass = #{pass},
        testing = #{testing},
        end_test_time = #{endTestTime, typeHandler=com.desaysv.workserver.typehandler.UpdateTimeTypeHandler},
        update_time = datetime('now','localtime')
        where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where id = #{id}
    </select>

    <select id="selectByTestCaseUUID" parameterType="java.lang.String" resultType="com.desaysv.workserver.model.TestCase">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where testcase_uuid = #{testcaseUUID}
        ORDER BY create_time DESC LIMIT 1;
    </select>

    <select id="selectByUniqueRecord" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where module_name = #{moduleName} and testcase_name = #{testCaseName} and testsuite_uuid = #{testSuiteUUID}
    </select>

    <select id="selectByTestCaseId" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where id = #{testCaseId} and testsuite_uuid = #{testSuiteUUID}

    </select>

    <select id="selectAllTestCaseVoByTestSuiteUUID" parameterType="java.lang.String" resultMap="TestCaseVoResultMap">
        select c.testcase_uuid, c.module_name, c.testcase_name, c.precondition, c.operational_step,
        c.expectation_result,
        r.test_cycle, r.fail_cycle, r.sum_cycle,
        convert(r.fail_cycle / r.sum_cycle, decimal(5,4)) as fail_rate, c.is_pass, c.begin_test_time, c.end_test_time,
        timestampdiff(second, c.begin_test_time, c.end_test_time) as consumed_time, c.testing
        from test_case c, test_result r
        where c.testsuite_uuid = #{testSuiteUUID} and c.testresult_uuid = r.uuid
    </select>

    <select id="selectTotalTestCycles" parameterType="map" resultType="java.lang.Integer">
        select ifnull(sum(r.test_cycle),0) from test_case c, test_result r, test_suite s
        where r.uuid = c.testresult_uuid and c.module_name = #{moduleName} and c.testcase_name = #{testCaseName} and
        c.testsuite_uuid = s.uuid and s.test_project_name = #{testProjectName}
    </select>

</mapper>