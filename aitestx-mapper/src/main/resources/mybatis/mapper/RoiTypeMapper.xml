<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.desaysv.workserver.mapper.roi.RoiTypeMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.roi.RoiType">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
    </resultMap>


    <sql id="table_name">
        roi_type
    </sql>

    <sql id="all_columns">
        id, name
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="name!=null and name!=''">
                AND name LIKE '%' || #{name} || '%'
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="table_name"/>
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.roi.RoiType"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, name)
        values (#{id}, #{name})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.roi.RoiType">
        update
        <include refid="table_name"/>
        set name = #{name} where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = #{name}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.roi.RoiType"
            resultType="java.lang.Integer">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.roi.RoiType"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>


</mapper>