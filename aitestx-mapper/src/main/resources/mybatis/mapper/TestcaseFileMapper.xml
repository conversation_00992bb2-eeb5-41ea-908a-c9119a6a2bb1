<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestcaseFileMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestScriptFile">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="selected" javaType="BOOLEAN" property="selected"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="case_name" jdbcType="VARCHAR" property="caseName"/>
        <result column="comment" jdbcType="VARCHAR" property="comment"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="client_id" jdbcType="INTEGER" property="clientId"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        testcase_file
    </sql>

    <sql id="if_test">
        <where>
            <if test="moduleName!=null and moduleName!=''">
                AND module_name=#{moduleName}
            </if>
            <if test="caseName!=null and caseName!=''">
                AND case_name=#{caseName}
            </if>
            AND project_id = #{projectId}
            AND client_id = #{clientId}
        </where>
    </sql>

    <sql id="all_columns">
        id, selected, uuid, module_name, case_name, comment, project_id, client_id, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.desaysv.workserver.model.TestScriptFile"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, selected, uuid, module_name, case_name, comment, project_id, client_id, create_time, update_time)
        values (#{id}, #{selected}, #{uuid}, #{moduleName}, #{caseName}, #{comment}, #{projectId}, #{clientId},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>


    <select id="selectByTestCaseFileUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </select>

    <select id="selectByCondition" parameterType="com.desaysv.workserver.vo.testcase.TestcaseFileVo"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        <!--        where module_name = #{moduleName} and case_name = #{caseName}-->
        <!--        and project_id = #{projectId} and client_id = #{clientId}-->
    </select>

    <select id="selectAll" parameterType="com.desaysv.workserver.vo.testcase.TestcaseFileVo" resultMap="BaseResultMap">
        select * from
        <include refid="table_name"/>
        where project_id = #{projectId} and client_id = #{clientId}
    </select>

    <update id="updateByTestScriptFileUUID" parameterType="com.desaysv.workserver.model.TestScriptFile">
        update
        <include refid="table_name"/>
        set selected = #{selected},
        module_name = #{moduleName},
        case_name = #{caseName},
        comment = #{comment},
        update_time = datetime('now','localtime')
        where uuid = #{uuid}
    </update>

    <update id="updateAllSelected" parameterType="com.desaysv.workserver.bo.TestScriptFileSelector">
        update
        <include refid="table_name"/>
        set selected = #{selectAll}
        where project_id = #{projectId} and client_id = #{clientId}
    </update>

    <delete id="deleteByTestCaseFileUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </delete>

    <delete id="deleteAll" parameterType="com.desaysv.workserver.vo.testcase.TestcaseFileVo">
        delete from
        <include refid="table_name"/>
        where project_id = #{projectId} and client_id = #{clientId}
    </delete>

</mapper>