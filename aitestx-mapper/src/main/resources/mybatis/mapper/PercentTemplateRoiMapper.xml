<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.roi.PercentTemplateRoiMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.roi.PercentTemplateRoi">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_id" jdbcType="TINYINT" property="typeId"/>
        <result column="template_picture_uuid" jdbcType="VARCHAR" property="templatePictureUUID"/>
        <result column="start_x" jdbcType="DOUBLE" property="startX"/>
        <result column="start_y" jdbcType="DOUBLE" property="startY"/>
        <result column="end_x" jdbcType="DOUBLE" property="endX"/>
        <result column="end_y" jdbcType="DOUBLE" property="endY"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        percent_template_roi
    </sql>

    <sql id="all_columns">
        id, type_id, template_picture_uuid, start_x, start_y, end_x, end_y, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="type_id!=null">
                AND type_id=#{typeId}
            </if>
            <if test="template_picture_uuid!=null and template_picture_uuid!=''">
                AND template_picture_uuid=#{templatePictureUUID}
            </if>
            <if test="startX!=null">
                AND start_x=#{startX}
            </if>
            <if test="startY!=null">
                AND start_y=#{startY}
            </if>
            <if test="endX!=null">
                AND end_x=#{endX}
            </if>
            <if test="endY!=null">
                AND end_y=#{endY}
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByTemplatePictureUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where template_picture_uuid = #{templatePictureUUID}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.roi.PercentTemplateRoi"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, type_id, template_picture_uuid, start_x, start_y, end_x, end_y, create_time, update_time)
        values (#{id}, #{typeId}, #{templatePictureUUID}, #{startX}, #{startY}, #{endX}, #{endY},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByTemplatePictureUUID" parameterType="com.desaysv.workserver.model.roi.PercentTemplateRoi">
        update
        <include refid="table_name"/>
        set type_id = #{typeId},
        start_x = #{startX},
        start_y = #{startY},
        end_x = #{endX},
        end_y = #{endY},
        update_time = datetime('now','localtime')
        where template_picture_uuid = #{templatePictureUUID}
    </update>

    <select id="selectByTemplatePictureUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where template_picture_uuid = #{templatePictureUUID}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.roi.PercentTemplateRoi"
            resultType="java.lang.Long">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.roi.PercentTemplateRoi"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>
    <select id="selectAllByProjectAndDevice" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where template_picture_uuid in (select template_picture_uuid from template_picture where project_name =
        #{projectName} and device_unique_code = #{deviceUniqueCode})
    </select>
</mapper>