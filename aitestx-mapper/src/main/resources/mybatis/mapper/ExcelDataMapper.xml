<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.ExcelDataMapper">
    <insert id="insertExcelFile" parameterType="com.desaysv.workserver.excel.ExcelFileEntity" useGeneratedKeys="true"
            keyProperty="excelFileId">
        INSERT INTO excel_case_file_info (project_id, excel_file_name) VALUES (#{projectId}, #{excelFileName})
    </insert>

    <delete id="truncateExcelFileTable">
        DELETE FROM excel_case_file_info
    </delete>

    <insert id="insertExcelSheet" parameterType="com.desaysv.workserver.excel.ExcelSheetEntity" useGeneratedKeys="true"
            keyProperty="sheetId">
        INSERT INTO excel_case_sheet_info (excel_file_id, sheet_name, selected, table_header, header_filter_options)
        VALUES (#{excelFileId}, #{sheetName}, #{selected}, #{tableHeader}, #{headerFilterOptions})
    </insert>

    <select id="findSheetDataBySheetName" parameterType="java.lang.String" resultType="com.desaysv.workserver.excel.ExcelSheetEntity">
        SELECT * FROM excel_case_sheet_info WHERE sheet_name = #{sheetName}
    </select>

    <delete id="truncateExcelSheetTable">
        DELETE FROM excel_case_sheet_info
    </delete>

    <insert id="insertExcelData" parameterType="com.desaysv.workserver.excel.ExcelDataEntity" useGeneratedKeys="true"
            keyProperty="dataId">
        INSERT INTO excel_case_data_info (
        sheet_id, row_number, column1, column2, column3, column4, column5, column6, column7, column8, column9,
        column10, column11, column12, column13, column14, column15, column16, column17, column18, column19, column20,
        column21, column22, column23, column24, column25, column26, column27, column28, column29, column30, column31,
        column32, column33, column34, column35, column36, column37, column38, column39, column40, column41, column42,
        column43, column44, column45, column46, column47, column48, column49, column50)
        VALUES (
        #{sheetId}, #{rowNumber}, #{column1}, #{column2}, #{column3}, #{column4}, #{column5}, #{column6},
        #{column7}, #{column8}, #{column9}, #{column10}, #{column11}, #{column12}, #{column13}, #{column14}, #{column15},
        #{column16}, #{column17}, #{column18}, #{column19}, #{column20}, #{column21}, #{column22}, #{column23}, #{column24},
        #{column25}, #{column26}, #{column27}, #{column28}, #{column29}, #{column30}, #{column31}, #{column32}, #{column33},
        #{column34}, #{column35}, #{column36}, #{column37}, #{column38}, #{column39}, #{column40}, #{column41}, #{column42},
        #{column43}, #{column44}, #{column45}, #{column46}, #{column47}, #{column48}, #{column49}, #{column50})
    </insert>

    <insert id="insertExcelDataBatch" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="dataId">
        INSERT INTO excel_case_data_info (
        sheet_id, row_number, column1, column2, column3, column4, column5, column6, column7, column8, column9,
        column10, column11, column12, column13, column14, column15, column16, column17, column18, column19, column20,
        column21, column22, column23, column24, column25, column26, column27, column28, column29, column30, column31,
        column32, column33, column34, column35, column36, column37, column38, column39, column40, column41, column42,
        column43, column44, column45, column46, column47, column48, column49, column50)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.sheetId}, #{item.rowNumber}, #{item.column1}, #{item.column2}, #{item.column3}, #{item.column4}, #{item.column5}, #{item.column6},
            #{item.column7}, #{item.column8}, #{item.column9}, #{item.column10}, #{item.column11}, #{item.column12}, #{item.column13}, #{item.column14}, #{item.column15},
            #{item.column16}, #{item.column17}, #{item.column18}, #{item.column19}, #{item.column20}, #{item.column21}, #{item.column22}, #{item.column23}, #{item.column24},
            #{item.column25}, #{item.column26}, #{item.column27}, #{item.column28}, #{item.column29}, #{item.column30}, #{item.column31}, #{item.column32}, #{item.column33},
            #{item.column34}, #{item.column35}, #{item.column36}, #{item.column37}, #{item.column38}, #{item.column39}, #{item.column40}, #{item.column41}, #{item.column42},
            #{item.column43}, #{item.column44}, #{item.column45}, #{item.column46}, #{item.column47}, #{item.column48}, #{item.column49}, #{item.column50}
            )
        </foreach>
    </insert>

    <update id="updateExcelData" parameterType="com.desaysv.workserver.excel.ExcelDataEntity">
        UPDATE excel_case_data_info
        <set>
            <if test="column1 != null">column1 = #{column1},</if>
            <if test="column2 != null">column2 = #{column2},</if>
            <if test="column3 != null">column3 = #{column3},</if>
            <if test="column4 != null">column4 = #{column4},</if>
            <if test="column5 != null">column5 = #{column5},</if>
            <if test="column6 != null">column6 = #{column6},</if>
            <if test="column7 != null">column7 = #{column7},</if>
            <if test="column8 != null">column8 = #{column8},</if>
            <if test="column9 != null">column9 = #{column9},</if>
            <if test="column10 != null">column10 = #{column10},</if>
            <if test="column11 != null">column11 = #{column11},</if>
            <if test="column12 != null">column12 = #{column12},</if>
            <if test="column13 != null">column13 = #{column13},</if>
            <if test="column14 != null">column14 = #{column14},</if>
            <if test="column15 != null">column15 = #{column15},</if>
            <if test="column16 != null">column16 = #{column16},</if>
            <if test="column17 != null">column17 = #{column17},</if>
            <if test="column18 != null">column18 = #{column18},</if>
            <if test="column19 != null">column19 = #{column19},</if>
            <if test="column20 != null">column20 = #{column20},</if>
            <if test="column21 != null">column21 = #{column21},</if>
            <if test="column22 != null">column22 = #{column22},</if>
            <if test="column23 != null">column23 = #{column23},</if>
            <if test="column24 != null">column24 = #{column24},</if>
            <if test="column25 != null">column25 = #{column25},</if>
            <if test="column26 != null">column26 = #{column26},</if>
            <if test="column27 != null">column27 = #{column27},</if>
            <if test="column28 != null">column28 = #{column28},</if>
            <if test="column29 != null">column29 = #{column29},</if>
            <if test="column30 != null">column30 = #{column30},</if>
            <if test="column31 != null">column31 = #{column31},</if>
            <if test="column32 != null">column32 = #{column32},</if>
            <if test="column33 != null">column33 = #{column33},</if>
            <if test="column34 != null">column34 = #{column34},</if>
            <if test="column35 != null">column35 = #{column35},</if>
            <if test="column36 != null">column36 = #{column36},</if>
            <if test="column37 != null">column37 = #{column37},</if>
            <if test="column38 != null">column38 = #{column38},</if>
            <if test="column39 != null">column39 = #{column39},</if>
            <if test="column40 != null">column40 = #{column40},</if>
            <if test="column41 != null">column41 = #{column41},</if>
            <if test="column42 != null">column42 = #{column42},</if>
            <if test="column43 != null">column43 = #{column43},</if>
            <if test="column44 != null">column44 = #{column44},</if>
            <if test="column45 != null">column45 = #{column45},</if>
            <if test="column46 != null">column46 = #{column46},</if>
            <if test="column47 != null">column47 = #{column47},</if>
            <if test="column48 != null">column48 = #{column48},</if>
            <if test="column49 != null">column49 = #{column49},</if>
            <if test="column50 != null">column50 = #{column50},</if>
        </set>
        WHERE sheet_id = #{sheetId} AND row_number = #{rowNumber}
    </update>

    <select id="findExcelDataBySheetId" parameterType="java.lang.Integer" resultType="com.desaysv.workserver.excel.ExcelDataEntity">
        SELECT * FROM excel_case_data_info WHERE sheet_id = #{sheetId} ORDER BY row_number ASC
    </select>

    <delete id="deleteDataById" parameterType="java.lang.Integer">
        delete from excel_case_data_info
        where sheet_id = #{sheetId}
    </delete>

    <delete id="truncateExcelDataTable">
        DELETE FROM excel_case_data_info
    </delete>

    <update id="clearMapColumnData" parameterType="java.util.Map">
        update
        excel_case_data_info
        set
        <foreach collection="columns" item="column" separator=",">
            ${column} = NULL
        </foreach>
        where sheet_id = #{sheetId}
    </update>

    <!--    <insert id="insertTestResult" parameterType="com.desaysv.workserver.excel.TestResult" useGeneratedKeys="true" keyProperty="testResultId">-->
    <!--        INSERT INTO test_result_info (data_id, test_time, is_passed, failure_rate, test_count, pass_count, fail_count, remarks)-->
    <!--        VALUES (#{dataId}, #{testTime}, #{isPassed}, #{failureRate}, #{testCount}, #{passCount}, #{failCount}, #{remarks})-->
    <!--    </insert>-->

</mapper>