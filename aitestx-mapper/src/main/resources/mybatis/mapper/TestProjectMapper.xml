<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestProjectMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestProject">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="status" jdbcType="SMALLINT" property="status"/>
        <result column="customer_id" jdbcType="INTEGER" property="customerId"/>
        <result column="department_id" jdbcType="INTEGER" property="departmentId"/>
        <result column="project_info_id" jdbcType="INTEGER" property="projectInfoId"/>
        <result column="communal" jdbcType="BOOLEAN" property="communal"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        test_project
    </sql>

    <sql id="all_columns">
        id, name, model, status, customer_id, department_id, project_info_id, communal, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="name!=null and name!=''">
                AND name LIKE '%' || #{name} || '%'
            </if>
            <if test="model!=null and model!=''">
                AND model LIKE '%' || #{model} || '%'
            </if>
            <if test="status!=null">
                AND status=#{status}
            </if>
            <if test="customerId!=null">
                AND customer_id=#{customerId}
            </if>
            <if test="departmentId!=null">
                AND department_id=#{departmentId}
            </if>
            <if test="projectInfoId!=null">
                AND project_info_id=#{projectInfoId}
            </if>
            <if test="communal!=null">
                AND communal=#{communal}
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from
        <include refid="table_name"/>
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.TestProject"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, name, model, status, customer_id, department_id, project_info_id, communal, create_time, update_time)
        values (#{id}, #{name}, #{model}, #{status}, #{customerId}, #{departmentId}, #{projectInfoId}, #{communal},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.desaysv.workserver.model.TestProject">
        update
        <include refid="table_name"/>
        set name = #{name},
        model = #{model},
        status = #{status},
        customer_id = #{customerId},
        department_id = #{departmentId},
        project_info_id = #{projectInfoId},
        communal = #{communal},
        update_time = datetime('now','localtime')
        where id = #{id}
    </update>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where id = #{id}
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = #{name}
    </select>

    <select id="selectPublicProject" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where name = "__PUBLIC_PROJECT__"
    </select>

    <select id="selectDepartments" parameterType="com.desaysv.workserver.model.TestProject"
            resultType="com.desaysv.workserver.model.Department">
        select * from
        department
        where id in (select department_id from
        <include refid="table_name"/>
        where id = #{id})
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.TestProject"
            resultType="java.lang.Integer">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.TestProject"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>
</mapper>