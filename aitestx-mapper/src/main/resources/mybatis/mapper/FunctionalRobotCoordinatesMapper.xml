<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.FunctionalRobotCoordinatesMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.FunctionalRobotCoordinates">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="func" jdbcType="INTEGER" property="function"/>
        <result column="enable" jdbcType="BOOLEAN" property="enable"/>
        <result column="x" jdbcType="DOUBLE" property="x"/>
        <result column="y" jdbcType="DOUBLE" property="y"/>
        <result column="z" jdbcType="DOUBLE" property="z"/>
        <result column="r" jdbcType="DOUBLE" property="r"/>
        <result column="slide_rail" jdbcType="DOUBLE" property="slideRail"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="device_id" jdbcType="INTEGER" property="deviceId"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="table_name">
        functional_robot_coordinates
    </sql>

    <sql id="all_columns">
        id, uuid, name, alias, func, enable, x, y, z, r, slide_rail, project_id, device_id, create_time, update_time
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByFunctionCoordinatesUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </delete>

    <delete id="deleteByCondition" parameterType="com.desaysv.workserver.model.FunctionalRobotCoordinates">
        delete from
        <include refid="table_name"/>
        where name = #{name} and func = #{function} and project_id = #{projectId} and device_id = #{deviceId}
    </delete>

    <delete id="clearAllByCondition" parameterType="java.lang.Integer">
        delete from
        <include refid="table_name"/>
        where func = #{function} and project_id = #{projectId} and device_id = #{deviceId}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.FunctionalRobotCoordinates"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, uuid, name, alias, func, enable, x, y, z, r, slide_rail, project_id, device_id, create_time, update_time)
        values
        (#{id}, #{uuid}, #{name}, #{alias}, #{function}, #{enable}, #{x}, #{y}, #{z}, #{r}, #{slideRail}, #{projectId},
        #{deviceId}, datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <update id="updateByFunctionCoordinatesUUID" parameterType="com.desaysv.workserver.model.FunctionalRobotCoordinates">
        update
        <include refid="table_name"/>
        set name = #{name},
        alias = #{alias},
        func = #{function},
        enable = #{enable},
        x = #{x},
        y = #{y},
        z = #{z},
        r = #{r},
        slide_rail = #{slideRail},
        project_id = #{projectId},
        device_id = #{deviceId},
        update_time = datetime('now','localtime')
        where uuid = #{uuid}
    </update>

    <select id="selectByFunctionCoordinatesUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </select>

    <select id="selectByCondition" parameterType="com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where func = #{function} and project_id = #{projectId} and device_id = #{deviceId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectAllByCondition" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where func = #{function} and project_id = #{projectId} and device_id = #{deviceId}
    </select>


</mapper>