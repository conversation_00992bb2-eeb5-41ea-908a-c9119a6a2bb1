<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.OriginalPictureMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.OriginalPicture">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="width" jdbcType="SMALLINT" property="width"/>
        <result column="height" jdbcType="SMALLINT" property="height"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>


    <sql id="table_name">
        original_picture
    </sql>

    <sql id="all_columns">
        id, uuid, width, height, create_time, update_time
    </sql>

    <sql id="if_test">
        <where>
            <if test="id!=null">
                AND id=#{id}
            </if>
            <if test="uuid!=null and uuid!=''">
                AND uuid LIKE '%' || #{uuid} || '%'
            </if>
        </where>
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="table_name"/>
    </delete>

    <delete id="deleteByOriginalPictureUUID" parameterType="java.lang.String">
        delete from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </delete>

    <insert id="insert" parameterType="com.desaysv.workserver.model.OriginalPicture"
            useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (id, uuid, width, height, create_time, update_time)
        values (#{id}, #{uuid}, #{width}, #{height}, datetime('now','localtime'),
        datetime('now','localtime'))
    </insert>

    <update id="updateByOriginalPictureUUID" parameterType="com.desaysv.workserver.model.OriginalPicture">
        update
        <include refid="table_name"/>
        set width = #{width},
        height = #{height},
        update_time = datetime('now','localtime')
        where uuid = #{uuid}
    </update>

    <select id="selectByOriginalPictureUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where uuid = #{uuid}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
    </select>

    <select id="selectTotalPage"
            parameterType="com.desaysv.workserver.model.OriginalPicture"
            resultType="java.lang.Long">
        select count(id) as totalPage from
        <include refid="table_name"/>
        <include refid="if_test"/>
    </select>

    <select id="selectByDimension"
            parameterType="com.desaysv.workserver.model.OriginalPicture"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        where width=#{width} and height=#{height}
    </select>

    <select id="selectByCondition"
            parameterType="com.desaysv.workserver.model.OriginalPicture"
            resultMap="BaseResultMap">
        select
        <include refid="all_columns"/>
        from
        <include refid="table_name"/>
        <include refid="if_test"/>
        order by update_time desc
    </select>

</mapper>