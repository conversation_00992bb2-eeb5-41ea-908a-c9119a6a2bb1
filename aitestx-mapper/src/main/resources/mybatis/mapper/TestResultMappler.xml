<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.TestResultMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.TestResult">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="uuid" jdbcType="VARCHAR" property="uuid"/>
        <result column="sum_cycle" jdbcType="INTEGER" property="sumCycle"/>
        <result column="test_cycle" jdbcType="INTEGER" property="testCycle"/>
        <result column="fail_cycle" jdbcType="INTEGER" property="failCycle"/>
        <result column="summary" jdbcType="VARCHAR" property="summary"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"
                typeHandler="com.desaysv.workserver.typehandler.UpdateTimeTypeHandler"/>
    </resultMap>

    <sql id="tableName">
        test_result
    </sql>

    <sql id="allColumns">
        id, uuid, sum_cycle, test_cycle, fail_cycle, summary, create_time, update_time
    </sql>

    <delete id="truncate">
        truncate table
        <include refid="tableName"/>
    </delete>

    <delete id="deleteByTestResultUUID" parameterType="java.lang.String">
        delete from
        <include refid="tableName"/>
        where uuid = #{uuid}
    </delete>

    <insert id="insert"
            useGeneratedKeys="true"
            keyColumn="id"
            keyProperty="id"
            parameterType="com.desaysv.workserver.model.TestResult">
        insert into
        <include refid="tableName"/>
        (uuid, sum_cycle, test_cycle, fail_cycle, summary, create_time, update_time)
        values
        (#{uuid}, #{sumCycle}, #{testCycle}, #{failCycle}, #{summary},
        datetime('now','localtime'), datetime('now','localtime'))
    </insert>

    <select id="selectByTestResultUUID" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from
        <include refid="tableName"/>
        where uuid = #{uuid}
    </select>

    <update id="updateByTestResultUUID" parameterType="com.desaysv.workserver.model.TestResult">
        update
        <include refid="tableName"/>
        set sum_cycle = #{sumCycle},
        test_cycle = #{testCycle},
        fail_cycle = #{failCycle},
        summary = #{summary},
        update_time = datetime('now','localtime')
        where uuid = #{uuid}
    </update>

</mapper>