<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.desaysv.workserver.mapper.ElectricRelayMapper">
    <resultMap id="BaseResultMap" type="com.desaysv.workserver.model.relay.ElectricRelayEntity">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="alias_name" jdbcType="VARCHAR" property="aliasName"/>
        <result column="project" jdbcType="VARCHAR" property="project"/>
        <result column="connected_status" jdbcType="VARCHAR" property="connectedStatus"/>
        <result column="relay_text" jdbcType="VARCHAR" property="relayText"/>
        <result column="channel" jdbcType="INTEGER" property="channel"/>
        <result column="switch_on" jdbcType="INTEGER" property="switchOn"/>
    </resultMap>

    <sql id="table_name">
        electric_relay_info
    </sql>

    <sql id="all_columns">
        id, alias_name, project, connected_status, relay_text, channel, switch_on
    </sql>

    <!-- 新增 -->
    <insert id="insertText" parameterType="com.desaysv.workserver.model.relay.ElectricRelayEntity">
        INSERT INTO
        <include refid="table_name"/>
        (alias_name, project, connected_status, relay_text, channel, switch_on)
        VALUES(
        #{aliasName}, #{project}, #{connectedStatus},
        #{relayText}, #{channel}, #{switchOn}
        )
    </insert>

    <!-- 修改文本消息 -->
    <update id="updateText" parameterType="com.desaysv.workserver.model.relay.ElectricRelayEntity">
        UPDATE  <include refid="table_name"/>
        <set>
            alias_name = #{aliasName},
            <if test="connectedStatus != null">connected_status = #{connectedStatus},</if>
            <if test="relayText != null">relay_text = #{relayText},</if>
            <if test="channel != null">channel = #{channel},</if>
            <if test="switchOn != null">switch_on = #{switchOn},</if>
        </set>
        WHERE alias_name = #{aliasName}
             AND project = #{project}
            AND channel = #{channel}
    </update>

    <!-- 修改连接状态 -->
    <update id="updateStatus" parameterType="com.desaysv.workserver.model.relay.ElectricRelayEntity">
        UPDATE  <include refid="table_name"/>
        set connected_status = #{connectedStatus}
        WHERE alias_name = #{aliasName}
        AND project = #{project}
    </update>

    <!-- 设备断开 修改连接、开关状态  -->
    <update id="updateConnectSwitchStatus" parameterType="com.desaysv.workserver.model.relay.ElectricRelayEntity">
        UPDATE  <include refid="table_name"/>
        set connected_status = #{connectedStatus},
            switch_on = #{switchOn}
        WHERE alias_name = #{aliasName}
        AND project = #{project}
    </update>

    <!-- 查询根据项目名称和设备别名 -->
    <select id="selectByAliasAndProject" resultMap="BaseResultMap">
        SELECT
        <include refid="all_columns"/>
        FROM  <include refid="table_name"/>
        WHERE project = #{project}
        <if test="aliasName!=null and aliasName!=''">
            AND alias_name = #{aliasName}
        </if>
    </select>
</mapper>