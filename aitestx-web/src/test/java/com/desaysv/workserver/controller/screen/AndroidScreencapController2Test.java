package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * AndroidScreencapController2 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class AndroidScreencapController2Test {

    @Mock
    private DeviceRegisterManager deviceRegisterManager;

    @Mock
    private AndroidDevice androidDevice;

    @InjectMocks
    private AndroidScreencapController2 androidScreencapController2;

    private MockMvc mockMvc;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(androidScreencapController2).build();
    }

    @Test
    public void testStartVideoStream_Success() throws Exception {
        // 模拟视频流数据
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        // 模拟设备管理器返回Android设备
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(deviceRegisterManager.isRegistered("test_device")).thenReturn(true);
        when(androidDevice.startVideoStream(0, 0, 0)).thenReturn(mockInputStream);

        mockMvc.perform(get("/android/screencap2/stream/test_device"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andExpect(header().string("Cache-Control", "no-cache, no-store, must-revalidate"))
                .andExpect(header().string("X-Device-Name", "test_device"))
                .andExpect(header().string("X-Video-Resolution", "默认"))
                .andExpect(header().string("X-Video-BitRate", "默认"));

        verify(deviceRegisterManager).getDevice("test_device");
        verify(androidDevice).startVideoStream(0, 0, 0);
    }

    @Test
    public void testStartVideoStreamWithParams_Success() throws Exception {
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(deviceRegisterManager.isRegistered("test_device")).thenReturn(true);
        when(androidDevice.startVideoStream(1920, 1080, 4000000)).thenReturn(mockInputStream);

        mockMvc.perform(get("/android/screencap2/stream/test_device/params")
                        .param("width", "1920")
                        .param("height", "1080")
                        .param("bitRate", "4000000"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andExpect(header().string("X-Device-Name", "test_device"))
                .andExpect(header().string("X-Video-Resolution", "1920x1080"))
                .andExpect(header().string("X-Video-BitRate", "4Mbps"));

        verify(androidDevice).startVideoStream(1920, 1080, 4000000);
    }

    @Test
    public void testStartVideoStream_DeviceNotFound() throws Exception {
        when(deviceRegisterManager.getDevice("nonexistent_device")).thenReturn(null);

        mockMvc.perform(get("/android/screencap2/stream/nonexistent_device"))
                .andExpect(status().isNotFound());

        verify(deviceRegisterManager).getDevice("nonexistent_device");
        verify(androidDevice, never()).startVideoStream(anyInt(), anyInt(), anyInt());
    }

    @Test
    public void testStartVideoStream_NotAndroidDevice() throws Exception {
        // 模拟返回非Android设备
        Object nonAndroidDevice = new Object();
        when(deviceRegisterManager.getDevice("non_android_device")).thenReturn(nonAndroidDevice);

        mockMvc.perform(get("/android/screencap2/stream/non_android_device"))
                .andExpect(status().isNotFound());

        verify(deviceRegisterManager).getDevice("non_android_device");
        verify(androidDevice, never()).startVideoStream(anyInt(), anyInt(), anyInt());
    }

    @Test
    public void testStartVideoStream_StartVideoStreamFails() throws Exception {
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(androidDevice.startVideoStream(0, 0, 0)).thenReturn(null);

        mockMvc.perform(get("/android/screencap2/stream/test_device"))
                .andExpect(status().isInternalServerError());

        verify(androidDevice).startVideoStream(0, 0, 0);
    }

    @Test
    public void testStartVideoStream_IOException() throws Exception {
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(androidDevice.startVideoStream(0, 0, 0)).thenThrow(new java.io.IOException("Test IOException"));

        mockMvc.perform(get("/android/screencap2/stream/test_device"))
                .andExpect(status().isInternalServerError());

        verify(androidDevice).startVideoStream(0, 0, 0);
    }

    @Test
    public void testGetActiveStreamCount() {
        // 初始状态应该没有活跃流
        assertEquals(0, androidScreencapController2.getActiveStreamCount());
    }

    @Test
    public void testHasActiveStream() {
        // 初始状态应该没有活跃流
        assertFalse(androidScreencapController2.hasActiveStream("test_device"));
    }

    @Test
    public void testForceStopStream() {
        // 测试强制停止流（当没有活跃流时应该不会出错）
        androidScreencapController2.forceStopStream("test_device");
        
        // 验证没有异常抛出
        assertFalse(androidScreencapController2.hasActiveStream("test_device"));
    }

    @Test
    public void testStopAllStreams() {
        // 测试停止所有流（当没有活跃流时应该不会出错）
        androidScreencapController2.stopAllStreams();
        
        // 验证没有异常抛出
        assertEquals(0, androidScreencapController2.getActiveStreamCount());
    }

    @Test
    public void testDefaultParameterValues() throws Exception {
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(deviceRegisterManager.isRegistered("test_device")).thenReturn(true);
        when(androidDevice.startVideoStream(0, 0, 0)).thenReturn(mockInputStream);

        // 测试不提供参数时使用默认值
        mockMvc.perform(get("/android/screencap2/stream/test_device/params"))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Video-Resolution", "默认"))
                .andExpect(header().string("X-Video-BitRate", "默认"));

        verify(androidDevice).startVideoStream(0, 0, 0);
    }

    @Test
    public void testCustomParameterValues() throws Exception {
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(deviceRegisterManager.isRegistered("test_device")).thenReturn(true);
        when(androidDevice.startVideoStream(800, 600, 2000000)).thenReturn(mockInputStream);

        // 测试自定义参数
        mockMvc.perform(get("/android/screencap2/stream/test_device/params")
                        .param("width", "800")
                        .param("height", "600")
                        .param("bitRate", "2000000"))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Video-Resolution", "800x600"))
                .andExpect(header().string("X-Video-BitRate", "2Mbps"));

        verify(androidDevice).startVideoStream(800, 600, 2000000);
    }

    @Test
    public void testBitRateFormatting() throws Exception {
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(deviceRegisterManager.getDevice("test_device")).thenReturn(androidDevice);
        when(deviceRegisterManager.isRegistered("test_device")).thenReturn(true);
        when(androidDevice.startVideoStream(0, 0, 500000)).thenReturn(mockInputStream);

        // 测试Kbps格式
        mockMvc.perform(get("/android/screencap2/stream/test_device/params")
                        .param("bitRate", "500000"))
                .andExpect(status().isOk())
                .andExpect(header().string("X-Video-BitRate", "500Kbps"));

        verify(androidDevice).startVideoStream(0, 0, 500000);
    }
}
