package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.dto.VideoStreamRequest;
import com.desaysv.workserver.service.AndroidVideoStreamService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * AndroidScreencapController 测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class AndroidScreencapControllerTest {

    @Mock
    private AndroidVideoStreamService androidVideoStreamService;

    @InjectMocks
    private AndroidScreencapController androidScreencapController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(androidScreencapController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testStartVideoStream_Success() throws Exception {
        // 模拟视频流数据
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(androidVideoStreamService.startVideoStream(eq("test_device"), eq(0), eq(0), eq(0)))
                .thenReturn(mockInputStream);

        mockMvc.perform(get("/android/screencap/stream/test_device"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andExpect(header().string("Cache-Control", "no-cache, no-store, must-revalidate"));

        verify(androidVideoStreamService).startVideoStream("test_device", 0, 0, 0);
        verify(androidVideoStreamService).stopVideoStream("test_device");
    }

    @Test
    public void testStartVideoStreamWithParams_Success() throws Exception {
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(androidVideoStreamService.startVideoStream(eq("test_device"), eq(1920), eq(1080), eq(4000000)))
                .thenReturn(mockInputStream);

        mockMvc.perform(get("/android/screencap/stream/test_device/params")
                        .param("width", "1920")
                        .param("height", "1080")
                        .param("bitRate", "4000000"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andExpect(header().string("X-Device-Name", "test_device"))
                .andExpect(header().string("X-Video-Resolution", "1920x1080"))
                .andExpect(header().string("X-Video-BitRate", "4Mbps"));

        verify(androidVideoStreamService).startVideoStream("test_device", 1920, 1080, 4000000);
    }

    @Test
    public void testStartVideoStreamWithRequest_Success() throws Exception {
        VideoStreamRequest request = new VideoStreamRequest("test_device", 1920, 1080, 4000000);
        byte[] videoData = "mock video stream data".getBytes();
        InputStream mockInputStream = new ByteArrayInputStream(videoData);
        
        when(androidVideoStreamService.startVideoStream(eq("test_device"), eq(1920), eq(1080), eq(4000000)))
                .thenReturn(mockInputStream);

        mockMvc.perform(post("/android/screencap/stream")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"));

        verify(androidVideoStreamService).startVideoStream("test_device", 1920, 1080, 4000000);
    }

    @Test
    public void testStartVideoStream_DeviceNotFound() throws Exception {
        when(androidVideoStreamService.startVideoStream(eq("nonexistent_device"), eq(0), eq(0), eq(0)))
                .thenThrow(new IllegalArgumentException("未找到Android设备: nonexistent_device"));

        mockMvc.perform(get("/android/screencap/stream/nonexistent_device"))
                .andExpect(status().isNotFound());

        verify(androidVideoStreamService).startVideoStream("nonexistent_device", 0, 0, 0);
    }

    @Test
    public void testStopVideoStream_Success() throws Exception {
        doNothing().when(androidVideoStreamService).stopVideoStream("test_device");

        mockMvc.perform(post("/android/screencap/stop/test_device"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.data").value("test_device"));

        verify(androidVideoStreamService).stopVideoStream("test_device");
    }

    @Test
    public void testStopVideoStream_DeviceNotFound() throws Exception {
        doThrow(new IllegalArgumentException("未找到Android设备: nonexistent_device"))
                .when(androidVideoStreamService).stopVideoStream("nonexistent_device");

        mockMvc.perform(post("/android/screencap/stop/nonexistent_device"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(false));

        verify(androidVideoStreamService).stopVideoStream("nonexistent_device");
    }

    @Test
    public void testGetVideoStreamStatus_Success() throws Exception {
        when(androidVideoStreamService.isVideoStreamRunning("test_device")).thenReturn(true);

        mockMvc.perform(get("/android/screencap/status/test_device"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.data").value(true));

        verify(androidVideoStreamService).isVideoStreamRunning("test_device");
    }

    @Test
    public void testGetActiveStreamCount_Success() throws Exception {
        when(androidVideoStreamService.getActiveStreamCount("test_device")).thenReturn(2);

        mockMvc.perform(get("/android/screencap/count/test_device"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.data").value(2));

        verify(androidVideoStreamService).getActiveStreamCount("test_device");
    }

    @Test
    public void testGetActiveStreamDevices_Success() throws Exception {
        java.util.Set<String> activeDevices = java.util.Set.of("device1", "device2");
        when(androidVideoStreamService.getActiveStreamDevices()).thenReturn(activeDevices);

        mockMvc.perform(get("/android/screencap/active"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true))
                .andExpect(jsonPath("$.data").isArray());

        verify(androidVideoStreamService).getActiveStreamDevices();
    }

    @Test
    public void testStopAllVideoStreams_Success() throws Exception {
        doNothing().when(androidVideoStreamService).stopAllVideoStreams();

        mockMvc.perform(post("/android/screencap/stopAll"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.ok").value(true));

        verify(androidVideoStreamService).stopAllVideoStreams();
    }

    @Test
    public void testVideoStreamRequest_Validation() throws Exception {
        // 测试无效的设备名称
        VideoStreamRequest invalidRequest = new VideoStreamRequest();
        invalidRequest.setDeviceName(""); // 空设备名称
        invalidRequest.setWidth(-1); // 负数宽度

        mockMvc.perform(post("/android/screencap/stream")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }
}
