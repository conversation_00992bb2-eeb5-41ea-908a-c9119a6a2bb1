package com.desaysv.workserver;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.net.Socket;

/**
 * socket实例控制
 */
@Slf4j
public class SocketInstanceControl extends Thread {

    private static final String INSTANCE_ID_PROPERTY = "flytest.instance.id";
    private final int port;

    public SocketInstanceControl(int port) {
        this.port = port;
    }

    /**
     * 获取当前实例ID
     */
    private String getInstanceId() {
        return System.getProperty(INSTANCE_ID_PROPERTY, "flytest-not-needed");
    }

    private void quitWhenOtherInstanceOpen() {
        try {
            //创建socket，连接端口
            Socket sock = new Socket("127.0.0.1", port);
            sock.getOutputStream().write(getInstanceId().getBytes());
            sock.close();
        } catch (Exception ignored) {
        }
        ServerSocket server = null;
        try {
            server = new ServerSocket(port); //创建socket,端口监听
            while (true) {
                Socket clientSocket = server.accept(); //等待连接
                // 读取客户端发送的消息
                BufferedReader reader = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                String message = reader.readLine(); // 从输入流中读取一行消息
                log.info("读取到消息:{}", message);
                boolean exit = message.contains("flytest") && !message.equalsIgnoreCase(getInstanceId());
                log.warn("检测到另一个实例，自动退出:{}", port);
                reader.close();
                clientSocket.close();
                server.close(); //有连接到来，也就是说有新的实例
                if (exit) {
                    System.exit(0); //这个实例退出
                }
            }
        } catch (Exception e) {
            if (server != null && !server.isClosed()) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 检测实例是否正在运行
     */
    public void checkInstanceRunning(int port, String instanceName) {
        //创建socket，连接端口
        try (Socket sock = new Socket("127.0.0.1", port)) {
            String currentInstanceId = getInstanceId();
            // 发送实例ID，添加换行符
            sock.getOutputStream().write((currentInstanceId + "\n").getBytes());
            sock.getOutputStream().flush();  // 确保数据发送
            // 等待响应
            BufferedReader reader = new BufferedReader(new InputStreamReader(sock.getInputStream()));
            String responseInstanceId = reader.readLine();
            log.info("接收到{}实例ID:{}", instanceName, responseInstanceId);

            if (responseInstanceId.contains("flytest") && !responseInstanceId.equalsIgnoreCase(currentInstanceId)) {
                log.info("检测到不同实例ID的flyTest{}正在运行，当前实例:{}, 运行实例:{}", instanceName, currentInstanceId, responseInstanceId);
                log.info("不允许同时运行两个不同ID的软件，应用即将关闭...");
                System.exit(0);
            }
        } catch (Exception ignored) {
        }
    }

    private void keepWhenOtherInstanceOpen() {
        try {
            log.info("启动后端socket检测服务:{}", port);
            try (ServerSocket server = new ServerSocket(port)) {//创建socket,端口监听
                while (true) {
                    Socket clientSocket = server.accept(); //等待连接
                    // 发送当前实例ID作为响应
                    String currentInstanceId = getInstanceId();
                    clientSocket.getOutputStream().write((currentInstanceId + "\n").getBytes());

                    clientSocket.close();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void run() {
        keepWhenOtherInstanceOpen();
    }
}