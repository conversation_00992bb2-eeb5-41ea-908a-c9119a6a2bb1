package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.filemanager.ClientRootFolder;
import com.desaysv.workserver.filemanager.Folder;
import com.desaysv.workserver.utils.FileUtils;
import com.desaysv.workserver.utils.ZipUtils;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 工具安装引导
 */
public interface Installer {

    String componentPath = new File(ClientRootFolder.rootPath, ClientRootFolder.TOOLS).getAbsolutePath();

    List<Installer> installerList = new ArrayList<>();

    void install(boolean isDev) throws InstallerException;

    static void installAll(boolean isDev) throws InstallerException {
        installerList.add(new RootLibInstaller());
        installerList.add(new AppLibraryInstaller());
        installerList.add(new RtspServerInstaller());
        installerList.add(new SqliteInstaller());
        for (Installer installer : installerList) {
            installer.install(isDev);
        }
    }

    default void unzip(File descDir, String installerPath, String targetName) throws IOException {
        unzip(descDir, installerPath, targetName, false);
    }

    default void unzip(File descDir, String installerPath, String targetName, boolean replaceDescDir) throws IOException {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(installerPath);
        assert inputStream != null;
        File zipFile = new File(componentPath, targetName);
        org.apache.commons.io.FileUtils.copyToFile(inputStream, zipFile);
        ZipUtils.unZipFiles(zipFile, descDir, replaceDescDir);
    }

    default boolean installFolder(String locationPattern, Folder rootFolder) throws IOException {
        Map<String, File> dllCategories = new HashMap<>();
        boolean isLibraryUpdated = false;
        Resource[] resources = new PathMatchingResourcePatternResolver().getResources(locationPattern);
//            log.info("resources:{]", resources);
        for (Resource resource : resources) {
            List<String> folders = new ArrayList<>(Arrays.asList(URLDecoder.decode(resource.getURL().toString(),
                    StandardCharsets.UTF_8.displayName()).split("/")));
            //jar:file:, D:, FlyTest, bin, flyTestServer.jar!, BOOT-INF, classes!
//                log.info("folders:{}", folders);
            String fileType = folders.get(0);
            List<String> subFolders;
            if (fileType.contains("jar:")) {
                subFolders = folders.subList(folders.indexOf("classes!") + 1, folders.size() - 1);
            } else {
                //代码运行
                subFolders = folders.subList(folders.indexOf("classes") + 1, folders.size() - 1);
            }
            String category = String.join(File.separator, subFolders);
//            System.out.println("rootFolder:" + rootFolder);
//            System.out.println("category:" + category);
//            System.out.println("subFolders:" + subFolders);
            File destFile = dllCategories.get(category);
            if (destFile == null) {
                destFile = rootFolder.createFolder(category).toFile();
                dllCategories.put(category, destFile);
            }
            boolean isCopied = FileUtils.copyResourceToDirectoryWhenLastModified(resource, destFile);
            if (isCopied) {
                isLibraryUpdated = true;
            }
        }
        return isLibraryUpdated;
    }


}
