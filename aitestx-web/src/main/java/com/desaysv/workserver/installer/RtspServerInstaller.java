package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.filemanager.AppPathManager;
import com.desaysv.workserver.filemanager.ClientRootFolder;
import com.desaysv.workserver.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;

/**
 * RTSP服务安装
 */
@Slf4j
public class RtspServerInstaller extends ClientRootFolder implements Installer {
    private static final String rtspServerName = "rtsp-simple-server";

    private static final String rtspExecutable = "rtsp-simple-server.exe";

    @Override
    public void install(boolean isDev) throws InstallerException {
        try {
            AppPathManager.rtspServerPath = getToolsFolder().createFolder(rtspServerName).toFile();
            AppPathManager.rtspServerExecutable = rtspExecutable;
            installRtspServer(AppPathManager.rtspServerPath);
        } catch (IOException e) {
            throw new InstallerException(e);
        }
    }

    private void installRtspServer(File descDir) throws IOException {
        File binPath = new File(descDir, rtspExecutable);
        if (!binPath.exists()) {
            FileUtils.copyFileInJar("installer/rtsp/rtsp-simple-server/rtsp-simple-server.exe", new File(descDir, "rtsp-simple-server.exe"));
            FileUtils.copyFileInJar("installer/rtsp/rtsp-simple-server/rtsp-simple-server.yml", new File(descDir, "rtsp-simple-server.yml"));
//            unzip(descDir, "installer/rtsp/rtsp-simple-server.zip", "rtsp-simple-server.zip");
        } else {
            log.info("RTSP服务器已配置");
        }
    }
}
