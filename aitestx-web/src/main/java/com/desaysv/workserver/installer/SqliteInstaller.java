package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.filemanager.ClientRootFolder;
import com.desaysv.workserver.filemanager.Folder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * SQLite数据库安装
 */
@Slf4j
public class SqliteInstaller extends ClientRootFolder implements Installer {

    private static final String appDbName = "app.db";

    @Override
    public void install(boolean isDev) throws InstallerException {
        try {
            Folder appDb = getDatabaseFolder().createFolder("app");
            if (!appDb.exists(appDbName)) {
                appDb.createFile(appDbName);
                log.info("数据库安装成功");
            } else {
                log.info("数据库已配置");
            }
        } catch (IOException e) {
            throw new InstallerException(e);
        }
    }

}
