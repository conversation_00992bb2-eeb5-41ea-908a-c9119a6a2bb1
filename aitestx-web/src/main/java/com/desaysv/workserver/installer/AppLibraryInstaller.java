package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.filemanager.ClientRootFolder;
import com.desaysv.workserver.filemanager.Folder;
import com.desaysv.workserver.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Dll配置
 */
@Slf4j
public class AppLibraryInstaller extends ClientRootFolder implements Installer {
    //    private static final File system32Path = new File("C:\\Windows\\System32");
    public void install(boolean isDev) throws InstallerException {
        if (isDev) {
            return;
        }
        try {
            boolean isLibraryUpdated = installFolder("/library/**/*.*", new Folder(new File("D:\\FlyTest")));
            log.info(isLibraryUpdated ? "库目录链接库完成更新" : "库目录链接库已配置");
        } catch (IOException e) {
            throw new InstallerException(e);
        }
    }

    @Deprecated
    private void installDll() throws InstallerException {
        try {
            Map<String, File> dllCategories = new HashMap<>();
            //library->dlls
            Folder dllFolder = getLibraryFolder().createFolder("dlls");
            Resource[] resources = new PathMatchingResourcePatternResolver().getResources("/dlls/**/*.dll");
            boolean isDllUpdated = false;
//            StringBuilder systemPath = new StringBuilder(System.getenv("Path"));
            List<String> libraryPaths = new ArrayList<>();
            for (Resource resource : resources) {
                List<String> folders = new ArrayList<>(Arrays.asList(URLDecoder.decode(resource.getURL().toString(),
                        StandardCharsets.UTF_8.displayName()).split("/")));
                List<String> subFolders = folders.subList(folders.indexOf("dlls") + 1, folders.size() - 1);
                String category = String.join(File.separator, subFolders);
                File destFile = dllCategories.get(category);
                if (destFile == null) {
                    destFile = dllFolder.createFolder(category).toFile();
//                    systemPath.append(destFile.getAbsolutePath()).append(";");
                    libraryPaths.add(destFile.getAbsolutePath());
                    dllCategories.put(category, destFile);
                }
                boolean isCopied = FileUtils.copyResourceToDirectoryWhenLastModified(resource, destFile);
                if (isCopied) {
                    isDllUpdated = true;
                }
            }
//            envMap.put("Path", systemPath.toString());
//            EnvUtils.setEnv(envMap);
//            EnvUtils.addLibraryDir(libraryPaths);
//            log.debug("Path:{}", System.getenv("Path"));
            log.info(isDllUpdated ? "DLL动态链接库完成更新" : "DLL动态链接库已配置");
        } catch (Exception e) {
            throw new InstallerException(e);
        }
    }

}
