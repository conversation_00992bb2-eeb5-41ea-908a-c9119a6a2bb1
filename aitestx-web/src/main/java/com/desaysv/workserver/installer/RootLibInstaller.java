package com.desaysv.workserver.installer;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.filemanager.Folder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;

import java.io.IOException;

/**
 * jar运行根目录库安装
 */
@Slf4j
public class RootLibInstaller implements Installer {

    @Override
    public void install(boolean isDev) throws InstallerException {
        if (isDev) {
            return;
        }
        try {
            ApplicationHome home = new ApplicationHome(getClass());
            Folder rootFolder = new Folder(home.getDir());
            boolean isLibraryUpdated = installFolder("/lib/**/*.*", rootFolder);
            log.info(isLibraryUpdated ? "根目录链接库完成更新" : "根目录链接库已配置");
        } catch (IOException e) {
            throw new InstallerException(e);
        }
    }

}
