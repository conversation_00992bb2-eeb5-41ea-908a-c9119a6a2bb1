package com.desaysv.workserver;

import com.desaysv.workserver.service.RoiTypeService;
import com.desaysv.workserver.service.TestDeviceModelService;
import com.desaysv.workserver.service.TestDeviceTypeService;
import com.desaysv.workserver.service.TestProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 启动器
 */
@Component
public class BootStarter {
    @Autowired
    private TestDeviceTypeService testDeviceTypeService;

    @Autowired
    private TestDeviceModelService testDeviceModelService;

    @Autowired
    private RoiTypeService roiTypeService;

    @Autowired
    private TestProjectService testProjectService;

    @PostConstruct
    public void start() {
        testDeviceTypeService.initDB();
        testDeviceModelService.initDB();
        roiTypeService.initDB();
        testProjectService.initDB();
    }
}

