package com.desaysv.workserver.accesser;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 数据库更新
 */
@Service
@Slf4j
//no lazy
public class DbUpdate {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 修改表列
     *
     * @param tableName  表名
     * @param columnName 字段列名
     * @param type       字段类型
     */
    private void alterColumn(String tableName, String columnName, String type) {
        alterColumn(tableName, columnName, type, null);
    }

    /**
     * 修改表列
     *
     * @param tableName    表名
     * @param columnName   字段列名
     * @param type         字段类型
     * @param defaultValue 默认值
     */
    private void alterColumn(String tableName, String columnName, String type, Object defaultValue) {
        // 首先检查表是否存在
        String CHECK_TABLE_EXISTS = String.format("SELECT name FROM sqlite_master WHERE type='table' AND name='%s'", tableName);
        SqlRowSet tableCheck = jdbcTemplate.queryForRowSet(CHECK_TABLE_EXISTS);
        if (tableCheck.next()) {
            // 表存在，继续执行修改列的操作
            String ALTER_COLUMN = String.format("ALTER TABLE %s ADD COLUMN %s %s", tableName, columnName, type);
            if (defaultValue != null) {
                ALTER_COLUMN += "DEFAULT(" + defaultValue + ")";
            }
            String QUERY_COLUMN = String.format("SELECT * FROM sqlite_master WHERE NAME='%s' and sql like '%%%s%%'", tableName, columnName);
            SqlRowSet rowSet;
            rowSet = jdbcTemplate.queryForRowSet(QUERY_COLUMN);
            if (!rowSet.next()) {
                jdbcTemplate.execute(ALTER_COLUMN);
            }
        } else {
            // 表不存在，可以选择抛出异常或者记录日志
            log.warn("数据库表{}不存在.", tableName);
        }
    }

    /**
     * 数据库更新
     */
    // ALTER TABLE "template_picture"  ADD COLUMN "device_type_id" INT DEFAULT(6);
    // ALTER TABLE "template_picture"  ADD COLUMN "device_index" INT DEFAULT(1);
    @PostConstruct
    public void update() {
        try {
            alterColumn("testcase_file", "selected", "Integer");
            alterColumn("template_picture", "device_type_id", "Integer", 6);
            alterColumn("template_picture", "device_index", "Integer", 1);
            alterColumn("excel_case_sheet_info", "selected", "Integer");
            alterColumn("excel_case_sheet_info", "table_header", "TEXT");
            alterColumn("excel_case_sheet_info", "header_filter_options", "TEXT");
        } catch (Exception e) {
            log.error("数据库更新异常", e);
        }
    }

}
