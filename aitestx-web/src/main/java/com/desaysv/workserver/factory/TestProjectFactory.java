package com.desaysv.workserver.factory;

import com.desaysv.workserver.vo.TestProjectRegisterVo;
import com.desaysv.workserver.model.TestProject;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 14:02
 * @description : 测试项目工厂
 * @modified By :
 * @since : 2022-3-30
 */
@Component
@Lazy
public class TestProjectFactory implements AbstractProjectFactory {

    @Override
    public TestProject createProject(TestProjectRegisterVo testProjectRegisterVo) {
        String projectName = testProjectRegisterVo.getProjectName();
        return new TestProject();
    }

}
