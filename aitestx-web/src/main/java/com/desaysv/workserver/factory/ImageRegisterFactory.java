package com.desaysv.workserver.factory;

import com.desaysv.workserver.entity.Image;
import com.desaysv.workserver.vo.ImageRegisterDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 13:33
 * @description : 图片注册工厂
 * @modified By :
 * @since : 2022-3-30
 */
@Slf4j
@Service
@Lazy
public class ImageRegisterFactory implements AbstractImageRegisterFactory {
    private final Map<String, Image> imageMap = new LinkedHashMap<>();

    @Autowired
    private ImageFactory imageFactory;

    @Override
    public Image registerImage(ImageRegisterDTO imageRegisterDTO) {
        String imageName = imageRegisterDTO.getTemplatePicture().getName();
        Image image;
        if (!imageMap.containsKey(imageName)) {
            //图片从未注册过，进行注册
            image = imageFactory.createImage(imageRegisterDTO);
            if (image != null) {
                log.info("新注册图片:{},实例:{}", imageName, image);
                imageMap.put(imageName, image);
            }
        } else {
            image = imageMap.get(imageName);
            log.info("图片已注册过:{},实例:{}", imageName, image);
        }
        return image;
    }

    @Override
    public boolean unregisterImage(String imageName) {
        boolean isExist = imageMap.containsKey(imageName);
        log.info("注销图片:{}", imageName);
        imageMap.remove(imageName);
        return isExist;
    }

    @Override
    public boolean deleteImage(Image image) {
        return false;
    }

    @Override
    public List<Image> getAllImages() {
        return new ArrayList<>(imageMap.values());
    }

    @Override
    public Image getImage(String imageName) {
        return imageMap.get(imageName);
    }
}
