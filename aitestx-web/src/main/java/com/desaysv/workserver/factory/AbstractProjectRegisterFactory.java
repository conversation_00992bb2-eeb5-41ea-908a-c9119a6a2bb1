package com.desaysv.workserver.factory;

import com.desaysv.workserver.vo.TestProjectRegisterVo;
import com.desaysv.workserver.model.TestProject;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 14:32
 * @description : 抽象项目注册工厂
 * @modified By :
 * @since : 2022-3-30
 */
public interface AbstractProjectRegisterFactory {

    /**
     * 注册项目
     *
     * @param testProjectRegisterVo 项目注册表单
     * @return 项目接口
     */
    TestProject registerProject(TestProjectRegisterVo testProjectRegisterVo);


    /**
     * 注销项目
     *
     * @param projectName 项目名称
     * @return 项目是否存在
     */
    boolean unregisterProject(String projectName);


    /**
     * 获取所有项目
     *
     * @return 所有项目列表
     */
    List<TestProject> getAllProjects();

    /**
     * 获取项目
     *
     * @param projectName 项目名称
     * @return 项目接口
     */
    TestProject getProject(String projectName);

}
