package com.desaysv.workserver.factory;


import com.desaysv.workserver.vo.TestProjectRegisterVo;
import com.desaysv.workserver.model.TestProject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 14:37
 * @description : 项目注册工厂
 * @modified By :
 * @since : 2022-3-30
 */
@Slf4j
@Service
@Lazy
public class ProjectRegisterFactory implements AbstractProjectRegisterFactory {
    private final Map<String, TestProject> testProjectMap = new LinkedHashMap<>();
    private final static String DEFAULT_PROJECT_NAME = "<def>";

    @Autowired
    private TestProjectFactory testProjectFactory;

    /**
     * 注册默认项目
     *
     * @return 项目接口
     */
    public TestProject registerDefaultProject() {
        TestProjectRegisterVo testProjectRegisterVo = new TestProjectRegisterVo();
        testProjectRegisterVo.setProjectName(DEFAULT_PROJECT_NAME);
        return registerProject(testProjectRegisterVo);
    }

    @Override
    public TestProject registerProject(TestProjectRegisterVo testProjectRegisterVo) {
        String projectName = testProjectRegisterVo.getProjectName();
        TestProject testProject;
        if (!testProjectMap.containsKey(projectName)) {
            //项目从未注册过，进行注册
            testProject = testProjectFactory.createProject(testProjectRegisterVo);
            if (testProject != null) {
                log.info("新注册项目:{},实例:{}", projectName, testProject);
                testProjectMap.put(projectName, testProject);
            }
        } else {
            testProject = testProjectMap.get(projectName);
            log.info("项目已注册过:{},实例:{}", projectName, testProject);
        }
        return testProject;
    }

    @Override
    public boolean unregisterProject(String projectName) {
        boolean isExist = testProjectMap.containsKey(projectName);
        log.info("注销项目:{}", projectName);
        testProjectMap.remove(projectName);
        return isExist;
    }

    @Override
    public List<TestProject> getAllProjects() {
        return new ArrayList<>(testProjectMap.values());
    }

    @Override
    public TestProject getProject(String projectName) {
        return testProjectMap.get(projectName);
    }

    /**
     * 获取当前项目
     *
     * @param currentProjectName 当前项目名称
     * @return 项目接口
     */
    //TODO: 根据cookie确认项目身份
    public TestProject getCurrentProject(String currentProjectName) {
        TestProject testProject = getProject(currentProjectName);
        if (testProject == null) {
            testProject = registerDefaultProject();
        }
        return testProject;
    }

}
