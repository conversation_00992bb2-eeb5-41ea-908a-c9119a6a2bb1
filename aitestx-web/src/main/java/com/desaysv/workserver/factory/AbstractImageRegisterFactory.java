package com.desaysv.workserver.factory;

import com.desaysv.workserver.entity.Image;
import com.desaysv.workserver.vo.ImageRegisterDTO;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 13:15
 * @description : 抽象图片注册工厂接口
 * @modified By :
 * @since : 2022-3-30
 */
public interface AbstractImageRegisterFactory {

    /**
     * 注册图片
     *
     * @param imageRegisterDTO 图片注册表单
     * @return 图片接口
     */
    Image registerImage(ImageRegisterDTO imageRegisterDTO);


    /**
     * 注销图片
     *
     * @param imageName 图片名称
     * @return 图片是否存在
     */
    boolean unregisterImage(String imageName);

    /**
     * 删除图片
     *
     * @param image 图片接口
     * @return 图片是否删除成功
     */
    boolean deleteImage(Image image);


    /**
     * 获取所有图片
     *
     * @return 所有图片列表
     */
    List<Image> getAllImages();


    /**
     * 获取图片
     *
     * @param imageName 图片名称
     * @return 图片接口
     */
    Image getImage(String imageName);


}
