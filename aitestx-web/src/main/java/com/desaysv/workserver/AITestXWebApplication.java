package com.desaysv.workserver;

import com.desaysv.workserver.exceptions.InstallerException;
import com.desaysv.workserver.installer.Installer;
import com.desaysv.workserver.secure.SecureVerify;
import com.desaysv.workserver.utils.MavenBuildUtil;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.BeansException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.util.StopWatch;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.util.Arrays;
import java.util.Scanner;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-16 15:32
 * @description : 主程序启动类
 * @modified By :
 * @since : 2022-2-16
 */
@Slf4j
@EnableAsync
@EnableTransactionManagement
@EnableEncryptableProperties
@MapperScan(basePackages = {
        "com.desaysv.workserver.mapper"
})
@ServletComponentScan("com.desaysv.workserver.interceptions")
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableCaching //开启基于注解的缓存
@EnableWebMvc
@EnableAutoConfiguration
@SpringBootApplication(scanBasePackages = {
        "com.desaysv"
})
@DependsOn("springContextHolder")
public class AITestXWebApplication implements CommandLineRunner, ApplicationContextAware {

    //默认情况下Tomcat等服务器是拒绝url中带%2F或者%5C的URL,因为它们经浏览器解析之后就变成了/和\,
    // 服务器默认是拒绝访问的,所以需要通过服务的配置来解决这个问题。
//    static {
//        //解决URL中包含%2F的问题
//        System.setProperty("org.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH", "true");
//        //解决URL中包含%5C的问题
//        System.setProperty("org.apache.catalina.connector.CoyoteAdapter.ALLOW_BACKSLASH", "true");
//    }
    private ApplicationContext applicationContext;
    private static final String COMPILE_DATE = MavenBuildUtil.getBuildTime();
    private static final String SERVER_DESCRIPTION = "";
    private static final String SERVER_VERSION = "1.3.1";
    private static final boolean released = false;

    private static final boolean previewChannel = MavenBuildUtil.getBuildComputerName().equalsIgnoreCase("hzh23512c");
    private static final boolean releaseVersion = previewChannel && released;

    public static String getServerVersion() {
        return String.format("%s-%s.%s", SERVER_VERSION, releaseVersion ? "release" : previewChannel ? "preview" : "dev", MavenBuildUtil.getBuildTime(true));
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private void shutdown() {
        ConfigurableApplicationContext ctx = (ConfigurableApplicationContext) applicationContext;
        ctx.close();
    }

    @Override
    public void run(String... args) {
//        ParserConfig.getGlobalInstance().setSafeMode(true);
    }

    public static boolean checkJdkVersion64Bit() {
        int versionCode = Integer.parseInt(System.getProperty("sun.arch.data.model", "-1"));
        return versionCode == 64;
    }

    private static void createTrayIcon() {
        if (SystemTray.isSupported()) {// 判断系统是否托盘
            TrayIcon icon = new TrayIcon(Toolkit.getDefaultToolkit()
                    .getImage(AITestXWebApplication.class.getClassLoader().getResource("icons/favicon.png")));// 创建一个托盘图标对象
            icon.setToolTip("FlyTest服务端已开启");
            icon.setImageAutoSize(true);
            PopupMenu menu = new PopupMenu();// 创建弹出菜单
            MenuItem item1 = new MenuItem("打开"); // 添加一个用于打开的按钮
            item1.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    try {
                        Runtime.getRuntime().exec("cmd /c start http://localhost:12399/log");
                    } catch (IOException e1) {
                        log.error(e1.getMessage(), e1);
                    }
                }
            });
            menu.add(item1);// 添加弹出菜单到托盘图标
            MenuItem item = new MenuItem("退出"); // 添加一个用于退出的按钮
            item.addActionListener(new ActionListener() {
                @Override
                public void actionPerformed(ActionEvent e) {
                    System.exit(0);
                }
            });
            menu.add(item);// 添加弹出菜单到托盘图标
            icon.setPopupMenu(menu);
            SystemTray tray = SystemTray.getSystemTray();// 获取系统托盘
            try {
                tray.add(icon);
            } catch (AWTException e1) {
                log.error(e1.getMessage(), e1);
            } // 将托盘图表添加到系统托盘
            icon.addMouseListener(new MouseAdapter() {
                @Override
                public void mouseClicked(MouseEvent evt) {
                    if (evt.getClickCount() == 2) {// 处理鼠标双击
                        try {
                            Runtime.getRuntime().exec("cmd /c start http://localhost:12399/log");
                        } catch (IOException e) {
                            log.error(e.getMessage(), e);
                        }
                    }
                    super.mouseClicked(evt);
                }
            });
        }
    }

    private static void printEssentialInfo() {
        log.info("---------FlyTest服务端启动----------");
        log.info("服务端编译日期:{}", COMPILE_DATE);
        log.info("服务端版本号:{}", getServerVersion());
        // 获取Java版本
        String javaVersion = System.getProperty("java.version");

        // 获取Java安装路径
        String javaHome = System.getenv("JAVA_HOME");

        // 获取实例ID
        String instanceId = System.getProperty("flytest.instance.id", "未设置");

        // 打印Java版本和Java安装路径
        log.info("Java版本: {}", javaVersion);
        log.info("Java路径: {}", javaHome);
        log.info("实例ID: {}", instanceId);

        if (!SERVER_DESCRIPTION.isEmpty()) {
            log.info("服务端说明:{}", SERVER_DESCRIPTION);
        }
    }

    public static void main(String[] args) {
        int serverPort = 61535;
        int clientPort = 62535;
        printEssentialInfo();

        if (!checkJdkVersion64Bit()) {
            log.info("jdk版本需要安装64位才能运行");
            new Scanner(System.in).nextLine();
            return;
        }
        SocketInstanceControl socketInstanceControl = new SocketInstanceControl(serverPort);

        // 检查是否已经检测到另一个实例，如果是，等待用户输入
        socketInstanceControl.checkInstanceRunning(serverPort, "后端");
        socketInstanceControl.checkInstanceRunning(clientPort, "前端");

        // 启动Socket检测服务
        socketInstanceControl.start();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (!SecureVerify.verifyByServer()) {
            new Scanner(System.in).nextLine();
            return;
        }
        CrashMonitor.initCrashMonitor();
        System.setProperty("java.awt.headless", "false");
        System.setProperty("spring.devtools.restart.enabled", "false");
        boolean going = false;
        try {
            Installer.installAll(Arrays.stream(args).anyMatch(arg -> arg.contains("--spring.profiles.active=dev")));
            going = true;
        } catch (InstallerException e) {
            log.error(e.getMessage(), e);
        }
        if (going) {
//            System.setProperty("jna.debug_load", "true");

            SpringApplication.run(AITestXWebApplication.class, args);
//            ConfigurableApplicationContext ctx = new SpringApplicationBuilder(AITestXWebApplication.class).headless(false)
//                    .run(args);
//            EventQueue.invokeLater(AITestXWebApplication::createTrayIcon);
            stopWatch.stop();
            log.info("FlyTest服务端已启动...");
            log.info("本次启动耗时{}秒", stopWatch.getLastTaskTimeMillis() / 1000);
        }

    }

}

