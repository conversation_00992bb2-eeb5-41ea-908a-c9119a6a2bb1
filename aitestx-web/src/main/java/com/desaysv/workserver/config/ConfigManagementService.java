package com.desaysv.workserver.config;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Map;

/**
 * 配置管理服务
 * 用于处理JSON配置文件的保存和读取
 */
@Service
@Slf4j
public class ConfigManagementService {

    private static final String CONFIG_BASE_PATH = "D:\\FlyTest\\data\\server\\projects";
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 保存配置到指定文件
     *
     * @param project    项目名称
     * @param filename   文件名
     * @param jsonConfig JSON配置内容
     * @throws IOException 如果保存过程中出错
     */
    public void saveConfig(String project, String filename, Map<String, Object> jsonConfig) throws IOException {
        // 验证项目名和文件名
        validateProjectName(project);
        validateFilename(filename);

        // 构建完整的配置路径
        String configPath = Paths.get(CONFIG_BASE_PATH, project, "config", "settings").toString();

        // 确保目录存在
        File directory = new File(configPath);
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                throw new IOException("无法创建配置目录: " + configPath);
            }
        }

        // 构建文件路径
        Path filePath = Paths.get(configPath, sanitizeFilename(filename) + ".json");

        // 将JSON写入文件
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(filePath.toFile(), jsonConfig);
        log.info("配置已保存到文件: {}", filePath);
    }

    /**
     * 从指定文件读取配置
     *
     * @param project  项目名称
     * @param filename 文件名
     * @return JSON配置内容
     * @throws IOException 如果读取过程中出错
     */
    public String getConfig(String project, String filename) throws IOException {
        // 验证项目名和文件名
        validateProjectName(project);
        validateFilename(filename);

        // 构建完整的配置路径
        String configPath = Paths.get(CONFIG_BASE_PATH, project, "config", "settings").toString();

        // 构建文件路径
        Path filePath = Paths.get(configPath, sanitizeFilename(filename) + ".json");

        // 检查文件是否存在
        if (!Files.exists(filePath)) {
            throw new IOException("配置文件不存在: " + filePath);
        }

        // 从文件读取JSON
        return ThreadSafeFileUtils.readFileToString(filePath.toFile());
    }

    /**
     * 验证项目名是否合法
     *
     * @param project 项目名称
     */
    private void validateProjectName(String project) {
        if (project == null || project.trim().isEmpty()) {
            throw new IllegalArgumentException("项目名不能为空");
        }

        if (project.contains("..") || project.contains("/") || project.contains("\\")) {
            throw new IllegalArgumentException("项目名包含非法字符");
        }
    }

    /**
     * 验证文件名是否合法
     *
     * @param filename 文件名
     */
    private void validateFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            throw new IllegalArgumentException("文件名包含非法字符");
        }
    }

    /**
     * 清理文件名或项目名，确保安全
     *
     * @param name 文件名或项目名
     * @return 清理后的文件名或项目名
     */
    private String sanitizeFilename(String name) {
        // 先trim，然后只保留字母、数字、连字符、下划线和点，其他字符替换为下划线
        return name.trim().replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5\\-_.]", "_");
    }
}
