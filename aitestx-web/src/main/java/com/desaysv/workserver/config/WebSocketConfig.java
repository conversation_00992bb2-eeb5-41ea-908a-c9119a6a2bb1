package com.desaysv.workserver.config;

import com.desaysv.workserver.text.TextWebSocketManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-17 16:19
 * @description : 开启WebSocket支持
 * @modified By :
 * @since : 2022-2-17
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new TextWebSocketManager(), "/ws").setAllowedOrigins("*");
    }

    /**
     * 将ServerEndpointExporter注入到spring容器
     * 使用springboot内置web容器时时必须注入此bean
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 设置为0表示永不超时
        container.setMaxSessionIdleTimeout(0L);
        //配置WebSocket 消息的接收缓冲区大小
        container.setMaxTextMessageBufferSize(64 * 1024);      // 64KB
        container.setMaxBinaryMessageBufferSize(1024 * 1024);  // 1MB
        return container;
    }

}
