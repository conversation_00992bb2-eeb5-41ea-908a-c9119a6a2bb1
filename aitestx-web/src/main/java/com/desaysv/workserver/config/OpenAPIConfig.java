package com.desaysv.workserver.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Collections;

// 网址：/AITestX/swagger-ui/index.html
@Configuration
public class OpenAPIConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 部分接口不拦截
//        registry.excludePathPatterns("/swagger-ui/**", "/v3/api-docs/**");
    }

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("AITestX API Documentation")
                        .description("AITestX 自动化测试平台 RESTful API 文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("AITestX Team")
                                .email("<EMAIL>")
                                .url("https://example.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")))
                .servers(Collections.singletonList(
                        new Server()
                                .url("/AITestX")
                                .description("AITestX API Server")
                ));
    }
}