package com.desaysv.workserver.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

/**
 * 监听spring关闭事件
 */
@Slf4j
@Component
public class SpringShutdownEventListening implements ApplicationListener<ContextClosedEvent> {

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("SpringShutdownEventListening->applicationContext:{}", event.getApplicationContext());
    }

}