package com.desaysv.workserver.mail;

import com.desaysv.workserver.monitor.PolyTestUrls;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.*;

@Service
@Slf4j
public class MailUtils {

    @Autowired
    private PolyTestUrls polyTestUrls;

    @Autowired
    private RestTemplate restTemplate;

    // 邮件重试队列
    private final BlockingQueue<MailParamDto> retryQueue = new LinkedBlockingQueue<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private ScheduledFuture<?> scheduledFuture;

    private final int MAX_RETRY_COUNT = 3;

    @PostConstruct
    private void init() {
        // 启动定时任务，每5分钟重试发送队列中的邮件
        scheduledFuture = scheduler.scheduleWithFixedDelay(this::processRetryQueue, 0, 5, TimeUnit.MINUTES);

    }

    public void sendEmail(MailParamDto mailParamDto) {
        if ("SENT".equals(mailParamDto.getStatus())) {
            log.info("邮件已经发送过，不再发送: {}", mailParamDto.getSubject());
            return;
        }
        try {
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("content", mailParamDto.getContent());
            body.add("subject", mailParamDto.getSubject());

            if (mailParamDto.getRecipients() != null) {
                for (String recipient : mailParamDto.getRecipients()) {
                    body.add("recipients", recipient);
                }
            }

            if (mailParamDto.getFiles() != null) {
                for (File file : mailParamDto.getFiles()) {
                    if (file.exists()) {
                        body.add("files", new FileSystemResource(file));
                    }
                }
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(polyTestUrls.CLOUD_EMAIL_URL, requestEntity, Map.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                Map responseBody = response.getBody();
                if (responseBody != null && Boolean.TRUE.equals(responseBody.get("success"))) {
                    mailParamDto.setStatus("SENT");
                } else {
                    log.warn("邮件发送失败: {}", responseBody.get("message"));
                    handleRetry(mailParamDto); // 处理重试
                }
            } else {
                log.warn("邮件发送失败，HTTP状态码: {}", response.getStatusCodeValue());
                handleRetry(mailParamDto); // 处理重试
            }
        } catch (RestClientException e) {
            log.warn("邮件发送失败，将加入重试队列: {}", e.getMessage());
            handleRetry(mailParamDto); // 处理重试
        }
        log.info("发送邮件{}:{}->邮件至{}", "SENT".equals(mailParamDto.getStatus()) ? "成功" : "失败", mailParamDto.getSubject(), mailParamDto.getRecipients());
    }

    /**
     * 处理邮件重试逻辑
     */
    private void handleRetry(MailParamDto mailParamDto) {
        mailParamDto.setStatus("FAILED");
        mailParamDto.setRetryCount(mailParamDto.getRetryCount() + 1);
        if (mailParamDto.getRetryCount() <= MAX_RETRY_COUNT) {
            retryQueue.offer(mailParamDto); // 发送失败时将邮件加入重试队列
        } else {
            log.warn("邮件发送失败次数超过限制，邮件ID: {}", mailParamDto.getId());
        }
    }

    /**
     * 处理重试队列中的邮件
     */
    private void processRetryQueue() {
        if (retryQueue.isEmpty()) {
            return;
        }
        String url = polyTestUrls.CLOUD_EMAIL_URL;
        if (!isReachable(url)) {
            log.warn("目标地址{}不可达，邮件发送暂时跳过", url);
            scheduledFuture.cancel(false);
            scheduledFuture = scheduler.schedule(this::init, 10, TimeUnit.MINUTES); //网络不好时 10分钟再检查一遍网络
            return;
        }
        while (!retryQueue.isEmpty()) {
            MailParamDto mail = retryQueue.poll();
            if (mail != null) {
                try {
                    log.info("重试发送邮件: {}", mail.getSubject());
                    sendEmail(mail);
                } catch (Exception e) {
                    log.warn("重试发送邮件失败: {}", e.getMessage());
                    mail.setStatus("FAILED"); // 重新设置状态为失败
                    if (mail.getRetryCount() < MAX_RETRY_COUNT) {
                        mail.setRetryCount(mail.getRetryCount() + 1);
                        retryQueue.offer(mail); // 重新加入队列
                    } else {
                        log.warn("邮件发送失败次数超过限制，邮件ID: {}", mail.getId());
                    }
                }
            }
        }
    }


    /**
     * 检查目标URL是否可达
     *
     * @param url 要检查的URL
     * @return 可达返回true，反之返回false
     */
    private boolean isReachable(String url) {
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(3000); // 设置超时时间
            connection.setReadTimeout(3000);
            int responseCode = connection.getResponseCode();
            return (responseCode >= 200 && responseCode < 500); // 2xx和3xx的响应都视为可达
        } catch (IOException e) {
            log.warn("检查目标URL可达性时出错: {}", e.getMessage());
            return false; // 发生异常时返回不可达
        }
    }
}
