package com.desaysv.workserver.mail;

import lombok.Data;

import java.io.File;
import java.util.UUID;

/**
 * 发送邮件接口的请求体
 */

@Data
public class MailParamDto {
    private String id; // 唯一标识
    private String content;
    private String recipient;
    private String[] recipients;
    private String subject;
    private File[] files;
    private String status; // 状态字段
    private int retryCount;// 重试计数器

    public MailParamDto() {
        this.id = UUID.randomUUID().toString();
    }
}