package com.desaysv.workserver.mail;

import lombok.Data;

import java.util.List;

@Data
public class TestResultReportDto {
    private boolean isFunctionMode;//功能测试标识 如果不是功能测试则为冒烟点检
    private String startTime;
    private String endTime;
    private String localReportPath;//本地报告地址
    private String cloudReportPath;//网盘报告地址
    private String qnxVersion;//  QNX版本
    private String socVersion;//  SoC版本
    private String mcuVersion; //   MCU版本
    private String version;//项目版本
    private List<TestResult> functionTestResults;// 功能测试结果
    private TestResult smokeTestResults;// 冒烟点检测试结果
    private int passRate;// Pass目标通过率
    /**
     * 功能测试对象
     *
     * @param isFunctionMode
     * @param startTime
     * @param endTime
     * @param localReportPath
     * @param cloudReportPath
      * @param qnxVersion
      * @param socVersion
      * @param mcuVersion
     * @param version
     * @param functionTestResults
     */
    public TestResultReportDto(boolean isFunctionMode, String startTime, String endTime, String localReportPath, String cloudReportPath, String  qnxVersion, String socVersion, String mcuVersion, String version, List<TestResult> functionTestResults, int passRate) {
        this( isFunctionMode, startTime, endTime, localReportPath, cloudReportPath,  qnxVersion, socVersion, mcuVersion, version, functionTestResults, null, passRate);
    }

     /**
     * 冒烟点检对象
     *
     * @param isFunctionMode
     * @param startTime
     * @param endTime
     * @param localReportPath
     * @param cloudReportPath
      * @param qnxVersion
      * @param socVersion
      * @param mcuVersion
     * @param smokeTestResults
     * @param passRate
     */
    public TestResultReportDto(boolean isFunctionMode, String startTime, String endTime, String localReportPath, String cloudReportPath, String  qnxVersion,  String socVersion, String mcuVersion, String version, TestResult smokeTestResults, int passRate) {
        this(isFunctionMode, startTime, endTime, localReportPath, cloudReportPath, qnxVersion, socVersion, mcuVersion, version, null, smokeTestResults, passRate);
    }

    public TestResultReportDto(boolean isFunctionMode, String startTime, String endTime, String localReportPath,String cloudReportPath, String  qnxVersion, String socVersion, String mcuVersion, String version, List<TestResult> functionTestResults, TestResult smokeTestResults, int passRate) {
        this.isFunctionMode = isFunctionMode;
        this.startTime = startTime;
        this.endTime = endTime;
        this.localReportPath = localReportPath;
        this.cloudReportPath = cloudReportPath;
        this.qnxVersion = qnxVersion;
        this.socVersion = socVersion;
        this.mcuVersion = mcuVersion;
        this.version = version;
        this.functionTestResults = functionTestResults;
        this.smokeTestResults = smokeTestResults;
        this.passRate = passRate;
    }
}