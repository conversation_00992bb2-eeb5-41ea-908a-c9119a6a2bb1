package com.desaysv.workserver.mail;

import lombok.Data;

@Data
public class TestResult {
    private int index;// 序号
    private String moduleName;// 模块名称
    private int executedCases;// 执行测试案例
    private int passCases;// 通过案例数
    private String conclusion;// 测试结论
    private int passRate;//通过率

    public TestResult(int index, String moduleName, int executedCases, int passCases,int passRate) {
        this.index = index;
        this.moduleName = moduleName;
        this.executedCases = executedCases;
        this.passCases = passCases;
        this.passRate = passRate;
        this.conclusion = calculateConclusion(); // 自动设置测试结论
    }

    public TestResult(int executedCases, int passCases,int passRate) {
        this.index = 1;
        this.moduleName = null;
        this.executedCases = executedCases;
        this.passCases = passCases;
        this.passRate = passRate;
        this.conclusion = calculateConclusion(); // 自动设置测试结论
    }

    public int getFailCases() {
        return executedCases - passCases;
    }

    public String getPassRate() {
        return executedCases == 0 ? "0%" : String.format("%.2f%%", ((double) passCases / executedCases) * 100);
    }
    /**
     * 获取整数类型的 PassRate
     * @return int类型的通过率（0 - 100）
     */
    public int getPassRateAsInt() {
        if (executedCases == 0) {
            return 0; // 如果执行用例为0，则通过率为0
        }
        return (int) Math.round(((double) passCases / executedCases) * 100);
    }

    private String calculateConclusion() {
        double passRate = executedCases == 0 ? 0 : ((double) passCases / executedCases) * 100;
        return passRate >= (this.passRate != 0 ? this.passRate : 90.0) ? "GO" : "NG";  // 如果通过率大于等于90%，结论为“GO”，否则为“NG”
    }
}