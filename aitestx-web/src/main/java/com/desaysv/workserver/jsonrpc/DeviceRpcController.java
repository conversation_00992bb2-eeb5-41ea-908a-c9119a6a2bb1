package com.desaysv.workserver.jsonrpc;

import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 15:06
 * @description : 远程调用设备web接口
 * @modified By :
 * @since : 2022-4-12
 */
//@Service
//@AutoJsonRpcServiceImpl
public class DeviceRpcController implements DeviceRpcAPI {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    @Override
    public CameraDevice getCamera(String deviceName) {
        Device device = deviceRegisterManager.getDevice(deviceName);
        return (CameraDevice) device;
    }
}
