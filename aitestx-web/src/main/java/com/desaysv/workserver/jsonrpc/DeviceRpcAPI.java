package com.desaysv.workserver.jsonrpc;

import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.googlecode.jsonrpc4j.JsonRpcParam;
import com.googlecode.jsonrpc4j.JsonRpcService;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 18:02
 * @description :
 * @modified By :
 * @since : 2022-4-12
 */
@JsonRpcService("/rpc/device")
public interface DeviceRpcAPI {

    CameraDevice getCamera(@JsonRpcParam("deviceName") String deviceName);

}
