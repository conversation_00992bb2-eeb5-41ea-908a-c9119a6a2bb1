package com.desaysv.workserver.secure;

import com.desaysv.workserver.utils.NetworkUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 安全验证
 */
@Slf4j
public class SecureVerify {

    private static final String MAIN_VERIFY_URL = "http://10.219.9.19:83";
    private static final String SECOND_VERIFY_URL = "http://10.99.17.96";

    //UUID白名单
    private static final List<String> UUID_WHITE_LIST = new ArrayList<>(Arrays.asList(
            "0B85A76E-6693-49D2-9B8C-23C5F92B7B8C", //ME
            "4C4C4544-0056-5210-804A-B9C04F333258", //ST菊水
            "00000000-0000-0000-0000-C400ADB45CE3", //ADS压测
            "F5E0CAA5-6AA2-4CE1-90CC-A813877C1BEC", //lhy computer
            "00000000-0000-0000-0000-C400ADB45CE5", //惠南
            "4C4C4544-0050-4C10-804D-C6C04F304633", //程威
            "4C4C4544-0059-4B10-8052-B5C04F424B33", //王玲
            "4C4C4544-004E-4710-8052-B7C04F374733",  //王玲
            "E1C7F561-4B4F-4C77-8001-31B054DC01E6", //奇瑞客户
            "E962234C-27B9-11B2-A85C-AB6116659B3A", //奇瑞客户
            "B2E31E0D-527F-4F43-9DAC-875A304F0636" //赵国强
    ));

    private static boolean verifyByNetwork() {
        return NetworkUtils.isUrlConnect(MAIN_VERIFY_URL) || NetworkUtils.isUrlConnect(SECOND_VERIFY_URL);
    }

    /**
     * 安全验证入口（内部）
     *
     * @param uuid 主板uuid
     * @return 是否验证通过
     */
    public static boolean verify(String uuid, String source) {
        if (StringUtils.isEmpty(uuid)) {
            log.warn("电脑UUID为空，无法通过验证");
            return false;
        }
        if (UUID_WHITE_LIST.contains(uuid)) {
            log.info("{}离线验证通过", source);
            return true;
        }
        if (verifyByNetwork()) {
            log.info("{}网络验证通过", source);
            return true;
        }
        log.warn("{}网络验证失败，请电脑连接DesaySV网络重新打开软件进行验证", source);
        return false;
    }

    /**
     * 安全验证入口（对外）
     *
     * @return 是否验证通过
     */
    public static boolean verifyByServer() {
        String uuid = CommandUtils.getPcUUID();
        return verify(uuid, "服务端");
    }

    public static void main(String[] args) {
        System.out.println(NetworkUtils.isUrlConnect(SECOND_VERIFY_URL));
    }

}
