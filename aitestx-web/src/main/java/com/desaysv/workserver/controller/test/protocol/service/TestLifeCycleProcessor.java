package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.utils.command.CommandExecutor;
import com.desaysv.workserver.utils.sse.SseUtils;
import org.springframework.stereotype.Component;

/**
 * 生命周期监听器
 */
@Component
public class TestLifeCycleProcessor implements TestProcessListener {

    public TestLifeCycleProcessor() {
        TestProcessManager.addTestProcessListener(this);
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        SseUtils.outputRunLog("开始测试");
    }

    @Override
    public void testTerminated() {
        SseUtils.outputRunLog("终止测试");
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        SseUtils.outputRunLog("结束测试");
        CommandExecutor.killAll();
    }

}
