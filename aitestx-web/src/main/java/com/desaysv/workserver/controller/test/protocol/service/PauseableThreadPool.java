package com.desaysv.workserver.controller.test.protocol.service;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 描述：演示每个任务执行前后放钩子函数
 */
@Slf4j
public class PauseableThreadPool extends ThreadPoolExecutor {

    private final ReentrantLock lock = new ReentrantLock();
    private final Condition unpaused = lock.newCondition();
    private volatile boolean isPaused;  // 使用volatile确保可见性
    private volatile boolean isShutdown;  // 添加关闭状态标识
    @Setter
    private String poolName;
    // 添加一个集合来存储线程对象的引用
    private final CopyOnWriteArrayList<Thread> workThreads = new CopyOnWriteArrayList<>();

    public PauseableThreadPool(String poolName) {
        super(
                Runtime.getRuntime().availableProcessors(), // 核心线程数设为CPU核心数
                Runtime.getRuntime().availableProcessors() * 2, // 最大线程数设为CPU核心数的2倍
                60L, // 空闲线程存活时间改为60秒
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000), // 设置队列容量上限，防止OOM
                new ThreadFactory() { // 自定义线程工厂，提供有意义的线程名
                    private final AtomicInteger threadNumber = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, "PauseablePool-Worker-" + threadNumber.getAndIncrement());
                        if (t.isDaemon()) {
                            t.setDaemon(false);
                        }
                        if (t.getPriority() != Thread.NORM_PRIORITY) {
                            t.setPriority(Thread.NORM_PRIORITY);
                        }
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 使用CallerRunsPolicy作为拒绝策略
        );
    }

    public PauseableThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                               TimeUnit unit,
                               BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public PauseableThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                               TimeUnit unit, BlockingQueue<Runnable> workQueue,
                               ThreadFactory threadFactory) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory);
    }

    public PauseableThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                               TimeUnit unit, BlockingQueue<Runnable> workQueue,
                               RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, handler);
    }

    public PauseableThreadPool(int corePoolSize, int maximumPoolSize, long keepAliveTime,
                               TimeUnit unit, BlockingQueue<Runnable> workQueue,
                               ThreadFactory threadFactory, RejectedExecutionHandler handler) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue, threadFactory,
                handler);
    }

    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        super.beforeExecute(t, r);
        // 添加线程到集合中
        if (!workThreads.contains(t)) {
            workThreads.add(t);
        }
        lock.lock();
        try {
            while (isPaused && !isShutdown) {  // 添加关闭状态检查
                try {
                    unpaused.await();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                    log.warn("暂停时线程终止失败: {}", t.getName(), e);
                    break; // 退出等待循环
                }
            }
        } finally {
            lock.unlock();
        }
    }

    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        super.afterExecute(r, t);
        if (t != null) {
            log.error("线程池{}任务执行失败", poolName, t);
        }
    }

    /**
     * 获取线程池中所有线程的名称
     *
     * @return 返回线程名称列表
     */
    public List<String> getAllThreadNames() {
        List<String> threadNames = new ArrayList<>();
        for (Thread thread : workThreads) {
            // 检查线程是否仍然存活
            if (thread.isAlive()) {
                threadNames.add(thread.getName());
            } else {
                // 如果线程已经不存活，从列表中移除
                workThreads.remove(thread);
            }
        }
        return threadNames;
    }

    @Override
    protected void terminated() {
        super.terminated();
        // 清理线程列表
        workThreads.clear();
    }


    public void stop() {
        lock.lock();
        try {
            isShutdown = true;
            isPaused = false;  // 确保没有线程卡在暂停状态
            unpaused.signalAll();  // 唤醒所有等待的线程

            // 关闭线程池
            shutdown();

            try {
                // 等待任务完成，最多等待30秒
                if (!awaitTermination(5, TimeUnit.SECONDS)) {
                    // 如果超时，强制关闭
                    shutdownNow();
                    // 再次等待，确保关闭
                    if (!awaitTermination(5, TimeUnit.SECONDS)) {
                        log.error("线程池{}无法停止", poolName);
                    }
                }
            } catch (InterruptedException e) {
                // 如果等待被中断，强制关闭
                shutdownNow();
                Thread.currentThread().interrupt();
            } finally {
                workThreads.clear(); // 确保清理线程列表
            }
        } finally {
            log.info("线程池{}停止", poolName);
            lock.unlock();
        }
    }

    public void pause() {
        lock.lock();
        try {
            if (!isShutdown) {  // 只在非关闭状态下允许暂停
                isPaused = true;
                log.info("线程池{}暂停", poolName);
            }
        } finally {
            lock.unlock();
        }
    }

    public void pauseIfNecessary() {
        lock.lock();
        try {
            while (isPaused && !isShutdown) {
                unpaused.await();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
    }


    public void resume() {
        lock.lock();
        try {
            if (!isShutdown) {  // 只在非关闭状态下允许恢复
                isPaused = false;
                unpaused.signalAll();
                log.info("线程池{}恢复，线程池剩余线程:{}", poolName, getAllThreadNames());
            }
        } finally {
            lock.unlock();
        }
    }

    public boolean isPaused() {
        lock.lock();
        try {
            return isPaused;
        } finally {
            lock.unlock();
        }
    }


    public static void main(String[] args) throws InterruptedException {
        PauseableThreadPool pauseableThreadPool = new PauseableThreadPool(10, 20, 10l,
                TimeUnit.SECONDS, new LinkedBlockingQueue<>());
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                System.out.println("我被执行");
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        };
        for (int i = 0; i < 10000; i++) {
            pauseableThreadPool.execute(runnable);
        }
        Thread.sleep(1500);
        pauseableThreadPool.pause();
        System.out.println("线程池被暂停了");
        Thread.sleep(1500);
        pauseableThreadPool.resume();
        System.out.println("线程池被恢复了");

    }
}
