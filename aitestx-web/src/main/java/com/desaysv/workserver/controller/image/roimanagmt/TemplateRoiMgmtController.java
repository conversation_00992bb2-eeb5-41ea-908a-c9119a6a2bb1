package com.desaysv.workserver.controller.image.roimanagmt;

import com.desaysv.workserver.bo.TemplateRoiQuery;
import com.desaysv.workserver.model.roi.PercentTemplateRoi;
import com.desaysv.workserver.model.roi.RoiType;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.RoiTypeService;
import com.desaysv.workserver.service.TemplateRoiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 16:51
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Slf4j
@RestController
@RequestMapping("templateRoi")
@Lazy
public class TemplateRoiMgmtController {

    @Autowired
    private RoiTypeService roiTypeService;

    @Autowired
    private TemplateRoiService templateRoiService;


    @GetMapping("/roiType/rect")
    public ResultEntity<RoiType> getRectRoiType() {
        return ResultEntity.ok(roiTypeService.getRectRoiType());
    }

    @GetMapping("/roiType/circle")
    public ResultEntity<RoiType> getCircleRoiType() {
        return ResultEntity.ok(roiTypeService.getCircleRoiType());
    }

    @PostMapping("/roi")
    public ResultEntity<PercentTemplateRoi> getScaledRoiRect(@RequestBody TemplateRoiQuery templateRoiQuery) {
        return ResultEntity.ok(templateRoiService.getPercentRoiByTemplateName(
                templateRoiQuery.getProjectName(),
                templateRoiQuery.getDeviceUniqueCode(),
                templateRoiQuery.getTemplateName()));
    }

    @PostMapping("/allRoi")
    public ResultEntity<PercentTemplateRoi> getAllScaledRoiRect(@RequestBody TemplateRoiQuery templateRoiQuery) {
        return ResultEntity.ok(templateRoiService.getAllPercentRoiByTemplateName(
                templateRoiQuery.getProjectName(),
                templateRoiQuery.getDeviceUniqueCode()));
    }

}
