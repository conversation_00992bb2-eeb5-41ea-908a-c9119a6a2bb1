package com.desaysv.workserver.controller.hud;


import com.desaysv.workserver.hud.HudDetectionRequest;
import com.desaysv.workserver.hud.HudDetectionResponse;
import com.desaysv.workserver.hud.HudDetectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.http.ResponseEntity;

@Slf4j
@RestController
@RequestMapping("/hudDetection")
@Lazy

public class HudDetectionController {

    private final HudDetectionService hudDetectionService;

    public HudDetectionController(HudDetectionService hudDetectionService) {
        this.hudDetectionService = hudDetectionService;
    }

    @PostMapping
    public ResponseEntity<HudDetectionResponse> detectHud(@RequestBody HudDetectionRequest request) {
        log.info("请求数据：{}", request);
        try {
            log.info("请求开始");
            HudDetectionResponse response = hudDetectionService.detectHud(request);
//            log.info("response：{}",response);
            log.info("ResponseEntity.ok(response)：{}",ResponseEntity.ok(response));
            log.info("请求结束");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(500).build();  // Handle exceptions gracefully
        }
    }
}