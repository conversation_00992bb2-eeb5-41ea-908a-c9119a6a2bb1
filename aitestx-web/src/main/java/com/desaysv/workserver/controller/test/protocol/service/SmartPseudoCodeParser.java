package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.variable.JsScriptLanguageEngine;
import lombok.Data;

import javax.script.ScriptException;
import java.util.*;

interface PseudoCodeElement {
    void execute();
}

@Data
class Result {
    private boolean ok;
}

interface ConditionGetter<T> {
    boolean isBeginKeyword(T line);

    boolean isIfKeyword(T line);

    boolean isElseKeyword(T line);

    boolean isEndIfKeyword(T line);

    boolean isEndKeyword(T line);

    int extractLoopCount(T line);

    boolean isAnnotation(T line);

    boolean execute(int index, T line);

    boolean getIfResult(int index);

}

@Data
class Context<T> {
    private List<T> lines;

    private ConditionGetter<T> conditionGetter;

}

class Line<T> implements PseudoCodeElement {
    private final int index;
    private final T content;
    private final Context<T> context;

    public Line(int index, T content, Context<T> context) {
        this.index = index;
        this.content = content;
        this.context = context;
    }

    @Override
    public void execute() {
        if (!context.getConditionGetter().isAnnotation(content)) {
            context.getConditionGetter().execute(index, content);
        }
    }

    @Override
    public String toString() {
        return String.valueOf(content);
    }
}

public class SmartPseudoCodeParser<T> {

    private final List<PseudoCodeElement> elements = new ArrayList<>();
    private int currentLineIndex = 0;
    private final Context<T> context = new Context<>();

    public void parse(List<T> lines, ConditionGetter<T> conditionGetter) {
        context.setLines(lines);
        context.setConditionGetter(conditionGetter);

        while (currentLineIndex < lines.size()) {
            T line = lines.get(currentLineIndex);

            if (context.getConditionGetter().isBeginKeyword(line)) {
                parseBeginEndBlock(lines, elements, context);
            } else if (context.getConditionGetter().isIfKeyword(line)) {
                parseIfElseBlock(lines, elements, currentLineIndex, context);
            } else {
                elements.add(new Line<>(currentLineIndex, line, context));
                currentLineIndex++;
            }

        }
    }

    private void parseBeginEndBlock(List<T> lines, List<PseudoCodeElement> targetElements, Context<T> context) {
        int loopCount = context.getConditionGetter().extractLoopCount(lines.get(currentLineIndex++));
        List<PseudoCodeElement> blockElements = new ArrayList<>();

        while (currentLineIndex < lines.size() && !context.getConditionGetter().isEndKeyword(lines.get(currentLineIndex))) {
            parse(lines, blockElements, context);
        }

        targetElements.add(new Loop(blockElements, loopCount));
        currentLineIndex++; // Skip the END keyword
    }

    private void parseIfElseBlock(List<T> lines, List<PseudoCodeElement> targetElements, int startIndex, Context<T> context) {
        List<PseudoCodeElement> ifBlockElements = new ArrayList<>();
        List<PseudoCodeElement> elseBlockElements = new ArrayList<>();

        // Parse IF block
        boolean isInsideIfBlock = true;
        currentLineIndex++;
        while (currentLineIndex < lines.size() && isInsideIfBlock) {
            T line = lines.get(currentLineIndex);

            if (context.getConditionGetter().isBeginKeyword(line)) {
                parseBeginEndBlock(lines, ifBlockElements, context);
            } else if (context.getConditionGetter().isIfKeyword(line)) {
                parseIfElseBlock(lines, ifBlockElements, currentLineIndex, context); // Recursively parse nested IF blocks
            } else if (isControlKeyword(line)) {
                if (context.getConditionGetter().isElseKeyword(line) || context.getConditionGetter().isEndIfKeyword(line)) {
                    isInsideIfBlock = false;
                }
            } else {
                ifBlockElements.add(new Line<>(currentLineIndex, line, context));
                currentLineIndex++;
            }
        }

        // Parse ELSE block if present
        boolean hasElseBlock = currentLineIndex < lines.size() && context.getConditionGetter().isElseKeyword(lines.get(currentLineIndex));
        if (hasElseBlock) {
            currentLineIndex++; // Skip the ELSE keyword

            while (currentLineIndex < lines.size() && !context.getConditionGetter().isEndIfKeyword(lines.get(currentLineIndex))) {
                T line = lines.get(currentLineIndex);

                if (context.getConditionGetter().isBeginKeyword(line)) {
                    parseBeginEndBlock(lines, elseBlockElements, context);
                } else if (context.getConditionGetter().isIfKeyword(line)) {
                    parseIfElseBlock(lines, elseBlockElements, currentLineIndex, context); // Recursively parse nested IF blocks
                } else {
                    elseBlockElements.add(new Line<>(currentLineIndex, line, context));
                    currentLineIndex++;
                }
            }
        }

        targetElements.add(new IfElse<>(startIndex, ifBlockElements, elseBlockElements, context));
        currentLineIndex++; // Skip the ENDIF keyword
    }

    private void parse(List<T> lines, List<PseudoCodeElement> targetElements, Context<T> context) {
        T line = lines.get(currentLineIndex);

        if (isControlKeyword(line)) {
            // Handle control keywords (BEGIN, END, IF, ELSE, ENDIF)
            if (context.getConditionGetter().isBeginKeyword(line)) {
                parseBeginEndBlock(lines, targetElements, context);
            } else if (context.getConditionGetter().isIfKeyword(line)) {
                parseIfElseBlock(lines, targetElements, currentLineIndex, context);
            }
        } else {
            targetElements.add(new Line<>(currentLineIndex, line, context));
            currentLineIndex++;
        }
    }

    private boolean isControlKeyword(T line) {
        return context.getConditionGetter().isBeginKeyword(line) || context.getConditionGetter().isIfKeyword(line) ||
                context.getConditionGetter().isElseKeyword(line) || context.getConditionGetter().isEndKeyword(line) ||
                context.getConditionGetter().isEndIfKeyword(line);
    }


    public void execute() {
        for (PseudoCodeElement element : elements) {
            element.execute();
        }
    }
}

class Loop implements PseudoCodeElement {
    private final List<PseudoCodeElement> elements;
    private final int loopCount;

    public Loop(List<PseudoCodeElement> elements, int loopCount) {
        this.elements = elements;
        this.loopCount = loopCount;
    }

    @Override
    public void execute() {
        for (int i = 0; i < loopCount; i++) {
            for (PseudoCodeElement element : elements) {
                element.execute();
            }
        }
    }
}

class IfElse<T> implements PseudoCodeElement {
    private final int index;
    private final List<PseudoCodeElement> ifBlockElements;
    private final List<PseudoCodeElement> elseBlockElements;
    private final Context<T> context;

    public IfElse(int index, List<PseudoCodeElement> ifBlockElements, List<PseudoCodeElement> elseBlockElements, Context<T> context) {
        this.index = index;
        this.context = context;
        this.ifBlockElements = ifBlockElements;
        this.elseBlockElements = elseBlockElements;
    }

    @Override
    public void execute() {
        if (context.getConditionGetter().getIfResult(index)) {
            for (PseudoCodeElement element : ifBlockElements) {
                element.execute();
            }
        } else {
            for (PseudoCodeElement element : elseBlockElements) {
                element.execute();
            }
        }
    }

    @Override
    public String toString() {
        return String.format("%s | %s", index, context.getLines().get(index));
    }
}

class StringPseudoCodeParserTest {

    public static final String BEGIN_KEYWORD = "begin";
    public static final String END_KEYWORD = "end";
    public static final String IF_KEYWORD = "if";
    public static final String ELSE_KEYWORD = "else";
    public static final String ENDIF_KEYWORD = "endif";

    public static void main(String[] args) {
//        String[] pseudoCodeLines = {
//                "if 3>2",
//                "iiff1-1-if-true-yes",
//                "if 2>10",
//                "iiff2-1-if-false-no",
//                "begin 100",
//                "kkkk",
//                "end",
//                "else",
//                "iiff2-1-else-yes",
//                "endif",
//                "iiff1-1-if-true-yes",
//                "else",
//                "iiff1-1-else-no",
//                "endif",
//        };
//        String[] pseudoCodeLines = {
//                "begin 2",
//                "outer 1",
//                "begin 3",
//                "inner",
//                "begin 2",
//                "hhh",
//                "end",
//                "end",
//                "outer 2",
//                "end"
//        };
        String[] pseudoCodeLines = {
                "if 33>22 ::0",
                "#iiff0-1-if-true-yes",
                "iiff0-2-if-true-yes",
                "else",
                "iiff0-1-else-false-no",
                "endif",
                "line0",
                "begin 1",
                "line1-begin-10",
                "line2-begin-10",
                "begin 3",
                "line3-begin-20",
                "-----------",
                "if 3>2 ::12",
                "iiff1-1-if-true-yes",
                "iiff1-2-if-true-yes",
                "if 2>10 ::15",
                "iiff2-1-if-false-no",
                "else",
                "iiff2-1-else-yes",
                "iiff2-2-else-yes",
                "if 4>2 ::20",
                "kfc",
                "endif",
                "endif",
                "iiff1-1-if-true-yes",
                "else",
                "if 1>2 ::23",
                "abc",
                "endif",
                "iiff1-1-else-no",
                "iiff1-2-else-no",
                "iiff1-3-else-no",
                "endif",
                "linekkk-begin-20",
                "-----------",
                "end",
                "line12-begin-10",
                "end",
                "line00",
        };


        SmartPseudoCodeParser<String> parser = new SmartPseudoCodeParser<>();
        ConditionGetter<String> conditionGetter = new ConditionGetter<String>() {
            private final Map<Integer, Boolean> results = new HashMap<>();

            @Override
            public boolean isBeginKeyword(String line) {
                return line.startsWith(BEGIN_KEYWORD);
            }

            @Override
            public boolean isIfKeyword(String line) {
                return line.startsWith(IF_KEYWORD);
            }

            @Override
            public boolean isElseKeyword(String line) {
                return line.startsWith(ELSE_KEYWORD);
            }

            @Override
            public boolean isEndIfKeyword(String line) {
                return line.startsWith(ENDIF_KEYWORD);
            }

            @Override
            public boolean isEndKeyword(String line) {
                return line.startsWith(END_KEYWORD);
            }

            @Override
            public int extractLoopCount(String line) {
                // Extract the loop count from the BEGIN line (e.g., "begin 10")
                return Integer.parseInt(line.substring(BEGIN_KEYWORD.length()).trim());
            }

            @Override
            public boolean isAnnotation(String line) {
                return line.startsWith("#");
            }

            @Override
            public boolean execute(int index, String line) {
                System.out.println(line);
                results.put(index, true);
                return true;
            }

            @Override
            public boolean getIfResult(int index) {
                String line = pseudoCodeLines[index];
                String[] lines = line.split("\\s+");
                if (lines.length > 1) {
                    String expr = lines[1];
                    try {
                        return Boolean.parseBoolean(String.valueOf(JsScriptLanguageEngine.getInstance().eval(expr)));
                    } catch (ScriptException e) {
                        return false;
                    }
                }
                return false;
            }
        };
        parser.parse(Arrays.asList(pseudoCodeLines), conditionGetter);
        parser.execute();
    }
}