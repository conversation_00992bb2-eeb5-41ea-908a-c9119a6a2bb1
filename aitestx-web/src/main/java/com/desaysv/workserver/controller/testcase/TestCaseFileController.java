package com.desaysv.workserver.controller.testcase;

import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.TestScriptFileContent;
import com.desaysv.workserver.controller.testcase.converter.TestCasePreConverter;
import com.desaysv.workserver.exceptions.TestCaseFileNotFoundException;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.filemanager.project.TestCaseFileManager;
import com.desaysv.workserver.model.TestClient;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.model.TestScriptFile;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.TestCaseFileService;
import com.desaysv.workserver.service.TestClientService;
import com.desaysv.workserver.service.TestProjectService;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.bo.TestScriptFileSelector;
import com.desaysv.workserver.vo.testcase.TestcaseFileVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-3 11:03
 * @description :
 * @modified By :
 * @since : 2022-8-3
 */
@Slf4j
@RestController
@RequestMapping("/testScript") //testcaseFile
@Lazy
public class TestCaseFileController {

    @Autowired
    private TestCasePreConverter testCasePreConverter;

    @Autowired
    private TestCaseFileService testCaseFileService;

    @Autowired
    private TestClientService testClientService;

    @Autowired
    private TestProjectService testProjectService;

    private void prepare(TestcaseFileVo testcaseFileVo) throws TestCaseFileNotFoundException {
        TestClient testClient = testClientService.getClientByName(testcaseFileVo.getClientName());
        TestProject testProject = testProjectService.getProjectByName(testcaseFileVo.getProjectName());
        if (testClient == null) {
            throw new TestCaseFileNotFoundException("测试客户端未在数据库中");
        }
        if (testProject == null) {
            throw new TestCaseFileNotFoundException("测试项目未在数据库中");
        }
        testcaseFileVo.setClientId(testClient.getId());
        testcaseFileVo.setProjectId(testProject.getId());
        testcaseFileVo.setClientName(testClient.getName());
        testcaseFileVo.setProjectName(testProject.getName());
    }

    private TestcaseFileVo fromTestcaseFile(TestScriptFile testScriptFile) {
        TestcaseFileVo testcaseFileVo = new TestcaseFileVo();
        testcaseFileVo.setId(testScriptFile.getId());
        testcaseFileVo.setSelected(testScriptFile.isSelected());
        testcaseFileVo.setUuid(testScriptFile.getUuid());
        testcaseFileVo.setModuleName(testScriptFile.getModuleName());
        testcaseFileVo.setCaseName(testScriptFile.getCaseName());
        testcaseFileVo.setComment(testScriptFile.getComment());
        testcaseFileVo.setProjectId(testScriptFile.getProjectId());
        testcaseFileVo.setClientId(testScriptFile.getClientId());
        return testcaseFileVo;
    }

    @GetMapping
    public ResultEntity<TestScriptFile> getTestCaseFile(@RequestBody TestcaseFileVo testcaseFileVo) {
        //TODO：clientId、projectID尝试写到session中携带
        try {
            prepare(testcaseFileVo);
        } catch (TestCaseFileNotFoundException e) {
            return ResultEntity.fail(e.getMessage(), null);
        }
        TestScriptFile testScriptFile = testCaseFileService.getFileByCondition(testcaseFileVo);
        return testScriptFile == null ? ResultEntity.fail() : ResultEntity.ok(testScriptFile);
    }

    //不公开的接口
    @PostMapping("/convert")
    public ResultEntity<Object> convertTestScript(@RequestBody TestcaseFileVo testcaseFileVo) {
        try {
            prepare(testcaseFileVo);
        } catch (TestCaseFileNotFoundException e) {
            return ResultEntity.fail(e.getMessage(), new ArrayList<>());
        }
        List<TestScriptFile> testScriptFiles = testCaseFileService.getAllTestCaseFiles(testcaseFileVo);
        for (TestScriptFile testScriptFile : testScriptFiles) {
            log.info("转换测试脚本:{}.json", testScriptFile.getCaseName());
            TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
            TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
            TestScriptFileContent testScriptFileContent = fileManager.readTestCaseFile(testScriptFile.getCaseName());
            testScriptFileContent.setId(testScriptFile.getId());
            testScriptFileContent.setUuid(testScriptFile.getUuid());
            List<Operation> operations = testCasePreConverter.manualConvertMethods(testScriptFileContent.getOperationList());
            testScriptFileContent.setOperationList(operations);
            updateTestScriptContent(testScriptFileContent);
        }
        return ResultEntity.ok();
    }

    @GetMapping("/load/{testCaseFileUUID}")
    public ResultEntity<TestScriptFileContent> loadTestScriptFromFile(@PathVariable("testCaseFileUUID") String testCaseFileUUID) {
        TestScriptFile testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testCaseFileUUID);
        if (testScriptFile != null) {
            log.info("加载测试脚本:{}.json", testScriptFile.getCaseName());
            TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
            TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
            TestScriptFileContent testScriptFileContent = fileManager.readTestCaseFile(testScriptFile.getCaseName());
            testScriptFileContent.setId(testScriptFile.getId());
            testScriptFileContent.setUuid(testScriptFile.getUuid());
            //自动更新脚本
            List<Operation> operations = testCasePreConverter.autoConvertMethods(testScriptFileContent.getOperationList());
            testScriptFileContent.setOperationList(operations);
            updateTestScriptContent(testScriptFileContent);
            //更新end
//            List<Operation> operationList = JSON.parseArray(testCaseContent, Operation.class);
            return ResultEntity.ok("请求成功", testScriptFileContent);
        } else {
            return ResultEntity.fail();
        }
    }

    @DeleteMapping("/clear")
    public ResultEntity<String> clearAllTestCaseFiles(@RequestBody TestcaseFileVo testcaseFileVo) {
        try {
            prepare(testcaseFileVo);
        } catch (TestCaseFileNotFoundException e) {
            return ResultEntity.fail(e.getMessage());
        }
        log.info("清空测试脚本:{}", testcaseFileVo);
        List<TestScriptFile> testScriptFiles = testCaseFileService.getAllTestCaseFiles(testcaseFileVo);
        ResultEntity<String> result = null;
        for (TestScriptFile testScriptFile : testScriptFiles) {
            result = deleteTestCaseFile(testScriptFile.getUuid());
        }
        return result == null ? ResultEntity.ok() : result;
    }

    @PostMapping("/all")
    public ResultEntity<List<TestcaseFileVo>> getAllTestCaseFiles(@RequestBody TestcaseFileVo testcaseFileVo) {
        try {
            prepare(testcaseFileVo);
        } catch (TestCaseFileNotFoundException e) {
            return ResultEntity.fail(e.getMessage(), new ArrayList<>());
        }
        log.info("查询测试脚本:{}", testcaseFileVo);
        List<TestScriptFile> testScriptFiles = testCaseFileService.getAllTestCaseFiles(testcaseFileVo);
        List<TestcaseFileVo> testcaseFileVos = new ArrayList<>();
        for (TestScriptFile file : testScriptFiles) {
            TestcaseFileVo vo = fromTestcaseFile(file);
            vo.setProjectName(testcaseFileVo.getProjectName());
            vo.setClientName(testcaseFileVo.getClientName());
            testcaseFileVos.add(vo);
        }
        return ResultEntity.ok(testcaseFileVos);
    }


    @PostMapping("/add")
    public ResultEntity<TestcaseFileVo> addTestCaseFile(@RequestBody TestcaseFileVo testcaseFileVo) {
        //TODO:判断是否存在
        try {
            prepare(testcaseFileVo);
        } catch (TestCaseFileNotFoundException e) {
            return ResultEntity.fail(e.getMessage(), testcaseFileVo);
        }
        TestScriptFile testScriptFile = testCaseFileService.getFileByCondition(testcaseFileVo);
        TestCaseFileManager fileManager = ProjectFileManager.of(testcaseFileVo.getProjectName(), TestCaseFileManager.class);
        boolean created = false;
        boolean copy = false;
        if (testScriptFile == null) {
            //新建用例文件
            log.info("添加测试脚本:{}", testcaseFileVo);
            testScriptFile = new TestScriptFile();
            testScriptFile.setUuid(StrUtils.getSaltMD5(testcaseFileVo.toString(), StrUtils.generateUUID()));
            testScriptFile.setModuleName(testcaseFileVo.getModuleName());
            testScriptFile.setCaseName(testcaseFileVo.getCaseName());
            testScriptFile.setClientId(testcaseFileVo.getClientId());
            testScriptFile.setProjectId(testcaseFileVo.getProjectId());
            testCaseFileService.addTestCaseFile(testScriptFile);
            TestScriptFileContent testScriptFileContent = new TestScriptFileContent();
            testScriptFileContent.setTestCycle(-1);
            if (!testcaseFileVo.isImported()) {
                fileManager.updateTestCaseFile(testcaseFileVo.getCaseName(), null);
            }
            created = true;
        } else {
            //数据库存在脚本名
            log.info("数据库已存在相同测试脚本文件名:{}", testcaseFileVo.getCaseName());
//            fileManager.updateTestCase(testcaseFile.getFileName(), testcaseFileVo.getFileName(), testcaseFileVo.getFileContent());
        }
        if (testcaseFileVo.isImported() && !StrUtils.isEmpty(testcaseFileVo.getImportedFilePath())) {
            //复制导入的文件
            if (fileManager.copyTestCaseFile(testcaseFileVo.getImportedFilePath())) {
                copy = true;
            }
        }
        testcaseFileVo.setId(testScriptFile.getId());
        testcaseFileVo.setUuid(testScriptFile.getUuid());
        testcaseFileVo.setClientId(testScriptFile.getClientId());
        testcaseFileVo.setProjectId(testScriptFile.getProjectId());
        return created ? ResultEntity.ok(testcaseFileVo) : copy ?
                ResultEntity.fail("测试脚本已重新复制到脚本文件夹", testcaseFileVo) :
                ResultEntity.fail("测试脚本已存在，请重新命名！", testcaseFileVo);
    }

    @DeleteMapping("/delete/{testCaseFileUUID}")
    public ResultEntity<String> deleteTestCaseFile(@PathVariable("testCaseFileUUID") String testCaseFileUUID) {
        //FIXME：无法删除！！！
        boolean isOk = false;
        TestScriptFile testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testCaseFileUUID);
        if (testScriptFile != null) {
            log.info("删除测试脚本:{}", testScriptFile.getCaseName());
            TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
            TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
            if (fileManager.deleteTestCaseFile(testScriptFile.getCaseName())) {
                // 删除数据库case文件
                isOk = testCaseFileService.deleteTestCaseFile(testCaseFileUUID);
            }
        }
        return isOk ? ResultEntity.ok() : ResultEntity.fail(
                String.format("测试脚本文件UUID %s不存在", testCaseFileUUID));
    }

    @PutMapping("/rename")
    public ResultEntity<TestcaseFileVo> renameTestCaseFile(@RequestBody TestcaseFileVo testcaseFileVo) {
        TestScriptFile testScriptFile = testCaseFileService.getFileByCondition(testcaseFileVo);
        if (testScriptFile != null) {
            return ResultEntity.fail("测试测试已存在，请重新命名！", testcaseFileVo);
        } else {
            testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testcaseFileVo.getUuid());
            if (testScriptFile != null) {
                log.info("重命名测试脚本:{}", testcaseFileVo);
                String oldTestCaseName = testScriptFile.getCaseName();
                String newTestCaseName = testcaseFileVo.getCaseName();
                testScriptFile.setCaseName(newTestCaseName);
                testCaseFileService.updateTestCaseFile(testScriptFile);
                TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
                TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
                fileManager.renameTestCaseFile(oldTestCaseName, newTestCaseName);
                return ResultEntity.ok(testcaseFileVo);
            } else {
                return ResultEntity.fail(String.format("测试脚本文件UUID错误:%s", testcaseFileVo.getUuid()), testcaseFileVo);
            }
        }
    }

    @PutMapping("/update")
    public ResultEntity<TestcaseFileVo> updateTestCaseFile(@RequestBody TestcaseFileVo testcaseFileVo) {
        TestScriptFile testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testcaseFileVo.getUuid());
        if (testScriptFile != null) {
            log.info("更新测试脚本:{}", testcaseFileVo);
            testScriptFile.setSelected(testcaseFileVo.isSelected());
            testScriptFile.setComment(testcaseFileVo.getComment());
            testScriptFile.setModuleName(testcaseFileVo.getModuleName());
            testCaseFileService.updateTestCaseFile(testScriptFile);
            return ResultEntity.ok(testcaseFileVo);
        } else {
            return ResultEntity.fail(String.format("测试脚本文件UUID错误:%s", testcaseFileVo.getUuid()), testcaseFileVo);
        }
    }

    @PutMapping("/update/testCycle")
    public ResultEntity<TestcaseFileVo> updateTestScriptTestCycle(@RequestBody TestScriptFileContent testScriptFileContent) {
        TestScriptFile testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testScriptFileContent.getUuid());
        if (testScriptFile != null) {
            log.info("更新测试脚本:{}.json->测试次数:{}", testScriptFile.getCaseName(), testScriptFileContent.getTestCycle());
            TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
            TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
            TestScriptFileContent rawTestScriptFileContent = fileManager.readTestCaseFile(testScriptFile.getCaseName());
            rawTestScriptFileContent.setTestCycle(testScriptFileContent.getTestCycle());
            fileManager.updateTestCaseFile(testScriptFile.getCaseName(), rawTestScriptFileContent);
            return ResultEntity.ok(fromTestcaseFile(testScriptFile));
        } else {
            return ResultEntity.fail(String.format("测试脚本文件UUID错误:%s", testScriptFileContent.getUuid()), null);
        }
    }

    @PutMapping("/update/file")
    public ResultEntity<TestcaseFileVo> updateTestScriptContent(@RequestBody TestScriptFileContent testScriptFileContent) {
        TestScriptFile testScriptFile = testCaseFileService.getFileByTestCaseFileUUID(testScriptFileContent.getUuid());
        if (testScriptFile != null) {
            log.info("更新测试脚本:{}.json", testScriptFile.getCaseName());
            TestProject testProject = testProjectService.getProjectById(testScriptFile.getProjectId());
            TestCaseFileManager fileManager = ProjectFileManager.of(testProject.getName(), TestCaseFileManager.class);
            fileManager.updateTestCaseFile(testScriptFile.getCaseName(), testScriptFileContent);
            return ResultEntity.ok(fromTestcaseFile(testScriptFile));
        } else {
            return ResultEntity.fail(String.format("测试脚本文件UUID错误:%s", testScriptFileContent.getUuid()), null);
        }
    }

    @PutMapping("/update/selectAll")
    public ResultEntity<TestScriptFileSelector> updateSelectAll(@RequestBody TestScriptFileSelector testScriptFileSelector) {
        log.info("全选测试脚本内容:{}", testScriptFileSelector);
        TestClient testClient = testClientService.getClientByName(testScriptFileSelector.getClientName());
        TestProject testProject = testProjectService.getProjectByName(testScriptFileSelector.getProjectName());
        testScriptFileSelector.setClientId(testClient.getId());
        testScriptFileSelector.setProjectId(testProject.getId());
        if (testCaseFileService.updateAllSelected(testScriptFileSelector)) {
            return ResultEntity.ok(testScriptFileSelector);
        } else {
            return ResultEntity.fail(testScriptFileSelector);
        }
    }
}
