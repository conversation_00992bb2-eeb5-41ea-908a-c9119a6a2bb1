package com.desaysv.workserver.controller;

import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.NetworkUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * 异常处理
 */
@Slf4j
@RestControllerAdvice
public class ControllerExceptionHandleAdvice {
    @ExceptionHandler(Exception.class)
    public ResultEntity<String> errorHandler(Exception e) {
        String stackTrace = getStackTraceAsString(e);
        getMemoryInfo();
        String status = NetworkUtils.isNetworkAvailable() ? "正常" : "异常";
        log.info("异常时网络-->{}", status);
        log.error("发生异常:", e);
        log.debug("堆栈信息:{}", stackTrace);
        return ResultEntity.fail(ExceptionUtils.getExceptionString(e));
    }

    // 将堆栈跟踪信息转换为字符串
    private String getStackTraceAsString(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }

    private static void getMemoryInfo() {
        long kb = 1024;
        // 获取JVM内存总量
        long totalMemory = Runtime.getRuntime().totalMemory() / kb;
        // 获取JVM空闲内存量
        long freeMemory = Runtime.getRuntime().freeMemory() / kb;
        // 获取JVM最大可用内存量
        long maxMemory = Runtime.getRuntime().maxMemory() / kb;
        log.info("JVM内存总量:{} KB ", totalMemory);
        log.info("JVM空闲内存量:{} KB ", freeMemory);
        log.info("JVM最大可用内存量:{} KB ", maxMemory);
    }

}


