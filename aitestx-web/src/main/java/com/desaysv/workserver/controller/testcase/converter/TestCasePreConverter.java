package com.desaysv.workserver.controller.testcase.converter;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.operation.handlers.RandomWaitTime;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 测试脚本前置转换器（每次脚本文件拦截后更新）
 */
@Component
@Lazy
public class TestCasePreConverter {

    public static Integer extractNumberAfterHash(String input) {
        Pattern pattern = Pattern.compile("#(\\d+)");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return Integer.valueOf(matcher.group(1));
        }
        return null; // 如果没有找到匹配的数字，返回null
    }

    /**
     * 自动转换
     *
     * @param operationList 操作列表
     * @return List<Operation>
     */
    public List<Operation> autoConvertMethods(List<Operation> operationList) {
        if (operationList == null) {
            return null;
        }
        operationList.forEach(operation -> {
            if (operation.getOperationMethod().getKeyword().equals("waitRandomTime")) {
                operation.getOperationMethod().setMethodName("等待时间(秒)");
                operation.getOperationMethod().setKeyword("waitTimeWithUnit");
                if (operation.getOperationObject() instanceof JSON) {
                    RandomWaitTime randomWaitTime = JSON.to(RandomWaitTime.class, operation.getOperationObject());
                    if (randomWaitTime.getDataType().equals("int")) {
                        operation.setOperationObject(String.format("%s~%s", (int) randomWaitTime.getLowerValue(), (int) randomWaitTime.getUpperValue()));
                    } else {
                        operation.setOperationObject(String.format("%s~%s", randomWaitTime.getLowerValue(), randomWaitTime.getUpperValue()));
                    }
                }
            }

            if (operation.getOperationTarget() != null
                    && operation.getOperationTarget().getAliasName() != null
                    && operation.getOperationTarget().getAliasName().equals("ZCAN_USBCANFD_200U#0")) {
                operation.getOperationTarget().setAliasName("ZCAN_USBCANFD_200U#1");
            }
            if (operation.getOperationTarget() != null
                    && operation.getOperationTarget().getAliasName() != null
                    && operation.getOperationTarget().getAliasName().equals("NI_CAN_8502#0")) {
                operation.getOperationTarget().setAliasName("NI_CAN_8502#1");
            }
            if (operation.getOperationTarget() != null
                    && operation.getOperationTarget().getAliasName() != null
                    && operation.getOperationTarget().getAliasName().equals("AUTOCLICKER#0")) {
                operation.getOperationTarget().setAliasName("AutoClicker#1");
            }

            if (operation.getOperationTarget() != null
                    && operation.getOperationTarget().getAliasName() != null
                    && operation.getOperationTarget().getAliasName().contains("powerBox")) {
                Integer index = extractNumberAfterHash(operation.getOperationTarget().getAliasName());
                if (index != null) {
                    operation.getOperationTarget().setAliasName(String.format("PowerBox#%d", index));
                }
            }

            if (operation.getOperationTarget() != null && StringUtils.isEmpty(operation.getOperationTarget().getDeviceType())) {
                String deviceModel = operation.getOperationTarget().getDeviceModel();
                try {
                    operation.getOperationTarget().setDeviceType(DeviceModel.getDeviceTypeByModel(deviceModel));
                } catch (IllegalAccessException ignored) {

                }
            }

            if (operation.getOperationTarget() != null && operation.getOperationTarget().getDeviceIndex() == 0) {
                operation.getOperationTarget().setDeviceIndex(1);
            }

        });
        return operationList;
    }

    /**
     * 手动转换
     *
     * @param operationList 操作列表
     * @return List<Operation>
     */
    public List<Operation> manualConvertMethods(List<Operation> operationList) {
        if (operationList == null) {
            return null;
        }
        operationList.forEach(operation -> {
            if (operation.getOperationTarget() != null && (operation.getOperationTarget().getAliasName().contains(DeviceModel.Bus.ZLG_USBCANFD_200U) ||
                    operation.getOperationTarget().getAliasName().contains(DeviceModel.Bus.NI_CAN_8502))) {
                Integer channel = operation.getOperationTarget().getChannel();
                operation.getOperationTarget().setChannel((channel == null || channel == 0) ? 1 : 2);
            }
        });
        return operationList;
    }

}
