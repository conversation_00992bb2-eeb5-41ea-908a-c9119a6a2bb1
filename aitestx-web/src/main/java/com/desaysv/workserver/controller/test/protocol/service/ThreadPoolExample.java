package com.desaysv.workserver.controller.test.protocol.service;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class ThreadPoolExample {
    public static void main(String[] args) {
        // 创建一个有界线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            2,                      // 核心线程数
            4,                      // 最大线程数
            60, TimeUnit.SECONDS,   // 空闲线程存活时间
            new ArrayBlockingQueue<>(2),  // 任务队列容量为2
            new ThreadPoolExecutor.CallerRunsPolicy()  // 使用CallerRunsPolicy
        );

        // 模拟提交10个任务
        for (int i = 0; i < 10; i++) {
            final int taskId = i;
            try {
                executor.submit(() -> {
                    System.out.println("Task " + taskId + " executed by " + 
                        Thread.currentThread().getName());
                    try {
                        Thread.sleep(5000); // 模拟任务执行
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            } catch (Exception e) {
                System.out.println("Task " + taskId + " rejected: " + e.getMessage());
            }
        }
    }
}