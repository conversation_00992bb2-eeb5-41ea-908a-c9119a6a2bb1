package com.desaysv.workserver.controller.relay;

import com.desaysv.workserver.devices.electric_relay.ElectricRelayConfig;
import com.desaysv.workserver.devices.electric_relay.service.ElectricRelayService;
import com.desaysv.workserver.response.ResultEntity;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 继电器控制器类，提供继电器相关的REST API接口。
 *
 */

@Slf4j
@RestController
@RequestMapping("/relay")
@Lazy
public class ElectricRelayController {
    @Autowired
    private ElectricRelayService electricRelayService;

    @Operation(summary = "更新继电器延时配置", description = "提交新的延时配置到指定继电器设备")
    @PostMapping("/updateRelayDelayConfig")
    public ResultEntity<String> updateRelayDelayConfig(@RequestBody Map<String, Map<String, String>> deviceDelayConfigs) {
        electricRelayService.forwardRelayConfig(deviceDelayConfigs);
        return ResultEntity.ok(deviceDelayConfigs.toString());
    }

    @Operation(summary = "更新继电器文本数据", description = "更新继电器文本数据")
    @PostMapping("/updateRelayTextConfig")
    public ResultEntity<String> updateRelayTextConfig(@RequestBody ElectricRelayConfig electricRelayConfig) {
        electricRelayService.updateRelayTextConfig(electricRelayConfig);
        return ResultEntity.ok(electricRelayConfig.toString());
    }

    @Operation(summary = "更新继电器连接状态", description = "更新继电器连接状态")
    @PostMapping("/updateRelayStatusConfig")
    public ResultEntity<String> updateRelayStatusConfig(@RequestBody ElectricRelayConfig electricRelayConfig) {
        electricRelayService.updateRelayStatusConfig(electricRelayConfig);
        return ResultEntity.ok(electricRelayConfig.toString());
    }

    @Operation(summary = "更新继电器连接开关状态", description = "更新继电器连接开关状态")
    @PostMapping("/updateConnectSwitchStatus")
    public ResultEntity<String> updateConnectSwitchStatus(@RequestBody ElectricRelayConfig electricRelayConfig) {
        electricRelayService.updateConnectSwitchStatus(electricRelayConfig);
        return ResultEntity.ok(electricRelayConfig.toString());
    }

    @Operation(summary = "获取继电器文本数据", description = "获取继电器文本数据")
    @PostMapping("/getRelayTextConfig")
    public ResultEntity<List<ElectricRelayConfig>> getRelayTextConfig(@RequestBody Map<String, String> paramsMap) {
        List<ElectricRelayConfig> relayTextConfig = electricRelayService.getRelayTextConfig(paramsMap);
        return ResultEntity.ok(relayTextConfig);
    }
}