package com.desaysv.workserver.controller.device.robot;

import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.robot.RobotDevice;
import com.desaysv.workserver.devices.robot.interfaces.IDobot;
import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.NamedRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RandomRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.SwipeRobotCoordinates;
import com.desaysv.workserver.service.CoordinatesRoiService;
import com.desaysv.workserver.exceptions.robot.RobotCoordinatesQueryException;
import com.desaysv.workserver.manager.DeviceManager;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.model.TestDevice;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.model.roi.CoordinatesRoi;
import com.desaysv.workserver.service.*;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.vo.RobotCoordinatesVo;
import com.desaysv.workserver.vo.robot.RobotCoordinatesQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.awt.geom.Rectangle2D;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 14:42
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
@Slf4j
@Component
@Lazy
public class RobotCoordinatesApplication {

    @Autowired
    private RobotCoordinatesService robotCoordinatesService;

    @Autowired
    private CoordinatesRoiService coordinatesRoiService;

    @Autowired
    private TestProjectService testProjectService;

    @Autowired
    private TestDeviceTypeService testDeviceTypeService;

    @Autowired
    private TestDeviceModelService testDeviceModelService;

    @Autowired
    private TestDeviceService testDeviceService;

    @Autowired
    private DeviceManager deviceManager;

    private RobotCoordinatesVo fromRobotCoordinates(RobotCoordinates robotCoordinates) {
        RobotCoordinatesVo robotCoordinatesVo = new RobotCoordinatesVo();
        robotCoordinatesVo.setId(robotCoordinates.getId());
        robotCoordinatesVo.setUuid(robotCoordinates.getUuid());
        robotCoordinatesVo.setName(robotCoordinates.getName());
        robotCoordinatesVo.setX(robotCoordinates.getX());
        robotCoordinatesVo.setY(robotCoordinates.getY());
        robotCoordinatesVo.setZ(robotCoordinates.getZ());
        robotCoordinatesVo.setR(robotCoordinates.getR());
        robotCoordinatesVo.setSlideRail(robotCoordinates.getSlideRail());
        TestProject testProject = testProjectService.getProjectById(robotCoordinates.getProjectId());
        robotCoordinatesVo.setProjectId(robotCoordinates.getProjectId());
        robotCoordinatesVo.setProjectName(testProject.getName());
        TestDevice testDevice = testDeviceService.getDevice(robotCoordinates.getDeviceId());
        robotCoordinatesVo.setDeviceId(robotCoordinates.getDeviceId());
        robotCoordinatesVo.setDeviceUniqueCode(testDevice.getUniqueCode());
        return robotCoordinatesVo;
    }

    private RobotCoordinates fromRobotCoordinatesVo(RobotCoordinatesVo robotCoordinatesVo) {
//        System.out.println("robotCoordinatesVo:" + robotCoordinatesVo);
        RobotCoordinates robotCoordinates = new RobotCoordinates();
        robotCoordinates.setId(robotCoordinatesVo.getId());
        if (robotCoordinatesVo.getUuid() == null) {
            String robotCoordinatesUUID = StrUtils.getSaltMD5(robotCoordinatesVo.toString(), StrUtils.generateUUID());
            robotCoordinatesVo.setUuid(robotCoordinatesUUID);
        }
        robotCoordinates.setUuid(robotCoordinatesVo.getUuid());
        robotCoordinates.setName(robotCoordinatesVo.getName());
        robotCoordinates.setX(robotCoordinatesVo.getX());
        robotCoordinates.setY(robotCoordinatesVo.getY());
        robotCoordinates.setZ(robotCoordinatesVo.getZ());
        robotCoordinates.setR(robotCoordinatesVo.getR());
        robotCoordinates.setSlideRail(robotCoordinatesVo.getSlideRail());
        if (robotCoordinatesVo.getProjectId() != null) {
            robotCoordinates.setProjectId(robotCoordinatesVo.getProjectId());
        } else {
            TestProject testProject = testProjectService.getProjectByName(robotCoordinatesVo.getProjectName());
            robotCoordinates.setProjectId(testProject.getId());
        }
        if (robotCoordinatesVo.getDeviceId() != null) {
            robotCoordinates.setDeviceId(robotCoordinatesVo.getDeviceId());
        } else {
            TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(robotCoordinatesVo.getDeviceUniqueCode());
            robotCoordinates.setDeviceId(testDevice.getId());
        }
        return robotCoordinates;
    }

    public RobotCoordinates queryNamedRobotCoordinates(NamedRobotCoordinates namedRobotCoordinates) {
//        log.info("queryNamedRobotCoordinates:{}", namedRobotCoordinates);
        RobotCoordinates robotCoordinates = new RobotCoordinates();
        TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(namedRobotCoordinates.getDeviceUniqueCode());
        TestProject testProject = testProjectService.getProjectByName(namedRobotCoordinates.getProjectName());
        if (testDevice == null || testProject == null) {
            return null;
        }
        robotCoordinates.setProjectId(testProject.getId());
        robotCoordinates.setDeviceId(testDevice.getId());
        robotCoordinates.setName(namedRobotCoordinates.getCoordinateName());
        RobotCoordinates coordinates = robotCoordinatesService.getCoordinatesByCondition(robotCoordinates);
        if (coordinates != null) {
            CoordinatesRoi coordinatesRoi = coordinatesRoiService.getRoiByCoordinatesUUID(coordinates.getUuid());
            if (coordinatesRoi != null) {
                coordinatesRoi.setDeviceType(DeviceType.DEVICE_ROBOT);
                coordinates.setCoordinatesRoi(coordinatesRoi);
            }
        }
//        System.out.println("coordinates:" + coordinates);
        return coordinates;
    }

    public RobotCoordinatesVo addCoordinates(RobotCoordinatesVo robotCoordinatesVo) {
        RobotCoordinates robotCoordinates = fromRobotCoordinatesVo(robotCoordinatesVo);
        Integer affectedRow = robotCoordinatesService.addCoordinates(robotCoordinates);
        if (affectedRow > 0) {
            return fromRobotCoordinates(robotCoordinates);
        }
        return null;
    }

    public RobotCoordinatesVo deleteCoordinates(RobotCoordinatesVo robotCoordinatesVo) {
        Integer affectedRow = robotCoordinatesService.deleteCoordinatesByUUID(robotCoordinatesVo.getUuid());
        if (affectedRow > 0) {
            //删除关联ROI
            coordinatesRoiService.deleteByCoordinatesUUID(robotCoordinatesVo.getUuid());
            return robotCoordinatesVo;
        }
        return null;
    }

    public RobotCoordinatesVo updateCoordinates(RobotCoordinatesVo robotCoordinatesVo) {
        RobotCoordinates robotCoordinates = fromRobotCoordinatesVo(robotCoordinatesVo);
        Integer affectedRow = robotCoordinatesService.updateCoordinatesByUUID(robotCoordinates);
        if (affectedRow > 0) {
            return robotCoordinatesVo;
        }
        return null;
    }

    public boolean clearAllRobotCoordinates(RobotCoordinatesQueryVo robotCoordinatesQueryVo) {
        String projectName = robotCoordinatesQueryVo.getProjectName();
        String deviceUniqueCode = robotCoordinatesQueryVo.getDeviceUniqueCode();
        TestProject testProject = testProjectService.getProjectByName(projectName);
        TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(deviceUniqueCode);
        return robotCoordinatesService.clearAllCoordinates(testProject.getId(), testDevice.getId()) > 0;
    }

    public List<RobotCoordinatesVo> getAllRobotCoordinates(RobotCoordinatesQueryVo robotCoordinatesQueryVo) {
        String projectName = robotCoordinatesQueryVo.getProjectName();
        String deviceUniqueCode = robotCoordinatesQueryVo.getDeviceUniqueCode();
        TestProject testProject = testProjectService.getProjectByName(projectName);
        TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(deviceUniqueCode);

        List<RobotCoordinates> robotCoordinatesList = robotCoordinatesService.getAllCoordinates(
                testProject.getId(), testDevice.getId());
        List<RobotCoordinatesVo> robotCoordinatesVoList = new ArrayList<>();
        for (RobotCoordinates robotCoordinates : robotCoordinatesList) {
            robotCoordinatesVoList.add(fromRobotCoordinates(robotCoordinates));
        }
        return robotCoordinatesVoList;
    }

    public List<MoveEntity> swipeRobotCoordinates(SwipeRobotCoordinates swipeRobotCoordinates) throws RobotCoordinatesQueryException {
        List<String> robotCoordinateNames = swipeRobotCoordinates.getCoordinateNameList();
        List<MoveEntity> moveEntityList = new ArrayList<>();
        RobotDevice robotDevice = (RobotDevice) deviceManager.getDevice(swipeRobotCoordinates.getDeviceName());
        if (robotDevice == null) {
            throw new RobotCoordinatesQueryException(swipeRobotCoordinates.getDeviceName() + "设备不存在");
        }
        boolean noLift = swipeRobotCoordinates.isNoLift();
        for (String robotCoordinateName : robotCoordinateNames) {
            RobotCoordinates robotCoordinates = new RobotCoordinates();
            robotCoordinates.setName(robotCoordinateName);
            //TODO:使用缓存节省数据库查询时间
            TestProject testProject = testProjectService.getProjectByName(swipeRobotCoordinates.getProjectName());
            robotCoordinates.setProjectId(testProject.getId());
            TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(robotDevice.getDeviceUniqueCode());
            robotCoordinates.setDeviceId(testDevice.getId());
            robotCoordinates = robotCoordinatesService.getCoordinatesByCondition(robotCoordinates);
            if (robotCoordinates == null) {
                String warning = String.format("坐标名\"%s\"不存在，项目:%s，型号:%s",
                        robotCoordinateName,
                        swipeRobotCoordinates.getProjectName(),
                        robotDevice.getDeviceModel());
                log.warn(warning);
                throw new RobotCoordinatesQueryException(warning);
            }
            MoveEntity moveEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
            moveEntityList.add(moveEntity);
        }

        if (robotDevice instanceof IDobot) {
            IDobot dobotDevice = (IDobot) robotDevice;
            dobotDevice.swipe(moveEntityList, !noLift);
        }
        return moveEntityList;
    }

    public MoveEntity randomOrCenterRobotCoordinates(RandomRobotCoordinates randomRobotCoordinates, String type) throws RobotCoordinatesQueryException {
        List<String> robotCoordinateNames = randomRobotCoordinates.getCoordinateNameList();
        MoveEntity moveEntity = new MoveEntity();
        RobotDevice robotDevice = (RobotDevice) deviceManager.getDevice(randomRobotCoordinates.getDeviceName());
        if (robotDevice == null) {
            throw new RobotCoordinatesQueryException(randomRobotCoordinates.getDeviceName() + "设备不存在");
        }
        boolean goAhead = true;
        // 初始化矩形的左上角坐标
        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double minZ = Double.MAX_VALUE;
        double minR = Double.MAX_VALUE;
        // 初始化矩形的右下角坐标
        double maxX = Double.MIN_VALUE;
        double maxY = Double.MIN_VALUE;
        for (String robotCoordinateName : robotCoordinateNames) {
            RobotCoordinates robotCoordinates = new RobotCoordinates();
            robotCoordinates.setName(robotCoordinateName);
            //TODO:使用缓存节省数据库查询时间
            TestProject testProject = testProjectService.getProjectByName(randomRobotCoordinates.getProjectName());
            robotCoordinates.setProjectId(testProject.getId());
            TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(robotDevice.getDeviceUniqueCode());
            robotCoordinates.setDeviceId(testDevice.getId());
            robotCoordinates = robotCoordinatesService.getCoordinatesByCondition(robotCoordinates);
            if (robotCoordinates == null) {
                String warning = String.format("坐标名\"%s\"不存在，项目:%s，型号:%s",
                        robotCoordinateName,
                        randomRobotCoordinates.getProjectName(),
                        robotDevice.getDeviceModel());
                log.warn(warning);
                throw new RobotCoordinatesQueryException(warning);
            }

            double x = MoveEntity.fromRobotCoordinates(robotCoordinates).getX();
            double y = MoveEntity.fromRobotCoordinates(robotCoordinates).getY();
            double z = MoveEntity.fromRobotCoordinates(robotCoordinates).getZ();
            double r = MoveEntity.fromRobotCoordinates(robotCoordinates).getR();
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            minZ = Math.min(minZ, z);
            minR = Math.min(minR, r);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
        }
        double width = Math.abs(maxX - minX);
        double height = Math.abs(maxY - minY);
        Rectangle2D.Double rectangle = new Rectangle2D.Double(minX, minY, width, height);
        double touchX;
        double touchY;
        if (type.equals("random")) {
            // 随机生成点在矩形范围内
            Random random = new Random();
            touchX = rectangle.x + random.nextDouble() * rectangle.width;
            touchY = rectangle.y + random.nextDouble() * rectangle.height;
        } else {
            //中间点
            touchX = rectangle.getCenterX();
            touchY = rectangle.getCenterY();
        }
        moveEntity.setX(touchX);
        moveEntity.setY(touchY);
        moveEntity.setZ(minZ);
        moveEntity.setR(minR);
        if (robotDevice instanceof IDobot) {
            IDobot dobotDevice = (IDobot) robotDevice;
            log.info(String.format("执行点击%s",moveEntity));
            dobotDevice.smartTouch(moveEntity,true,true);
        }
        return moveEntity;
    }

    public CoordinatesRoi addCoordinatesRoi(CoordinatesRoi coordinatesRoi) {
        coordinatesRoi.setDeviceTypeId(testDeviceTypeService.getDeviceTypeByName(coordinatesRoi.getDeviceType()).getId());
        return coordinatesRoiService.insertOrUpdate(coordinatesRoi);
    }

    public boolean deleteRoiByCoordinatesUUID(String coordinatesUUID) {
        return coordinatesRoiService.deleteByCoordinatesUUID(coordinatesUUID) > 0;
    }

    public CoordinatesRoi getRoiByCoordinatesUUID(String coordinatesUUID) {
        return coordinatesRoiService.getRoiByCoordinatesUUID(coordinatesUUID);
    }

}
