package com.desaysv.workserver.controller.client;

import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.TestProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-1 11:02
 * @description : 测试项目web接口
 * @modified By :
 * @since : 2022-4-1
 */
@Slf4j
@RestController
@RequestMapping("/project")
@Lazy
public class TestProjectController {
    @Autowired
    private TestProjectService testProjectService;

    @GetMapping("/publicProject")
    public ResultEntity<TestProject> getPublicProject() {
        return ResultEntity.ok(testProjectService.getPublicProject());
    }

    @GetMapping("/all")
    public ResultEntity<List<TestProject>> getAllProjects() {
        return ResultEntity.ok(testProjectService.getAllProjects());
    }


    @PostMapping("/register")
    public ResultEntity<TestProject> registerProject(@RequestBody TestProject testProject) {
        TestProject dbTestProject = testProjectService.getProjectByName(testProject.getName());
        if (dbTestProject == null) {
            testProjectService.addProject(testProject);
            dbTestProject = testProjectService.getProjectById(testProject.getId());
            return ResultEntity.ok(dbTestProject);
        }
        return ResultEntity.fail(testProject.getName() + "项目已经存在", dbTestProject);
    }

    @PostMapping("/unregister")
    public ResultEntity<TestProject> unregisterProject(String projectName) {
        TestProject testProject = testProjectService.getProjectByName(projectName);
        if (testProject != null) {
            testProjectService.deleteProject(testProject);
            return ResultEntity.ok(String.format("项目%s注销成功", projectName), testProject);
        } else {
            return ResultEntity.fail(String.format("注销失败，原因：项目%s未注册过", projectName), null);
        }
    }

}
