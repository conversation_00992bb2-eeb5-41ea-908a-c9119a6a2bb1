package com.desaysv.workserver.controller.device;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.manager.DeviceManager;
import com.desaysv.workserver.response.ResultEntity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-17 16:17
 * @description : 设备管理web接口
 * @modified By :
 * @since : 2022-3-17
 */
//TODO:考虑简化deviceManagement为device，其他同理
@Slf4j
@RestController
@RequestMapping("device")
@Lazy
@Tag(name = "Sample API", description = "Sample API description")
public class DeviceMgmtController {

    @Autowired
    private DeviceManager deviceManager;

    @Operation(summary = "Get a user by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Found the user"),
            @ApiResponse(responseCode = "400", description = "Invalid id supplied"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/allDevices")
    public ResultEntity<List<Device>> getAllDevices() {
        List<Device> allDevices = deviceManager.getAllDevices();
        log.info("allDevices:{}", allDevices);
        return ResultEntity.ok(allDevices);
    }

    @GetMapping("/search/{deviceName}")
    public ResultEntity<Device> getDevice(@PathVariable String deviceName) {
        Device device = deviceManager.getDevice(deviceName);
        return device != null ? ResultEntity.ok(String.format("设备%s查询失败", deviceName), device) :
                ResultEntity.fail(String.format("设备%s查询失败", deviceName), null);
    }

    @GetMapping({"/allPhysicalCameras/{deviceModel}", "/allPhysicalCameras"})
    public ResultEntity<List<Device>> getAllPhysicalCameras(@PathVariable(value = "deviceModel", required = false) String deviceModel) {
        List<Device> physicalCameras = deviceManager.getAllPhysicalCameras(deviceModel);
        log.info("physicalCameras:{}", physicalCameras);
        return ResultEntity.ok(physicalCameras);
    }

    /**
     * 获取QNX
     *
     * @return 设备地址
     */
    @GetMapping("/allQnxDevices")
    public List<String> getAllQnxDevices() {
        return deviceManager.getAllQnxDevices();
    }

    @GetMapping({"/allAndroids/{deviceModel}", "/allAndroids"})
    public ResultEntity<List<Device>> getAllAndroidPorts(@PathVariable(value = "deviceModel", required = false) String deviceModel) {
        List<Device> androidPorts = deviceManager.getAllAndroids(deviceModel);
        log.info("androidPorts:{}", androidPorts);
        return ResultEntity.ok(androidPorts);
    }

    @GetMapping({"/allVisaPorts/{deviceModel}"})
    public ResultEntity<List<Device>> getAllVisaPorts(@PathVariable(value = "deviceModel") String deviceModel) {
        List<Device> physicalVisaPorts = deviceManager.getAllVisaInstrumentPorts(deviceModel);
        log.info("physicalVisaPorts:{}", physicalVisaPorts);
        return ResultEntity.ok(physicalVisaPorts);
    }

    private ResultEntity<List<Device>> handleAllSerialPorts(String deviceModel) {
        List<Device> physicalSerialPorts;
        try {
            physicalSerialPorts = deviceManager.getAllPhysicalSerialPorts(deviceModel);
        } catch (IllegalAccessException e) {
            physicalSerialPorts = new ArrayList<>();
            log.error(e.getMessage(), e);
            return ResultEntity.fail(e.getMessage(), physicalSerialPorts);
        }
        log.info("allSerialPorts:{}", physicalSerialPorts);
        return ResultEntity.ok(physicalSerialPorts);
    }


    @GetMapping({"/allSerialPorts/{deviceModel}"})
    public ResultEntity<List<Device>> getAllSerialPorts(@PathVariable("deviceModel") String deviceModel) {
        return handleAllSerialPorts(deviceModel);
    }

    @GetMapping("allIpRobots/{deviceModel}")
    public ResultEntity<List<Device>> getAllIpRobots(@PathVariable("deviceModel") String deviceModel) {
        List<Device> ipRobots = deviceManager.getAllIpRobots(deviceModel);
        log.info("allIpRobots:{}", ipRobots);
        return ResultEntity.ok(ipRobots);
    }

    @GetMapping("allAutoClicker/{deviceModel}")
    public ResultEntity<List<Device>> getAllAutoClicker(@PathVariable("deviceModel") String deviceModel) {
        List<Device> autoClikcer = deviceManager.getAllAutoClicker(deviceModel);
        log.info("allAutoClicker:{}", autoClikcer);
        return ResultEntity.ok(autoClikcer);
    }

    @GetMapping("allDaq/{deviceModel}")
    public ResultEntity<List<Device>> getAllDaq(@PathVariable("deviceModel") String deviceModel) {
        List<Device> daq = deviceManager.getAllDaq(deviceModel);
        log.info("allDaq:{}", daq);
        return ResultEntity.ok(daq);
    }

    @GetMapping("allUsbI2C/{deviceModel}")
    public ResultEntity<List<Device>> getAllUsbI2C(@PathVariable("deviceModel") String deviceModel) {
        List<Device> usbI2C = deviceManager.getAllUsbI2C(deviceModel);
        log.info("allUsbI2C:{}", usbI2C);
        return ResultEntity.ok(usbI2C);
    }

    @GetMapping("allRemoteResistance/{deviceModel}")
    public ResultEntity<List<Device>> getAllRemoteResistance(@PathVariable("deviceModel") String deviceModel) {
        List<Device> remoteResistance = deviceManager.getAllRemoteResistance(deviceModel);
        log.info("remoteResistance:{}", remoteResistance);
        return ResultEntity.ok(remoteResistance);
    }

    @GetMapping("allTcpServer/{deviceModel}")
    public ResultEntity<List<Device>> getAllTcpServer(@PathVariable("deviceModel") String deviceModel) {
        List<Device> tcpServer = deviceManager.getAllTcpServer(deviceModel);
        log.info("tcpServer:{}", tcpServer);
        return ResultEntity.ok(tcpServer);
    }


    @GetMapping("allSoundDevices/{deviceModel}")
    public ResultEntity<List<Device>> getAllSoundDevices(@PathVariable("deviceModel") String deviceModel) {
        List<Device> audioInput = deviceManager.getAllSoundDevices(deviceModel);
        log.info("getAllSoundDevices:{}", audioInput);
        return ResultEntity.ok(audioInput);
    }

    @GetMapping("allUsb4704Devices/{deviceModel}")
    public ResultEntity<List<Device>> getAllUSB4704Devices(@PathVariable("deviceModel") String deviceModel) {
        List<Device> usb4704Devices = deviceManager.getAllUSB4704Devices(deviceModel);
        log.info("getAllUSB4704Devices:{}", usb4704Devices);
        return ResultEntity.ok(usb4704Devices);
    }

    @GetMapping("allSpeakerDevices/{deviceModel}")
    public ResultEntity<List<Device>> getAllSpeakerDevices(@PathVariable("deviceModel") String deviceModel) {
        List<Device> audioOutput = deviceManager.getAllSpeakerDevices(deviceModel);
        log.info("getAllSpeakerDevices:{}", audioOutput);
        return ResultEntity.ok(audioOutput);
    }

    @GetMapping("allVideoCaptureDevices/{deviceModel}")
    public ResultEntity<List<Device>> getAllVideoCaptureDevices(@PathVariable("deviceModel") String deviceModel) {
        List<Device> videoCapture = deviceManager.getAllVideoCaptureDevices(deviceModel);
        log.info("getAllVideoCaptureDevices:{}", videoCapture);
        return ResultEntity.ok(videoCapture);
    }

//    @GetMapping("allCanPorts/{deviceModel}/{deviceChannel}")
//    public ResultEntity<List<Device>> getAllCanPorts(@PathVariable("deviceModel") String deviceModel,
//                                                     @PathVariable("deviceChannel") Integer deviceChannel) {
//        List<Device> canPorts = deviceManager.getAllCanPorts(deviceModel, deviceChannel);
//        log.info("allCanPorts:{}", canPorts);
//        return ResultEntity.success(canPorts);
//    }
}
