package com.desaysv.workserver.controller.screen;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Android投屏控制器2 - 简化版
 * 只提供stream接口，当Android设备被关闭时自动停止视频流
 */
@Slf4j
@RestController
@RequestMapping("/android/screencap2")
@Lazy
public class AndroidScreencapController2 {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    // 用于跟踪活跃的视频流，以便在设备关闭时自动停止
    private final ConcurrentHashMap<String, AtomicBoolean> activeStreams = new ConcurrentHashMap<>();

    /**
     * 启动Android设备视频流（默认参数）
     * 
     * @param deviceName Android设备名称/序列号
     * @return 流式视频响应
     */
    @GetMapping("/stream/{deviceName}")
    public ResponseEntity<StreamingResponseBody> startVideoStream(@PathVariable String deviceName) {
        return startVideoStreamWithParams(deviceName, 0, 0, 0);
    }

    /**
     * 启动Android设备视频流（带参数）
     * 
     * @param deviceName Android设备名称/序列号
     * @param width 视频宽度（0表示使用默认值）
     * @param height 视频高度（0表示使用默认值）
     * @param bitRate 视频比特率（0表示使用默认值）
     * @return 流式视频响应
     */
    @GetMapping("/stream/{deviceName}/params")
    public ResponseEntity<StreamingResponseBody> startVideoStreamWithParams(
            @PathVariable String deviceName,
            @RequestParam(defaultValue = "0") int width,
            @RequestParam(defaultValue = "0") int height,
            @RequestParam(defaultValue = "0") int bitRate) {
        
        log.info("请求Android设备视频流: 设备={}, 分辨率={}x{}, 比特率={}", 
                deviceName, width, height, bitRate);

        try {
            // 获取Android设备实例
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                log.error("未找到Android设备: {}", deviceName);
                return ResponseEntity.notFound().build();
            }

            // 启动视频流
            InputStream videoStream = androidDevice.startVideoStream(width, height, bitRate);
            if (videoStream == null) {
                log.error("无法启动Android设备视频流: {}", deviceName);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            // 标记为活跃流
            AtomicBoolean streamActive = new AtomicBoolean(true);
            activeStreams.put(deviceName, streamActive);

            // 创建流式响应体
            StreamingResponseBody streamingResponseBody = outputStream -> {
                try (InputStream inputStream = videoStream) {
                    byte[] buffer = new byte[8192]; // 8KB缓冲区
                    int bytesRead;
                    
                    log.info("开始传输Android设备视频流: {}", deviceName);
                    
                    while ((bytesRead = inputStream.read(buffer)) != -1 && streamActive.get()) {
                        // 检查设备是否仍然可用
                        if (!isDeviceAvailable(deviceName)) {
                            log.info("检测到Android设备已关闭，自动停止视频流: {}", deviceName);
                            streamActive.set(false);
                            break;
                        }
                        
                        outputStream.write(buffer, 0, bytesRead);
                        outputStream.flush();
                    }
                    
                    log.info("Android设备视频流传输完成: {}", deviceName);
                } catch (IOException e) {
                    log.error("Android设备视频流传输异常: 设备={}, 错误={}", deviceName, e.getMessage());
                    streamActive.set(false);
                    throw e;
                } finally {
                    // 清理资源
                    cleanupStream(deviceName, androidDevice);
                }
            };

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.set("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.set("Pragma", "no-cache");
            headers.set("Expires", "0");
            headers.set("Connection", "keep-alive");
            headers.set("X-Device-Name", deviceName);
            headers.set("X-Video-Resolution", formatResolution(width, height));
            headers.set("X-Video-BitRate", formatBitRate(bitRate));
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(streamingResponseBody);

        } catch (IOException e) {
            log.error("启动Android设备视频流失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        } catch (Exception e) {
            log.error("处理Android设备视频流请求时发生未知错误: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 检查设备是否仍然可用
     * 
     * @param deviceName 设备名称
     * @return 如果设备可用返回true，否则返回false
     */
    private boolean isDeviceAvailable(String deviceName) {
        try {
            AndroidDevice androidDevice = getAndroidDevice(deviceName);
            if (androidDevice == null) {
                return false;
            }
            
            // 检查设备是否仍然打开/连接
            // 这里可以根据AndroidDevice的具体实现来判断设备状态
            // 例如检查设备是否仍在设备管理器中注册，或者ping设备等
            return deviceRegisterManager.isRegistered(deviceName);
            
        } catch (Exception e) {
            log.warn("检查设备可用性时出错: 设备={}, 错误={}", deviceName, e.getMessage());
            return false;
        }
    }

    /**
     * 清理视频流资源
     * 
     * @param deviceName Android设备名称
     * @param androidDevice Android设备实例
     */
    private void cleanupStream(String deviceName, AndroidDevice androidDevice) {
        try {
            // 移除活跃流标记
            activeStreams.remove(deviceName);
            
            // 停止视频流
            if (androidDevice != null) {
                androidDevice.stopVideoStream();
                log.info("已停止Android设备视频流: {}", deviceName);
            }
        } catch (Exception e) {
            log.warn("清理视频流资源时出错: 设备={}, 错误={}", deviceName, e.getMessage());
        }
    }

    /**
     * 获取Android设备实例
     * 
     * @param deviceName 设备名称
     * @return Android设备实例，如果不存在或不是Android设备则返回null
     */
    private AndroidDevice getAndroidDevice(String deviceName) {
        try {
            Object device = deviceRegisterManager.getDevice(deviceName);
            if (device instanceof AndroidDevice) {
                return (AndroidDevice) device;
            } else {
                log.warn("设备 {} 不是Android设备类型: {}", deviceName, 
                        device != null ? device.getClass().getSimpleName() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取Android设备失败: 设备={}, 错误={}", deviceName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 格式化分辨率字符串
     * 
     * @param width 宽度
     * @param height 高度
     * @return 格式化的分辨率字符串
     */
    private String formatResolution(int width, int height) {
        if (width <= 0 || height <= 0) {
            return "默认";
        }
        return width + "x" + height;
    }

    /**
     * 格式化比特率字符串
     * 
     * @param bitRate 比特率
     * @return 格式化的比特率字符串
     */
    private String formatBitRate(int bitRate) {
        if (bitRate <= 0) {
            return "默认";
        }
        if (bitRate >= 1000000) {
            return (bitRate / 1000000) + "Mbps";
        } else if (bitRate >= 1000) {
            return (bitRate / 1000) + "Kbps";
        } else {
            return bitRate + "bps";
        }
    }

    /**
     * 获取当前活跃的视频流数量
     * 
     * @return 活跃视频流数量
     */
    public int getActiveStreamCount() {
        return activeStreams.size();
    }

    /**
     * 检查指定设备是否有活跃的视频流
     * 
     * @param deviceName 设备名称
     * @return 如果有活跃流返回true
     */
    public boolean hasActiveStream(String deviceName) {
        AtomicBoolean streamActive = activeStreams.get(deviceName);
        return streamActive != null && streamActive.get();
    }

    /**
     * 手动停止指定设备的视频流
     * 这个方法主要用于内部调用或紧急情况
     * 
     * @param deviceName 设备名称
     */
    public void forceStopStream(String deviceName) {
        AtomicBoolean streamActive = activeStreams.get(deviceName);
        if (streamActive != null) {
            streamActive.set(false);
            log.info("手动停止设备视频流: {}", deviceName);
        }
    }

    /**
     * 停止所有活跃的视频流
     * 这个方法主要用于应用关闭时的清理
     */
    public void stopAllStreams() {
        log.info("停止所有活跃的Android设备视频流，当前数量: {}", activeStreams.size());
        for (String deviceName : activeStreams.keySet()) {
            forceStopStream(deviceName);
        }
        activeStreams.clear();
    }
}
