package com.desaysv.workserver.controller.client;

import com.desaysv.workserver.AITestXWebApplication;
import com.desaysv.workserver.base.manager.*;
import com.desaysv.workserver.model.TestClient;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.TestClientService;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-2 17:56
 * @description :
 * @modified By :
 * @since : 2022-8-2
 */
@Slf4j
@RestController
@RequestMapping("/client")
@Lazy
public class TestClientController implements ClientStatusListener {

    @Autowired
    private TestClientService testClientService;

    @Autowired
    private ApplicationContext applicationContext;
    @Override
    public void clientRegister(TestClient testClient) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.clientRegister(testClient);
        }
    }

    public void userRegister(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.userRegister(clientPackage);
        }
    }

    public void userLogin(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.userLogin(clientPackage);
        }
    }

    @Override
    public void userLogout(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.userLogout(clientPackage);
        }
    }

    @Override
    public void clientExit(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.clientExit(clientPackage);
        }
    }

    @Override
    public void configurationUserEmails(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.configurationUserEmails(clientPackage);
        }
    }

    @Override
    public void configurationUserScriptEmails(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.configurationUserScriptEmails(clientPackage);
        }
    }

    @Override
    public void configurationUserRobotUrls(ClientPackage clientPackage) {
        for (ClientStatusListener listener : ClientStatusManager.getClientStatusListeners()) {
            listener.configurationUserRobotUrls(clientPackage);
        }
    }

    @PostMapping("/register")
    public ResultEntity<TestClient> register(@RequestBody TestClient client) {
        log.info("客户端{}注册", client.getName());
        ClientInfoReceiveFromClient clientInfoReceiveFromClient = new ClientInfoReceiveFromClient();
        clientInfoReceiveFromClient.setTestUnit(client.getPcName());
        clientInfoReceiveFromClient.setWorkstation(client.getAddr());
        userRegister(clientInfoReceiveFromClient);
        TestClient dbClient = testClientService.getClientByName(client.getName());
        if (dbClient == null) {
            testClientService.addClient(client);
            dbClient = testClientService.getClientById(client.getId());
        }
        dbClient.setVersion(AITestXWebApplication.getServerVersion());
        dbClient.setPid(client.getPid());
        clientRegister(dbClient);
        return ResultEntity.ok(dbClient);
    }

    @PostMapping("/login")
    public ResultEntity<String> login(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
//        记录登录时间
        log.info("用户{}登录{}", clientInfoReceiveFromClient.getUserName(), clientInfoReceiveFromClient.getClient());
        userLogin(clientInfoReceiveFromClient);
        return ResultEntity.ok(String.format("用户%s登录成功", clientInfoReceiveFromClient.getUserName()));
    }

    @PostMapping("/logout")
    public ResultEntity<String> logout(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        log.info("用户{}登出{}", clientInfoReceiveFromClient.getUserName(), clientInfoReceiveFromClient.getClient());
        userLogout(clientInfoReceiveFromClient);
        return ResultEntity.ok(String.format("用户%s登出成功", clientInfoReceiveFromClient.getUserName()));
    }

    @PostMapping("/exit")
    public ResultEntity<String> exit(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        log.info("收到客户端{}关闭指令", clientInfoReceiveFromClient.getClient());
        clientExit(clientInfoReceiveFromClient);
        // 创建一个新线程来关闭 Spring Boot 应用
        //判断是否为dev的profile
        if (!SpringContextHolder.isDevEnvironment()) {
            new Thread(() -> {
                try {
                    // 给一些时间让响应返回到客户端
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                // 退出 Spring Boot 应用
                // 退出状态码为0，表示正常退出
                System.exit(0);
            }).start();
        }
        return ResultEntity.ok(String.format("客户端%s退出成功", clientInfoReceiveFromClient.getClient()));
    }


    @PostMapping("/configurationEmails")
    public ResultEntity<String> configurationEmails(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        log.info("用户{}修改测试状态多邮件接收人为{}", clientInfoReceiveFromClient.getUserName(), clientInfoReceiveFromClient.getEmails());
        configurationUserEmails(clientInfoReceiveFromClient);
        return ResultEntity.ok(String.format("用户%s修改测试状态多邮件接收人成功", clientInfoReceiveFromClient.getUserName()));
    }

    @PostMapping("/configurationScriptEmails")
    public ResultEntity<String> configurationScriptEmails(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        log.info("用户{}修改脚本测试模式下测试状态多邮件接收人为{}", clientInfoReceiveFromClient.getUserName(), clientInfoReceiveFromClient.getScriptEmails());
        configurationUserScriptEmails(clientInfoReceiveFromClient);
        return ResultEntity.ok(String.format("用户%s修改脚本测试模式下测试状态多邮件接收人成功", clientInfoReceiveFromClient.getUserName()));
    }

    @PostMapping("/configurationRobotUrls")
    public ResultEntity<String> configurationRobotUrls(@RequestBody ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        log.info("用户{}修改测试状态robot多Url地址为{}", clientInfoReceiveFromClient.getUserName(), clientInfoReceiveFromClient.getUrls());
        configurationUserRobotUrls(clientInfoReceiveFromClient);
        return ResultEntity.ok(String.format("用户%s修改测试状态robot多Url成功", clientInfoReceiveFromClient.getUserName()));
    }

}
