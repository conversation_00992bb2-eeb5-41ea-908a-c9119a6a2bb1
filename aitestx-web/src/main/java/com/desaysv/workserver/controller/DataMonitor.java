package com.desaysv.workserver.controller;

import com.desaysv.workserver.MonitorType;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.I2C.I2CDevice;
import com.desaysv.workserver.devices.robot.base.DobotDeviceWithTouchPoint;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.monitor.data.MonitorAction;
import com.desaysv.workserver.screen.ScreenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据监控Controller
 */
@RestController
@RequestMapping("/monitor")
@Lazy
public class DataMonitor {
    @Autowired
    private ScreenService screenService;

    @Autowired
    private DeviceFinderManager deviceFinderManager;

    @PostMapping
    public OperationResult registerMonitor(@RequestBody MonitorAction monitorAction) {
        String deviceAliasName = monitorAction.getDeviceAliasName();
        String monitorType = monitorAction.getMonitorType();
        OperationResult operationResult = new OperationResult();
        switch (monitorType) {
            case MonitorType.TOUCH_POINT_DATA: {
                //监控报点数据
                Device device = deviceFinderManager.findDeviceByAliasName(deviceAliasName);
                if (device != null) {
                    SerialPortDevice serialDevice = (SerialPortDevice) device;
                    serialDevice.startKeepAliveTouchPointMonitor(screenService, monitorAction);
                    operationResult.ok("报点长连接成功");
                } else {
                    operationResult.fail("设备不存在");
                }
            }
            case MonitorType.LOG_DATA: {
                //监控log
                Device device = deviceFinderManager.findDeviceByAliasName(deviceAliasName);
                if (device != null) {
                    if (deviceAliasName.contains(DeviceModel.UsbI2C.USB_I2C)) {
                        I2CDevice i2CDevice = (I2CDevice) device;
                        //i2CDevice.monitorByteLog(monitorAction);
                    } else {
                        SerialPortDevice serialDevice = (SerialPortDevice) device;
                        serialDevice.monitorByteLog(monitorAction);
                    }
                    operationResult.ok("监控日志成功");
                } else {
                    operationResult.fail("设备不存在");

                }
                break;
            }
            case MonitorType.TOUCH_REPORT: {
                Device device = deviceFinderManager.findDeviceByAliasName(deviceAliasName);
                if (device != null) {
                    DobotDeviceWithTouchPoint dobotDeviceWithTouchPoint = (DobotDeviceWithTouchPoint) device;
                    dobotDeviceWithTouchPoint.monitorTouchReport(monitorAction);
                    operationResult.ok("监控报点成功");
                } else {
                    operationResult.fail("设备不存在");
                }
            }
        }
        return operationResult;
    }

}
