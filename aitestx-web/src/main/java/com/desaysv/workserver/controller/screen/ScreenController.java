package com.desaysv.workserver.controller.screen;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.screen.ScreenService;
import com.desaysv.workserver.screen.config.ScreenConfig;
import com.desaysv.workserver.screen.entity.TouchParsePackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * 屏幕控制
 */
@RestController
@RequestMapping("/screen")
@Lazy
public class ScreenController {

    @Autowired
    private ScreenService screenService;

    @PostMapping("/loadConfig")
    public ResultEntity<ScreenConfig> loadScreenConfig(@RequestSingleParam String projectName) {
        return ResultEntity.ok(screenService.loadConfig(projectName));
    }

    @PostMapping("/parseTouchPoint")
    public OperationResult parseTouchPoint(@RequestBody TouchParsePackage touchParsePackage) {
        return screenService.parseTouchPoint(touchParsePackage);
    }

    @PutMapping("/updateConfig")
    public ResultEntity<String> updateScreenConfig(@RequestBody JSONObject jsonConfig) {
        ScreenConfig screenConfig = jsonConfig.to(ScreenConfig.class);
        screenService.updateConfig(screenConfig);
        return ResultEntity.ok();
    }

}

