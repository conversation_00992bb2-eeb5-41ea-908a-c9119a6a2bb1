package com.desaysv.workserver.controller.cicd;

import com.desaysv.workserver.cicd.AutoUpgradeService;
import com.desaysv.workserver.cicd.UpgradeResponse;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/cicdTest")
@Lazy
public class CicdTestController {
    @Autowired
    private final AutoUpgradeService autoUpgradeService;

    public CicdTestController(AutoUpgradeService autoUpgradeService) {
        this.autoUpgradeService = autoUpgradeService;
    }

    /**
     * 自动升级接口
     * @return 自动升级结果
     */
    @GetMapping("/autoUpgrade")
    public ResultEntity<UpgradeResponse> autoUpgrade() {
        return ResultEntity.ok(autoUpgradeService.autoUpgrade());
    }

    @GetMapping("/clientReportUpgradeResultToCloudDc")
    public ResultEntity<Boolean> clientReportUpgradeResultToCloudDc() {
        return ResultEntity.ok(autoUpgradeService.clientReportUpgradeResultToCloudDc());
    }

}
