package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.WebSocketServerListener;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.execution.EnhancedExecution;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.nodes.NodeExecutor;
import com.desaysv.workserver.base.nodes.base.LoopBreakNotification;
import com.desaysv.workserver.base.nodes.base.LoopNode;
import com.desaysv.workserver.base.nodes.base.NodeContext;
import com.desaysv.workserver.base.nodes.base.NodeVisitor;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.suite.ExecutionSuite;
import com.desaysv.workserver.test.ExecutionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.*;

@Component
public class DebugOperationProtocolInterpreter extends ProtocolInterpreter implements WebSocketServerListener, NodeVisitor, NodeExecutor {
    private Future<Map<Integer, ExecuteResult>> debugFuture;
    private ExecutorService debugExecutorService;
    private volatile boolean singleExecutionStopped;

    @Autowired
    public DebugOperationProtocolInterpreter(
            @Qualifier("debugThreadPoolManager") ThreadPoolManager threadPoolManager,
            @Qualifier("debugExecutorNotificator") ExecutorNotificator executorNotificator
    ) {
        super(threadPoolManager, executorNotificator);
    }

    /**
     * 停止调试
     */
    public void stopDebug() {
        singleExecutionStopped = true;
        if (debugFuture != null && !debugFuture.isCancelled()) {
            debugFuture.cancel(true);
        }
    }

    /**
     * 调试
     *
     * @param execution
     * @param executionContext
     * @return
     */
    public Map<Integer, ExecuteResult> debug(Execution execution, ExecutionContext executionContext) {
        // 如果当前正在执行，拒绝新的请求
        //获取测试通知ws
        singleExecutionStopped = false;
        getExecutorNotificator().addWebSocketServerListener(this);
        if (debugFuture == null || debugFuture.isCancelled()) {
            debugExecutorService = new ThreadPoolExecutor(
                    1, // 核心线程数
                    1, // 最大线程数
                    0L, // 非核心线程空闲回收时间
                    TimeUnit.MILLISECONDS,
                    new SynchronousQueue<>(), // 不存储任务的队列
                    Executors.defaultThreadFactory(),
                    new ThreadPoolExecutor.AbortPolicy() {
                        @Override
                        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                            log.warn("调试任务被拒绝，当前有任务正在执行");
                            throw new IllegalStateException("当前正在执行调试任务，请等待完成后再试");
                        }
                    });
        }
        debugFuture = debugExecutorService.submit(() -> interpret(executionContext, execution));
        try {
            return debugFuture.get();
        } catch (CancellationException ignored) {

        } catch (InterruptedException | ExecutionException e) {
            log.warn(e.getMessage(), e);
        }
        return new HashMap<>();
    }


    @Override
    public void onWebSocketError() {

    }

    @Override
    public void onWebSocketClose() {

    }

    @Override
    public Map<String, Map<Integer, ExecuteResult>> interpret(ExecutionSuite executionSuite) {
        throw new IllegalArgumentException("调试不支持当前模式");
    }

    @Override
    public Map<Integer, ExecuteResult> interpret(ExecutionContext executionContext, Execution execution) throws LoopBreakNotification {
        //测试前准备执行环境
        executionContext.setTestCycle(execution.getTestCycle());
        executionContext.setOperationContext(execution.getOperationContext());
        getExecutorNotificator().initMainNotificator(executionContext);
        if (execution.getOperationList().isEmpty()) {
            log.info("调试步骤为空");
            return new LinkedHashMap<>();
        }

        //分析脚本语法
        EnhancedExecution enhancedExecution = analyseGrammar(execution);

        //通知编译结果
        return execute(enhancedExecution, executionContext);
    }

    public Map<Integer, ExecuteResult> execute(EnhancedExecution enhancedExecution,
                                               ExecutionContext executionContext) throws LoopBreakNotification {
        log.info("开始调试");
        //创建节点
        NodeContext nodeContext = createNodeContext(enhancedExecution, executionContext);

        //执行测试脚本
        executeByNode(nodeContext.getNode(), nodeContext);

        //注意：当前不支持调试结果记录
        return new LinkedHashMap<>();
    }

    @Override
    public void executeByNode(LoopNode node) throws LoopBreakNotification {
        executeByNode(node, null);
    }

    @Override
    public void executeByNode(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification {
        node.accept(this, nodeContext);
    }

    @Override
    public boolean visit(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification {
        if (singleExecutionStopped) {
            //终止调试
            return false;
        }
        Operation operation = node.getOperation();
        if (operation == null) {
            return true;
        }
        return beginExecuteOperation(operation, nodeContext);
    }

    @Override
    public void visitCompleted(LoopNode node, NodeContext nodeContext) {

    }

    @Override
    protected void distributeOperationBegin(Operation operation) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~第{}行步骤调试开始", operation.getLineNo() + 1);
        String outputLog = String.format("调试步骤: %s", operation);
        log.info(outputLog);
    }

    @Override
    protected void distributeOperationEnd(Operation operation) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~第{}行步骤调试结束", operation.getLineNo() + 1);
    }

    @Override
    public boolean executeOperation(LoopNode node, NodeContext nodeContext, Operation operation, boolean enterRetryCallback) throws LoopBreakNotification {
        //检测是否为内置语法
        if (checkIfBuiltinGrammar(nodeContext, operation)) {
            return true;
        }

        if (!enterRetryCallback) {
            // 通知正在测试的行
            getExecutorNotificator().notifySingleTestStatus(operation);
        }

        OperationContext operationContext = nodeContext.getExecutionContext().getOperationContext();
        waitSeconds(operationContext.getBeforeWait());
//        int timeout = operationContext.getTimeout();
        OperationResult operationResult = interpret(nodeContext.getExecutionContext(), operation);
        if (operationResult.isFailed()) {
            getExecutorNotificator().notifyPausingToClient(operationResult);
        }
        return operationResult.isOk();
    }

    // 用于判断 是否执行从当前步骤开始
    public void setOperationList(OperationGroup operationGroup) {
        String groupName = operationGroup.getGroupName();
        if (ExecutionStatus.single_test.toString().equals(groupName)) {
            for (Operation operation : operationGroup.getOperationList()) {
                operation.setFriendlyOperationObject(groupName);
            }
        }
    }
}
