package com.desaysv.workserver.controller.config;

import com.desaysv.workserver.common.aspect.AppConfig;
import com.desaysv.workserver.response.ResultEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/config/mock")
@Slf4j
public class ConfigController {
    @Setter
    @Getter
    public static class MockEnabledRequest {
        private boolean mockEnabled;

    }

    @Autowired
    private AppConfig appConfig;

    // 获取当前的 mockEnabled 值
    @GetMapping("/mockEnabled")
    public ResultEntity<Boolean> getMockEnabled() {
        return ResultEntity.ok(appConfig.isMockEnabled());
    }

    // 更新 mockEnabled 的值
    @PutMapping("/mockEnabled")
    public ResultEntity<String> setMockEnabled(@RequestBody MockEnabledRequest requst) {
        appConfig.setMockEnabled(requst.isMockEnabled());
        log.info("mockEnabled 已更新为: {}", requst.isMockEnabled());
        return ResultEntity.ok("mockEnabled 已更新为: " + requst.isMockEnabled());
    }
}
