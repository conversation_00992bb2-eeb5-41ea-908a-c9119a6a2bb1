package com.desaysv.workserver.controller.device.robot;

import com.desaysv.workserver.model.FunctionalRobotCoordinates;
import com.desaysv.workserver.model.TestDevice;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.service.FunctionalRobotCoordinatesService;
import com.desaysv.workserver.service.TestDeviceModelService;
import com.desaysv.workserver.service.TestDeviceService;
import com.desaysv.workserver.service.TestProjectService;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能机械臂坐标
 */
@Component
@Lazy
public class FunctionalRobotCoordinatesApplication {

    @Autowired
    private FunctionalRobotCoordinatesService functionalRobotCoordinatesService;

    @Autowired
    private TestProjectService testProjectService;

    @Autowired
    private TestDeviceModelService testDeviceModelService;

    @Autowired
    private TestDeviceService testDeviceService;

    private FunctionalRobotCoordinates fromFunctionalRobotCoordinatesVo(FunctionalRobotCoordinatesVo functionalRobotCoordinatesVo) {
        FunctionalRobotCoordinates functionalRobotCoordinates = new FunctionalRobotCoordinates();
        if (functionalRobotCoordinatesVo.getUuid() == null) {
            String uuid = StrUtils.getSaltMD5(functionalRobotCoordinatesVo.toString(), StrUtils.generateUUID());
            functionalRobotCoordinatesVo.setUuid(uuid);
        }
        functionalRobotCoordinates.setUuid(functionalRobotCoordinatesVo.getUuid());
        functionalRobotCoordinates.setId(functionalRobotCoordinatesVo.getId());
        functionalRobotCoordinates.setName(functionalRobotCoordinatesVo.getName());
        functionalRobotCoordinates.setAlias(functionalRobotCoordinatesVo.getAlias());
        functionalRobotCoordinates.setFunction(functionalRobotCoordinatesVo.getFunction());
        functionalRobotCoordinates.setEnable(functionalRobotCoordinatesVo.isEnable());
        functionalRobotCoordinates.setX(functionalRobotCoordinatesVo.getX());
        functionalRobotCoordinates.setY(functionalRobotCoordinatesVo.getY());
        functionalRobotCoordinates.setZ(functionalRobotCoordinatesVo.getZ());
        functionalRobotCoordinates.setR(functionalRobotCoordinatesVo.getR());
        functionalRobotCoordinates.setSlideRail(functionalRobotCoordinatesVo.getSlideRail());
        if (functionalRobotCoordinatesVo.getProjectId() != null) {
            functionalRobotCoordinates.setProjectId(functionalRobotCoordinatesVo.getProjectId());
        } else {
            TestProject testProject = testProjectService.getProjectByName(functionalRobotCoordinatesVo.getProjectName());
            functionalRobotCoordinates.setProjectId(testProject.getId());
        }
        if (functionalRobotCoordinatesVo.getDeviceId() != null) {
            functionalRobotCoordinates.setDeviceId(functionalRobotCoordinatesVo.getDeviceId());
        } else {
            TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(functionalRobotCoordinatesVo.getDeviceUniqueCode());
            functionalRobotCoordinates.setDeviceId(testDevice.getId());
        }
        return functionalRobotCoordinates;
    }

    public FunctionalRobotCoordinatesVo fromFunctionalRobotCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates) {
        FunctionalRobotCoordinatesVo vo = new FunctionalRobotCoordinatesVo();
        vo.setId(functionalRobotCoordinates.getId());
        vo.setUuid(functionalRobotCoordinates.getUuid());
        vo.setName(functionalRobotCoordinates.getName());
        vo.setAlias(functionalRobotCoordinates.getAlias());
        vo.setFunction(functionalRobotCoordinates.getFunction());
        vo.setEnable(functionalRobotCoordinates.isEnable());
        vo.setX(functionalRobotCoordinates.getX());
        vo.setY(functionalRobotCoordinates.getY());
        vo.setZ(functionalRobotCoordinates.getZ());
        vo.setR(functionalRobotCoordinates.getR());
        vo.setSlideRail(functionalRobotCoordinates.getSlideRail());
        TestProject testProject = testProjectService.getProjectById(functionalRobotCoordinates.getProjectId());
        vo.setProjectName(testProject.getName());
        vo.setProjectId(testProject.getId());
        TestDevice testDevice = testDeviceService.getDevice(functionalRobotCoordinates.getDeviceId());
        vo.setDeviceUniqueCode(testDevice.getUniqueCode());
        vo.setDeviceId(testDevice.getId());
        return vo;
    }

    public FunctionalRobotCoordinatesVo addFunctionalCoordinates(FunctionalRobotCoordinatesVo functionalRobotCoordinatesVo) {
        FunctionalRobotCoordinates functionalRobotCoordinates = fromFunctionalRobotCoordinatesVo(functionalRobotCoordinatesVo);
        functionalRobotCoordinatesService.addFunctionalCoordinates(functionalRobotCoordinates);
        FunctionalRobotCoordinates dbFunctionalRobotCoordinates =
                functionalRobotCoordinatesService.getFunctionalCoordinatesByUUID(functionalRobotCoordinates.getUuid());
        functionalRobotCoordinatesVo.setProjectId(dbFunctionalRobotCoordinates.getProjectId());
        functionalRobotCoordinatesVo.setDeviceId(dbFunctionalRobotCoordinates.getDeviceId());
        return functionalRobotCoordinatesVo;
    }

    public FunctionalRobotCoordinatesVo updateFunctionalCoordinates(FunctionalRobotCoordinatesVo functionalRobotCoordinatesVo) {
        FunctionalRobotCoordinates functionalRobotCoordinates = fromFunctionalRobotCoordinatesVo(functionalRobotCoordinatesVo);
        functionalRobotCoordinatesService.updateFunctionalCoordinates(functionalRobotCoordinates);
        return functionalRobotCoordinatesVo;
    }

    public List<FunctionalRobotCoordinatesVo> getFunctionalCoordinates(FunctionalRobotCoordinatesQueryVo functionalRobotCoordinatesVo) {
        TestProject testProject = testProjectService.getProjectByName(functionalRobotCoordinatesVo.getProjectName());
        functionalRobotCoordinatesVo.setProjectId(testProject.getId());
        TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(functionalRobotCoordinatesVo.getDeviceUniqueCode());
        functionalRobotCoordinatesVo.setDeviceId(testDevice.getId());
        List<FunctionalRobotCoordinates> coordinatesList = functionalRobotCoordinatesService.getFunctionalCoordinates(functionalRobotCoordinatesVo);
        List<FunctionalRobotCoordinatesVo> vos = new ArrayList<>();
        for (FunctionalRobotCoordinates coordinates : coordinatesList) {
            vos.add(fromFunctionalRobotCoordinates(coordinates));
        }
        return vos;
    }

    public FunctionalRobotCoordinatesVo getLastFunctionalCoordinates(FunctionalRobotCoordinatesQueryVo functionalRobotCoordinatesVo) {
        List<FunctionalRobotCoordinatesVo> vos = getFunctionalCoordinates(functionalRobotCoordinatesVo);
        return vos.size() > 0 ? vos.get(0) : null;
    }

}
