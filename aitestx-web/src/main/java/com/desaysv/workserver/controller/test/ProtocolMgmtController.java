package com.desaysv.workserver.controller.test;

import com.desaysv.workserver.annotation.ExecutionSuiteJsonProtocol;
import com.desaysv.workserver.annotation.OperationGroupJsonProtocol;
import com.desaysv.workserver.annotation.OperationJsonProtocol;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.execution.ExecutionUtils;
import com.desaysv.workserver.base.operation.base.CallResult;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.DeviceOperationMethod;
import com.desaysv.workserver.base.suite.ExecutionSuite;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.controller.test.protocol.service.DebugOperationProtocolInterpreter;
import com.desaysv.workserver.controller.test.protocol.service.ExecutionProtocolInterpreter;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.utils.DateUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 13:27
 * @description : 协议执行web接口
 * @modified By : lwj
 * @since : 2022-3-30
 */
@Slf4j
@RestController
//FIXME：简化urls
@RequestMapping("/test")
@Lazy
@DependsOn("executionProtocolInterpreterConfig")
public class ProtocolMgmtController {

    /**
     * TODO: 如何让不同客户端拥有不同的ClientInfo
     */
//    private static final ThreadLocal<ClientInfo> sharedClientInfo = new ThreadLocal<>();

    @Autowired
    private ExecutionProtocolInterpreter executionProtocolInterpreter;

    @Autowired
    private DebugOperationProtocolInterpreter debugProtocolInterpreter;

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;


    private <T extends CallResult> ResultEntity<T> checkResult(T result) {
        if (result.isOk()) {
            return ResultEntity.ok(result);
        } else {
            return ResultEntity.fail(result);
        }
    }

    private OperationResult checkDeviceName(String deviceName) {
        OperationResult operationResult = new OperationResult();
        if (!deviceRegisterManager.isRegistered(deviceName)) {
            String message = String.format("%s设备未注册", deviceName);
            log.warn(message);
            return operationResult.fail(message);
        }
        return operationResult.ok();
    }

    @PostMapping("/execute/start")
    public ResultEntity<Map<String, Map<Integer, ExecuteResult>>> execute(@ExecutionSuiteJsonProtocol ExecutionSuite executionSuite) {
        log.info("客户端执行:{}", executionSuite);
        executionSuite.getClientInfo().setStartTime(DateUtils.getNow());
        Map<String, Map<Integer, ExecuteResult>> executeResult = executionProtocolInterpreter.interpret(executionSuite);
        return ExecutionUtils.checkResults(executeResult);
    }

    @PostMapping("/execute/debug")
    public ResultEntity<Map<Integer, ExecuteResult>> debug(@OperationGroupJsonProtocol OperationGroup operationGroup) {
        try {
            log.info("客户端调试:{}", operationGroup);
            ExecutionContext executionContext = new ExecutionContext();
            executionContext.setDebugModeEnabled(true);
            executionContext.setProjectName(operationGroup.getProjectName());
            debugProtocolInterpreter.setOperationList(operationGroup);
            Execution execution = new Execution();
            OperationContext operationContext = new OperationContext();
            execution.setTestCycle(1);
            execution.setOperationContext(operationContext);
            execution.setOperationList(operationGroup.getOperationList());
            Map<Integer, ExecuteResult> executeResult = debugProtocolInterpreter.debug(execution, executionContext);
            return ResultEntity.ok(executeResult);
        } catch (IllegalStateException e) {
            // 捕获任务正在执行的异常
            log.warn("调试任务执行中: {}", e.getMessage());
            return ResultEntity.fail(new LinkedHashMap<>());
        }
    }

    @DeleteMapping("/execute/stopDebug")
    public ResultEntity<String> stopDebug() {
        log.info("客户端停止调试");
        debugProtocolInterpreter.stopDebug();
        return ResultEntity.ok();
    }

    @PostMapping("/execute/next")
    public ResultEntity<String> resumeSingleExecution() {
        executionProtocolInterpreter.resumeSingleExecution();
        return ResultEntity.ok("成功执行");
    }

    @PostMapping("/execute/pause")
    public ResultEntity<String> pauseExecution() {
        log.info("暂停测试执行");
        executionProtocolInterpreter.pauseInterpret();
        return ResultEntity.ok();
    }

    @PostMapping("/execute/resume")
    public ResultEntity<String> resumeExecute() {
        log.info("恢复测试执行");
        executionProtocolInterpreter.resumeInterpret();
        return ResultEntity.ok();
    }

    @PostMapping("/execute/stop")
    public ResultEntity<String> stopExecute() {
        log.info("停止测试执行");
        executionProtocolInterpreter.stopInterpret();
        return ResultEntity.ok();
    }

    @PostMapping("/operate")
    public OperationResult operate(@OperationJsonProtocol Operation operation, @RequestHeader Map<String, Object> headers) {
        if (!operation.getOperationMethod().getKeyword().equals(DeviceOperationMethod.fetchFeedbackData.getKeyword())) {
            log.info("执行操作步骤:{}", operation);
        }
//        log.info("operate:{}", Thread.currentThread());
        //TODO: 后续考虑改成ClientHeader类管理
//        operation.getOperationTarget().getOperationHeaderThreadLocal().set();
        ExecutionContext executionContext = new ExecutionContext();
        String projectName = (String) headers.get("project");
        if (projectName != null) {
            //TODO:研究Header怎么支持中文，不用decode
            try {
                projectName = URLDecoder.decode(projectName, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                log.warn(e.getMessage(), e);
            }
        }
        executionContext.setProjectName(projectName);
        return executionProtocolInterpreter.interpret(executionContext, operation);
    }

    //example: /AITestX/testManagement/execute/device/kfc/outputOn?v=10&&c=12
    @GetMapping("/execute/device/{operationTarget}/{operationMethod}")
    public ResultEntity<Map<String, Map<Integer, ExecuteResult>>> executeForDevice(@PathVariable("operationTarget") String operationTarget,
                                                                                   @PathVariable("operationMethod") String operationMethod,
                                                                                   @RequestParam Map<String, Object> operationParameter) {
        ExecutionSuite executionSuite = new ExecutionSuite();
        return execute(executionSuite);
    }


    @GetMapping("/operate/device")
    public OperationResult operateDevice(@RequestParam("deviceName") String deviceName,
                                         @RequestParam("methodName") String methodName) {
        return checkDeviceName(deviceName);
    }

    @GetMapping("/operate/device/autoOpen")
    public OperationResult autoOpenDevice(@RequestParam("deviceName") String deviceName) {
        OperationResult operationResult = checkDeviceName(deviceName);
        if (operationResult.isFailed()) {
            return operationResult;
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        operationResult = device.openForOperationResult(true);
        return operationResult;
    }

    @GetMapping("/operate/device/open")
    public OperationResult openDevice(@RequestParam("deviceName") String deviceName) {
        //TODO:open/close/send写到通用方法中
        OperationResult operationResult = checkDeviceName(deviceName);
        if (operationResult.isFailed()) {
            return operationResult;
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        operationResult = device.openForOperationResult(false);
        return operationResult;
    }

    @GetMapping("/operate/device/close")
    public OperationResult closeDevice(@RequestParam("deviceName") String deviceName) {
        log.info("接收关闭设备协议：{}", deviceName);
        OperationResult operationResult = checkDeviceName(deviceName);
        if (operationResult.isFailed()) {
            return operationResult;
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device != null) {
            try {
                operationResult = device.closeForOperationResult();
            } catch (DeviceCloseException e) {
                return OperationResult.staticFail(e.getMessage());
            }
        }
        return operationResult;
    }

    @PostMapping("/operate/device/send")
    public OperationResult sendToDevice(@RequestParam("deviceName") String deviceName,
                                        @RequestBody String message) {
        message = StrUtils.cleanRequestString(message);
        OperationResult operationResult = checkDeviceName(deviceName);
        if (operationResult.isFailed()) {
            return operationResult;
        }
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device instanceof PortDevice) {
            try {
                ((PortDevice<?>) device).send(message);
                return OperationResult.staticOk();
            } catch (DeviceSendException e) {
                return OperationResult.staticFail(e.getMessage());
            }
        }
        return operationResult;
    }

}
