package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.WebSocketServerListener;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.execution.ExecutionNotification;
import com.desaysv.workserver.base.execution.TestResultReport;
import com.desaysv.workserver.base.nodes.NodeType;
import com.desaysv.workserver.base.nodes.base.LoopNode;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationsReport;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.test.ExecutionMonitorWebSocketServer;
import com.desaysv.workserver.test.ExecutionPreview;
import com.desaysv.workserver.test.ExecutionStatus;
import lombok.Getter;

import java.util.Map;

/**
 * 执行器通知器（线程安全）
 */
//@Slf4j
public class ExecutorNotificator {
    private ExecutionMonitorWebSocketServer executionMonitorWebSocketServer;

    // 使用volatile确保多线程对executionPreview的可见性
    @Getter
    private volatile ExecutionPreview executionPreview;

    public ExecutorNotificator() {

    }

    /**
     * 初始化主通知器
     *
     * @param executionContext
     */
    public void initMainNotificator(ExecutionContext executionContext) {
        //初始化通知
        executionPreview = new ExecutionPreview();
        executionPreview.getNotification().setDebugModeEnabled(executionContext.isDebugModeEnabled());
        executionPreview.getNotification().setCaseName(executionContext.getOperationContext().getCaseName());
    }

    /**
     * 添加WebSocket监听器
     *
     * @param listener
     */
    public void addWebSocketServerListener(WebSocketServerListener listener) {
        executionMonitorWebSocketServer = (ExecutionMonitorWebSocketServer)
                ExecutionMonitorWebSocketServer.getExecutionMonitorWebSocketServer(listener);
    }


    // 修改状态更新方法，将消息放入队列
    private void sendPreview(ExecutionPreview preview) {
        if (executionMonitorWebSocketServer != null) {
//            log.info("sendPreview：{}", preview);
            executionMonitorWebSocketServer.sendExecutionPreview(preview);
        }
    }

    /**
     * 通知开始执行单个用例
     *
     * @param execution
     */
    public void notifySingleExecutionStart(Execution execution) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifySingleExecutionStart");
        executionPreview.setStatus(ExecutionStatus.single_started);
        executionPreview.getNotification().setExecutionIndex(execution.getExecutionIndex());

        sendPreview(executionPreview);
    }

    /**
     * 通知正在测试
     *
     * @param operation
     * @param node
     */
    public void notifyTestingStatus(Operation operation, LoopNode node) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifyTestingStatus");

        ExecutionNotification notification = executionPreview.getNotification();

        if (node.getNodeType() == NodeType.FUNCTION_NODE) {
            executionPreview.setNodeType(NodeType.FUNCTION_NODE);
        } else {
            executionPreview.setNodeType(NodeType.OPERATION_NODE);
        }

        executionPreview.setStatus(ExecutionStatus.testing);
        notification.setBackground(operation.isBackground());
        notification.setPosition(operation.getParentLineNo());
        notification.setOperation(operation);

        sendPreview(executionPreview);
    }

    /**
     * 通知暂停
     *
     * @param operationResult
     */
    public void notifyPausingToClient(OperationResult operationResult) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifyPausingToClient");

        executionPreview.getNotification().setOperationResult(operationResult);
        executionPreview.setStatus(ExecutionStatus.pausing);

        sendPreview(executionPreview);
    }

    /**
     * 通知报告
     *
     * @param row
     * @param operationsReport
     */
    public void notifyOperationsReport(int row, OperationsReport operationsReport) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifyOperationsReport");

        //FIXME:采用UUID：operationsReport的方式
        ExecutionPreview reportPreview = new ExecutionPreview();
        reportPreview.getNotification().setCaseName(executionPreview.getNotification().getCaseName());
        //FIXME:减少map的存储
        reportPreview.getNotification().getExecutionResultReport().getOperationsReportMap()
                .put(row, operationsReport);
        reportPreview.setStatus(ExecutionStatus.report);
        sendPreview(reportPreview);
    }

    /**
     * 通知正在测试的行
     */
    public void notifySingleTestStatus(Operation operation) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
        // 用于判断 是否是从当前步骤开始执行
        String friendlyOperationObject = operation.getFriendlyOperationObject() != null
                ? operation.getFriendlyOperationObject().toString()
                : "";

        if (ExecutionStatus.single_test.toString().equals(friendlyOperationObject)) {
            ExecutionNotification notification = executionPreview.getNotification();
            executionPreview.setStatus(ExecutionStatus.single_test);
            notification.setBackground(operation.isBackground());
            notification.setOperation(operation);
            notification.setPosition(operation.getLineNo() + 1);
            sendPreview(executionPreview);
        }
    }

    /**
     * 增加测试循环
     */
    public void increaseTestCycle() {
        executionPreview.getNotification().setCycle(executionPreview.getNotification().getCycle() + 1);
        executionPreview.getNotification().setTotalCycle(executionPreview.getNotification().getTotalCycle() + 1);
    }

    /**
     * 设置测试循环
     *
     * @param testCycle
     * @param totalCycle
     */
    public void addTestCycle(int testCycle, int totalCycle) {
        executionPreview.getNotification().setCycle(testCycle);
        executionPreview.getNotification().setTotalCycle(totalCycle);
    }

    /**
     * 设置测试报告
     *
     * @param testResultReport
     */
    public void setTestResultReport(TestResultReport testResultReport) {
        executionPreview.getNotification().getExecutionResultReport().setTestResultReport(testResultReport);
    }

    /**
     * 向客户端通知异常
     */
    public void notifyExceptionToClient(Exception e) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifyExceptionToClient");

        executionPreview.setStatus(ExecutionStatus.exception);
        executionPreview.getNotification().setExtraMessage(e.getMessage());

        sendPreview(executionPreview);
    }

    /**
     * 通知单个测试完成
     */
    public void notifySingleExecutionCompleted() {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifySingleExecutionCompleted");

        executionPreview.setStatus(ExecutionStatus.single_completed);

        sendPreview(executionPreview);
    }

    /**
     * 通知所有测试完成
     *
     * @param results
     */
    public void notifyAllTestCompleted(Map<String, Map<Integer, ExecuteResult>> results) {
        if (executionMonitorWebSocketServer == null) {
            return;
        }
//        log.info("notifyAllTestCompleted");

        executionPreview.setStatus(ExecutionStatus.all_completed);

        sendPreview(executionPreview);
    }
}
