package com.desaysv.workserver.controller.testcase.actions;

import com.desaysv.workserver.annotation.OperationGroupJsonProtocol;
import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.filemanager.project.OperationGroupFileManager;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 操作动作组合
 */
@Slf4j
@RestController
@RequestMapping("operationGroup")
public class OperationGroupController {

    @PostMapping("/add")
    public ResultEntity<Object> addOperationGroup(@OperationGroupJsonProtocol OperationGroup operationGroup) {
        boolean isOk = OperationGroupFileManager.of(operationGroup.getProjectName(), OperationGroupFileManager.class).
                addOperationGroup(operationGroup);
        log.info("生成步骤组合:{}", operationGroup.getGroupName());
        return isOk ? ResultEntity.ok() : ResultEntity.fail();
    }

    @PostMapping("/load/all")
    public ResultEntity<List<OperationGroup>> loadOperationGroups(@RequestSingleParam String projectName) {
        List<OperationGroup> operationGroups = OperationGroupFileManager.of(projectName, OperationGroupFileManager.class).
                loadOperationGroups();
        log.info("遍历步骤组合:{}", operationGroups);
        return ResultEntity.ok(operationGroups);
    }

    @PostMapping("/load")
    public ResultEntity<OperationGroup> loadOperationGroup(@RequestBody OperationGroup operationGroup) {
        OperationGroup op = OperationGroupFileManager.of(operationGroup.getProjectName(), OperationGroupFileManager.class).
                loadOperationGroup(operationGroup.getGroupName());
        if (op != null) {
            log.info("导入步骤组合:{}", op.getGroupName());
        }
        return ResultEntity.ok(op);
    }

}
