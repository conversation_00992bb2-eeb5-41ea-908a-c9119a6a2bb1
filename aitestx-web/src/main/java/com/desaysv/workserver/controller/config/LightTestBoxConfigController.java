package com.desaysv.workserver.controller.config;

import com.desaysv.workserver.devices.testbox.light.ExcelParser;
import com.desaysv.workserver.devices.testbox.light.LightTestBoxConfig;
import com.desaysv.workserver.devices.testbox.light.TestBoxDeviceConfig;
import com.desaysv.workserver.filemanager.project.LightTestBoxFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;

/**
 * FDX配置文件管理
 */
@Slf4j
@RestController
@RequestMapping("/lightTestBoxConfig")
@Lazy
public class LightTestBoxConfigController {
    @Deprecated
    @PostMapping("/readJsonConfigFile")
    public ResultEntity<LightTestBoxConfig> readConfigFile() {
        //此接口为了前端记忆测试箱配置文件路径的功能
        LightTestBoxFileManager lightTestBoxFileManager = ProjectFileManager.of(LightTestBoxFileManager.class);
        if (lightTestBoxFileManager.isConfigFileExist()) {
            LightTestBoxConfig.getInstance().initLightTestBoxConfig();
            return ResultEntity.ok(LightTestBoxConfig.getInstance());
        }
        return ResultEntity.fail();
    }

    @PostMapping("/loadConfigFile")
    public ResultEntity<String> loadFile(@RequestBody String filePath) {
        //此接口为了保存配置
        LightTestBoxConfig.getInstance().setLoadConfigExcelFile(new File(filePath));
        LightTestBoxConfig.getInstance().writeJsonConfigFile();
        return ResultEntity.ok();
    }

    @PostMapping("/parseConfig")
    public ResultEntity<Map<String, TestBoxDeviceConfig>> parseConfig(@RequestBody String filePath) {
        Map<String, TestBoxDeviceConfig> configs = null;
        try (FileInputStream fis = new FileInputStream(filePath)) {
             configs = ExcelParser.parse(fis, null);
             LightTestBoxConfig.getInstance().setLightTestBoxConfigMap(configs);
        } catch (IOException e) {
            log.error("解析失败", e);
            return ResultEntity.fail(e.getMessage());
        }
        return ResultEntity.ok(configs);
    }
}
