package com.desaysv.workserver.controller.testcase;

import com.desaysv.workserver.TestCaseService;
import com.desaysv.workserver.model.TestResult;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试用例相关接口
 */
@Slf4j
@RestController
@RequestMapping("/testcase")
@Lazy
public class TestCaseController {

    @Autowired
    private TestCaseService testCaseService;

    @GetMapping("/testresult/{testcaseUUID}")
    public ResultEntity<TestResult> getTestResult(@PathVariable("testcaseUUID") String testcaseUUID) {
        return ResultEntity.ok(testCaseService.findTestCaseResult(testcaseUUID));
    }

}
