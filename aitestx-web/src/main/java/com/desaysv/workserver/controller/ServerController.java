package com.desaysv.workserver.controller;

import ch.qos.logback.classic.LoggerContext;
import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.secure.SecureVerify;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务端Controller
 */
@Slf4j
@RestController
public class ServerController {

    @PostMapping("/verify/generate")
    public String status(@RequestSingleParam String uuid) {
        if (uuid != null && SecureVerify.verify(uuid, "客户端")) {
            return StrUtils.generate(uuid);
        }
        return "";
    }

    @GetMapping("/health/serverPid")
    public ResponseEntity<String> getPid() {
        // 返回pid响应
        return ResponseEntity.ok(CommandUtils.getAppPID());
    }

    @GetMapping("/logfile")
    public ResponseEntity<String> getLogFile() {
        //返回日志文件
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        return ResponseEntity.ok(context.getName()); // 获取contextName，即文件夹路径
    }

}
