package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.execution.ExecutionUtils;
import com.desaysv.workserver.base.execution.ProtocolInterpreter;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationType;
import com.desaysv.workserver.base.operation.invoker.OperationInvoker;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.operation.command.CommonOperationCommand;
import com.desaysv.workserver.operation.command.DeviceOperationCommand;
import com.desaysv.workserver.operation.command.ImageOperationCommand;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class BaseProtocolInterpreter implements ProtocolInterpreter {

    @Autowired
    private OperationInvoker operationInvoker;

    @Autowired
    private DeviceOperationCommand deviceOperationCommand;

    @Autowired
    private ImageOperationCommand imageOperationCommand;

    @Autowired
    private CommonOperationCommand commonOperationCommand;

    //FIXME:新的调用架构
//    @Autowired
//    private UnifiedOperationCommand operationCommand;

    @Override
    public OperationResult interpret(Operation operation) {
        return interpret(null, operation);
    }

    /**
     * 解析操作步骤
     *
     * @param executionContext 执行上下文
     * @param operation        操作步骤
     * @return
     */
    public OperationResult interpret(ExecutionContext executionContext, Operation operation) {
        Integer operationType = operation.getOperationType();
        if (operationType == null) {
            operationType = ExecutionUtils.checkOperationType(operation);
        }
        if (operationType.equals(OperationType.DEVICE)) {
            operationInvoker.setOperationCommand(deviceOperationCommand);
        } else if (operationType.equals(OperationType.IMAGE)) {
            operationInvoker.setOperationCommand(imageOperationCommand);
        } else if (operationType.equals(OperationType.COMMON)) {
            operationInvoker.setOperationCommand(commonOperationCommand);
        }
        return operationInvoker.invoke(executionContext, operation);
    }

    @Override
    public boolean pauseInterpret() {
        return false;
    }

    @Override
    public boolean stopInterpret() {
        return false;
    }

    @Override
    public boolean resumeInterpret() {
        return false;
    }
}
