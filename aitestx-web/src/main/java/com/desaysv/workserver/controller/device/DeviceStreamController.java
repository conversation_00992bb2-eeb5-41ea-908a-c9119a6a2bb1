package com.desaysv.workserver.controller.device;

import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.entity.Resolution;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.TestDeviceService;
import com.desaysv.workserver.stream.StreamService;
import com.desaysv.workserver.stream.rtsp.RtspStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 设备流控制器
 */
@Slf4j
@RestController
@RequestMapping("device/stream")
@Lazy
//TODO：测试Netty视频流传输效果
public class DeviceStreamController {
    @Autowired
    private StreamService streamService;

    @Autowired
    private TestDeviceService testDeviceService;

    @PostMapping("/stopGrab")
    public ResultEntity<RtspStream> stopGrabStream(@RequestBody GrabRequest grabRequest) {
        if (grabRequest.isSimulated()) {
            return ResultEntity.ok("设备模拟", null);
        }
        String deviceUUID = grabRequest.getDeviceUUID();
        if (streamService.isDistributed(deviceUUID)) {
            streamService.stopStream(deviceUUID);
            String message = String.format("视频流停止:%s", deviceUUID);
            log.info(message);
            return ResultEntity.ok(message, null);
        } else {
            String message = String.format("视频流不存在:%s", deviceUUID);
            log.warn(message);
            return ResultEntity.fail(message, null);
        }
    }

    @PostMapping("/grab")
    public ResultEntity<RtspStream> grabStream(@RequestBody GrabRequest grabRequest) {
        if (grabRequest.isSimulated()) {
            return ResultEntity.ok("设备模拟", null);
        }
//    public ResultEntity<RtspStream> grabStream(@PathVariable("devicePort") Integer devicePort) {
        String deviceUUID = grabRequest.getDeviceUUID();
        if (streamService.isDistributed(deviceUUID)) {
            //TODO:streamProducer正在获取视频流时候，wait阻塞
            String message = String.format("视频流已经创建:%s", deviceUUID);
            //FIXME：检查视频流是否有效，无效则自动修复并启动
            streamService.outputDynamicFrame(deviceUUID, true);
            log.info(message);
            return ResultEntity.ok(message, null);
        }

        try {
            String url = streamService.pushStream(grabRequest);
            String message = String.format("视频流创建成功:%s", deviceUUID);
            log.info(message);
            return ResultEntity.ok(message, streamService.getRtspStream(url));
        } catch (Exception e) {
            String message = String.format("视频流无法创建:%s,%s", deviceUUID, e.getMessage());
            log.warn(message);
            return ResultEntity.fail(message, null);
        }
    }

    @PostMapping("/dynamic")
    public ResultEntity<String> outputDynamicStream(@RequestSingleParam String deviceUUID) {
        streamService.outputDynamicFrame(deviceUUID, true);
        return ResultEntity.ok();
    }

    @PostMapping("/still")
    public ResultEntity<String> outputStillStream(@RequestSingleParam String deviceUUID) {
        streamService.outputDynamicFrame(deviceUUID, false);
        streamService.saveSnapshotFrameToCache(deviceUUID);
        return ResultEntity.ok();
    }

    @PostMapping("/getDefaultSize")
    public ResultEntity<Resolution> getDefaultSize(@RequestSingleParam String deviceUUID) {
        RectSize size = streamService.getDefaultSize(deviceUUID);
        if (size != null) {
            log.info("相机{}获取到默认分辨率:{}x{}", deviceUUID, size.getWidth(), size.getHeight());
            return ResultEntity.ok(String.format("相机%s获取默认分辨率成功", deviceUUID), new Resolution(size.getWidth(), size.getHeight()));
        }
        return ResultEntity.fail(String.format("相机%s无法获取默认分辨率", deviceUUID), null);
    }

    @PostMapping("/camera/sync")
    public ResultEntity<String> sync(@RequestSingleParam String deviceUUID) {
        streamService.sync(deviceUUID);
        return ResultEntity.ok();
    }


}
