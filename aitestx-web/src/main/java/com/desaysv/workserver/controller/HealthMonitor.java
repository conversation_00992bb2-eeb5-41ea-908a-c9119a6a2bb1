package com.desaysv.workserver.controller;

import com.desaysv.workserver.base.manager.ClientPackage;
import com.desaysv.workserver.base.manager.ClientStatusListener;
import com.desaysv.workserver.base.manager.ClientStatusManager;
import com.desaysv.workserver.model.TestClient;
import lombok.extern.slf4j.Slf4j;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 客户端健康检查器
 */
@Slf4j
public class HealthMonitor implements ClientStatusListener {
    private final static int MAX_RETRIES = 5;
    private Timer timer;
    private String clientPid;
    private volatile TimerTask monitorTask;

    public HealthMonitor() {
        ClientStatusManager.addClientStatusListener(this);
    }

    @Override
    public void clientExit(ClientPackage clientPackage) {
        stop();
    }

    @Override
    public void clientRegister(TestClient testClient) {
        clientPid = testClient.getPid();
        log.info("检测到客户端PID:{}", clientPid);
        timer = new Timer(true);
        start();
    }


    public void start() {
        if (clientPid == null) {
            return;
        }
        monitorTask = new TimerTask() {
            String message;
            int retryCount = 0;


            @Override
            public void run() {
                try {
                    boolean pidExists = checkProcessExists();
                    if (!pidExists) {
                        retryCount++;
                        log.warn("客户端PID {} 未找到，第 {} 次检测", clientPid, retryCount);
                        if (retryCount >= MAX_RETRIES) {
                            message = String.format("客户端PID %s 连续 %d 次未找到，确认已退出", clientPid, MAX_RETRIES);
                            log.warn(message);
                            // 取消当前任务
                            cancel();
                            timer.cancel();
                            timer.purge();
                            // 最后退出程序
                            System.exit(0);
                        }
                    } else {
                        retryCount = 0;
                    }
                } catch (Exception e) {
                    log.error("检测客户端PID错误: ", e);
                }
            }

            private boolean checkProcessExists() {
                try {
                    int pid = Integer.parseInt(clientPid);
                    Process proc = new ProcessBuilder("cmd.exe", "/c", "tasklist /fi \"PID eq " + pid + "\"").start();
                    try (java.io.BufferedReader reader = new java.io.BufferedReader(
                            new java.io.InputStreamReader(proc.getInputStream(), java.nio.charset.Charset.forName("GBK")))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.contains(String.valueOf(pid))) {
                                log.debug("通过tasklist找到进程 {}", pid);
                                return true;
                            }
                        }
                    }
                    log.debug("通过tasklist未找到进程 {}", pid);
                    return false;
                } catch (Exception e) {
                    log.warn("tasklist命令调用异常，不视为进程退出", e);
                    return true;
                }
            }
        };
        timer.schedule(monitorTask, 0, 30000);
    }

    /**
     * 停止定时器
     */
    public void stop() {
        if (monitorTask != null) {
            monitorTask.cancel();
        }
        if (timer != null) {
            timer.cancel();
            timer.purge();
        }
        log.info("pid检查已停止");
    }

    // 添加资源清理
    @Override
    protected void finalize() throws Throwable {
        stop();
        super.finalize();
    }

}