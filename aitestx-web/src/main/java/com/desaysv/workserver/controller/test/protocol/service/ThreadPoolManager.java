package com.desaysv.workserver.controller.test.protocol.service;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class ThreadPoolManager {
    private volatile PauseableThreadPool threadPool;
    private final ReentrantLock lock = new ReentrantLock();
    private final String poolName;

    // 线程池配置参数
    private final int corePoolSize;
    private final int maximumPoolSize;
    private final long keepAliveTime;
    private final TimeUnit unit;
    private final BlockingQueue<Runnable> workQueue;

    public ThreadPoolManager() {
        this("DefaultPool");
    }

    public ThreadPoolManager(String poolName) {
        this.poolName = poolName;
        // 默认配置
        this.corePoolSize = Runtime.getRuntime().availableProcessors();
        this.maximumPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        this.keepAliveTime = 60L;
        this.unit = TimeUnit.SECONDS;
        this.workQueue = new LinkedBlockingQueue<>(1000);

        // 初始化线程池
        initializeThreadPool();
    }

    private void initializeThreadPool() {
        threadPool = new PauseableThreadPool(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                unit,
                workQueue,
                new ThreadFactory() {
                    private final AtomicInteger threadNumber = new AtomicInteger(1);

                    @Override
                    public Thread newThread(Runnable r) {
                        Thread t = new Thread(r, poolName + "-Worker-" + threadNumber.getAndIncrement());
                        if (t.isDaemon()) {
                            t.setDaemon(false);
                        }
                        if (t.getPriority() != Thread.NORM_PRIORITY) {
                            t.setPriority(Thread.NORM_PRIORITY);
                        }
                        return t;
                    }
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        threadPool.setPoolName(poolName);
    }

    public <T> Future<T> submit(Callable<T> task) {
        lock.lock();
        try {
            if (threadPool.isShutdown()) {
                log.info("线程池{}被关闭了，重新初始化...", poolName);
                initializeThreadPool();
            }
            return threadPool.submit(task);
        } finally {
            lock.unlock();
        }
    }

    public void shutdown() {
        lock.lock();
        try {
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.stop();
            }
        } finally {
            lock.unlock();
        }
    }

    public void pause() {
        lock.lock();
        try {
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.pause();
            }
        } finally {
            lock.unlock();
        }
    }

    public void resume() {
        lock.lock();
        try {
            if (threadPool != null && !threadPool.isShutdown()) {
                threadPool.resume();
            }
        } finally {
            lock.unlock();
        }
    }

    public boolean isShutdown() {
        lock.lock();
        try {
            return threadPool == null || threadPool.isShutdown();
        } finally {
            lock.unlock();
        }
    }

    public boolean isPaused() {
        lock.lock();
        try {
            return threadPool != null && threadPool.isPaused();
        } finally {
            lock.unlock();
        }
    }

    @PreDestroy
    public void destroy() {
        shutdown();
    }

    public void pauseIfNecessary() {
        threadPool.pauseIfNecessary();
    }
}