package com.desaysv.workserver.controller.device.robot;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.robot.interfaces.IDobot;
import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.NamedRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RandomRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.SwipeRobotCoordinates;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.robot.RobotCoordinatesQueryException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.model.FunctionalRobotCoordinates;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.model.roi.CoordinatesRoi;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.vo.RobotCoordinatesVo;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesVo;
import com.desaysv.workserver.vo.robot.RobotCoordinatesQueryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 14:04
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
@Slf4j
@RestController
@RequestMapping("/robotCoordinates")
@Lazy
public class RobotCoordinatesController {

    @Autowired
    private RobotCoordinatesApplication robotCoordinatesApplication;

    @Autowired
    private FunctionalRobotCoordinatesApplication functionalRobotCoordinatesApplication;

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    @PostMapping("/add")
    public ResultEntity<RobotCoordinatesVo> addCoordinates(@RequestBody RobotCoordinatesVo robotCoordinatesVo) {
        RobotCoordinatesVo coordinates = robotCoordinatesApplication.addCoordinates(robotCoordinatesVo);
        if (coordinates != null) {
            return ResultEntity.ok(coordinates);
        } else {
            return ResultEntity.fail();
        }
    }

    @DeleteMapping("/delete")
    public ResultEntity<RobotCoordinatesVo> deleteCoordinates(@RequestBody RobotCoordinatesVo robotCoordinatesVo) {
        RobotCoordinatesVo coordinates = robotCoordinatesApplication.deleteCoordinates(robotCoordinatesVo);
        if (coordinates != null) {
            return ResultEntity.ok(coordinates);
        } else {
            return ResultEntity.fail();
        }
    }

    @DeleteMapping("/clear")
    public ResultEntity<String> clearCoordinates(@RequestBody RobotCoordinatesQueryVo robotCoordinatesQueryVo) {
        boolean isOk = robotCoordinatesApplication.clearAllRobotCoordinates(robotCoordinatesQueryVo);
        if (isOk) {
            return ResultEntity.ok();
        } else {
            return ResultEntity.fail();
        }
    }

    @PutMapping("/update")
    public ResultEntity<RobotCoordinatesVo> updateCoordinates(@RequestBody RobotCoordinatesVo robotCoordinatesVo) {
        RobotCoordinatesVo coordinates = robotCoordinatesApplication.updateCoordinates(robotCoordinatesVo);
        if (coordinates != null) {
            return ResultEntity.ok(coordinates);
        } else {
            return ResultEntity.fail();
        }
    }

    @PostMapping("/all")
    public ResultEntity<List<RobotCoordinatesVo>> getAllRobotCoordinates(@RequestBody RobotCoordinatesQueryVo robotCoordinatesQueryVo) {
        return ResultEntity.ok(robotCoordinatesApplication.getAllRobotCoordinates(robotCoordinatesQueryVo));
    }

    @PostMapping("/swipe")
    public ResultEntity<Object> swipeRobotCoordinates(@RequestBody SwipeRobotCoordinates swipeRobotCoordinates) {
        try {
            return ResultEntity.ok(robotCoordinatesApplication.swipeRobotCoordinates(swipeRobotCoordinates));
        } catch (RobotCoordinatesQueryException e) {
            return ResultEntity.fail(e.getMessage(), e);
        }
    }

    @PostMapping("/touchRandom")
    public ResultEntity<Object> randomRobotCoordinates(@RequestBody RandomRobotCoordinates randomRobotCoordinates) {
        try {
            return ResultEntity.ok(robotCoordinatesApplication.randomOrCenterRobotCoordinates(randomRobotCoordinates, "random"));
        } catch (RobotCoordinatesQueryException e) {
            return ResultEntity.fail(e.getMessage(), e);
        }
    }

    @PostMapping("/touchCenter")
    public ResultEntity<Object> centerRobotCoordinates(@RequestBody RandomRobotCoordinates randomRobotCoordinates) {
        try {
            return ResultEntity.ok(robotCoordinatesApplication.randomOrCenterRobotCoordinates(randomRobotCoordinates, "center"));
        } catch (RobotCoordinatesQueryException e) {
            return ResultEntity.fail(e.getMessage(), e);
        }
    }

    @PostMapping("/query")
    //TODO：推广直接返回类的接口
    public RobotCoordinates queryRobotCoordinates(@RequestBody NamedRobotCoordinates namedRobotCoordinates) throws OperationFailNotification {
        return robotCoordinatesApplication.queryNamedRobotCoordinates(namedRobotCoordinates);
    }

    @PostMapping("/addFunction")
    public ResultEntity<FunctionalRobotCoordinatesVo> addFunctionalRobotCoordinates(@RequestBody FunctionalRobotCoordinatesVo functionalRobotCoordinatesVo) {
        log.info("设置机械臂特殊功能坐标:{}", functionalRobotCoordinatesVo);
        return ResultEntity.ok(functionalRobotCoordinatesApplication.addFunctionalCoordinates(functionalRobotCoordinatesVo));
    }

    @PostMapping("/updateFunction")
    public ResultEntity<FunctionalRobotCoordinatesVo> updateFunctionalRobotCoordinates(@RequestBody FunctionalRobotCoordinatesVo functionalRobotCoordinatesVo) {
        log.info("更新机械臂特殊功能坐标:{}", functionalRobotCoordinatesVo);
        return ResultEntity.ok(functionalRobotCoordinatesApplication.updateFunctionalCoordinates(functionalRobotCoordinatesVo));
    }

    @PostMapping("/queryFunction")
    public ResultEntity<List<FunctionalRobotCoordinatesVo>> queryFunctionalRobotCoordinates(@RequestBody FunctionalRobotCoordinatesQueryVo coordinatesQueryVo) {
        log.info("查询机械臂特殊功能坐标:{}", coordinatesQueryVo);
        return ResultEntity.ok(functionalRobotCoordinatesApplication.getFunctionalCoordinates(coordinatesQueryVo));
    }

    @PostMapping("/addRoi")
    public ResultEntity<CoordinatesRoi> addRobotRoi(@RequestBody CoordinatesRoi coordinatesRoi) {
        return ResultEntity.ok(robotCoordinatesApplication.addCoordinatesRoi(coordinatesRoi));
    }

    @DeleteMapping("/deleteRoi/{coordinatesUUID}")
    public ResultEntity<String> deleteRobotRoi(@PathVariable("coordinatesUUID") String coordinatesUUID) {
        boolean deleted = robotCoordinatesApplication.deleteRoiByCoordinatesUUID(coordinatesUUID);
        if (deleted) {
            return ResultEntity.ok("坐标ROI已删除: " + coordinatesUUID);
        } else {
            return ResultEntity.fail("坐标ROI没找到: " + coordinatesUUID);
        }
    }

    @GetMapping("/roi/{coordinatesUUID}")
    public ResultEntity<CoordinatesRoi> getRobotRoi(@PathVariable("coordinatesUUID") String coordinatesUUID) {
        return ResultEntity.ok(robotCoordinatesApplication.getRoiByCoordinatesUUID(coordinatesUUID));
    }

    @PostMapping("/executeSafePoint")
    public ResultEntity<String> enableSafePoint(String deviceName, String projectName) {
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device != null) {
            FunctionalRobotCoordinatesQueryVo queryVo = new FunctionalRobotCoordinatesQueryVo();
            queryVo.setProjectName(projectName);
            queryVo.setFunction(FunctionalRobotCoordinates.safePointFunction);
            queryVo.setDeviceUniqueCode(device.getDeviceUniqueCode());
            FunctionalRobotCoordinatesVo coordinatesVo = functionalRobotCoordinatesApplication.getLastFunctionalCoordinates(queryVo);
            if (coordinatesVo != null) {
                MoveEntity moveEntity = new MoveEntity();
                moveEntity.setX(coordinatesVo.getX());
                moveEntity.setY(coordinatesVo.getY());
                moveEntity.setZ(coordinatesVo.getZ());
                moveEntity.setSlideRail(coordinatesVo.getSlideRail());
                ((IDobot) device).moveJoint(moveEntity);
                log.info("机械臂执行安全点:{}", moveEntity);
            }
            return ResultEntity.ok();
        }
        return ResultEntity.fail(deviceName + "设备无法找到");
    }


}
