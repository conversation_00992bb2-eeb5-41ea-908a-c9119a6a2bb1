package com.desaysv.workserver.controller.image;

import com.desaysv.workserver.bo.TemplateImageBo;
import com.desaysv.workserver.bo.TemplateImageQuery;
import com.desaysv.workserver.bo.TemplateRoiInfo;
import com.desaysv.workserver.model.TemplatePicture;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.TemplateImageService;
import com.desaysv.workserver.service.TestDeviceService;
import com.desaysv.workserver.stream.StreamService;
import com.desaysv.workserver.utils.ExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * 图片管理
 */
@Slf4j
@RestController
@RequestMapping("/image")
@Lazy
public class ImageMgmtController {
    @Autowired
    private TemplateImageService templateImageService;

    @Autowired
    private StreamService streamService;

    @Autowired
    private TestDeviceService testDeviceService;

    /**
     * 获取所有模板图片
     *
     * @return
     */
    @PostMapping("/allTemplates")
    public ResultEntity<List<TemplatePicture>> getAllTemplates() {
        return ResultEntity.ok(templateImageService.getAllTemplates());
    }

    /**
     * 保存模板图片
     *
     * @param templatePicture 模板图片
     * @return
     */
    @PostMapping("/saveTemplate")
    public ResultEntity<TemplateImageBo> saveTemplate(@RequestBody TemplateImageBo templatePicture) {
        log.info("接收到模板图片:{}", templatePicture);
        TemplateImageBo templateImageBo;
        try {
            templateImageBo = templateImageService.saveTemplate(templatePicture);
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
            return ResultEntity.fail(ExceptionUtils.getExceptionString(e), null);
        }
        return ResultEntity.ok(templateImageBo);
    }
    @PostMapping("/saveTemplateROI")
    public ResultEntity<TemplateRoiInfo> saveTemplateROI(@RequestBody TemplateRoiInfo templateRoiInfo) {
        log.info("接收到模板图片ROI:{}", templateRoiInfo);
        TemplateRoiInfo templateImageBo;
        try {
            templateImageBo = templateImageService.saveTemplateROI(templateRoiInfo);
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
            return ResultEntity.fail(ExceptionUtils.getExceptionString(e), null);
        }
        return ResultEntity.ok(templateImageBo);
    }

    /**
     * 查询模板图片
     *
     * @param templateImageQuery 模板图片查询参数
     * @return
     */
    @PostMapping("/queryTemplate")
    public ResultEntity<TemplatePicture> queryTemplate(@RequestBody TemplateImageQuery templateImageQuery) {
        log.info("查询模板图片:{}", templateImageQuery.getTemplateName());
        TemplatePicture templatePicture = templateImageService.getTemplate(
                templateImageQuery.getTemplateName(),
                templateImageQuery.getDeviceUniqueCode(),
                templateImageQuery.getProjectName());
        return ResultEntity.ok(templatePicture);
    }

}
