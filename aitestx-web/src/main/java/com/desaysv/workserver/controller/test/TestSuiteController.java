package com.desaysv.workserver.controller.test;

import com.desaysv.workserver.base.manager.testSuite.TestCaseExecuteInfo;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfo;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfoListener;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfoManager;
import com.desaysv.workserver.monitor.PolyTestUsageStatusUpdater;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

/**
 * 测试集合管理器
 */
@Slf4j
@RestController
@RequestMapping("/test")
@Lazy
public class TestSuiteController implements TestSuiteInfoListener {

    @Autowired
    private PolyTestUsageStatusUpdater polyTestUsageStatusUpdater;

    @Override
    public void startTestSuite(TestSuiteInfo testSuiteInfo) {
        for (TestSuiteInfoListener testSuiteInfoListener : TestSuiteInfoManager.getTestSuiteInfoListeners()) {
            testSuiteInfoListener.startTestSuite(testSuiteInfo);
        }
    }

    @Override
    public void finishSingleCaseTesting(TestCaseExecuteInfo testCaseExecuteInfo) {
        for (TestSuiteInfoListener testSuiteInfoListener : TestSuiteInfoManager.getTestSuiteInfoListeners()) {
            testSuiteInfoListener.finishSingleCaseTesting(testCaseExecuteInfo);
        }
    }

    @PostMapping("/uploadTestSuiteInfo")
    public ResultEntity<String> uploadTestSuiteInfo(@RequestBody TestSuiteInfo testSuiteInfo) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        log.info("接收客户端上传的测试集合信息:{}", testSuiteInfo.getInfoWithoutTestCases());
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        try {
            startTestSuite(testSuiteInfo);
            return ResultEntity.ok("上传测试集合信息成功");
        } catch (Exception e) {
            log.error("上传测试集合信息失败:{}", e.getMessage());
            return ResultEntity.fail("上传测试集合信息失败，将切换到离线模式");
        }
    }

    @PostMapping("/uploadTestCaseExecuteInfo")
    public ResultEntity<String> uploadTestCaseExecuteInfo(@RequestBody TestCaseExecuteInfo testCaseExecuteInfo) {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        log.info("接收客户端上传的用例执行信息:{}", testCaseExecuteInfo);
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        finishSingleCaseTesting(testCaseExecuteInfo);
        return ResultEntity.ok("上传用例执行信息成功");
    }

    @GetMapping("/uploadManuallyTestInfo")
    public ResultEntity<String> uploadManuallyTestInfo() {
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        log.info("接收客户端手动测试");
        log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        polyTestUsageStatusUpdater.startManuallyTest();
        return ResultEntity.ok("上传手动测试信息成功");
    }
}
