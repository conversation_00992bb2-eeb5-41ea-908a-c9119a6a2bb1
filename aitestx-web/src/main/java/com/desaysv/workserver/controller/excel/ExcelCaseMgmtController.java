package com.desaysv.workserver.controller.excel;

import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.aspect.LogExecutionTime;
import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.excel.*;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.service.ExcelDataHandleService;
import com.desaysv.workserver.service.TestProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.desaysv.workserver.config.ExportTypeConstants.EXPORT_ORIGINAL_FILE;

/**
 * Excel管理
 */
@Slf4j
@RestController
@RequestMapping("/excelcase")
@Lazy
public class ExcelCaseMgmtController {
    @Autowired
    private ExcelDataHandleService excelDataService;
    @Autowired
    private TestProjectService testProjectService;


    @PostMapping("/loadExcel")
    public ResultEntity<Map<String, ExcelSheetTable>> loadExcel(@RequestBody ExcelEntity excelEntity) {
        log.info("导入Excel表:{}（案例路径:{}）", excelEntity.getSelectedSheetNames(), excelEntity.getOriginalFilePath());
        //读取全部的Excel sheet
        Map<String, ExcelSheetTable> allSheetTestCaseMap = ExcelUtils.readExcel(excelEntity);
        //获取全部sheet的列头是否有下拉筛选，并设置给ExcelSheetTable的成员变量
        List<ExcelHeaderOptions> excelHeaderOptions = ExcelUtils.getFilterDropdownValues(excelEntity);
        for (ExcelHeaderOptions excelHeaderOption : excelHeaderOptions) {
            String sheetName = excelHeaderOption.getSheetName();
            if (allSheetTestCaseMap.containsKey(sheetName)) {
                ExcelSheetTable table = allSheetTestCaseMap.get(sheetName);
                table.setTableHeaderDropDownOptions(excelHeaderOption.getHeadersOptions());
            }
        }
        //从excelEntity获取选择的sheet
        List<String> selectedSheetNames = excelEntity.getSelectedSheetNames();
        //从全部的sheet中过滤出选择的sheet，并使用 LinkedHashMap保留selectedSheetNames中的顺序，用于传到前端渲染Table
        Map<String, ExcelSheetTable> filteredSelectedSheetMap = new LinkedHashMap<>();
        for (String sheetName : selectedSheetNames) {
            if (allSheetTestCaseMap.containsKey(sheetName)) {
                filteredSelectedSheetMap.put(sheetName, allSheetTestCaseMap.get(sheetName));
            }
        }
        //如果allSheetTestCaseMap有数据，则把全部sheet内容存到数据库
        if (!allSheetTestCaseMap.isEmpty()) {
            Map<String, Object> dataMap = new HashMap<>();
            TestProject testProject = testProjectService.getProjectByName(excelEntity.getProjectName());
            dataMap.put("projectId", testProject.getId());
            dataMap.put("excelFilePath", excelEntity.getOriginalFilePath());
            dataMap.put("excelEntity", excelEntity);
            dataMap.put("excelSheetData", allSheetTestCaseMap);
            excelDataService.insertData(dataMap);
        }
        log.info("导入Excel工作表完成:{}", filteredSelectedSheetMap.keySet());
        //回传给前端的是选中sheet的内容：filteredSelectedSheetMap
        return ResultEntity.ok(filteredSelectedSheetMap);
    }

    @PostMapping("/loadDBTestCase")
    public ResultEntity<Map<String, ExcelSheetTable>> loadDBTestCase(@RequestBody ExcelEntity excelEntity) {
        Map<String, ExcelSheetTable> allSheetTestCaseMap = excelDataService.getDBTestCaseInfo(excelEntity);
        return ResultEntity.ok(allSheetTestCaseMap);
    }

    @PostMapping("/syncColumnConstantsTemplate")
    public ResultEntity<String> syncColumnConstantsTemplate(@RequestBody ColumnNameConstants columnNameConstants) {
        if (columnNameConstants != null) {
            log.info("设置Excel列名:{}", columnNameConstants);
            ColumnNameConstants.getInstance().setColumnNames(columnNameConstants);
        }
        return ResultEntity.ok();
    }


    @PostMapping("/sheetNames")
    public ResultEntity<List<String>> getSheetNames(@RequestSingleParam String excelPath) {
        List<String> sheetNames = ExcelUtils.getSheetNames(excelPath);
        log.info("获取Excel表格名称：{}（案例:{}）", sheetNames, excelPath);
        return ResultEntity.ok(sheetNames);
    }

    @PostMapping("/exportExcelCase")
    public ResultEntity<String> exportCaseReport(@RequestBody ExcelEntity excelEntity) {
        long st = System.currentTimeMillis();
        log.info("导出Excel数据:{}", excelEntity);
        String exportFilePath = excelEntity.getExportType().equals(EXPORT_ORIGINAL_FILE.name()) ?
                excelEntity.getOriginalFilePath() : excelEntity.getExportFilePath();
        Map<String, List<ExcelDataEntity>> sheetDataInfoMap = new LinkedHashMap<>();
//        String excelPath = excelEntity.isReloadProject() ? excelEntity.getTemplateFilePath() : excelEntity.getOriginalFilePath();
        for (String sheetName : excelEntity.getSelectedSheetNames()) {
            List<ExcelDataEntity> excelData = excelDataService.findExcelDataByName(sheetName);
            sheetDataInfoMap.put(sheetName, excelData);
        }
        log.info("EXCEL导出->从数据库导出全部Excel数据，耗时:{}s", (System.currentTimeMillis() - st) / 1000.0);
        st = System.currentTimeMillis();
        ResultEntity<String> resultEntity = ExcelBuilder.getInstance().exportExcelCaseFile(excelEntity.getTemplateFilePath(), exportFilePath, sheetDataInfoMap, excelEntity.getHeaderRowNumber());
        log.info("EXCEL导出->保存Excel文件，耗时:{}s", (System.currentTimeMillis() - st) / 1000.0);
        return resultEntity;
    }

    /**
     * 排序后重新写入数据库
     *
     * @param excelRowDataModels
     * @return
     */
    @PostMapping("/updateOrderExcelCase")
    @LogExecutionTime
    public ResultEntity<String> updateOrderExcelCase(@RequestBody List<ExcelRowDataModel> excelRowDataModels) {
        log.info("更新Excel数据行顺序:{}", excelRowDataModels);
        if (excelRowDataModels.isEmpty()) return ResultEntity.fail();
        try {
            excelDataService.updateOrderExcelCase(excelRowDataModels);
        } catch (Exception e) {
            log.error("操作数据库异常");
            return ResultEntity.fail(e.getMessage());
        }
        return ResultEntity.ok();
    }

    /**
     * 更新单行数据写入数据库
     *
     * @param excelRowDataModel
     * @return
     */
    //FIXME：单步调试也会调用
    @PostMapping("/updateExcelCase")
    public ResultEntity<String> updateExcelCase(@RequestBody ExcelRowDataModel excelRowDataModel) {
        log.info("更新Excel数据行:{}", excelRowDataModel);
        excelDataService.updateExcelData(excelRowDataModel);
        return ResultEntity.ok();
    }

    /**
     * 清空测试人员时间等数据，并更新数据库
     *
     * @param columnDataMap
     * @return
     */
    @PostMapping("/clearMapColumnData")
    @LogExecutionTime
    public ResultEntity<String> clearMapColumnData(@RequestBody Map<String, Object> columnDataMap) {
        log.info("清空Excel列数据:{}", columnDataMap);
        //找到sheetId，删除对应的column值
        excelDataService.clearMapColumnData(columnDataMap);
        return ResultEntity.ok();
    }

    @PostMapping("/deleteExcelCaseTable")
    @LogExecutionTime
    public ResultEntity<String> deleteExcelCaseTable(@RequestBody Map<String, String> dataMap) {
        log.info("删除Excel数据:{}", dataMap);
        excelDataService.deleteExcelDataByName(dataMap.get("sheetName"));
        return ResultEntity.ok();
    }

}
