package com.desaysv.workserver.controller.action_sequence;

import ch.qos.logback.core.FileAppender;
import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.clouddoc.CloudDocumentsService;
import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.context.ActionSequenceContext;
import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import com.desaysv.workserver.context.CanStopAllSequenceHandler;
import com.desaysv.workserver.context.SoundSequenceHandler;
import com.desaysv.workserver.controller.log.LoggerUtil;
import com.desaysv.workserver.converter.BaseSequenceConverter;
import com.desaysv.workserver.entity.ActionSequenceExecutionContextInfo;
import com.desaysv.workserver.entity.DeviceContextInfo;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceExecutionException;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceStopException;
import com.desaysv.workserver.executor.ActionSequenceExecutorContext;
import com.desaysv.workserver.executor.ActionSequenceExecutorService;
import com.desaysv.workserver.executor.ActionSequencesTestSseStatus;
import com.desaysv.workserver.filemanager.project.ActionSequenceTestConfig;
import com.desaysv.workserver.filemanager.project.ActionSequencesFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.mail.MailService;
import com.desaysv.workserver.mail.TestResultReportDto;
import com.desaysv.workserver.model.ExcelCaseModel;
import com.desaysv.workserver.monitor.PolyTestUploadTestSuite;
import com.desaysv.workserver.parser.ActionSequenceParserService;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.result.ActionSequenceCheckReporter;
import com.desaysv.workserver.result.ActionSequenceCheckService;
import com.desaysv.workserver.robot.RobotService;
import com.desaysv.workserver.service.ExcelCaseService;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * 动作序列控制层
 */
@Slf4j
@RestController
@RequestMapping("/actionSequence")
@Lazy
public class ActionSequenceController {
    @Autowired
    private ActionSequenceCheckService actionSequenceCheckService;
    @Autowired
    private ActionSequenceParserService actionSequenceParserService;
    @Autowired
    private ActionSequenceExecutorService actionSequenceExecutorService;
    @Autowired
    private TestProcessManager testProcessManager;
    @Autowired
    private PolyTestUploadTestSuite uploadStartTestSuite;
    @Autowired
    private ExcelCaseService excelCaseService;
    @Autowired
    private BaseSequenceConverter baseSequenceConverter;
    @Autowired
    private ActionSequenceExecutionContextInfo actionSequenceExecutionContextInfo; //当前运行行数据
    @Autowired
    private MailService mailService;
    @Autowired
    private RobotService robotService;
    @Autowired
    private CloudDocumentsService cloudDocumentsService;
    @Autowired
    private SoundSequenceHandler soundSequenceHandler;
    @Autowired
    private CanStopAllSequenceHandler canStopAllSequenceHandler;

    /**
     * 更新动作序列的测试配置信息
     *
     * @param actionSequenceTestConfig 动作序列测试配置对象
     * @return ResultEntity 包含更新结果的响应
     */
    @PostMapping("/updateTestConfig")
    public ResultEntity<String> updateTestConfig(@RequestBody ActionSequenceTestConfig actionSequenceTestConfig) {
        if (actionSequenceTestConfig.getProjectName() == null) {
            return ResultEntity.fail("项目名不能为空");
        }
        ActionSequencesFileManager fileManager = ProjectFileManager.of(actionSequenceTestConfig.getProjectName(), ActionSequencesFileManager.class);
        fileManager.writeActionSequencesTestConfig(actionSequenceTestConfig);
        return ResultEntity.ok();
    }

    /**
     * 根据项目名称读取动作序列测试配置
     *
     * @param projectName 项目名称
     * @return ResultEntity<ActionSequenceTestConfig> 包含测试配置信息的响应
     */
    @GetMapping("/readTestConfig/{projectName}")
    public ResultEntity<ActionSequenceTestConfig> readTestConfig(@PathVariable String projectName) {
        ActionSequencesFileManager fileManager = ProjectFileManager.of(projectName, ActionSequencesFileManager.class);
        return ResultEntity.ok(fileManager.readActionSequencesTestConfig());
    }


    /**
     * 检查动作序列语法是否正确
     *
     * @param actionSequenceContext 动作序列上下文对象,包含需要检查的动作序列
     * @return ResultEntity<ActionSequenceCheckReporter> 包含检查结果的响应
     */
    @PostMapping("/check")
    public ResultEntity<ActionSequenceCheckReporter> check(@RequestBody ActionSequenceContext actionSequenceContext) {
        actionSequenceContext.removeAnnotation();
        actionSequenceContext.removeAndTrimAfterSlashSlash();
        soundSequenceHandler.addSoundStepsForChecks(actionSequenceContext);
        canStopAllSequenceHandler.addStopAllChecks(actionSequenceContext);
        log.info("检查动作序列语法:\n{}", actionSequenceContext);
        actionSequenceExecutorService.startExecute();
        ActionSequenceCheckReporter actionSequenceCheckReporter = actionSequenceCheckService.checkActionSequenceGrammar(actionSequenceContext);
        log.info("actionSequenceCheckReporter:{}", actionSequenceCheckReporter);
        return ResultEntity.ok(actionSequenceCheckReporter);
    }

    /**
     * 初始化动作序列执行环境
     * 该方法在执行整个测试表格前只调用一次
     *
     * @param sequenceStatusDTO 序列状态数据传输对象
     * @return ResultEntity<String> 初始化结果
     */
    @PostMapping("/prepareExecute")
    public ResultEntity<String> startExecute(@RequestBody ActionSequenceStatusDTO sequenceStatusDTO) {
        actionSequenceExecutorService.startExecute();
        if (!sequenceStatusDTO.isSimulated()) {
            testProcessManager.testSuiteStart(new ExecutionContext());   //Add by lhy
        }
        ActionSequencesLoggerUtil.info("^^^^^^^^开始执行动作序列^^^^^^^^");
        return ResultEntity.ok();
    }

    /**
     * 检查并执行动作序列
     * 处理每一行的测试用例执行
     *
     * @param actionSequenceContext 动作序列上下文对象
     * @return ResultEntity<ActionSequenceCheckReporter> 包含执行结果的响应
     */
    @PostMapping("/checkAndExecute")
    public ResultEntity<Object> checkAndExecute(@RequestBody ActionSequenceContext actionSequenceContext) {
        //清空设备消息数据
        DeviceContextInfo.getInstance().getMessageDataMap().clear();
        String tcId = actionSequenceContext.getTcId();
        String tableName = actionSequenceContext.getTableName();
        String userLogPath = actionSequenceContext.getUserLogPath();
        String reportTimeFileDirPath = actionSequenceContext.getSmokingTestConfigModel().getReportTimeFileDirPath();
        actionSequenceExecutionContextInfo.setTcId(tcId);
        actionSequenceExecutionContextInfo.setTableName(tableName);
        actionSequenceExecutionContextInfo.setUserLogPath(userLogPath);
        actionSequenceExecutionContextInfo.setReportTimeFileDirPath(reportTimeFileDirPath);
        FileAppender fileAppender = null;
        if (!StringUtils.isEmpty(userLogPath)) {
//        if (userLogPath != null && !userLogPath.isEmpty()) {
            String logDir = Paths.get(userLogPath, tableName).toString();
            fileAppender = LoggerUtil.configureLogger("com.desaysv", tcId, logDir);
        }

        log.info("~~~请求表格行测试开始: {}~~~", actionSequenceContext.getRow() + 1);

        try {
            // 3. 清理上下文中的注释和无效内容
            actionSequenceContext.removeAnnotation();
            actionSequenceContext.removeAndTrimAfterSlashSlash();
            soundSequenceHandler.addSoundStepsForChecks(actionSequenceContext);
            canStopAllSequenceHandler.addStopAllChecks(actionSequenceContext);

            // 4. 如果不是模拟执行，并且存在测试套件ID，则触发测试用例开始事件
            if (!actionSequenceContext.isSimulated()) {
                if (uploadStartTestSuite.getTestSuiteId() != 0) {
                    ExecutionContext executionContext = new ExecutionContext();
                    executionContext.setTestSuiteId(uploadStartTestSuite.getTestSuiteId());
                    testProcessManager.testcaseStart(executionContext);
                }
            }

            // 5. 执行动作序列处理
            ResultEntity<Object> resultEntity = actionSequenceExecutorService.processActionSequenceContext(actionSequenceContext, actionSequenceCheckService);

            // 6. 更新Excel测试用例状态（如果有）
            if (actionSequenceContext.getExcelCaseModel() != null) {
                excelCaseService.updateExcelCase(actionSequenceContext.getExcelCaseModel());
            }

            return resultEntity;
        } finally {
            log.info("~~~请求表格行测试结束: {}~~~", actionSequenceContext.getRow() + 1);
            if (fileAppender != null) {
                LoggerUtil.cleanupLogger("com.desaysv", fileAppender);
            }
        }
    }

    private void updateCurrentExecutionInfo(ActionSequenceContext actionSequenceContext) {
        String tcId = actionSequenceContext.getTcId();
        String tableName = actionSequenceContext.getTableName();
        String userLogPath = actionSequenceContext.getUserLogPath();
        actionSequenceExecutionContextInfo.setTcId(tcId);
        actionSequenceExecutionContextInfo.setTableName(tableName);
        actionSequenceExecutionContextInfo.setUserLogPath(userLogPath);
    }

    /**
     * 标记当前执行的动作序列测试失败
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/failExecute")
    public ResultEntity<String> failExecute() {
        //TODO： 改成后端调用
        log.info("接收到前端动作序列失败测试请求");
        actionSequenceExecutorService.pauseExecute();
        testProcessManager.testFailed(new ExecutionContext(), 1);
        return ResultEntity.ok();
    }

    /**
     * 暂停执行动作序列测试
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/pauseExecute")
    public ResultEntity<String> pauseExecute() {
        log.info("接收到前端动作序列暂停测试请求");
        actionSequenceExecutorService.pauseExecute();
        testProcessManager.testPausing();
        return ResultEntity.ok();
    }

    /**
     * 恢复执行已暂停的动作序列测试
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/resumeExecute")
    public ResultEntity<String> resumeExecute() {
        log.info("接收到前端动作序列恢复测试请求");
        actionSequenceExecutorService.resumeExecute();
        testProcessManager.testResume();
        return ResultEntity.ok();
    }

    /**
     * 强制停止动作序列测试执行
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/stopExecute")
    public ResultEntity<String> stopExecute() {
        log.info("动作序列执行流程强制停止");
        actionSequenceExecutorService.stopExecute();
        testProcessManager.testTerminated();
        return ResultEntity.ok();
    }

    /**
     * 完成动作序列测试执行
     * 用于正常结束测试流程
     *
     * @param actionSequenceStatusDTO 动作序列状态数据传输对象
     * @return ResultEntity<String> 操作结果
     */
    @PostMapping("/completeExecute")
    public ResultEntity<String> completeExecute(@RequestBody ActionSequenceStatusDTO actionSequenceStatusDTO) {
        log.info("动作序列执行流程正常结束");
        //关闭sse状态连接
        ActionSequencesTestSseStatus.closeAll();
        if (!actionSequenceStatusDTO.isSimulated()) {
            testProcessManager.testComplete(new ExecutionContext(), !actionSequenceStatusDTO.isCompleteOk(), false);
        }
        return ResultEntity.ok();
    }

    /**
     * 关闭所有SSE(Server-Sent Events)连接
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/closeAllSSE")
    public ResultEntity<String> closeAllSSE() {
        log.info("关闭sse状态连接");
        //关闭sse状态连接
        ActionSequencesTestSseStatus.closeAll();
        return ResultEntity.ok();
    }

    /**
     * 将自然语言转换为动作序列命令
     *
     * @param actionSequenceContext 简单动作序列上下文对象
     * @return ResultEntity<List < String>> 转换后的动作序列列表
     */
    @PostMapping("/convert")
    public ResultEntity<List<String>> convertNaturalLanguageToSequence(@RequestBody ActionSequenceSimpleContext actionSequenceContext) {
        return ResultEntity.ok(baseSequenceConverter.convert(actionSequenceContext));
    }

    /**
     * 调试执行单个动作序列
     *
     * @param actionSequenceParagraph 动作序列文本
     * @param headers                 请求头信息,包含项目名等信息
     * @return ResultEntity<String> 调试执行结果
     */
    @SuppressWarnings("UnusedReturnValue")
    @PostMapping("/debug")
    public ResultEntity<String> debug(@RequestSingleParam String actionSequenceParagraph, @RequestHeader Map<String, Object> headers) {
        ExecutionContext executionContext = new ExecutionContext();
        String projectName = (String) headers.get("project");
        if (projectName != null) {
            //TODO:研究Header怎么支持中文，不用decode
            try {
                projectName = URLDecoder.decode(projectName, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                log.warn(e.getMessage(), e);
            }
        }
        executionContext.setProjectName(projectName);
        actionSequenceExecutorService.setExecutionContext(executionContext);
        List<ActionSequence> actionSequenceList = actionSequenceParserService.parseActionSequenceParagraph(actionSequenceParagraph);
        if (actionSequenceParserService.isActionSequenceListValid(actionSequenceList)) {
            try {
                actionSequenceExecutorService.execute(actionSequenceList, ActionSequenceExecutorContext.build(), null, null);
                return ResultEntity.ok();
            } catch (ActionSequenceExecutionException e) {
                log.warn(e.getMessage(), e);
                return ResultEntity.fail(e.getMessage());
            } catch (ActionSequenceStopException e) {
                return ResultEntity.fail("动作序列被强制停止");
            }
        }
        return ResultEntity.fail("动作序列无效");
    }

    private void setExcelModelToContext(ActionSequenceContext actionSequenceContext) {
        ExcelCaseModel excelCaseModel = excelCaseService.findRowCaseInfoByUuid(actionSequenceContext.getUuid());
        if (actionSequenceContext.getCurrentCycleTimes() == 1 && actionSequenceContext.getRowCurrentTestTimes() == 1) {
            excelCaseModel.setTestedPassTimes(String.valueOf(0));
        }
//        System.out.println("excelCaseModel+++++++++==>" + excelCaseModel);
        excelCaseModel.setTargetTestTimes(String.valueOf(actionSequenceContext.getTotalCycleTimes() * actionSequenceContext.getRowTotalTestTimes()));
        excelCaseModel.setTestedTimes(String.valueOf((actionSequenceContext.getCurrentCycleTimes() - 1) * actionSequenceContext.getRowTotalTestTimes() + actionSequenceContext.getRowCurrentTestTimes()));
        actionSequenceContext.setExcelCaseModel(excelCaseModel);
    }

    /**
     * 检查并执行单个测试用例
     *
     * @param actionSequenceContext 动作序列上下文对象
     * @return ResultEntity<ActionSequenceCheckReporter> 包含执行结果的响应
     */
    @PostMapping("/checkAndExecuteSingleTest")
    public ResultEntity<Object> checkAndExecuteSingleTest(@RequestBody ActionSequenceContext actionSequenceContext) {
        //清空设备消息数据
        DeviceContextInfo.getInstance().getMessageDataMap().clear();
        updateCurrentExecutionInfo(actionSequenceContext);
        actionSequenceContext.removeAnnotation();
        actionSequenceContext.removeAndTrimAfterSlashSlash();
        soundSequenceHandler.addSoundStepsForChecks(actionSequenceContext);
        canStopAllSequenceHandler.addStopAllChecks(actionSequenceContext);
        actionSequenceExecutorService.startSingleExecute();
        return actionSequenceExecutorService.processSingleActionSequenceContext(actionSequenceContext, actionSequenceCheckService);
    }

    /**
     * 停止单个测试用例的执行
     *
     * @return ResultEntity<String> 操作结果
     */
    @GetMapping("/stopExecuteSingleTest")
    public ResultEntity<String> stopExecuteSingleTest() {
        log.info("单步执行流程强制停止");
        actionSequenceExecutorService.stopSingleActionExecute();
        return ResultEntity.ok();
    }


    /**
     * 发送测试结果邮件报告
     *
     * @param mailTestDto 邮件测试结果数据传输对象
     * @return ResultEntity<String> 发送结果
     */
    @PostMapping("/mailTestResult")
    public ResultEntity<String> mailTestResult(@RequestBody TestResultReportDto mailTestDto) {
        System.out.println(mailTestDto);
        mailService.sendEmail(mailTestDto);
        return ResultEntity.ok();
    }

    /**
     * 发送cicd自动升级结果邮件报告
     *
     * @param updateResultReportDto cicd自动升级结果数据传输对象
     * @return ResultEntity<String> 发送结果
     */
    @PostMapping("/mailUpgradeResult")
    public ResultEntity<String> updateResultReportEmail(@RequestBody UpgradeResultReportDto updateResultReportDto) {
        System.out.println(updateResultReportDto);
        mailService.sendUpgradeResultReportEmail(updateResultReportDto);
        return ResultEntity.ok();
    }


    /**
     * 发送测试结果到机器人(如企业微信、钉钉等)
     *
     * @param robotReportDto 机器人测试结果数据传输对象
     * @return ResultEntity<String> 发送结果
     */
    @PostMapping("/robotTestResult")
    public ResultEntity<String> robotTestResult(@RequestBody TestResultReportDto robotReportDto) {
        System.out.println(robotReportDto);
        robotService.sendUpgradeResultRobotReport(robotReportDto);
        return ResultEntity.ok();
    }

    /**
     * 发送cicd自动升级结果到机器人(如企业微信、钉钉等)
     *
     * @param upgradeResultReportDto 机器人cicd自动升级结果数据传输对象
     * @return ResultEntity<String> 发送结果
     */
    @PostMapping("/robotUpgradeResult")
    public ResultEntity<String> updateResultReportRobot(@RequestBody UpgradeResultReportDto upgradeResultReportDto) {
        System.out.println(upgradeResultReportDto);
        robotService.sendUpgradeResultRobotReport(upgradeResultReportDto);
        return ResultEntity.ok();
    }

    /**
     * 发送测试结果写入云文档
     *
     * @param cloudDocReportDto 测试结果数据传输对象
     * @return ResultEntity<String> 发送结果
     */
    @PostMapping("/cloudDocTestResult")
    public ResultEntity<String> cloudDocTestResult(@RequestBody TestResultReportDto cloudDocReportDto) {
        System.out.println(cloudDocReportDto);
        cloudDocumentsService.addNewDataToTestResultTable(cloudDocReportDto);
        return ResultEntity.ok();
    }

    @PostMapping("/cloudDocUpgradeResult")
    public ResultEntity<String> cloudDocUpgradeResult(@RequestBody UpgradeResultReportDto upgradeResultReportDto) {
        System.out.println(upgradeResultReportDto);
        cloudDocumentsService.addNewDataToUpgradeResultTable(upgradeResultReportDto);
        return ResultEntity.ok();
    }
}
