package com.desaysv.workserver.controller.test.protocol.service;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.execution.ExecuteResult;
import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.suite.ExecutionSuite;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 智能操作步骤解析器
 */
public class SmartExecutionProtocolInterpreter extends BaseProtocolInterpreter {


    private void executeOperation(Operation operation) {
//        OperationResult operationResult;
//        try {
//            int i = 0;
//            do {
////                operationResult = interpret(nodeContext.getExecutionContext(), operation);
//            } while (operationResult.isFailed() && operationResult.isRetry() && i < operationResult.getRetryTimes());
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
    }

    /**
     * 操作执行核心逻辑
     */
    private final ExecutionUtils.ExecutorHandler executorHandler = (operationContext, operation) -> {
        //等待前置时间
        waitSeconds(operationContext.getBeforeWait());
        //执行操作步骤
        executeOperation(operation);
        //等待后置时间
        waitSeconds(operationContext.getAfterWait());
        return null;
    };

    /**
     * 前端执行用例集合
     *
     * @param executionSuite 用例集合
     * @return
     */
    @Override
    public Map<String, Map<Integer, ExecuteResult>> interpret(ExecutionSuite executionSuite) {
        final Map<String, Map<Integer, ExecuteResult>> results = new LinkedHashMap<>();
        for (Execution execution : executionSuite.getExecutionList()) {
            OperationContext operationContext = execution.getOperationContext();
            ExecutionUtils.executeOperationList(operationContext, execution.getOperationList(), executorHandler);
        }
        return results;
    }

    @Override
    public Map<Integer, ExecuteResult> interpret(ExecutionContext executionContext, Execution execution) {
        return Collections.emptyMap();
    }


    /**
     * 暂停解析
     *
     * @return
     */
    @Override
    public boolean pauseInterpret() {
        return false;
    }

    /**
     * 停止解析
     *
     * @return
     */
    @Override
    public boolean stopInterpret() {
        return false;
    }

    /**
     * 停止解析
     *
     * @return
     */
    @Override
    public boolean resumeInterpret() {
        return false;
    }

}
