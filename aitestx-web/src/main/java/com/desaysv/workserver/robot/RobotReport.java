package com.desaysv.workserver.robot;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

@Data
@AllArgsConstructor
public class RobotReport {
    private String projectName;
    private String teamName;
    private String result;
    private Date startTime;
    private Date endTime;
    private String soc;
    private String mcu;
    private String qnx;
    private String version;
    private String passRate;
    private Integer caseTotal;
    private Integer passTotal;
    private Integer failTotal;
    private String reportPath;
    private String problemPath;
    private String problemSummery;
    private String moduleNameSummary;

    public RobotReport() {

    }

    public RobotReport(String projectName, String teamName, String result, Date startTime, Date endTime, String soc, String mcu, String qnx, String passRate, Integer caseTotal, Integer passTotal, Integer failTotal, String reportPath, String problemPath, String problemSummery) {
        this.projectName = projectName;
        this.teamName = teamName;
        this.result = result;
        this.startTime = startTime;
        this.endTime = endTime;
        this.soc = soc;
        this.mcu = mcu;
        this.qnx = qnx;
        this.passRate = passRate;
        this.caseTotal = caseTotal;
        this.passTotal = passTotal;
        this.failTotal = failTotal;
        this.reportPath = reportPath;
        this.problemPath = problemPath;
        this.problemSummery = problemSummery;
    }

    public String subDate() {
        long totalT = (endTime.getTime()) - (startTime.getTime());
        Long h = totalT / 3600000;
        Long min = totalT % 3600000 / (60 * 1000);
        Long sec = totalT % (60 * 1000) / 1000;
        return String.format("%2d时%2d分%2d秒", h, min, sec);
    }

}
