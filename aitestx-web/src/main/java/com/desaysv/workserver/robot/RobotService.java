package com.desaysv.workserver.robot;

import com.desaysv.workserver.base.manager.ClientInfoObserver;
import com.desaysv.workserver.base.manager.ClientInfoReceiveFromClient;
import com.desaysv.workserver.controller.cicd.AutomaticUpgradeController;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.mail.TestResult;
import com.desaysv.workserver.mail.TestResultReportDto;
import com.desaysv.workserver.monitor.PolyTestUsageStatusUpdater;
import com.desaysv.workserver.entity.SmokingTestConfigModel;
import com.desaysv.workserver.entity.SmokingTestConfigObserver;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class RobotService implements ClientInfoObserver, SmokingTestConfigObserver {

    private ClientInfoReceiveFromClient clientInfoReceiveFromClient;
    private SmokingTestConfigModel smokingTestConfigModel;
    @Autowired
    private PolyTestUsageStatusUpdater polyTestUsageStatusUpdater;
    @Autowired
    private AutomaticUpgradeController automaticUpgradeController;
    private final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private RestTemplate restTemplate;

    @PostConstruct
    private void init() {
        polyTestUsageStatusUpdater.addObserver(this);
        automaticUpgradeController.addObserver(this);

    }

    public RobotService() {
    }

    public JsonNode addition(JsonNode initNode) {
        JsonNode curNode = initNode.path("elements").path(1).path("columns").path(0).path("elements").path(0).path("text");
        return curNode;
    }

    /**
     * 发送机器人消息
     *
     * @param robotReportDto
     */
    @SneakyThrows
    public boolean sendUpgradeResultRobotReport(TestResultReportDto robotReportDto) {
        String postParams = paramsReport(robotReportDto);
        //获取机器人地址
        List<String> urlsList = new ArrayList<>(Arrays.asList(clientInfoReceiveFromClient.getUrls()));
        //调用请求方法
        for (String url : urlsList) {
            String response = postReport(url, postParams);
            System.out.println(response);
        }
        return true;
    }


    /**
     * 发送自动升级结果通知机器人消息
     *
     * @param upgradeResultReportDto  cicd自动升级结果
     */
    @SneakyThrows
    public boolean sendUpgradeResultRobotReport(UpgradeResultReportDto upgradeResultReportDto) {
        String postParams = getUpgradeFailRobotParamDto(upgradeResultReportDto);
        //获取机器人地址
        List<String> urlsList = new ArrayList<>(Arrays.asList(clientInfoReceiveFromClient.getUrls()));
        //调用请求方法
        for (String url : urlsList) {
            String response = postReport(url, postParams);
            log.info(response);
        }
        return true;
    }


    @SneakyThrows
    public RobotReport getSmokeRobotReport(TestResultReportDto robotReportDto) {
        RobotReport robotReport = new RobotReport();
        robotReport.setProjectName(clientInfoReceiveFromClient.getProject());
        robotReport.setTeamName(clientInfoReceiveFromClient.getProject());
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        robotReport.setStartTime(ft.parse(robotReportDto.getStartTime()));
        robotReport.setEndTime(ft.parse(robotReportDto.getEndTime()));
        robotReport.setSoc(robotReportDto.getSocVersion());
        robotReport.setMcu(robotReportDto.getMcuVersion());
        robotReport.setQnx(robotReportDto.getQnxVersion());
        TestResult smokeTestResult = robotReportDto.getSmokeTestResults();
        robotReport.setCaseTotal(smokeTestResult.getExecutedCases());
        robotReport.setPassTotal(smokeTestResult.getPassCases());
        robotReport.setFailTotal(smokeTestResult.getFailCases());
        robotReport.setPassRate(smokeTestResult.getPassRate());
        robotReport.setReportPath(robotReportDto.getCloudReportPath());
        robotReport.setProblemPath(robotReportDto.getCloudReportPath());//TODO 未确认 暂时不处理
        robotReport.setProblemSummery(robotReportDto.getCloudReportPath());//TODO 未确认 暂时不处理
        robotReport.setModuleNameSummary("冒烟");
        String passRateStr = smokeTestResult.getPassRate();
        float passRate = 0.0f;
        if (passRateStr != null && passRateStr.endsWith("%")) {
            passRateStr = passRateStr.substring(0, passRateStr.length() - 1);
        }
        try {
            passRate = Float.parseFloat(passRateStr);
        } catch (NumberFormatException e) {
            log.error("无法解析目标通过率: {}", smokeTestResult.getPassRate(), e);
            passRate = 0.0f;
        }
        robotReport.setResult(passRate > robotReportDto.getPassRate() ? "PASS" : "FAIL");
        return robotReport;
    }

    @SneakyThrows
    public RobotReport getFunctionRobotReport(TestResultReportDto robotReportDto) {
        RobotReport robotReport = new RobotReport();
        robotReport.setProjectName(clientInfoReceiveFromClient.getProject());
        robotReport.setTeamName(clientInfoReceiveFromClient.getProject());
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        robotReport.setStartTime(ft.parse(robotReportDto.getStartTime()));
        robotReport.setEndTime(ft.parse(robotReportDto.getEndTime()));
        robotReport.setSoc(smokingTestConfigModel.getSocVersion());
        robotReport.setMcu(smokingTestConfigModel.getMcuVersion());
        robotReport.setQnx(smokingTestConfigModel.getQnxVersion());
        List<TestResult> functionTestResults = robotReportDto.getFunctionTestResults();
        robotReport.setResult("PASS");
        robotReport.setCaseTotal(0);
        robotReport.setPassTotal(0);
        robotReport.setFailTotal(0);
        StringBuilder moduleName = new StringBuilder();
        boolean isFail = false;
        for (TestResult testResult : functionTestResults) {
            moduleName.append(testResult.getModuleName());
            moduleName.append(" ");
            robotReport.setCaseTotal(robotReport.getCaseTotal() + testResult.getExecutedCases());
            robotReport.setPassTotal(robotReport.getPassTotal() + testResult.getPassCases());
            robotReport.setFailTotal(robotReport.getFailTotal() + testResult.getFailCases());
            if (testResult.getConclusion().contains("NG")) {
                isFail = true;
            }
        }
        robotReport.setModuleNameSummary(String.valueOf(moduleName));
        double passRate = ((double) robotReport.getPassTotal() / robotReport.getCaseTotal()) * 100;
        robotReport.setPassRate(String.format("%.2f%%", passRate));
        if (smokingTestConfigModel.isModulePassRateRequired()) {
            if (isFail) {
                robotReport.setResult("FAIL");
            } else {
                robotReport.setResult("PASS");
            }
        } else {
            if (passRate < robotReportDto.getPassRate()) {
                robotReport.setResult("FAIL");
            } else {
                robotReport.setResult("PASS");
            }
        }

        robotReport.setReportPath(robotReportDto.getCloudReportPath());
        robotReport.setProblemPath(robotReportDto.getCloudReportPath());
        robotReport.setProblemSummery(robotReportDto.getCloudReportPath());
        return robotReport;
    }

    /**
     * 发送请求
     *
     * @param urlString
     * @param postParams
     * @return
     * @throws Exception
     */
    private String postReport(String urlString, String postParams) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(postParams, headers);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(urlString, request, String.class);
            log.info("Response Code: {}", response.getStatusCodeValue());
            return response.getBody();
        } catch (ResourceAccessException e) {
            log.error("网络连接异常: {}", e.getMessage());
            throw new Exception("机器人服务不可达");
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP 错误: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new Exception("机器人服务响应异常");
        }

    }

    private String getUpgradeFailRobotParamDto(UpgradeResultReportDto upgradeResultReportDto) throws IOException {
        String[] testUnitArr = clientInfoReceiveFromClient.getTestUnit().split("/");
        upgradeResultReportDto.setTestUnit(testUnitArr[0]);
        upgradeResultReportDto.setProjectName(clientInfoReceiveFromClient.getProject());

        try (InputStream is = getClass().getClassLoader().getResourceAsStream("robot/UpgradeResultRobotData.json")) {
            if (is == null) {
                throw new FileNotFoundException("Resource not found: robot/UpgradeResultRobotData.json");
            }

            JsonNode rootNode = mapper.readTree(is);

            // 修改 header.title.content
            ObjectNode titleNode = (ObjectNode) rootNode.path("header").path("title");
            titleNode.put("content", "亲爱的" + upgradeResultReportDto.getProjectName()
                    + "团队，" + upgradeResultReportDto.getVersion() + "版本升级失败。");

            // 获取 body.elements
            JsonNode elements = rootNode.path("body").path("elements");

            // 修改 测试电脑
            ObjectNode hostNameNode = (ObjectNode) elements.get(1).path("columns").get(1).path("elements").get(0);
            hostNameNode.put("content", upgradeResultReportDto.getTestUnit());

            // 修改 失败原因
            ObjectNode errorMessageNode = (ObjectNode) elements.get(2).path("columns").get(1).path("elements").get(0);
            errorMessageNode.put("content", upgradeResultReportDto.getErrorMessage());

            // 修改 失败时间
            ObjectNode endTimeNode = (ObjectNode) elements.get(3).path("columns").get(1).path("elements").get(0);
            endTimeNode.put("content", upgradeResultReportDto.getEndTime());

            // 构造最终发送给机器人的 JSON 结构
            ObjectNode finalJson = mapper.createObjectNode();
            finalJson.put("msg_type", "interactive");

            // 把整个原始 JSON 作为 card 字段嵌套进去
            ((ObjectNode) finalJson).set("card", rootNode);

            return finalJson.toString();
        }
    }

    /**
     * 获取请求参数
     *
     * @param robotReportDto
     * @return
     * @throws Exception
     */
    private String paramsReport(TestResultReportDto robotReportDto) throws Exception {
        JsonNode rootNode = null;
        RobotReport report = null;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
        if (robotReportDto.isFunctionMode()) {
            //封装机器人消息
            report = getFunctionRobotReport(robotReportDto);
            try (InputStream is = getClass().getClassLoader().getResourceAsStream("robot/FunctionTestRobotData.json")) {
                if (is == null) {
                    throw new FileNotFoundException("Resource not found: robot/FunctionTestRobotData.json.json");
                }
                rootNode = mapper.readTree(is);
            } catch (IOException e) {
                log.error("获取FunctionTestRobotData.json错误");
            }
        } else {
            //封装机器人消息
            report = getSmokeRobotReport(robotReportDto);
            try (InputStream is = getClass().getClassLoader().getResourceAsStream("robot/RobotData.json")) {
                if (is == null) {
                    throw new FileNotFoundException("Resource not found: robot/RobotData.json");
                }
                rootNode = mapper.readTree(is);
            } catch (IOException e) {
                log.error("获取RobotData.json错误");
            }
        }

        JsonNode title = rootNode.path("card").path("i18n_header").path("zh_cn").path("title");

        JsonNode card = rootNode.path("card").path("i18n_elements").path("zh_cn");
        JsonNode team = card.path(1).path("columns").path(0).path("elements").path(0).path("text");

        RobotService main = new RobotService();

        JsonNode res = main.addition(card.path(2).path("columns").path(0));
        JsonNode totalTime = main.addition(card.path(2).path("columns").path(1));

        JsonNode startTime = main.addition(card.path(3).path("columns").path(0));
        JsonNode endTime = main.addition(card.path(3).path("columns").path(1));

        JsonNode soc = main.addition(card.path(4).path("columns").path(0));
        JsonNode mcu = main.addition(card.path(4).path("columns").path(1));

        JsonNode QNX = main.addition(card.path(5).path("columns").path(0));

        if (robotReportDto.isFunctionMode()) {
            ((ObjectNode) title).put("content", "【" + report.getProjectName() + " 功能自动化测试报告】");
            ((ObjectNode) team).put("content", "亲爱的" + report.getTeamName() + "团队，本次" + smokingTestConfigModel.getVersion() + "功能测试结果已发布，请参见如下信息获取最新软件及相关信息");
        } else {
            ((ObjectNode) title).put("content", "【" + report.getProjectName() + " 冒烟自动化测试报告】");
            ((ObjectNode) team).put("content", "亲爱的" + report.getTeamName() + "团队，本次" + robotReportDto.getVersion() + "冒烟测试结果已发布，请参见如下信息获取最新软件及相关信息");
        }

        if (report.getResult() != "PASS") {
            ((ObjectNode) res).put("text_color", "red");
        }
        ((ObjectNode) res).put("content", report.getResult());
        ((ObjectNode) totalTime).put("content", report.subDate());
        ((ObjectNode) startTime).put("content", formatter.format(report.getStartTime()));
        ((ObjectNode) endTime).put("content", formatter.format(report.getEndTime()));
        ((ObjectNode) soc).put("content", report.getSoc());
        ((ObjectNode) mcu).put("content", report.getMcu());
        ((ObjectNode) QNX).put("content", report.getQnx());

        if (robotReportDto.isFunctionMode()) {
            List<TestResult> testResults = robotReportDto.getFunctionTestResults();

            // 定位到最后一个元素（即表格）
            JsonNode tableNode = card.path(7);

            // 直接获取 rows 数组
            ArrayNode rows = (ArrayNode) tableNode.path("rows");

            // 清空默认行
            rows.removeAll();

            for (TestResult result : testResults) {
                ObjectNode row = mapper.createObjectNode(); // 改用ObjectNode作为行容器

                // 使用列名作为key添加单元格（与columns定义中的name对应）
                row.set("module_name", createTextCell(result.getModuleName()));
                row.set("executed_cases", createNumberNode(result.getExecutedCases())); // 直接生成数值节点
                row.set("pass_cases", createNumberNode(result.getPassCases()));
                row.set("fail_cases", createNumberNode(result.getFailCases()));
                row.set("pass_rate", createTextCell(result.getPassRate()));
                row.set("conclusion", createConclusionCell(result.getConclusion()));

                rows.add(row); // 直接添加对象到rows数组
            }
            //汇总列
            ObjectNode summaryRow = mapper.createObjectNode(); // 改用ObjectNode作为行容器
            // 使用列名作为key添加单元格（与columns定义中的name对应）
            summaryRow.set("module_name", createTextCell("汇总"));
            summaryRow.set("executed_cases", createNumberNode(report.getCaseTotal())); // 直接生成数值节点
            summaryRow.set("pass_cases", createNumberNode(report.getPassTotal()));
            summaryRow.set("fail_cases", createNumberNode(report.getFailTotal()));
            summaryRow.set("pass_rate", createTextCell(report.getPassRate()));
            summaryRow.set("conclusion", createConclusionCell(report.getResult()));
            rows.add(summaryRow);

            JsonNode reportPath = main.addition(card.path(9).path("columns").path(0));

            JsonNode problemPath = main.addition(card.path(10).path("columns").path(0));

            JsonNode problemSummery = main.addition(card.path(12).path("columns").path(0));
            ((ObjectNode) reportPath).put("content", report.getReportPath());
            ((ObjectNode) problemPath).put("content", report.getProblemPath());
            ((ObjectNode) problemSummery).put("content", report.getProblemSummery());

        } else {
            JsonNode passRate = main.addition(card.path(7).path("columns").path(0));
            JsonNode caseTotal = main.addition(card.path(7).path("columns").path(1));

            JsonNode passTotal = main.addition(card.path(8).path("columns").path(0));
            JsonNode failTotal = main.addition(card.path(8).path("columns").path(1));

            JsonNode reportPath = main.addition(card.path(10).path("columns").path(0));

            JsonNode problemPath = main.addition(card.path(11).path("columns").path(0));

            JsonNode problemSummery = main.addition(card.path(13).path("columns").path(0));

            ((ObjectNode) passRate).put("content", report.getPassRate());
            ((ObjectNode) caseTotal).put("content", String.valueOf(report.getCaseTotal()));
            ((ObjectNode) passTotal).put("content", String.valueOf(report.getPassTotal()));
            ((ObjectNode) failTotal).put("content", String.valueOf(report.getFailTotal()));
            ((ObjectNode) reportPath).put("content", report.getReportPath());
            ((ObjectNode) problemPath).put("content", report.getProblemPath());
            ((ObjectNode) problemSummery).put("content", report.getProblemSummery());
        }
        // 保存或输出最终 JSON
//        System.out.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode));

        //转换字符串
        return rootNode.toString();
    }

    @Override
    public void update(ClientInfoReceiveFromClient clientInfo) {
        this.clientInfoReceiveFromClient = clientInfo;
    }

    @Override
    public void update(SmokingTestConfigModel configModel) {
        this.smokingTestConfigModel = configModel;
    }

    private JsonNode createNumberNode(int number) {
        return mapper.getNodeFactory().numberNode(number);
    }

    private JsonNode createTextCell(String content) {
        return mapper.getNodeFactory().textNode(content);
    }

    private JsonNode createConclusionCell(String conclusion) {
        ArrayNode options = mapper.createArrayNode();
        options.add(mapper.createObjectNode()
                .put("text", conclusion)
                .put("color", conclusion.equals("PASS") ? "green" : "red"));
        return options; // 直接返回数组
    }


}