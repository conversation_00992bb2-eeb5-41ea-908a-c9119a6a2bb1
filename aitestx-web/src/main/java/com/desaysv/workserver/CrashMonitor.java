package com.desaysv.workserver;

import com.desaysv.workserver.filemanager.ClientRootFolder;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
public class CrashMonitor {
    private static final String LOG_DIR = "log/crash_logs/";

    public static void initCrashMonitor() {
        // 创建日志目录
        File logFolder = new File(ClientRootFolder.rootPath, LOG_DIR);
        logFolder.mkdirs();

        // 注册JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                recordCrashInfo(logFolder);
            } catch (Exception e) {
                log.error("Failed to record crash information", e);
            }
        }));

        // 注册未捕获异常处理器
        Thread.setDefaultUncaughtExceptionHandler((thread, throwable) -> {
            try {
                recordCrashInfo(logFolder, thread, throwable);
            } catch (Exception e) {
                log.error("Failed to record crash information", e);
            }
        });
    }

    private static void recordCrashInfo(File logFolder) throws Exception {
        String timestamp = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new Date());
        File file = new File(logFolder, "SystemInfo_" + timestamp + ".log");

        try (FileWriter writer = new FileWriter(file)) {
            // 记录系统信息
            writer.write("=== System Information ===\n");
            writer.write("OS: " + System.getProperty("os.name") + "\n");
            writer.write("Java Version: " + System.getProperty("java.version") + "\n");
            writer.write("Memory Usage: " + getMemoryUsage() + "\n");

            // 记录所有线程的堆栈信息
            writer.write("\n=== Thread Stack Traces ===\n");
            for (Map.Entry<Thread, StackTraceElement[]> entry : Thread.getAllStackTraces().entrySet()) {
                Thread thread = entry.getKey();
                StackTraceElement[] stack = entry.getValue();

                writer.write("\nThread " + thread.getName() + " (State: " + thread.getState() + ")\n");
                for (StackTraceElement element : stack) {
                    writer.write("\tat " + element + "\n");
                }
            }
        }
    }

    private static void recordCrashInfo(File logFolder, Thread thread, Throwable throwable) throws Exception {
        String timestamp = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss").format(new Date());
        File file = new File(logFolder, "Crash_" + timestamp + ".log");

        try (FileWriter writer = new FileWriter(file)) {
            // 记录异常信息
            writer.write("=== Crash Information ===\n");
            writer.write("Thread: " + thread.getName() + "\n");
            writer.write("Exception: " + throwable.getClass().getName() + "\n");
            writer.write("Message: " + throwable.getMessage() + "\n\n");

            // 记录异常堆栈
            writer.write("Stack Trace:\n");
            for (StackTraceElement element : throwable.getStackTrace()) {
                writer.write("\tat " + element + "\n");
            }

            // 记录系统信息
            writer.write("\n=== System Information ===\n");
            writer.write("OS: " + System.getProperty("os.name") + "\n");
            writer.write("Java Version: " + System.getProperty("java.version") + "\n");
            writer.write("Memory Usage: " + getMemoryUsage() + "\n");
        }
    }

    private static String getMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        return String.format("Used: %dMB / Total: %dMB",
                usedMemory / (1024 * 1024),
                totalMemory / (1024 * 1024));
    }
}