package com.desaysv.workserver.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 视频流请求DTO
 * 用于封装Android设备视频流的请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoStreamRequest {

    /**
     * Android设备名称/序列号
     */
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;

    /**
     * 视频宽度
     * 0或负数表示使用设备默认宽度
     */
    @Min(value = 0, message = "视频宽度不能为负数")
    private int width = 0;

    /**
     * 视频高度
     * 0或负数表示使用设备默认高度
     */
    @Min(value = 0, message = "视频高度不能为负数")
    private int height = 0;

    /**
     * 视频比特率
     * 0或负数表示使用默认比特率
     */
    @Min(value = 0, message = "视频比特率不能为负数")
    private int bitRate = 0;

    /**
     * 是否自动停止之前的视频流
     * 默认为true
     */
    private boolean autoStopPrevious = true;

    /**
     * 流式传输缓冲区大小（字节）
     * 默认8KB
     */
    @Min(value = 1024, message = "缓冲区大小不能小于1KB")
    private int bufferSize = 8192;

    /**
     * 构造函数 - 仅设备名称
     * 
     * @param deviceName 设备名称
     */
    public VideoStreamRequest(String deviceName) {
        this.deviceName = deviceName;
    }

    /**
     * 构造函数 - 设备名称和分辨率
     * 
     * @param deviceName 设备名称
     * @param width 视频宽度
     * @param height 视频高度
     */
    public VideoStreamRequest(String deviceName, int width, int height) {
        this.deviceName = deviceName;
        this.width = width;
        this.height = height;
    }

    /**
     * 构造函数 - 设备名称、分辨率和比特率
     * 
     * @param deviceName 设备名称
     * @param width 视频宽度
     * @param height 视频高度
     * @param bitRate 视频比特率
     */
    public VideoStreamRequest(String deviceName, int width, int height, int bitRate) {
        this.deviceName = deviceName;
        this.width = width;
        this.height = height;
        this.bitRate = bitRate;
    }

    /**
     * 检查是否使用默认分辨率
     * 
     * @return 如果宽度或高度为0则返回true
     */
    public boolean isUsingDefaultResolution() {
        return width <= 0 || height <= 0;
    }

    /**
     * 检查是否使用默认比特率
     * 
     * @return 如果比特率为0则返回true
     */
    public boolean isUsingDefaultBitRate() {
        return bitRate <= 0;
    }

    /**
     * 获取格式化的分辨率字符串
     * 
     * @return 分辨率字符串，如"1920x1080"或"默认"
     */
    public String getResolutionString() {
        if (isUsingDefaultResolution()) {
            return "默认";
        }
        return width + "x" + height;
    }

    /**
     * 获取格式化的比特率字符串
     * 
     * @return 比特率字符串，如"4Mbps"或"默认"
     */
    public String getBitRateString() {
        if (isUsingDefaultBitRate()) {
            return "默认";
        }
        if (bitRate >= 1000000) {
            return (bitRate / 1000000) + "Mbps";
        } else if (bitRate >= 1000) {
            return (bitRate / 1000) + "Kbps";
        } else {
            return bitRate + "bps";
        }
    }

    @Override
    public String toString() {
        return String.format("VideoStreamRequest{deviceName='%s', resolution=%s, bitRate=%s, autoStopPrevious=%s, bufferSize=%d}",
                deviceName, getResolutionString(), getBitRateString(), autoStopPrevious, bufferSize);
    }
}
