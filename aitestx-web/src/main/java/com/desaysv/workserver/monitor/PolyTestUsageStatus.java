package com.desaysv.workserver.monitor;

import lombok.Data;

@Data
public class PolyTestUsageStatus implements Cloneable {
    private String addr; //位置,示例值(主楼3楼测试1房工位1)
    private String ipAddr; //电脑ip地址,示例值(127.0.0.1)
    private String pcName; //计算机名称,示例值(HZH12178)
    private String peripherals; //当前连接（计算机的）外部设备，有多个时使用半角分号（;）隔开,示例值(甜甜圈;机械臂)
    private String remark; //备注
    private String startTime; //状态开始时间:2023-06-06 10:31:19,示例值(2023-06-06 10:31:19
    private String endTime; //状态结束时间：缓存数据上传时必传，实时上报非必传
    /**
     * 使用状态号：0:打开、1：登录、2：开始测试、3:暂停、4：继续（恢复）测试、5：测试通过、6：测试失败、
     * 7：测试完成、8：异常停止、9：手动停止、10、退出登录、11：关闭应用,示例值(1)
     */
    private int status;
    /**
     * 测试套件号：status为2：开始测试、3:暂停、4：继续（恢复）测试、5：测试通过、6：测试失败、
     * 7：测试完成、8：异常停止、9：手动停止时候必填，其他非必填
     */
    private int testSuiteId;

    /**
     * 测试员：status为0:打开、11：关闭应用：非必填;status非0:打开、11：关闭应用：必填,示例值(uidq7939)
     */
    private String userId;

    private int toolId; //工具id，对应appid,示例值(11)
    private String pcAppRunId = ""; //上位机运行唯一id
    private String pcUuid; //电脑uuid
    private String testsuiteAttachmentPath; //测试集合附件地址
    private String errorTypeName;//错误类型名称
    private String toolVersion; //上位机版本号

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            PolyTestUsageStatus polyTestUsageStatus = new PolyTestUsageStatus();
            polyTestUsageStatus.setAddr(addr);
            polyTestUsageStatus.setEndTime(endTime);
            polyTestUsageStatus.setIpAddr(ipAddr);
            polyTestUsageStatus.setPcName(pcName);
            polyTestUsageStatus.setPeripherals(peripherals);
            polyTestUsageStatus.setRemark(remark);
            polyTestUsageStatus.setStartTime(startTime);
            polyTestUsageStatus.setStatus(status);
            polyTestUsageStatus.setTestSuiteId(testSuiteId);
            polyTestUsageStatus.setToolId(toolId);
            polyTestUsageStatus.setUserId(userId);
            polyTestUsageStatus.setPcAppRunId(pcAppRunId);
            polyTestUsageStatus.setPcUuid(pcUuid);
            polyTestUsageStatus.setTestsuiteAttachmentPath(testsuiteAttachmentPath);
            polyTestUsageStatus.setErrorTypeName(errorTypeName);
            polyTestUsageStatus.setToolVersion(toolVersion);
            return polyTestUsageStatus;
        }
    }
}
