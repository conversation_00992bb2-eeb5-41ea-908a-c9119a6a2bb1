package com.desaysv.workserver.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

@Configuration
@Slf4j
public class PolyTestUrls {

    // 正式环境
    @Value("${constants.web.polytest-url}")
    private String BASE_URL;

    public String EXE_TEST_CASE_URL; //用例执行接口,用例执行结果采集,只需要测试执行完成时上传一次
    public String START_TEST_SUITE_URL; //启动集合接口,启动集合测试时调用一次 保存返回的testSuiteId,用于上报状态或者用例测试上报时入参填写
    public String UPLOAD_USAGE_STATUS_URL;
//    public String EMAIL;

    @Value("${constants.web.polytest-email-url}")
    private String BASE_EMAIL_URL;

    public String CLOUD_EMAIL_URL;

    private static final String email = "/polytest/common/sendMailWithAttachment?appid=8&secretkey=cdfvfggs";

    @PostConstruct
    public void PostConstruct() {
        log.info("加载云平台:{}", BASE_URL);
        EXE_TEST_CASE_URL = BASE_URL + "/polytest/autotest/exeTestCase";
        START_TEST_SUITE_URL = BASE_URL + "/polytest/autotest/startTestSuite";
        UPLOAD_USAGE_STATUS_URL = BASE_URL + "/polytest/autotest/updateUsageStatus";
//        EMAIL = BASE_URL + email;
        CLOUD_EMAIL_URL = BASE_EMAIL_URL + email;
    }

}
