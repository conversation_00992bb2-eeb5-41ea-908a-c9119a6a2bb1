package com.desaysv.workserver.monitor.deprecated;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.*;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.*;

/**
 * 上传旧监控中心
 */
@Slf4j
public class TestClientStatusUpdater implements TestProcessListener, ClientStatusListener, Runnable {

    @Data
    private static class MailMessage {

        private String toAddr;
        private String subject;
        private String content;

    }

    @Autowired
    private JavaMailSender mailSender;
    @Value("${spring.mail.username}")
    private String from;

    private final static String MONITOR_SERVER_URL_OF_DEBUG = "http://localhost:10099/ATServer";
    private final static String UPLOAD_CLIENT_URL_OF_DEBUG = MONITOR_SERVER_URL_OF_DEBUG + "/overview";
    private final static String MONITOR_SERVER_URL_OF_PROD = "http://**********:10099/ATServer";
    private final static String UPLOAD_CLIENT_URL_OF_PROD = MONITOR_SERVER_URL_OF_PROD + "/overview";

    @Autowired
    private RestTemplate restTemplate;
    private TestClientInfo testClientInfo;
    private final ScheduledExecutorService executor;
    private boolean scheduled;

    @Getter
    private final BlockingQueue<ClientPackage> testClientInfoQueue;

    public TestClientStatusUpdater() {
        testClientInfoQueue = new LinkedBlockingQueue<>();
        executor = Executors.newScheduledThreadPool(4);
        Executors.newSingleThreadExecutor().execute(this::receiveAndUploadPackage);
        TestProcessManager.addTestProcessListener(this);
        ClientStatusManager.addClientStatusListener(this);
        scheduled = false;
    }

    /**
     * 开始周期上传
     */
    public void startSchedule() {
        if (!scheduled) {
            int uploadInterval = 2; //2min
            executor.scheduleAtFixedRate(this, 0, uploadInterval, TimeUnit.MINUTES);
            scheduled = true;
        } else {
            uploadRightNow();
        }
    }

    /**
     * 接收包并上传
     */
    public void receiveAndUploadPackage() {
        while (true) {
            TestClientInfo testClientInfo;
            try {
                testClientInfo = (TestClientInfo) testClientInfoQueue.take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                continue;
            }
            String statusName = TestClientConstant.getStatusNameByCode(testClientInfo.getStatusCode());
            try {
                ResponseEntity<TestClientInfo> result = restTemplate.exchange(
                        UPLOAD_CLIENT_URL_OF_PROD,
                        HttpMethod.POST,
                        new HttpEntity<>(testClientInfo),
                        TestClientInfo.class);
                TestClientInfo response = result.getBody();
                if (response != null && result.getStatusCode().equals(HttpStatus.OK)) {
                    log.debug("上传客户端状态成功:{}>>{}", statusName, testClientInfo);
                } else {
                    log.warn("上传客户端状态失败:{}>>{}", statusName, testClientInfo);
                }
            } catch (ResourceAccessException e) {
                log.error("上传客户端状态失败:{}>>{}", statusName, e.getMessage());
            }
        }
    }

    /**
     * 上传到监控服务器
     */
    public void uploadPackageToServer() {
        testClientInfoQueue.add(testClientInfo);
    }

    @Override
    public void run() {
        uploadPackageToServer();
    }

    /**
     * 立即上传
     */
    public void uploadRightNow() {
        testClientInfoQueue.add((TestClientInfo) testClientInfo.clone());
    }

    @Override
    public void userRegister(ClientPackage clientPackage) {

    }

    @Override
    public void userLogin(ClientPackage clientPackage) {
        this.testClientInfo = (TestClientInfo) clientPackage;
        testClientInfo.setStatusCode(TestClientConstant.Status.READY.getValue());
        startSchedule();
    }

    @Override
    public void userLogout(ClientPackage clientPackage) {
        this.testClientInfo = (TestClientInfo) clientPackage;
        testClientInfo.setStatusCode(TestClientConstant.Status.LOGOUT.getValue());
        uploadRightNow();
    }

    @Override
    public void clientExit(ClientPackage clientPackage) {
        this.testClientInfo = (TestClientInfo) clientPackage;
        testClientInfo.setStatusCode(TestClientConstant.Status.EXITED.getValue());
        uploadRightNow();
    }

    @Override
    public void testcaseStart(ExecutionContext executionContext) {
        testClientInfo.setStatusCode(TestClientConstant.Status.TESTING.getValue());
        uploadRightNow();
    }

    @Override
    public void testing(ExecutionContext executionContext, int currentTestCycle) {
//        testClientInfo.setRemark(String.format("测试次数: %d", testCycle));
//        uploadRightNow();
    }

    /**
     * 要求远程服务器进行邮件转发
     *
     * @param mailMessage 邮件发送包
     */
    private void askForRemoteServerToSend(MailMessage mailMessage) {
        String url = String.format("%s/email/send",
                SpringContextHolder.isDevEnvironment() ? MONITOR_SERVER_URL_OF_DEBUG : MONITOR_SERVER_URL_OF_PROD);
        try {
            restTemplate.postForLocation(url, mailMessage);
        } catch (Exception e) {
            log.warn("远程服务器出错，邮件转发失败，原因如下:{}", e.getMessage());
            log.debug(e.getMessage(), e);
        }
    }

    @Async
    public void sendSimpleEmail(MailMessage mailMessage) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(mailMessage.getToAddr()); // 接收地址
            message.setSubject(mailMessage.getSubject()); // 标题
            message.setText(mailMessage.getContent()); // 内容
            mailSender.send(message);
            log.info("发送邮件成功->{}", mailMessage.getToAddr());
        } catch (MailException e) {
            //如果网络禁止邮件发送，转发服务器进行发送
            log.warn("当前网络策略已将邮件发送功能禁止，正在通过请求转发服务器进行邮件发送...");
            askForRemoteServerToSend(mailMessage);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void sendEmailForFail(ExecutionContext executionContext) {
        MailMessage mailMessage = new MailMessage();
        mailMessage.setToAddr(executionContext.getUserEmail());
        String subject = String.format("%s/%s-%s测试暂停", executionContext.getProjectName(), executionContext.getUserName(),
                executionContext.getClientName());
        mailMessage.setSubject(subject);
        mailMessage.setContent(String.format("%s已暂停，请及时处理", executionContext.getClientName()));
        log.info("发送邮件:{}->{}", subject, mailMessage.getToAddr());
        sendSimpleEmail(mailMessage);
    }

    @Override
    public void testFailed(ExecutionContext executionContext, int testCycle) {
        testClientInfo.setStatusCode(TestClientConstant.Status.FAILED.getValue());
        uploadRightNow();
        if (!executionContext.isDebugModeEnabled()) {
            sendEmailForFail(executionContext);
        }
    }
    @Override
    public void testPausing() {
        testClientInfo.setStatusCode(TestClientConstant.Status.PAUSING.getValue());
        uploadRightNow();
    }

    @Override
    public void testResume() {
        testClientInfo.setStatusCode(TestClientConstant.Status.TESTING.getValue());
        uploadRightNow();
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        testClientInfo.setStatusCode(isFailed ? TestClientConstant.Status.COMPLETED_FAIL.getValue() :
                TestClientConstant.Status.COMPLETED_PASS.getValue());
        uploadRightNow();
    }

    @Override
    public void testTerminated() {
        testClientInfo.setStatusCode(TestClientConstant.Status.TERMINATED.getValue());
        uploadRightNow();
    }

}
