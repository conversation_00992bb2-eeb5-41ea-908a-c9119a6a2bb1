package com.desaysv.workserver.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class TcpServerConnection {
//    @PostConstruct
//    public void PostConstruct() {
//        TCPServer tcpServer = TCPServer.getInstance();
//        tcpServer.startServer();
//        if (tcpServer.isServerRunning()) {
//            tcpServer.receiveData();
//        } else {
//            log.error("TCP服务启动失败");
//        }
//    }

}
