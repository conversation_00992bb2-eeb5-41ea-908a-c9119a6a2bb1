package com.desaysv.workserver.interceptions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-1 14:26
 * @description :
 * @modified By :
 * @since : 2022-7-1
 */
@Slf4j
@WebFilter(filterName = "sessionFilter", urlPatterns = "/*", asyncSupported = true)
@Order(1)
public class SessionWebFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;

        // 排除静态资源和 OPTIONS 请求
        if (req.getMethod().equalsIgnoreCase("OPTIONS") || 
            req.getRequestURI().startsWith("/api-docs") || 
            req.getRequestURI().startsWith("/webjars") || 
            req.getRequestURI().startsWith("/swagger-ui") || 
            req.getRequestURI().equals("/")) {
            chain.doFilter(request, response);
            return;
        }

        // 只对需要处理的业务请求设置 session 属性
        req.getSession().setAttribute("ip", req.getRemoteHost());
        req.getSession().setAttribute("port", req.getRemotePort());
        chain.doFilter(request, response);
    }
}
