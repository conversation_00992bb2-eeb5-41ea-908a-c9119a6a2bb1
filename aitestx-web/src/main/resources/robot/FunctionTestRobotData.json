{"msg_type": "interactive", "card": {"config": {"update_multi": true}, "i18n_elements": {"zh_cn": [{"tag": "markdown", "content": "<at id=all></at> ", "text_align": "left", "text_size": "normal"}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "亲爱的${team}团队，本次功能测试结果已发布，请参见如下信息获取最新软件及相关信息", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**Test Result：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "file-form_colorful"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "green"}}], "width": "weighted", "weight": 1}]}], "weight": 1}, {"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试总耗时：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "labs_filled", "color": "orange"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "x时xx分xx秒", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "column_set", "flex_mode": "stretch", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试开始时间：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "alarm-clock_outlined", "color": "orange"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "2024-3-11 09:24:01", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}, {"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试结束时间：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "history_outlined", "color": "orange"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "2024-3-11 09:24:01", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "column_set", "flex_mode": "stretch", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**SOC版本：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "setting_filled", "color": "light_grey"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}, {"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**MCU版本：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "setting_filled", "color": "light_grey"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "column_set", "flex_mode": "stretch", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**QNX版本：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "setting_filled", "color": "light_grey"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "hr"}, {"tag": "table", "page_size": 10, "row_height": "low", "freeze_first_column": true, "header_style": {"text_align": "left", "text_size": "normal", "background_style": "none", "text_color": "grey", "bold": true, "lines": 1}, "columns": [{"name": "module_name", "display_name": "模块", "width": "20%", "data_type": "text"}, {"name": "executed_cases", "display_name": "执行数", "width": "14%", "data_type": "number"}, {"name": "pass_cases", "display_name": "通过数", "width": "14%", "data_type": "number"}, {"name": "fail_cases", "display_name": "未通过数", "width": "14%", "data_type": "number"}, {"name": "pass_rate", "display_name": "通过率", "width": "14%", "data_type": "text"}, {"name": "conclusion", "display_name": "测试结论", "width": "14%", "data_type": "options"}], "rows": []}, {"tag": "hr"}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试报告网盘路径：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "computer_filled", "color": "blue"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试问题网盘路径：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "mac_filled", "color": "orange"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "None", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}, {"tag": "hr"}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "8px", "horizontal_align": "left", "columns": [{"tag": "column", "width": "weighted", "vertical_align": "top", "vertical_spacing": "2px", "elements": [{"tag": "markdown", "content": "**测试问题汇总如下：**", "text_align": "left", "text_size": "normal", "icon": {"tag": "standard_icon", "token": "warning_filled", "color": "orange"}}, {"tag": "column_set", "flex_mode": "none", "horizontal_spacing": "default", "background_style": "default", "columns": [{"tag": "column", "elements": [{"tag": "div", "text": {"tag": "plain_text", "content": "1:【模块:版本信息】【子功能:版本信息】执行进入工程模式版本信息界面检查,期望1.SOC版本号与邮件发布版本号一致； \n2.MCU(Checksum)版本号与邮件发布版本号一致； \n3.VR版本号与邮件发布版本号一致；", "text_size": "normal", "text_align": "left", "text_color": "default"}}], "width": "weighted", "weight": 1}]}], "weight": 1}], "margin": "16px 0px 0px 0px"}]}, "i18n_header": {"zh_cn": {"title": {"tag": "plain_text", "content": "【${projectName} 功能自动化测试报告】"}, "subtitle": {"tag": "plain_text", "content": ""}, "template": "green"}}}}