<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<!DOCTYPE configuration>
<configuration>
    <!--读取配置中心的属性-->
    <!-- <springProperty scope="context" name="logPath" source="logging.path"/>-->
    <!-- name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义变量后，可以使“${}”来使用变量。 -->
    <property resource="properties/logback.properties"/>
    <!-- 定义日期格式属性 -->
    <timestamp key="bySecond" datePattern="yyyy-MM-dd_HH-mm-ss"/>
    <!-- 定义日志存放的基础路径 -->
    <property name="LOG_HOME" value="${log.base}"/>
    <!-- 定义每次启动的独立文件夹名 -->
    <property name="FOLDER_NAME" value="${LOG_HOME}/${bySecond}"/>

    <!-- 输出到控制台 -->
    <!-- %m输出的信息,%p日志级别,%t线程名,%d日期,%c类的全名,%-5level:级别从左显示5个字符宽度,%msg:日志消息,%i索引【从数字0开始递增】 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <withJansi>true</withJansi>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--%logger{50}:表示logger名字最长50个字符，否则按照句点分割-->
            <pattern>
                <![CDATA[%blue(%date{yyyy-MM-dd HH:mm:ss.SSS}) %green([%thread]) %highlight(%-5level) %boldMagenta(%replace(%logger){'(\w+\.)+(\w+)','$2',simpleName=true}):%L - %cyan(%msg%n)]]>
            </pattern>
            <!-- <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-6level[%thread]%logger{20}.%method\(\):%L -%msg%n</pattern>-->
            <!-- 设置字符集 -->
            <!-- <charset>GBK</charset>-->
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${console.level}</level>
        </filter>
    </appender>

    <!-- 输出到文件 -->
    <!-- 时间滚动输出 level为全部日志 -->
    <appender name="SYSTEM_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 正在记录的日志文件的路径及文件名，注释掉这个按照每天生成一个日志文件 -->
        <!--
           file属性表示原始的未经加工的日志文件名，若不为空，则在日志触发滚动事件时，会调用该方法
           boolean result = srcFile.renameTo(targetFile)将原始日志文件名重命名到归档文件名，
           然后再生成一个原始日志文件名作为当前生效的日志文件，
           然而renameTo方法在WIN系统上不保证成功，即result有可能为false，导致日志文件不能分割；
           这时候logback框架的RenameUtil类会给出这样的建议：
           "Please consider leaving the [file] option of " + RollingFileAppender.class.getSimpleName() + " empty."。
           而file属性为空，则不会执行renameTo方法，而是旧文件直接作为归档文件，然后生成新的日志文件来作为当前生效的日志文件，
           所以规避了renameTo方法的失败，使日志文件能够正确分割
       -->
        <file>${FOLDER_NAME}/${log.system.name}.log</file>
        <!--日志文件输出格式-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- 指定文件输出编码 -->
            <charset>UTF-8</charset>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-6level[%thread]%logger{56}.%method\(\):%L -%msg%n</pattern>
            <!--            <charset>GBK</charset>-->
        </encoder>
        <!-- 日志记录器的滚动策略，按日期，按大小记录 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--定义文件滚动时的文件名的格式-->
            <!-- 日志归档 后面可以加.zip-->
            <FileNamePattern>${FOLDER_NAME}/${log.system.name}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!--每个日志文件最大500MB-->
            <maxFileSize>500MB</maxFileSize>
            <!-- 最大归档文件个数 -->
            <!--            <maxHistory>60</maxHistory>-->
            <!-- 日志量最大2048G -->
            <!--            <totalSizeCap>2048GB</totalSizeCap>-->
            <!-- 日志文件保留天数 -->
            <!--            <maxHistory>180</maxHistory>-->
            <!--<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
            <!--&lt;!&ndash;文件达到 最大时会被压缩和切割 &ndash;&gt;-->
            <!--<maxFileSize>10MB</maxFileSize>-->
            <!--</timeBasedFileNamingAndTriggeringPolicy>-->
            <!--             日志总保存量为20GB-->
            <!--            <totalSizeCap>20GB</totalSizeCap>-->
        </rollingPolicy>
    </appender>

    <!-- 添加专门用于保存 com.desaysv 包下日志的 appender -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${FOLDER_NAME}/${log.app.name}.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <charset>UTF-8</charset>
            <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %-6level[%thread]%logger{56}.%method\(\):%L -%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${FOLDER_NAME}/${log.app.name}-%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
    </appender>

    <!-- 根日志记录器 -->
    <root>
        <level value="${log.level}"/>
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="SYSTEM_FILE"/>
    </root>

    <!-- 为 com.desaysv 包配置专门的 logger -->
    <logger name="com.desaysv" level="INFO" additivity="false">
        <appender-ref ref="APP_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="com.ulisesbocchio.jasyptspringboot" level="DEBUG" additivity="false">
        <!--        additivity="false"， 该属性为false时表示不继承root的配置属性-->
        <appender-ref ref="SYSTEM_FILE"/>
    </logger>

    <!-- 添加访问日志的特定logger配置 -->
    <!--    <logger name="com.desaysv.workserver.config.AccessLogConfig" level="INFO" additivity="false">-->
    <!--        <appender-ref ref="CONSOLE"/>-->
    <!--        <appender-ref ref="FILE"/>-->
    <!--        <appender-ref ref="DESAYSV_FILE"/>-->
    <!--    </logger>-->

    <!-- 将文件夹路径导出为系统属性 -->
    <contextName>${FOLDER_NAME}</contextName>
    <!--开发环境:打印控制台-->
    <!--        <springProfile name="dev">-->
    <!--            <logger name="com.desaysv" level="debug"/>-->
    <!--            <root>-->
    <!--                <level value="${log.level}"/>-->
    <!--                <appender-ref ref="stdout"/>-->
    <!--                <appender-ref ref="logfile"/>-->
    <!--            </root>-->
    <!--        </springProfile>-->

</configuration>