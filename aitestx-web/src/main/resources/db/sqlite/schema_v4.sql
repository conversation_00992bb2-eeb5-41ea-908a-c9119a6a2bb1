--
-- File generated with SQLiteStudio v3.4.4 on 周一 4月 29 17:11:18 2024
--
-- Text encoding used: UTF-8
--
PRAGMA foreign_keys = off;
BEGIN TRANSACTION;

-- Table: coordinates_roi
CREATE TABLE IF NOT EXISTS "coordinates_roi" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "device_type_id" INTEGER NOT NULL, "coordinates_uuid" TEXT(48) NOT NULL, "type_id" INTEGER, "start_x" REAL, "start_y" REAL, "end_x" REAL, "end_y" REAL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: department
CREATE TABLE IF NOT EXISTS "department" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "code" INTEGER NOT NULL, "name" TEXT(64) NOT NULL );

-- Table: excel_test_case
CREATE TABLE IF NOT EXISTS excel_test_case (id INTEGER UNIQUE NOT NULL PRIMARY KEY AUTOINCREMENT, uuid TEXT (100) NOT NULL, tableName TEXT (30) NOT NULL, initTestSequences TEXT (200), actionTestSequences TEXT (200), expectedTestSequences TEXT (200), actualResult TEXT (100), testResult TEXT (100), tester TEXT (20), testTime TEXT (30));

-- Table: functional_robot_coordinates
CREATE TABLE IF NOT EXISTS "functional_robot_coordinates" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "uuid" TEXT(48) NOT NULL, "name" TEXT(64) NOT NULL, "alias" TEXT(64), "func" INTEGER NOT NULL, "enable" INTEGER, "x" REAL NOT NULL, "y" REAL NOT NULL, "z" REAL NOT NULL, "r" REAL NOT NULL, "slide_rail" REAL, "project_id" INTEGER, "device_id" INTEGER NOT NULL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: original_picture
CREATE TABLE IF NOT EXISTS "original_picture" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "uuid" TEXT(48) NOT NULL, "width" INTEGER, "height" INTEGER, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: percent_template_roi
CREATE TABLE IF NOT EXISTS "percent_template_roi" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "type_id" INTEGER NOT NULL, "template_picture_uuid" TEXT(48) NOT NULL, "start_x" REAL, "start_y" REAL, "end_x" REAL, "end_y" REAL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: robot_coordinates
CREATE TABLE IF NOT EXISTS "robot_coordinates" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "uuid" TEXT(48) NOT NULL, "name" TEXT(64) NOT NULL, "x" REAL NOT NULL, "y" REAL NOT NULL, "z" REAL NOT NULL, "r" REAL NOT NULL, "slide_rail" REAL, "project_id" INTEGER, "device_id" INTEGER NOT NULL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: roi_type
CREATE TABLE IF NOT EXISTS "roi_type" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(24) NOT NULL );

-- Table: template_picture
CREATE TABLE IF NOT EXISTS "template_picture" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL, "template_picture_uuid" TEXT(48) NOT NULL, "original_picture_uuid" TEXT(48) DEFAULT NULL, "device_type_id" INTEGER NOT NULL, "device_index" INTEGER NOT NULL DEFAULT 1, "device_id" INTEGER NOT NULL, "project_id" INTEGER NOT NULL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: test_case
CREATE TABLE IF NOT EXISTS "test_case" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "testcase_uuid" TEXT(48) NOT NULL, "module_name" TEXT(64), "testcase_name" TEXT(64) NOT NULL, "precondition" TEXT, "operational_step" TEXT, "expectation_result" TEXT, "testsuite_uuid" TEXT(48) NOT NULL, "testresult_uuid" TEXT(48) NOT NULL, "is_pass" INTEGER NOT NULL, "testing" INTEGER NOT NULL, "begin_test_time" TEXT NOT NULL, "end_test_time" TEXT NOT NULL, "create_time" TEXT, "update_time" TEXT );

-- Table: test_client
CREATE TABLE IF NOT EXISTS "test_client" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL );

-- Table: test_device
CREATE TABLE IF NOT EXISTS "test_device" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL, "port" INTEGER, "baud_rate" INTEGER, "type_id" INTEGER, "model_id" INTEGER, "alias_name" TEXT(64), "unique_code" TEXT(256) NOT NULL, "parameter" TEXT, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: test_device_model
CREATE TABLE IF NOT EXISTS "test_device_model" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL );

-- Table: test_device_type
CREATE TABLE IF NOT EXISTS "test_device_type" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL );

-- Table: test_machine
CREATE TABLE IF NOT EXISTS "test_machine" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: test_project
CREATE TABLE IF NOT EXISTS "test_project" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL, "model" TEXT(64), "status" INTEGER DEFAULT 0, "customer_id" INTEGER, "department_id" INTEGER, "project_info_id" INTEGER, "communal" INTEGER DEFAULT 0, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: test_result
CREATE TABLE IF NOT EXISTS "test_result" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "uuid" TEXT(48) NOT NULL, "sum_cycle" INTEGER NOT NULL, "test_cycle" INTEGER NOT NULL, "fail_cycle" INTEGER NOT NULL, "summary" TEXT, "create_time" TEXT, "update_time" TEXT );

-- Table: test_suite
CREATE TABLE IF NOT EXISTS "test_suite" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "uuid" TEXT(48) NOT NULL, "test_project_name" TEXT(64) NOT NULL, "testsuite_name" TEXT(128) NOT NULL, "hardware_version" TEXT(150), "software_version" TEXT(150), "begin_test_time" TEXT NOT NULL, "end_test_time" TEXT NOT NULL, "create_time" TEXT, "update_time" TEXT );

-- Table: testcase_file
CREATE TABLE IF NOT EXISTS "testcase_file" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "selected" INTEGER, "uuid" TEXT(48) NOT NULL, "module_name" TEXT(64), "case_name" TEXT(64) NOT NULL, "comment" TEXT(64), "project_id" INTEGER, "client_id" INTEGER, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: tester
CREATE TABLE IF NOT EXISTS "tester" ( "id" INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, "name" TEXT(64) NOT NULL, "password" TEXT(256), "status" INTEGER DEFAULT 0, "email" TEXT(64), "phone" TEXT(50), "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Table: tester_test_project
CREATE TABLE IF NOT EXISTS "tester_test_project" ( "tester_id" INTEGER NOT NULL, "project_id" INTEGER NOT NULL, "create_time" TEXT NOT NULL, "update_time" TEXT NOT NULL );

-- Index: coordinates_roi_device_type_id_coordinates_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "coordinates_roi_device_type_id_coordinates_uuid" ON "coordinates_roi" ("device_type_id" ASC, "coordinates_uuid" ASC);

-- Index: department_code
CREATE UNIQUE INDEX IF NOT EXISTS "department_code" ON "department" ("code" ASC);

-- Index: functional_robot_coordinates_name_project_id_device_id
CREATE UNIQUE INDEX IF NOT EXISTS "functional_robot_coordinates_name_project_id_device_id" ON "functional_robot_coordinates" ("name" ASC, "project_id" ASC, "device_id" ASC);

-- Index: functional_robot_coordinates_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "functional_robot_coordinates_uuid" ON "functional_robot_coordinates" ("uuid" ASC);

-- Index: original_picture_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "original_picture_uuid" ON "original_picture" ("uuid" ASC);

-- Index: robot_coordinates_name_project_id_device_id
CREATE UNIQUE INDEX IF NOT EXISTS "robot_coordinates_name_project_id_device_id" ON "robot_coordinates" ("name" ASC, "project_id" ASC, "device_id" ASC);

-- Index: robot_coordinates_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "robot_coordinates_uuid" ON "robot_coordinates" ("uuid" ASC);

-- Index: roi_type_name
CREATE UNIQUE INDEX IF NOT EXISTS "roi_type_name" ON "roi_type" ("name" ASC);

-- Index: template_picture_name_project_id_device_id
CREATE UNIQUE INDEX IF NOT EXISTS "template_picture_name_project_id_device_id" ON "template_picture" ("name" ASC, "project_id" ASC, "device_id" ASC);

-- Index: template_picture_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "template_picture_uuid" ON "template_picture" ("template_picture_uuid" ASC);

-- Index: test_case_module_name_testcase_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_case_module_name_testcase_name" ON "test_case" ("module_name" ASC, "testcase_name" ASC, "testsuite_uuid" ASC);

-- Index: test_case_test_result_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "test_case_test_result_uuid" ON "test_case" ("testresult_uuid" ASC);

-- Index: test_client_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_client_name" ON "test_client" ("name" ASC);

-- Index: test_device_model_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_device_model_name" ON "test_device_model" ("name" ASC);

-- Index: test_device_type_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_device_type_name" ON "test_device_type" ("name" ASC);

-- Index: test_device_unique_code
CREATE UNIQUE INDEX IF NOT EXISTS "test_device_unique_code" ON "test_device" ("unique_code" ASC);

-- Index: test_machine_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_machine_name" ON "test_machine" ("name" ASC);

-- Index: test_project_name_model
CREATE UNIQUE INDEX IF NOT EXISTS "test_project_name_model" ON "test_project" ("name" ASC, "model" ASC);

-- Index: test_result_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "test_result_uuid" ON "test_result" ("uuid" ASC);

-- Index: test_suite_test_project_name_testsuite_name
CREATE UNIQUE INDEX IF NOT EXISTS "test_suite_test_project_name_testsuite_name" ON "test_suite" ("test_project_name" ASC, "testsuite_name" ASC);

-- Index: test_suite_uuid
CREATE UNIQUE INDEX IF NOT EXISTS "test_suite_uuid" ON "test_suite" ("uuid" ASC);

-- Index: testcase_file_module_name_case_name_project_id_client_id
CREATE UNIQUE INDEX IF NOT EXISTS "testcase_file_module_name_case_name_project_id_client_id" ON "testcase_file" ("module_name" ASC, "case_name" ASC, "project_id" ASC, "client_id" ASC);

-- Index: tester_name
CREATE UNIQUE INDEX IF NOT EXISTS "tester_name" ON "tester" ("name" ASC);

-- Index: tester_test_project_tester_id_project_id
CREATE UNIQUE INDEX IF NOT EXISTS "tester_test_project_tester_id_project_id" ON "tester_test_project" ("tester_id" ASC,"project_id" ASC);

COMMIT TRANSACTION;
PRAGMA foreign_keys = on;
