@echo on

set SQLITE_PATH=sqlite3
set DATABASE_PATH="D:\FlyTest\db\app\app.db"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS test_project"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS original_picture_name"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS coordinates_roi_device_type_id_coordinates_id"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS template_picture_name_project_id"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS template_picture_uuid"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS template_picture"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS original_picture"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS percent_template_roi";

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS robot_coordinates_name_project_id_device_model_id"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS functional_robot_coordinates_name_project_id_device_model_id"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS functional_robot_coordinates"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS robot_coordinates"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS coordinates_roi"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP INDEX IF EXISTS test_case_test_result_id"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS test_suite"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS test_case"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS test_result"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS tester"
"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS testcase_file"

"%SQLITE_PATH%" "%DATABASE_PATH%" "DROP TABLE IF EXISTS test_project"

pause
