server:
  port: 12399
  shutdown: graceful
  tomcat:
    connection-timeout: 30s
  servlet:
    context-path: /AITestX
#  undertow:
#    websockets:
#      idle-timeout: 0  # 设置Undertow的WebSocket空闲超时为永不超时


springdoc:
  api-docs:
    enabled: true
  #    path: /api-docs
  swagger-ui:
    enabled: true
#    path: /swagger-ui.html

logging:
  config: classpath:logback-spring.xml
  level:
    com:
      desaysv:
        workserver: debug

healthCheck:
  enabled: false

python:
  import:
    site: false

constants:
  web:
    polytest-email-url: http://************:8019

spring:
  main:
    lazy-initialization: false #TODO：待测试
    banner-mode: off
  profiles:
    active: prod
  lifecycle:
    timeout-per-shutdown-phase: 5s
  thymeleaf:
    cache: false
    mode: HTML
  devtools:
    restart:
      enabled: false
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.163.com
    # 发送者邮箱
    username: <EMAIL>
    # 配置密码，注意不是真正的密码，而是刚刚申请到的授权码
    password: ENC(b4IT1y0hFlajLtTzJ2ugQWFjl81TWFIH)
    #指定协议
    protocol: smtp
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        #        display.sendmail: <EMAIL> #可以任意
        display.sendname: ptvautotest #可以任意
        smtp:
          ssl:
            enable: true
          auth: true # 使用
          port: 465  # 端口号465或587
          starttls: # 使用 SSL 安全协议，须如下配置
            enable: true
            required: true
        #表示开启 DEBUG 模式，这样，邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: false

appConfig:
  mockEnabled: false

mybatis:
  mapper-locations: classpath:mybatis/mapper/*.xml
  #  config-location: classpath:mybatis/mybatis-config.xml
  #  typeAliasesPackage: com.desaysv.workserver.dao
  configuration:
    # 开启驼峰转换
    mapUnderscoreToCamelCase: true
    # 缓存开启
    cache-enabled: true
    # 懒加载开启
    lazy-loading-enabled: true
  # SQL打印日志所使用的日志类 - 关键
#  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jasypt:
  encryptor:
    password: Amazing&Leading~AITestXServer
    algorithm: PBEWithMD5AndDES
    # 3.0.0版本及以上版本需要添加如下配置
    iv-generator-classname: org.jasypt.iv.NoIvGenerator

---
spring:
  #生产环境
  config:
    activate:
      on-profile: prod
  datasource:
    #    username: admin
    #    password:
    #    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: ***************************************************************************************************************************
    #    schema:
    #      - classpath:db/mysql/V0_initDB.sql
    url: ****************************************************************** HH:mm:ss
    driver-class-name: org.sqlite.JDBC
  sql:
    init:
      schema-locations: classpath:db/sqlite/schema_v8.sql
      mode: always
      continue-on-error: false

    hikari:
      # 连接池中允许的最小空闲连接数
      minimum-idle: 5
      # 连接池中最大连接数，包括闲置和使用中的连接
      maximum-pool-size: 10
      # 空闲连接最大存活时间，超时后会被释放
      idle-timeout: 600000  # 10分钟，默认10分钟
      # 连接超时时间，超过这个时长还没可用的连接则发生SQLException
      # 连接最大存活时间，超时后会被释放（retired）
      max-lifetime: 1800000  # 30分钟，默认是30分钟
      # 等待连接池分配连接的最大时长
      connection-timeout: 30000  # 30秒
      pool-name: HikariCP
#  jpa:
#    properties:
#      hibernate:
#        format_sql: true
#        enable_lazy_load_no_trans: true
#    show-sql: false

constants:
  web:
    polytest-url: http://10.219.9.19:8019  #prod


---
spring:
  # 开发环境
  config:
    activate:
      on-profile: dev
  datasource:
    #    username: root
    #    password:
    #    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: **********************************************************************************************************************
    url: ****************************************************************** HH:mm:ss
    driver-class-name: org.sqlite.JDBC
  sql:
    init:
      schema-locations: classpath:db/sqlite/schema_v7.sql
      mode: always
      continue-on-error: false

healthCheck:
  enabled: false

constants:
  web:
    polytest-url: http://10.219.9.26:8019 #dev 沙箱
#    polytest-url: http://************:8019 #dev

#appConfig:
#  mockEnabled: false #动态配置

#mybatis:
#  configuration:
#    # SQL打印日志所使用的日志类 - 关键
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl