<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>功能序列化测试报告</title>
    <style>
        .header {
            background-color: #d3d3d3; /* 灰色 */
        }
        .summary {
            background-color: #87CEEB; /* 蓝色 */
        }
        .highlight {
            color: red; /* 高亮文本颜色 */
            background-color: yellow; /* 黄色 */
        }
    </style>
</head>
<body>
<p>各位好：</p>
<p><span th:text="${project}">XXX项目</span> <span th:text="${version}"></span> 版本功能序列化测试已完成，PASS通过率 <span th:text="${passRate}"></span>，<span class="highlight">详情查看附件！</span></p>
<p>测试报告路径: <a th:href="${reportPath}">点击这里查看测试报告</a></p>
<p>测试开始时间: <span th:text="${startTime}"></span></p>
<p>测试结束时间: <span th:text="${endTime}"></span></p>
<p>SOC版本: <span th:text="${socVersion}">X</span></p>
<p>MCU版本: <span th:text="${mcuVersion}">X</span></p>
<p>QNX版本: <span th:text="${qnxVersion}">X</span></p>

<table border="1" cellspacing="0" cellpadding="5">
    <thead>
    <tr class="header">
        <th>序号</th>
        <th>模块</th>
        <th>执行测试案例</th>
        <th>通过数</th>
        <th>不通过数</th>
        <th>通过率</th>
        <th>测试结论</th>
    </tr>
    </thead>
    <tbody>
    <tr th:each="result, stat : ${testResults}">
        <td th:text="${stat.index + 1}"></td>
        <td th:text="${result.moduleName}"></td>
        <td th:text="${result.executedCases}"></td>
        <td th:text="${result.passCases}"></td>
        <td th:text="${result.failCases}"></td>
        <td th:text="${result.passRate}"></td>
        <td th:text="${result.conclusion}"></td>
    </tr>
    <tr class="summary">
        <td>汇总</td>
        <td>全功能</td>
        <td th:text="${totalCases}"></td>
        <td th:text="${totalPass}"></td>
        <td th:text="${totalFail}"></td>
        <td th:text="${overallPassRate}"></td>
        <td th:text="${totalConclusion}"></td>
    </tr>
    </tbody>
</table>
</body>
</html>
