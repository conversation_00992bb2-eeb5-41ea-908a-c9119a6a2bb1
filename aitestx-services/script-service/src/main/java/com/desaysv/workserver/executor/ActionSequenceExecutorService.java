package com.desaysv.workserver.executor;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.SharedConfig;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.context.ActionSequenceContext;
import com.desaysv.workserver.context.TestStep;
import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceExecutionException;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceStopException;
import com.desaysv.workserver.filemanager.project.ActionSequenceTestConfig;
import com.desaysv.workserver.model.ExcelCaseModel;
import com.desaysv.workserver.parser.ActionSequenceGroup;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.result.ActionSequenceCheckReporter;
import com.desaysv.workserver.result.ActionSequenceCheckResults;
import com.desaysv.workserver.result.ActionSequenceCheckService;
import com.desaysv.workserver.result.ActionSequenceResultSet;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.ExcelCaseLoggerUtils;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

/**
 * 动作序列执行服务 - 负责管理和执行动作序列的核心服务类
 * 提供异步执行、单步执行、暂停/恢复/停止等控制功能
 */
@Service
@Slf4j
@Lazy
public class ActionSequenceExecutorService {
    @Setter
    private ActionSequenceTestConfig actionSequenceTestConfig;

    @Autowired
    private ActionSequenceExecutorFactory actionSequenceExecutorFactory;
    private final ActionSequenceExecutorHandlerImpl actionSequenceExecutorHandler;
    private final ActionSequenceExecutorHandlerImpl singleActionSequenceExecutorHandler;

    private Future<?> executorFuture;
    private Future<?> singleActionExecutorFuture;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final ExecutorService singleActionExecutorService = Executors.newSingleThreadExecutor();

    private ActionSequenceExecutorListener globalActionSequenceExecutorListener;
    private ActionSequenceExecutorListener singleActionSequenceExecutorListener;
    @Getter
    private ActionSequenceContext actionSequenceContext;
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");

    @Autowired
    public ActionSequenceExecutorService(
            @Qualifier("actionSequenceExecutor") ActionSequenceExecutorHandlerImpl actionSequenceExecutor,
            @Qualifier("singleActionSequenceExecutor") ActionSequenceExecutorHandlerImpl singleActionSequenceExecutor) {
        this.actionSequenceExecutorHandler = actionSequenceExecutor;
        this.singleActionSequenceExecutorHandler = singleActionSequenceExecutor;
    }


    /**
     * 设置执行上下文
     *
     * @param executionContext 执行上下文
     */
    public void setExecutionContext(ExecutionContext executionContext) {
        actionSequenceExecutorFactory.setExecutionContext(executionContext);
    }

    /**
     * 全局动作序列执行监听器 - 负责处理执行结果、检查结果和异常
     * 通过SSE实现前端实时状态更新
     */
    public void startExecute() {
        if (globalActionSequenceExecutorListener == null) {
            globalActionSequenceExecutorListener = new ActionSequenceExecutorListener() {
                @Override
                public void cycleExecuteResult(ActionSequenceUnit actionSequence) {
                    SseUtils.pubMsg(SseConstants.TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID, JSON.toJSONString(actionSequence));
                    boolean executeOk = actionSequence.isExecuteOk();
                    if (!executeOk) {
                        //通知前端暂停状态
                        log.info("cycleExecuteResult：通知前端暂停状态");
                        SseUtils.pubMsg(SseConstants.EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID, "testFailed");
                        if (actionSequenceContext.isPressureMode() && actionSequenceContext.isPauseWhenTestFailed()) {
                            //暂停后端动作序列执行
                            pauseExecute();
                        }
                    }
                }

                @Override
                public void cycleCheckResult(ActionSequenceResultSet actionSequenceResultSet, ActualExpectedResult actualExpectedResult) {
                    boolean resultPassed = actualExpectedResult.isPass();
                    actionSequenceResultSet.addSumCycle();
                    if (!resultPassed) {
                        actionSequenceResultSet.addFailCycle();
                    }
                    if (actionSequenceResultSet.isPass()) {
                        //测试判断一直都是通过，更新测试通过的最新数据
                        actionSequenceResultSet.addNewlyTestData(actualExpectedResult);
                    } else {
                        //测试判断有失败的数据，添加最近一次失败数据
                        if (!resultPassed) {
                            //失败数据
                            actionSequenceResultSet.addNewlyTestData(actualExpectedResult);
                        }
                    }
                    log.info("期望结果序列检查结果：{}", actionSequenceResultSet);
                    SseUtils.pubMsg(SseConstants.CHECK_RESULT_SUBSCRIBE_ID, actionSequenceResultSet.toString());
                }

                @Override
                public void cycleException(Throwable e) {
                    //FIXME
//                actionSequenceResultSet.addSumCycle();
//                actionSequenceResultSet.exception();
                    //通知前端暂停状态
                    log.info("cycleException：通知前端暂停状态");
                    SseUtils.pubMsg(SseConstants.EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID, "testFailed");
                    if (actionSequenceContext.isPressureMode() && actionSequenceContext.isPauseWhenTestFailed()) {
                        //暂停后端动作序列执行
                        pauseExecute();
                    }
                }
            };
        }
    }

    /**
     * 开始执行
     */
    public void startSingleExecute() {
        if (singleActionSequenceExecutorListener == null) {
            singleActionSequenceExecutorListener = new ActionSequenceExecutorListener() {
                @Override
                public void cycleExecuteResult(ActionSequenceUnit actionSequence) {
                    SseUtils.pubMsg(SseConstants.TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID, JSON.toJSONString(actionSequence));
//                    if (!executeOk) {
//                        SseUtil.pubMsg(SseConstants.STATUS_SUBSCRIBE_ID, "testFailed");
//                        if (actionSequenceContext.isPressureMode() && actionSequenceContext.isPauseWhenTestFailed()) {
//                            pauseExecute();
//                        }
//                    }
                }

                @Override
                public void cycleCheckResult(ActionSequenceResultSet actionSequenceResultSet, ActualExpectedResult actualExpectedResult) {
                    boolean resultPassed = actualExpectedResult.isPass();
                    actionSequenceResultSet.addSumCycle();
                    if (!resultPassed) {
                        actionSequenceResultSet.addFailCycle();
                    }
                    if (actionSequenceResultSet.isPass()) {
                        //测试判断一直都是通过，更新测试通过的最新数据
                        actionSequenceResultSet.addNewlyTestData(actualExpectedResult);
                    } else {
                        //测试判断有失败的数据，添加最近一次失败数据
                        if (!resultPassed) {
                            //失败数据
                            actionSequenceResultSet.addNewlyTestData(actualExpectedResult);
                        }
                    }
                    log.info("序列期望检查结果：{}", actionSequenceResultSet);
                    SseUtils.pubMsg(SseConstants.CHECK_RESULT_SUBSCRIBE_ID, actionSequenceResultSet.toString());
                }

                @Override
                public void cycleException(Throwable e) {
                    //FIXME
//                actionSequenceResultSet.addSumCycle();
//                actionSequenceResultSet.exception();
//                    SseUtil.pubMsg(SseConstants.STATUS_SUBSCRIBE_ID, "testFailed");
//                    if (actionSequenceContext.isPressureMode() && actionSequenceContext.isPauseWhenTestFailed()) {
//                        pauseExecute();
//                    }
                }
            };
        }
    }


    /**
     * 暂停执行
     */
    public void pauseExecute() {
        actionSequenceExecutorHandler.pauseImmediately();
    }

    /**
     * 恢复执行
     */
    public void resumeExecute() {
        actionSequenceExecutorHandler.resume();
    }

    /**
     * 停止执行
     */
    public void stopExecute() {
        actionSequenceExecutorHandler.stopImmediately();
        if (executorFuture != null && !executorFuture.isCancelled() && !executorFuture.isDone()) {
            executorFuture.cancel(true);
        }
    }


    /**
     * 停止执行
     */
    public void stopSingleActionExecute() {
        singleActionSequenceExecutorHandler.stopImmediately();
        if (singleActionExecutorFuture != null && !singleActionExecutorFuture.isCancelled() && !singleActionExecutorFuture.isDone()) {
            singleActionExecutorFuture.cancel(true);
        }
    }


    /**
     * 异步执行
     *
     * @param actionSequenceContext       动作序列上下文
     * @param actionSequenceCheckReporter 测试结果报告
     */
    public void executeAsync(ActionSequenceContext actionSequenceContext, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        this.actionSequenceContext = actionSequenceContext;
        
        // 准备异步执行上下文
        RequestAttributes attributes = prepareAsyncRequestAttributes(actionSequenceContext.getProjectName());
        
        //FIXME: bug：上一次没执行完任务，前端再次执行，submit无法提交
        try {
            executorFuture = executorService.submit(() -> {
                try {
                    // 在异步线程中设置请求属性
                    RequestContextHolder.setRequestAttributes(attributes);
                    //打印当前线程
                    log.info("动作序列执行当前线程：{}", Thread.currentThread().getName());
                    execute(actionSequenceContext, actionSequenceCheckReporter, actionSequenceExecutorHandler, globalActionSequenceExecutorListener);
                } finally {
                    // 释放RequestAttributes，防止内存泄漏
                    RequestContextHolder.resetRequestAttributes();
                    log.debug("已释放异步线程的RequestAttributes");
                }
            });
            executorFuture.get();
        } catch (InterruptedException | CancellationException | ExecutionException e) {
            if (!executorFuture.isCancelled()) {
                if (e.getCause() instanceof IllegalArgumentException) {
                    // 处理IllegalArgumentException异常
                    log.warn("捕获到IllegalArgumentException异常：{}", ExceptionUtils.getExceptionString(e.getCause()));
                } else {
                    // 处理其他异常
                    log.warn("捕获到其他异常：{}", ExceptionUtils.getExceptionString(e.getCause()));
                }
            }
        }
    }

    /**
     * 异步执行单步调试
     *
     * @param actionSequenceContext       动作序列上下文
     * @param actionSequenceCheckReporter 测试结果报告
     */
    public void singleActionExecuteAsync(ActionSequenceContext actionSequenceContext, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        // 准备异步执行上下文
        RequestAttributes attributes = prepareAsyncRequestAttributes(actionSequenceContext.getProjectName());
        
        // 设置项目projectName属性
        singleActionExecutorFuture = singleActionExecutorService.submit(() -> {
            try {
                // 在异步线程中设置请求属性
                RequestContextHolder.setRequestAttributes(attributes);
                log.info("动作序列单步调试当前线程：{}", Thread.currentThread().getName());
                execute(actionSequenceContext, actionSequenceCheckReporter, singleActionSequenceExecutorHandler, singleActionSequenceExecutorListener);
            } finally {
                // 释放RequestAttributes，防止内存泄漏
                RequestContextHolder.resetRequestAttributes();
                log.debug("已释放异步线程的RequestAttributes");
            }
        });
        try {
            singleActionExecutorFuture.get();
        } catch (InterruptedException | CancellationException | ExecutionException e) {
            if (!singleActionExecutorFuture.isCancelled()) {
                if (e.getCause() instanceof IllegalArgumentException) {
                    // 处理IllegalArgumentException异常
                    log.warn("捕获到IllegalArgumentException异常：{}", ExceptionUtils.getExceptionString(e.getCause()));
                } else {
                    // 处理其他异常
                    log.warn("捕获到其他异常：{}", ExceptionUtils.getExceptionString(e.getCause()));
                }
            }
        }
    }

    /**
     * 准备异步执行的RequestAttributes，设置项目名到共享配置中
     * 
     * @param projectName 项目名称
     * @return 包含项目配置的RequestAttributes
     */
    private RequestAttributes prepareAsyncRequestAttributes(String projectName) {
        // 获取当前请求属性
        RequestAttributes attributes = RequestContextHolder.currentRequestAttributes();
        SharedConfig sharedConfig = SharedConfig.getInstance();
        sharedConfig.setProjectName(projectName);
        attributes.setAttribute(SharedConfig.class.getSimpleName(), sharedConfig, RequestAttributes.SCOPE_REQUEST);
        return attributes;
    }

    /**
     * 执行动作序列的核心方法
     * 根据动作序列类型(前置条件/操作步骤/期望结果)执行对应的动作序列列表
     */
    private void execute(ActionSequenceContext actionSequenceContext,
                         ActionSequenceCheckReporter actionSequenceCheckReporter,
                         ActionSequenceExecutorHandlerImpl actionSequenceExecutorHandler,
                         ActionSequenceExecutorListener actionSequenceExecutorListener) {
        try {
            ActionSequenceCheckResults actionSequenceCheckResults = actionSequenceCheckReporter.getActualResult();
            actionSequenceCheckResults.clear();
            actionSequenceExecutorHandler.startExecute(actionSequenceContext);
            ActionSequenceExecutorContext executorContext = new ActionSequenceExecutorContext();
            executorContext.setSimulated(actionSequenceContext.isSimulated());
            List<ActionSequence> expectedActionSequenceList = actionSequenceCheckReporter.getExpectResultCheckResult() != null ?
                    actionSequenceCheckReporter.getExpectResultCheckResult().getActionSequenceList() : new ArrayList<>();
            executorContext.setExpectedActionSequenceList(expectedActionSequenceList);
            executorContext.setActionSequenceCheckResults(actionSequenceCheckResults);
            if (actionSequenceContext.getSequenceType() == ActionSequenceContext.PRECONDITION) {
                execute(actionSequenceCheckReporter.getPreconditionCheckResult().getActionSequenceList(), executorContext, actionSequenceExecutorHandler, actionSequenceExecutorListener);
            } else if (actionSequenceContext.getSequenceType() == ActionSequenceContext.OPERATION_STEP) {
                execute(actionSequenceCheckReporter.getOperationStepCheckResult().getActionSequenceList(), executorContext, actionSequenceExecutorHandler, actionSequenceExecutorListener);
            } else if (actionSequenceContext.getSequenceType() == ActionSequenceContext.EXPECT_RESULT) {
                execute(actionSequenceCheckReporter.getExpectResultCheckResult().getActionSequenceList(), executorContext, actionSequenceExecutorHandler, actionSequenceExecutorListener);
            } else {
                //执行整行案例
                execute(actionSequenceCheckReporter.getPreconditionCheckResult().getActionSequenceList(), executorContext, actionSequenceExecutorHandler, actionSequenceExecutorListener);
                if (!executorContext.isExecutionFailed()) { // 只有前置条件成功才执行操作步骤
                    execute(actionSequenceCheckReporter.getOperationStepCheckResult().getActionSequenceList(), executorContext, actionSequenceExecutorHandler, actionSequenceExecutorListener);
                }
            }
            // 统一处理失败状态
            if (executorContext.isExecutionFailed()) {
                actionSequenceCheckReporter.setErrorMessage("存在关键步骤执行失败,余下步骤跳过执行！");
            }
        } catch (ActionSequenceExecutionException e) {
            log.warn(e.getMessage(), e);
            actionSequenceCheckReporter.setErrorMessage(e.getMessage());
        } catch (ActionSequenceStopException e) {
            actionSequenceCheckReporter.setTerminated(true);
            log.info(e.getMessage(), e);
        }
        ActionSequencesLoggerUtil.info("单行动作序列集合测试已完成:{}", actionSequenceContext.getRow() + 1);
    }

    /**
     * 执行动作序列集合
     *
     * @param actionSequenceList            动作序列集合
     * @param actionSequenceExecutorContext 执行动作序列上下文
     * @throws ActionSequenceExecutionException
     * @throws ActionSequenceStopException
     */
    public void execute(List<ActionSequence> actionSequenceList,
                        ActionSequenceExecutorContext actionSequenceExecutorContext,
                        ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                        ActionSequenceExecutorListener actionSequenceExecutorListener) throws ActionSequenceExecutionException, ActionSequenceStopException {
        for (ActionSequence actionSequence : actionSequenceList) {
            // 检查是否已经失败
            if (actionSequenceExecutorContext.isExecutionFailed()) {
                break; // 终止执行
            }
            if (actionSequence instanceof ActionSequenceUnit) {
                boolean executedOk = actionSequenceExecutorFactory.execute(
                        (ActionSequenceUnit) actionSequence,
                        actionSequenceExecutorContext,
                        actionSequenceExecutorHandler,
                        actionSequenceExecutorListener,
                        null);
                if (!executedOk) {
                    actionSequenceExecutorContext.setExecutionFailed(true);
                    log.info("动作序列执行失败: {}", actionSequence.getRawExpression());
                    break;// 终止执行
                }
            } else {
                //group
                ActionSequenceGroup actionSequenceGroup = (ActionSequenceGroup) actionSequence;
                log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
                log.info("循环序列[{}]执行{}次", actionSequenceGroup.getRawExpression(), actionSequenceGroup.getLoopCycle());
                log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
                for (int cycle = 0; cycle < actionSequenceGroup.getLoopCycle(); cycle++) {
                    if (actionSequenceExecutorContext.isExecutionFailed()) {
                        break;// 终止执行
                    }
                    log.info("循环序列[{}]执行第{}次", actionSequenceGroup.getRawExpression(), cycle + 1);
                    execute(actionSequenceGroup.getActionSequenceGroups(), actionSequenceExecutorContext,
                            actionSequenceExecutorHandler, actionSequenceExecutorListener);
                }
//                for (ActionSequence actionSequenceInTheGroup : actionSequenceGroup.getActionSequenceGroups()) {
//                    actionSequenceExecutorFactory.execute((ActionSequenceUnit) actionSequenceInTheGroup,
//                            actionSequenceExecutorContext, actionSequenceExecutorHandler, null);
//                }
            }
        }
    }

    /**
     * 检查动作序列正则表达式和结果序号语法
     *
     * @param actionSequence    待检查动作序列
     * @param resultCheckMarker 期望结果标记
     * @return 检查结果
     */
    public boolean check(ActionSequenceUnit actionSequence, List<String> resultCheckMarker) {
        return actionSequenceExecutorFactory.check(actionSequence, resultCheckMarker);
    }

    public ResultEntity<Object> processActionSequenceContext(ActionSequenceContext actionSequenceContext, ActionSequenceCheckService actionSequenceCheckService) {
        try {
            Map<String, Boolean> logScriptMap = needSetLogName(actionSequenceContext);
            if (!logScriptMap.isEmpty()) {
                ActionSequenceCheckReporter actionSequenceCheckReporter = setLogName(actionSequenceContext, actionSequenceCheckService, logScriptMap);
                if (actionSequenceCheckReporter != null && actionSequenceCheckReporter.isCheckOk() && actionSequenceCheckReporter.isExecuteOk()) {
                    return processCheckedContext(actionSequenceContext, actionSequenceCheckService);
                } else {
                    return ResultEntity.ok(actionSequenceCheckReporter.report());
                }
            } else {
                return processCheckedContext(actionSequenceContext, actionSequenceCheckService);
            }
        } catch (Exception e) {
            String error = String.format("处理序列过程发生错误:%s", e.getMessage());
            log.error(error, e);
            return ResultEntity.fail(error);
        }
    }

    public ResultEntity<Object> processSingleActionSequenceContext(ActionSequenceContext actionSequenceContext, ActionSequenceCheckService actionSequenceCheckService) {
        try {
            Map<String, Boolean> logScriptMap = needSetLogName(actionSequenceContext);
            if (!logScriptMap.isEmpty()) {
                ActionSequenceCheckReporter actionSequenceCheckReporter = setLogName(actionSequenceContext, actionSequenceCheckService, logScriptMap);
                if (actionSequenceCheckReporter != null && actionSequenceCheckReporter.isCheckOk() && actionSequenceCheckReporter.isExecuteOk()) {
                    return processCheckedContext(actionSequenceContext, actionSequenceCheckService);
                } else {
                    return ResultEntity.ok(actionSequenceCheckReporter.report());
                }
            } else {
                return processCheckedSingleActionContext(actionSequenceContext, actionSequenceCheckService);
            }
        } catch (Exception e) {
            String error = String.format("处理序列过程发生错误:%s", e.getMessage());
            log.error(error, e);
            return ResultEntity.fail(error);
        }
    }

    private ResultEntity<Object> processCheckedContext(ActionSequenceContext actionSequenceContext, ActionSequenceCheckService actionSequenceCheckService) {
        ActionSequenceCheckReporter actionSequenceCheckReporter = actionSequenceCheckService.checkActionSequenceGrammar(actionSequenceContext);
        //检查成功，就执行
        if (actionSequenceCheckReporter.isCheckOk()) {
            executeAsync(actionSequenceContext, actionSequenceCheckReporter);
        }
        logInfoAndReturnResult(actionSequenceContext, actionSequenceCheckReporter);
        return ResultEntity.ok(actionSequenceCheckReporter.report());
    }

    private ResultEntity<Object> processCheckedSingleActionContext(ActionSequenceContext actionSequenceContext, ActionSequenceCheckService actionSequenceCheckService) {
        ActionSequenceCheckReporter actionSequenceCheckReporter = actionSequenceCheckService.checkActionSequenceGrammar(actionSequenceContext);
        if (actionSequenceCheckReporter.isCheckOk()) {
            singleActionExecuteAsync(actionSequenceContext, actionSequenceCheckReporter);
        }
        logInfoAndReturnResult(actionSequenceContext, actionSequenceCheckReporter);
        return ResultEntity.ok(actionSequenceCheckReporter.report());
    }

    private void logInfoAndReturnResult(ActionSequenceContext actionSequenceContext, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        if (!actionSequenceContext.isSingleCase()) {
            ExcelCaseModel excelCaseModel = actionSequenceContext.getExcelCaseModel();
            if (excelCaseModel == null) return;
            int targetTestTimes = Integer.parseInt(excelCaseModel.getTargetTestTimes());
            int testedTimes = Integer.parseInt(excelCaseModel.getTestedTimes());
            int testedPassTimes = Integer.parseInt(excelCaseModel.getTestedPassTimes());
            testedPassTimes = actionSequenceCheckReporter.isExecuteOk() ? testedPassTimes + 1 : testedPassTimes;
            int failedTimes = testedTimes - testedPassTimes;
            ExcelCaseLoggerUtils.getLogger().info("失败率：{}/{}", failedTimes, targetTestTimes);
            ExcelCaseLoggerUtils.getLogger().info("结束单行脚本测试，用例:{}，第{}行", actionSequenceContext.getTableName(), actionSequenceContext.getRow() + 1);
            excelCaseModel.setTestedPassTimes(String.valueOf(testedPassTimes));
            actionSequenceContext.setExcelCaseModel(excelCaseModel);
            actionSequenceCheckReporter.setActionSequenceContext(actionSequenceContext);
            log.info("^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^");
        }
    }

    private Map<String, Boolean> needSetLogName(ActionSequenceContext actionSequenceContext) {
        Map<String, Boolean> logTypesMap = new LinkedHashMap<>();
        List<TestStep> preconditionList = actionSequenceContext.getPrecondition();
        List<TestStep> operationStepList = actionSequenceContext.getOperationStep();
        List<TestStep> expectResultList = actionSequenceContext.getExpectResult();
        List<TestStep> testSteps = new ArrayList<>();
        if (preconditionList != null) {
            testSteps.addAll(preconditionList);
        }
        if (operationStepList != null) {
            testSteps.addAll(operationStepList);
        }
        if (expectResultList != null) {
            testSteps.addAll(expectResultList);
        }
        for (TestStep testStep : testSteps) {
            String testStepStr = testStep.getTestStep();
            if (testStepStr.contains("-UDSLog-")) {
                logTypesMap.put("UDSLog", true);
            }
            if (testStepStr.contains("-CANLog-")) {
                logTypesMap.put("CANLog", true);
            }
            if (testStepStr.contains("-SetADSToolLog-")) {
                logTypesMap.put("SetADSToolLog", true);
            }
        }

        return logTypesMap;
    }

    private ActionSequenceCheckReporter setLogName(ActionSequenceContext actionSequenceContext, ActionSequenceCheckService actionSequenceCheckService, Map<String, Boolean> logScriptMap) {
        ActionSequenceContext actionSequenceContextRes = new ActionSequenceContext();
        ActionSequenceCheckReporter actionSequenceCheckReporter = null;
        for (Map.Entry<String, Boolean> entry : logScriptMap.entrySet()) {
            String fixFormat = entry.getKey().contains("UDSLog") ? "CAN-UDSLogName-" : entry.getKey().contains("SetADSToolLog") ? "ADSTool-SetLogName-" : "CAN-CANLogName-";
            ArrayList<TestStep> stepList = new ArrayList<>();
            String logName = String.format("%s_%s_%s", actionSequenceContext.getTableName(), actionSequenceContext.getTcId().replaceAll("-", "_"), sdf.format(new Date()));
            TestStep testStep = new TestStep();
            testStep.setTestStep(String.format("%s%s", fixFormat, logName));
            testStep.setType(ColumnNameConstants.getInstance().getPreconditionSequences());
            stepList.add(testStep);
            actionSequenceContextRes.setPrecondition(stepList);
            actionSequenceContextRes.setSequenceType(1);
            actionSequenceCheckReporter = actionSequenceCheckService.checkActionSequenceGrammar(actionSequenceContextRes);
            //全部动作序列检查通过才能执行
            if (actionSequenceCheckReporter.isCheckOk()) {
                executeAsync(actionSequenceContextRes, actionSequenceCheckReporter);
            }
            actionSequenceCheckReporter.isExecuteOk();
        }
        return actionSequenceCheckReporter;
    }
}


