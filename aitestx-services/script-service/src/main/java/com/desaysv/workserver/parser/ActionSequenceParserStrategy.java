package com.desaysv.workserver.parser;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.regex.ProcedureRegexRule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.SPLITTER;

/**
 * 动作序列解析策略接口
 */
public interface ActionSequenceParserStrategy {
    Logger log = LogManager.getLogger(ActionSequenceParserStrategy.class.getSimpleName());

    default ActionSequenceUnit filter(ActionSequenceUnit actionSequenceUnit) {
        return filterActionSequenceRepetitions(filterActionSequenceResultCycleChecker(filterActionSequenceResultChecker(actionSequenceUnit)));
    }

    /**
     * 过滤动作序列cycle结果检查序列部分
     *
     * @param actionSequenceUnit 动作序列
     * @return
     */
    default ActionSequenceUnit filterActionSequenceResultCycleChecker(ActionSequenceUnit actionSequenceUnit) {
        Pattern pattern = Pattern.compile(ProcedureRegexRule.CYCLE_CHECK_MULTI_LINES_WITH_TIME);
        String extraActionSequenceSentence = actionSequenceUnit.getExtraSequenceExpression();
        if (extraActionSequenceSentence != null) {
            Matcher matcher = pattern.matcher(extraActionSequenceSentence);
            if (matcher.find()) {
                String cycleTime = matcher.group(1);
                List<String> cycleCheckId = Arrays.asList(matcher.group(2).split(BaseRegexRule.SPLITTER));
                actionSequenceUnit.getOptions().setCycleTime(BaseRegexRule.getSecondsOfDefaultMills(cycleTime));
                actionSequenceUnit.getOptions().setCycleCheckResultIds(cycleCheckId);
//                extraActionSequenceSentence = extraActionSequenceSentence.replaceAll(BaseRegexRule.RegexRuleConstants.SINGLE_LINE_CHECK, "");
                actionSequenceUnit.setExtraSequenceExpression(extraActionSequenceSentence);
                log.info("动作序列捕捉到选项cycle:{}", actionSequenceUnit.getOptions());
            }
        }
        actionSequenceUnit.setRegexRuleApplyExpression(extraActionSequenceSentence == null ? "" : extraActionSequenceSentence);
        return actionSequenceUnit;
    }


    /**
     * 过滤动作序列结果检查序列部分
     *
     * @param actionSequenceUnit 动作序列
     * @return
     */
    default ActionSequenceUnit filterActionSequenceResultChecker(ActionSequenceUnit actionSequenceUnit) {
        Pattern pattern = Pattern.compile(ProcedureRegexRule.ONE_LINE_CHECK);
        String extraActionSequenceSentence = actionSequenceUnit.getExtraSequenceExpression();
        if (extraActionSequenceSentence != null) {
            Matcher matcher = pattern.matcher(extraActionSequenceSentence);
            if (matcher.find()) {
                List<String> checkId = Arrays.asList(matcher.group(1).split(SPLITTER));
                actionSequenceUnit.getOptions().setCheckResultIds(checkId);
                extraActionSequenceSentence = extraActionSequenceSentence.replaceAll(SPLITTER + ProcedureRegexRule.ONE_LINE_CHECK, "");
                actionSequenceUnit.setExtraSequenceExpression(extraActionSequenceSentence);
                log.info("动作序列捕捉到检测选项:{}", actionSequenceUnit.getOptions());
            }
        }
        actionSequenceUnit.setRegexRuleApplyExpression(extraActionSequenceSentence == null ? "" : extraActionSequenceSentence);
        return actionSequenceUnit;
    }

    /**
     * 过滤动作序列重复序列部分
     *
     * @param actionSequenceUnit 动作序列
     * @return
     */
    default ActionSequenceUnit filterActionSequenceRepetitions(ActionSequenceUnit actionSequenceUnit) {
        Pattern pattern = Pattern.compile(SPLITTER + BaseRegexRule.RegexRuleConstants.REPEAT_LINE);
        String extraActionSequenceSentence = actionSequenceUnit.getExtraSequenceExpression();
        if (extraActionSequenceSentence != null) {
            Matcher matcher = pattern.matcher(extraActionSequenceSentence);
            if (matcher.find()) {
                actionSequenceUnit.getOptions().setRepetitions(Integer.parseInt(matcher.group(1)));
                extraActionSequenceSentence = extraActionSequenceSentence.replaceAll(SPLITTER + BaseRegexRule.RegexRuleConstants.REPEAT_LINE, "");
                actionSequenceUnit.setExtraSequenceExpression(extraActionSequenceSentence);
                log.info("动作序列捕捉到重复选项:{}", actionSequenceUnit.getOptions());
            }
        }
        actionSequenceUnit.setRegexRuleApplyExpression(extraActionSequenceSentence == null ? "" : extraActionSequenceSentence);
        return actionSequenceUnit;
    }

    ActionSequenceUnit parse(ActionSequenceUnit actionSequenceUnit);

    static void main(String[] args) {
//        String regex = "[-——](?:\\b(?i)Check(?!\\+)\\b)[-——]([\\p{Script=Han}\\w]+)$";
//        String s = "1-手刹信号-check-1";
//        Pattern p = Pattern.compile(regex);
//        Matcher m = p.matcher(s);
//        if (m.find()) {
//            System.out.println(m.group(1));
//        }


//        Pattern pattern = Pattern.compile(WAIT_LINE);
//        Pattern pattern = Pattern.compile(RANDOM_WAIT_LINE);
        String patternRegex = "(?i)^wait-(\\d*\\.?\\d+(?:ms|s|min|h)?)-(.*)$";

        Pattern pattern = Pattern.compile(patternRegex);
        System.out.println();
        String extraActionSequenceSentence = "wait-1s-pwr-1-ON-KL30";
        Matcher matcher = pattern.matcher(extraActionSequenceSentence);
        if (matcher.find()) {
//                actionSequenceUnit.getOptions().setWaitTime(BaseRegexRule.getMilliSeconds(matcher.group(1)));
//                // StrUtils.removeSuffix
//                extraActionSequenceSentence = extraActionSequenceSentence.replaceAll(WAIT_LINE, "");
//                actionSequenceUnit.setExtraSequenceExpression(extraActionSequenceSentence);
        }
    }
}
