package com.desaysv.workserver.finder;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.ActionSequenceHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 动作序列执行工厂
 */
@Service
@Slf4j
@Lazy
public class ActionSequenceDeviceFinderFactory implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private BaseDeviceFindStrategy baseDeviceFindStrategy;
    private static final Map<String, DeviceFindStrategy> MAP = new HashMap<>();

    public OperationTarget findBySequenceDeviceName(ActionSequenceHeader actionSequenceHeader) {
        DeviceFindStrategy strategy = MAP.get(actionSequenceHeader.getExecutorName().toLowerCase());
        if (strategy == null) {
            strategy = baseDeviceFindStrategy;
        }
        return strategy.find(actionSequenceHeader);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // 获取bean工厂带LiveViewCode注解的service实例
        Map<String, Object> beansWithAnnotation = contextRefreshedEvent.getApplicationContext().getBeansWithAnnotation(DeviceFinder.class);
        beansWithAnnotation.forEach((key, value) -> {
            // 获取实现类注解里的type
//            ActionSequenceParser annotation = AnnotationUtils.findAnnotation(value.getClass(), ActionSequenceParser.class);
            // cglib代理无法获取到注解，这里需要使用spring自带的工具类来操作
            DeviceFinder annotation = value.getClass().getAnnotation(DeviceFinder.class);
            String[] sequenceDeviceNames = annotation.sequenceDeviceNames();
            boolean flag = annotation.flag();
            if (flag) {
                for (String sequenceDeviceName : sequenceDeviceNames) {
                    if (!MAP.containsKey(sequenceDeviceName)) {
                        MAP.put(sequenceDeviceName, (DeviceFindStrategy) value);
                    } else {
                        log.error("sequenceDeviceNames[{}]出现重复，请检查注解seqDeviceNames绑定", sequenceDeviceName);
                    }
                }
            } else {
                log.info("策略类[{}]标注失效，不注册策略工厂", value);
            }
        });
    }
}