package com.desaysv.workserver.finder;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.ActionSequenceHeader;
import com.desaysv.workserver.components.DeviceActionSequenceHeader;
import com.desaysv.workserver.components.SequenceDeviceKeywords;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@Lazy
@DeviceFinder(sequenceDeviceNames = {SequenceDeviceKeywords.POWER})
public class PowerDeviceFindStrategy extends BaseDeviceFindStrategy {

    @Override
    public OperationTarget find(ActionSequenceHeader actionSequenceHeader) {
        DeviceActionSequenceHeader deviceActionSequenceHeader = (DeviceActionSequenceHeader) actionSequenceHeader;
        Map<Integer, Device> deviceMap = Device.getDeviceInstancesMap().get(DeviceType.DEVICE_POWER);
        if (deviceMap != null) {
            return deviceMap.get(deviceActionSequenceHeader.getDeviceOrder());
        }
        return null;
    }
}
