package com.desaysv.workserver.executor.procedure;

import com.desaysv.workserver.annotation.RegexRule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IProcedure {

    Logger log = LogManager.getLogger(IProcedure.class.getSimpleName());

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ProcedureRegexRule).REPEAT_MULTI_LINES"})
    void repeatMultiLines(int startNo, int endNo, int repetitions);


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ProcedureRegexRule).ONE_LINE_CHECK"})
    void check(String checkMarker);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.ProcedureRegexRule).CYCLE_CHECK_MULTI_LINES_WITH_TIME"})
    void checkMultiLinesWithTime(String time,String checkMarker);

}
