package com.desaysv.workserver.executor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ActionSequenceExecutorConfig {

    @Bean(name = "actionSequenceExecutor")
    public ActionSequenceExecutorHandlerImpl actionSequenceExecutor() {
        return new ActionSequenceExecutorHandlerImpl();
    }

    @Bean(name = "singleActionSequenceExecutor")
    public ActionSequenceExecutorHandlerImpl singleActionSequenceExecutor() {
        return new ActionSequenceExecutorHandlerImpl();
    }
}