package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class SignalGeneratorRegexRule extends BaseRegexRule {
    // 1.Sig-1-SetSignalInput-4-5Vpp-10HZ-50%  ----1表示输出通道；4表示脉冲波形；5Vpp表示幅值；10HZ表示频率；50%表示占空比
//    public static final String SET_SIGNAL_INPUT = wholeCombine(RegexRuleConstants.SET_SIGNAL_INPUT_CONSTANTS, group(NUMBER), RegexRuleConstants.AMPLITUDE,
    // 1.Sig-1-SetSignalInput-10HZ-50%  ----1表示输出通道；10HZ表示频率；50%表示占空比，相比上一条，测试时只需要设置频率和占空比即可
    public static final String SET_SIGNAL_INPUT = wholeCombine(RegexRuleConstants.SET_SIGNAL_INPUT_CONSTANTS, RegexRuleConstants.AMPLITUDE,
            RegexRuleConstants.FREQUENCY, RegexRuleConstants.ANY_PERCENT);
    // 1.Sig-WaveForm-4  ----4表示脉冲波形；（1：正弦波；2：方波；3：任意波；4：脉冲波形；5：ARB；6：噪声波形）
    public static final String SET_WAVEFORM = wholeCombine(RegexRuleConstants.SET_WAVEFORM_CONSTANTS,  group(NUMBER));
    // 1.Sig-Freq-10Hz  设置频率10Hz
    public static final String SET_FREQUENCY = wholeCombine(RegexRuleConstants.SET_FREQUENCY_CONSTANTS, RegexRuleConstants.FREQUENCY);
    // 1.Sig-AMPL-5  设置幅值5Hz
    public static final String SET_AMPLITUDE = wholeCombine(RegexRuleConstants.SET_AMPLITUDE_CONSTANTS, RegexRuleConstants.AMPLITUDE);
    // 1.Sig-Duty-50%  设置占空比50%
    public static final String SET_DUTY_CYCLE = wholeCombine(RegexRuleConstants.SET_DUTY_CONSTANTS, RegexRuleConstants.ANY_PERCENT);
}
