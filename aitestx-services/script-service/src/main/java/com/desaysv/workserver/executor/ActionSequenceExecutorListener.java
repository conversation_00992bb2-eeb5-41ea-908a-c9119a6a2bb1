package com.desaysv.workserver.executor;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.result.ActionSequenceResultSet;

public interface ActionSequenceExecutorListener {

    void cycleExecuteResult(ActionSequenceUnit actionSequence);

    void cycleCheckResult(ActionSequenceResultSet actionSequenceResultSet, ActualExpectedResult actualExpectedResult);

    void cycleException(Throwable e);

}
