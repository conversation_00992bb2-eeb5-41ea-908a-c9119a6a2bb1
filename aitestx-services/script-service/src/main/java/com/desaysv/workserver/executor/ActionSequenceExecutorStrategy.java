package com.desaysv.workserver.executor;

import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceExecutionException;
import com.desaysv.workserver.exceptions.action_sequence.ActionSequenceStopException;
import com.desaysv.workserver.result.ActionSequenceResultSet;

import java.util.List;

/**
 * 动作序列执行器接口
 */
public interface ActionSequenceExecutorStrategy {

    /**
     * 语法检查
     *
     * @param actionSequence    动作序列
     * @param resultCheckMarker 期望结果检查标记（期望结果，如1、2、3）
     * @return
     */
    boolean check(ActionSequenceUnit actionSequence, List<String> resultCheckMarker);

    /**
     * 执行动作序列
     *
     * @param actionSequence                 动作序列
     * @param actionSequenceExecutorContext  执行上下文
     * @param actionSequenceExecutorHandler  执行处理器
     * @param actionSequenceExecutorListener 执行监听器
     * @param actionSequenceResultSet        执行结果集
     * @return
     * @throws ActionSequenceExecutionException 执行异常
     * @throws ActionSequenceStopException      停止执行异常
     */
    boolean execute(ActionSequenceUnit actionSequence,
                    ActionSequenceExecutorContext actionSequenceExecutorContext,
                    ActionSequenceExecutorHandler actionSequenceExecutorHandler,
                    ActionSequenceExecutorListener actionSequenceExecutorListener,
                    ActionSequenceResultSet actionSequenceResultSet) throws ActionSequenceExecutionException, ActionSequenceStopException;

    /**
     * 获取执行器类
     *
     * @return
     */
    default Class<?> getExecutorClass() {
        return null;
    }
}
