package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class TestBoxRegexRule extends BaseRegexRule {

    //1.PwmOut-1-10hz-50%-车速信号
    public static final String CHANGE_PWM_OUT_VALUE = wholeCombine(RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT, group(WORD_EXCLUDE_PURE_NUMBER));

    //1.HL-1-1-手刹信号
    public static final String CHANGE_TRI_STATE_OUTPUT_VALUE = wholeCombine(group(NUMBER), group(WORD_EXCLUDE_PURE_NUMBER));

    //1.PWMin-1-12hz-10%-50%-10%
    //频率-频率偏差-占空比-占空比偏差
    public static final String PWM_IN_ACQUIRE = wholeCombine(RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT,
            RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);

    //1.CollectV-1-20-12V-10%-电机反馈
    public static final String VOLTAGE_ACQUIRE = wholeCombine(group(NUMBER), RegexRuleConstants.VOLTAGE, RegexRuleConstants.PERCENT, group(WORD_EXCLUDE_PURE_NUMBER));

    //1.CollectV-1-20-12V-15V-电机反馈
    public static final String VOLTAGE_ACQUIRE_RANGE = wholeCombine(group(NUMBER), RegexRuleConstants.VOLTAGE, RegexRuleConstants.VOLTAGE, group(WORD_EXCLUDE_PURE_NUMBER));
}
