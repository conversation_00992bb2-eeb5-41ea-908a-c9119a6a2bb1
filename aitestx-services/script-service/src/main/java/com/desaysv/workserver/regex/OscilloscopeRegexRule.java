package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.*;

public class OscilloscopeRegexRule extends BaseRegexRule {
    //Osc-SetOscInit-200HZ
    public static final String SET_MEASURE_INIT_VALUE = wholeCombine(RegexRuleConstants.SET_OSC_INIT, RegexRuleConstants.FREQUENCY);
    //Osc-measurement-Range-10Hz-1Hz-50%-2%
    public static final String GET_MEASURE_ALL = wholeCombine(RegexRuleConstants.MEASUREMENT, RegexRuleConstants.RANGE, RegexRuleConstants.FREQUENCY, RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    //Osc-measurement-AutoSet-10s-Range-10Hz-1Hz-50%-2%
    public static final String GET_MEASURE_ALL_WITH_AUTOSET = wholeCombine(RegexRuleConstants.MEASUREMENT, RegexRuleConstants.AUTO_SET, group(ANY_TIME), RegexRuleConstants.RANGE, RegexRuleConstants.FREQUENCY, RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    //Osc-GetFreq-10Hz
    public static final String GET_FREQUENCY = wholeCombine(RegexRuleConstants.GET_FREQUENCY_CONSTANTS, RegexRuleConstants.FREQUENCY);
    //Osc-GetAMPL-5Vpp
    public static final String GET_AMPLITUDE = wholeCombine(RegexRuleConstants.GET_AMPLITUDE_CONSTANTS, RegexRuleConstants.AMPLITUDE);
    //Osc-GetDuty-50%
    public static final String GET_DUTY = wholeCombine(GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT);
    //Osc-SetStatus-STOP/ON
    public static final String SET_STATUS = wholeCombine(RegexRuleConstants.SET_OSC_STATUS, RegexRuleConstants.OSC_STATE);
    //1.OSC-1-getDuty-0.82%-0.1%
    public static final String GET_SINGLE_DUTY = wholeCombine(GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    //1.OSC-1-getDuty-0.82%-0.1%-2-getDuty-0.65%-0.1%
    public static final String GET_TWO_DUTY = wholeCombine(GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT, group(NUMBER), GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    //1.OSC-1-getDuty-0.82%-0.1%-2-getDuty-0.65%-0.1%-3-getDuty-0.5%-0.1%
    public static final String GET_MULTI_DUTY = wholeCombine(GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT
            , group(NUMBER), GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT
            , group(NUMBER), GET_DUTY_CONSTANTS, RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    //上升时间
    public static final String GET_RAISE_TIME = wholeCombine(GET_RAISE_TIME_CONSTANTS, group(ALL_NUMBER), group(ALL_NUMBER));
    //下降时间
    public static final String GET_FULL_TIME = wholeCombine(GET_FULL_TIME_CONSTANTS, group(ALL_NUMBER), group(ALL_NUMBER));
    //顶端值
    public static final String GET_TOP_VALUE = wholeCombine(GET_TOP_VALUE_CONSTANTS, group(ALL_NUMBER), group(ALL_NUMBER));
    //底端值
    public static final String GET_BASE_VALUE = wholeCombine(GET_BASE_VALUE_CONSTANTS, group(ALL_NUMBER), group(ALL_NUMBER));
}
