package com.desaysv.workserver.parser.customized;

import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.components.SequenceDeviceKeywords;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.parser.ActionSequenceParser;
import com.desaysv.workserver.parser.ActionSequenceParserStrategy;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 电源动作序列语法解析规则
 * ActionSequenceParser注解范例，用来做特殊自定义解析器逻辑，
 * 普通用途可以不用重写ActionSequenceParserStrategy
 */
@Service
@Lazy
@ActionSequenceParser(type = SequenceDeviceKeywords.POWER)
public class PowerActionSequenceParser implements ActionSequenceParserStrategy {

    @Override
    public ActionSequenceUnit parse(ActionSequenceUnit actionSequenceUnit) {
        return filter(actionSequenceUnit);
    }

    public static void main(String[] args) {
        String regex = String.format("%s(%s%s|%s)+", BaseRegexRule.METHOD_NAME, BaseRegexRule.SPLITTER, BaseRegexRule.NUMBER, BaseRegexRule.ALPHABET);
        System.out.println("regex======" + regex);
        String sampleA = "1.Vol-1-SupplyPower-13.5-12-14.5-16";
        String sampleB = "2.Vol-1-SupplyPower-kkk-Random-5.0-13.5";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcherA = pattern.matcher(sampleA);
        Matcher matcherB = pattern.matcher(sampleB);

        while (matcherA.find()) {
            System.out.println("matcherA:" + matcherA.group());
        }
        System.out.println("---------------------");
        while (matcherB.find()) {
            System.out.println("matcherB:" + matcherB.group());
        }

//        regex = "[ab]";
//        String input = "ddd111abc";
//        Pattern pattern1 = Pattern.compile(regex);
//        Matcher matcher = pattern1.matcher(input);
//        while (matcher.find()) {
//            System.out.println(matcher.group());
//        }
    }
}
