package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.action_sequence.RegexMatcher;

public class AdbRegexRule extends BaseRegexRule {
    private static final String CLICK = wholeMatchCaseInsensitive("Click");
    private static final String SWIPE = wholeMatchCaseInsensitive("Swipe");
    public static final String SEND_CONSTANT = wholeMatchCaseInsensitive(ADB_SEND);


    /**
     * 功能：ADB点击
     * 格式：ADB-Click-X-Y
     */
    public static final String ADB_CLICK = wholeCombine(CLICK, group(oneRequiredPlusZeroOrMoreSeparated(add(NUMBER, SPLITTER, NUMBER))));
    /**
     * 功能：ADB滑动
     * 格式：ADB-Swipe-X-Y
     */
    public static final String ADB_SWIPE = wholeCombine(SWIPE, group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER));


    /**
     * 功能：ADB发送
     * 格式：ADB-Send-(X)
     */
    public static final String SEND = wholeCombine(SEND_CONSTANT, group(PARENTHESIS_COMMAND));

    public static void main(String[] args) {
        System.out.println(ADB_CLICK);
        RegexMatcher matcher = BaseRegexRule.match("Click-1", ADB_CLICK);
        System.out.println(matcher.getMatcher().group());
    }
}
