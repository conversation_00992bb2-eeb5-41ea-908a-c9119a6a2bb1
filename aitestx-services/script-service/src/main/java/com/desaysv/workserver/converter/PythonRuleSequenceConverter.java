package com.desaysv.workserver.converter;

import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.python.core.Py;
import org.python.core.PyList;
import org.python.core.PyObject;
import org.python.core.PyString;
import org.python.util.PythonInterpreter;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Python规则序列解析器
 */
@Data
//@Component
@Slf4j
public class PythonRuleSequenceConverter implements SequenceConverter {
    private static final List<PatternStrategy> PATTERN_LIST = new ArrayList<>();
    private String dbcFilePath;

    public PythonRuleSequenceConverter() throws IOException {
        System.setProperty("python.home", "D:\\FlyTest\\library\\python\\jython-standalone-2.7.3");
        loadConfig();
    }

    private void loadConfig() throws IOException {
        try (PythonInterpreter pythonInterpreter = new PythonInterpreter()) {
            String pythonScript = loadPythonScript("configs/strategy.py");
            PyString code = Py.newStringUTF8(pythonScript);
//            System.out.println(code);
            pythonInterpreter.exec(code);

            PyList pyPatterns = (PyList) pythonInterpreter.get("patterns");
            for (Object obj : pyPatterns) {
                PyObject pyPatternDict = (PyObject) obj;
                Pattern pattern = Pattern.compile(pyPatternDict.__getitem__(new PyString("pattern")).toString());
                String strategyClassName = pyPatternDict.__getitem__(new PyString("strategyClass")).toString();
                ConversionStrategy strategy = createStrategy(pythonInterpreter, strategyClassName);
                if (strategy != null) {
                    PATTERN_LIST.add(new PatternStrategy(pattern, strategy));
                }
            }
        }
    }

    private String loadPythonScript(String resourcePath) throws IOException {
//        Properties props = new Properties();
//        props.put("python.import.site", "false");
//        Properties preprops = System.getProperties();
//        PythonInterpreter.initialize(preprops, props, new String[0]);
        ClassPathResource classPathResource = new ClassPathResource(resourcePath);
        StringBuilder script = new StringBuilder();
        try (InputStream inputStream = classPathResource.getInputStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                script.append(line).append(System.lineSeparator());
            }
        }
        return script.toString();
    }

    private ConversionStrategy createStrategy(PythonInterpreter pythonInterpreter, String strategyClassName) {
        try {
            PyObject strategyClass = pythonInterpreter.get(strategyClassName);
            if (strategyClass == null) {
                return null;
            }
            PyObject strategyInstance = strategyClass.__call__();
            return new PythonConversionStrategy(strategyInstance);
        } catch (Exception e) {
            return null;
        }
    }

    interface ConversionStrategy {
        String convert(Matcher matcher);
    }

    static class PythonConversionStrategy implements ConversionStrategy {
        private final PyObject pyStrategyInstance;

        PythonConversionStrategy(PyObject pyStrategyInstance) {
            this.pyStrategyInstance = pyStrategyInstance;
        }

        @Override
        public String convert(Matcher matcher) {
            PyObject pyMatcher = Py.java2py(matcher);
            PyObject result = pyStrategyInstance.invoke("convert", pyMatcher);
            return result.toString();
        }
    }

    static class PatternStrategy {
        private final Pattern pattern;
        private final ConversionStrategy strategy;

        public PatternStrategy(Pattern pattern, ConversionStrategy strategy) {
            this.pattern = pattern;
            this.strategy = strategy;
        }
    }

    public String convertLine(String naturalLanguage) {
        String preprocessedInput = naturalLanguage.trim();

        for (PatternStrategy patternStrategy : PATTERN_LIST) {
            Matcher matcher = patternStrategy.pattern.matcher(preprocessedInput);
            if (matcher.find()) {
                ConversionStrategy strategy = patternStrategy.strategy;
                if (strategy != null) {
                    return strategy.convert(matcher);
                }
            }
        }
        return "//无法匹配:" + naturalLanguage;
    }

    @Override
    public List<String> convertAll(ActionSequenceSimpleContext actionSequenceContext) {
        List<String> actionSequenceList = new ArrayList<>();
        actionSequenceList.add(convertParagraph(actionSequenceContext.getPrecondition()));
        actionSequenceList.add(convertParagraph(actionSequenceContext.getOperationStep()));
        actionSequenceList.add(convertParagraph(actionSequenceContext.getExpectResult()));
        return actionSequenceList;
    }

    public static void main(String[] args) {
        try {
            PythonRuleSequenceConverter converter = new PythonRuleSequenceConverter();
            String[] inputs = {
                    "1.UMM_UsageModeSt=1/3",
                    "2.测试电压13.5V",
                    "3.配置有效",
                    "4.CCU_8报文有效",
                    "5.TPMS_SystemWarningLamp=0",
                    "6.TPMS_TireWarningLamp=1"
            };

            for (String input : inputs) {
                System.out.println(converter.convertLine(input));
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
