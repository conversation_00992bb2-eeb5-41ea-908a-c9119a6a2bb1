package com.desaysv.workserver.regex;

public class EthernetRegexRule extends BusRegexRule {
    private static final String SET_DoIP_FUN_ETH_METHOD_NAME = wholeMatchCaseInsensitive("DoIP");
    private static final String SET_DoIP_UDP_ETH_METHOD_NAME = wholeMatchCaseInsensitive("Do<PERSON>U<PERSON>");
    private static final String SET_OTA_ETH_METHOD_NAME = wholeMatchCaseInsensitive("OTA");
    private static final String GET_DoIP_FUN_ETH_METHOD_NAME = wholeMatchCaseInsensitive("GetDoIP");
    private static final String GET_DoIP_UDP_ETH_METHOD_NAME = wholeMatchCaseInsensitive("GetDoIPUDP");
    private static final String GET_OTA_ETH_METHOD_NAME = wholeMatchCaseInsensitive("GetOTA");

    public static final String GET_DoIP_FUN_ETH = wholeCombine(GET_DoIP_FUN_ETH_METHOD_NAME, group(NAME_VARIABLE));
    public static final String GET_DoIP_EXPLAIN_FUN_ETH = wholeCombine(GET_DoIP_FUN_ETH_METHOD_NAME, EXPLAIN_KEYWORD, group(NAME_VARIABLE));
    public static final String GET_DoIP_UDP_ETH = wholeCombine(GET_DoIP_UDP_ETH_METHOD_NAME, group(PARAM), group(NUMBER));

    public static final String GET_OTA_ETH = wholeCombine(GET_OTA_ETH_METHOD_NAME, group(PARAM), group(NUMBER));
    public static final String SET_DoIP_FUN_ETH = wholeCombine(SET_DoIP_FUN_ETH_METHOD_NAME, group(ALPHABET), group(PARAM));
    public static final String SET_DoIP_UDP_ETH = wholeCombine(SET_DoIP_UDP_ETH_METHOD_NAME, group(PARAM));
    public static final String SET_OTA_ETH = wholeCombine(SET_OTA_ETH_METHOD_NAME, group(PARAM));
    public static final String SET_ETH_UDS_LOG = wholeCombine(SET_UDS_LOG_METHOD_NAME, group(NUMBER));

}
