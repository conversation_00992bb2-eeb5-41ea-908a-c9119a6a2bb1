package com.desaysv.workserver.context;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
@Service
public class CanStopAllSequenceHandler {
    private static final Pattern CAN_LINE_PATTERN = Pattern.compile(".*CAN.*", Pattern.CASE_INSENSITIVE);

    public void addStopAllChecks(ActionSequenceContext context){
        if (!context.getTestConfig().getImageAlgorithmConfig().isEnableStopAll()) return;
        List<TestStep> precondition = context.getPrecondition();
        List<TestStep> operation = context.getOperationStep();
        if(precondition == null && operation == null) return;
        boolean preconditionContainsCan = containsCanCheck(precondition);
        boolean operationContainsCan = containsCanCheck(operation);
        if ((!preconditionContainsCan) && (!operationContainsCan)) return;
        Integer deviceId;
        if (preconditionContainsCan){
            deviceId = extractDeviceId(precondition);
        }else {
            deviceId = extractDeviceId(operation);
        }
        TestStep firstStopAllStep = new TestStep();
        firstStopAllStep.setTestStep("CAN#" + deviceId + "-1-StopAll");
        TestStep secondStopAllStep = new TestStep();
        secondStopAllStep.setTestStep("CAN#" + deviceId + "-2-StopAll");
        List<TestStep> newPreconditionSteps = new ArrayList<>();
        newPreconditionSteps.add(firstStopAllStep);
        newPreconditionSteps.add(secondStopAllStep);
        if (precondition != null) {
            newPreconditionSteps.addAll(precondition);
            context.setPrecondition(newPreconditionSteps);
        }else {
            newPreconditionSteps.addAll(operation);
            context.setOperationStep(newPreconditionSteps);
        }

    }

    /**
     * 检查前提条件和操作序列中是否包含声音检查
     */
    private boolean containsCanCheck(List<TestStep> steps) {
        if (steps == null) return false;
        return steps.stream()
                .anyMatch(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        CAN_LINE_PATTERN.matcher(step.getTestStep()).find());
    }

    /**
     * 提取设备ID
     */
    private Integer extractDeviceId(List<TestStep> steps) {
        return steps.stream()
                .filter(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        CAN_LINE_PATTERN.matcher(step.getTestStep()).find())
                .findFirst()
                .map(step -> {
                    Matcher matcher = Pattern.compile("CAN#(\\d+)-", Pattern.CASE_INSENSITIVE)
                            .matcher(step.getTestStep());
                    if (matcher.find()) {
                        try {
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            return 1;
                        }
                    }
                    return 1;
                })
                .orElse(1);
    }

}
