package com.desaysv.workserver.components;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 动作序列定义类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActionSequenceUnit extends ActionSequence {

    private ActionSequenceHeader actionSequenceHeader; //序列头

//    private String expectResultLabel; //期望结果标签

    private String extraSequenceExpression; //除头部的表达式

    private String headerExpression; //头部表达式

    private String regexRuleApplyExpression; //正则表达式规则应用的序列方法


    public boolean isDeviceActionSequence() {
        return actionSequenceHeader instanceof DeviceActionSequenceHeader;
    }

    public ActionSequenceUnit error(String errorMessage) {
        getActionSequenceError().setErrorMessage(errorMessage);
        return this;
    }

    public String simpleExpression() {
        if (isDeviceActionSequence()) {
            return headerExpression + regexRuleApplyExpression;
        } else {
            return getUserSequenceOrder() == null ? regexRuleApplyExpression : getUserSequenceOrder() + "." + regexRuleApplyExpression;
        }
    }

    public String getActualResultExpression() {
        if (isDeviceActionSequence()) {
            return headerExpression + getKeyWord(regexRuleApplyExpression);
        } else {
            return headerExpression;
        }
    }

//    public static String getKeyWord(String str) {
//        if (StringUtils.isNotEmpty(str)) {
//            int lastDashIndex = str.lastIndexOf('-');
//            if (lastDashIndex != -1) {
//                return str.substring(0, lastDashIndex);
//            } else {
//                Pattern pattern = Pattern.compile("^[^-]*");
//                Matcher matcher = pattern.matcher(str);
//                if (matcher.find()) {
//                    return matcher.group(0);
//                }
//            }
//        }
//        return null;
//    }

    public static String getKeyWord(String str) {
        // 判断字符串是否以字母开头，不区分大小写
        if (str != null && str.matches("^[a-zA-Z].*")) {
            // 找到第一个 '-' 的位置
            int hyphenIndex = str.indexOf('-');
            if (hyphenIndex != -1) {
                // 返回第一个 '-' 前的内容
                return str.substring(0, hyphenIndex);
            }
        }
        // 如果不是字母开头或没有 '-'，返回空字符串
        return "";
    }

    @Override
    public String toString() {
        return String.format("checkOk:%s | executed:%s | executeOk:%s | %s  | %s",
                isCheckOk(), isExecuted(), isExecuteOk(),
                getOptions().isEnable() ? "enable" : "disable",
                getActionSequenceError().getErrorMessage() != null ?
                        String.format("%s | %s", getRawExpression() == null ? "" : getRawExpression(), getActionSequenceError().getErrorMessage()) :
                        getRawExpression() == null ? "" : getRawExpression());
    }


    public static void main(String[] args) {
        String str = "Current-0-0.5";
        Pattern pattern = Pattern.compile("^[^-]*");
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            System.out.println(matcher.group(0)); // 输出：Current
        }
    }

}
