package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class AdsToolRegexRule extends BaseRegexRule {
    private static final String XS_VALUE_METHOD_NAME = wholeMatchCaseInsensitive("Set58xs");
    private static final String XD_VALUE_METHOD_NAME = wholeMatchCaseInsensitive("Set58xd");
    private static final String Get_PWM_METHOD_NAME = wholeMatchCaseInsensitive("GetPWM");
    private static final String SET_LOG_METHOD_NAME = wholeMatchCaseInsensitive("SetADSToolLog");
    private static final String SET_LOG_NAME_METHOD_NAME = wholeMatchCaseInsensitive("SetLogName");

    public static final String XS_GET_PWM = wholeCombine(Get_PWM_METHOD_NAME, BaseRegexRule.RegexRuleConstants.ANY_PERCENT);
    public static final String GET_PWM = wholeCombine(Get_PWM_METHOD_NAME, BaseRegexRule.RegexRuleConstants.ANY_PERCENT);
    public static final String COMPARE_PWM = wholeCombine(Get_PWM_METHOD_NAME, BaseRegexRule.RegexRuleConstants.ANY_PERCENT, BaseRegexRule.RegexRuleConstants.ANY_PERCENT);
    public static final String COMPARE_TWO_PWM = wholeCombine( Get_PWM_METHOD_NAME, BaseRegexRule.RegexRuleConstants.ANY_PERCENT,
            BaseRegexRule.RegexRuleConstants.ANY_PERCENT, BaseRegexRule.RegexRuleConstants.ANY_PERCENT, BaseRegexRule.RegexRuleConstants.ANY_PERCENT);
    public static final String SET_ADS_TOOL_LOG_NAME = wholeCombine(SET_LOG_NAME_METHOD_NAME, group(NAME_VARIABLE));
    public static final String SET_ADS_TOOL_LOG = wholeCombine(SET_LOG_METHOD_NAME, group(NUMBER));

}
