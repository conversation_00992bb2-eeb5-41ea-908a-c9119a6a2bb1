package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

/**
 * 电源正则表达式规则
 */
public class PowerRegexRule extends BaseRegexRule {

    private static final String SUPPLY_POWER = wholeMatchCaseInsensitive("SupplyPower");
    private static final String WAVE = wholeMatchCaseInsensitive("Wave");
    private static final String TIME = wholeMatchCaseInsensitive("TimeResetPulse");
    private static final String PULSE_VOLTAGE = wholeMatchCaseInsensitive("VoltResetPulse");
    private static final String ARBITRARY = wholeMatchCaseInsensitive("ArbVoltResetPulse");
    private static final String INTERRUPT = wholeMatchCaseInsensitive("InterruptPulse");
    private static final String START = wholeMatchCaseInsensitive("StartPulse");


    /**
     * 作用：改变电压
     * 格式：Pwr-X0(通道)-SupplyPower-X1(电压，V)
     */
    public static final String CHANGE_VOLTAGE = wholeCombine(SUPPLY_POWER, group(NUMBER));

    /**
     * 作用：随机电压
     * 格式：Pwr-X0(通道)-SupplyPower-Random-X1(电压起始值)-X2(电压终止值)
     */
    public static final String RANDOM_VOLTAGE = wholeCombine(SUPPLY_POWER, RegexRuleConstants.RANDOM, group(NUMBER), group(NUMBER));

    /**
     * 作用：步进电压
     * 格式：Pwr-X0(通道)-SupplyPower-Step-X1(电压起始值)-X2(电压终止值)-X3(电压步进值)-X4(步进间隔时间)
     */
    public static final String STEP_VOLTAGE = wholeCombine(SUPPLY_POWER, RegexRuleConstants.STEP, group(NUMBER), group(NUMBER), group(NUMBER), group(ANY_TIME));

    /**
     * 作用：电压恒定持续时间
     * 格式：Pwr-X0(通道)-SupplyPower-Step-X1(电压)-X2(持续时间)
     */
    public static final String CONSTANT_VOLTAGE = wholeCombine(SUPPLY_POWER, RegexRuleConstants.STEP, group(NUMBER), group(ANY_TIME));

    /**
     * 作用：电压斜坡持续时间（只有终止）
     * 格式：Pwr-X0(通道)-SupplyPower-Ramp-X1(电压)-X2(持续时间)
     */
    public static final String RAMP_VOLTAGE = wholeCombine(SUPPLY_POWER, RegexRuleConstants.RAMP, group(NUMBER), group(ANY_TIME));

    /**
     * 作用：电压斜坡持续时间（起始到终止）
     * 格式：Pwr-X0(通道)-SupplyPower-Ramp-X1(起始电压)-X2(终止电压)-X8(持续时间)
     */
    public static final String RAMP_VOLTAGE_START_TO_END = wholeCombine(SUPPLY_POWER, RegexRuleConstants.RAMP, group(NUMBER), group(NUMBER), group(ANY_TIME));

    /**
     * 作用：执行菊水脉冲波形
     * 格式：Pwr-X0(通道)-Wave-X1(菊水电源内存位置)
     */
    public static final String EXECUTE_WAVE_FORM = wholeCombine(WAVE, group(NUMBER));
    /**
     * 作用：执行启动脉冲波形
     * 格式：Pwr-X0(通道)-StartPulse-X1(随机电压下限)-X2(随机电压上限)-X3(随机脉冲时间)-X4(随机脉冲时间)-X5(随机脉冲次数)-X6(随机脉冲次数)
     */
    public static final String EXECUTE_START_RESET_FORM = wholeCombine(START, group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER));
    /**
     * 作用：执行时间级复位脉冲波形
     * 格式：Pwr-X0(通道)-TimeResetPulse-X1(高电压)-X2(最低电压)-X3(低脉冲持续时间)-X4(高脉冲持续时间)
     */
    public static final String EXECUTE_TIME_RESET_FORM = wholeCombine(TIME, group(NUMBER), group(NUMBER), group(ANY_TIME), group(ANY_TIME));
    /**
     * 作用：执行电压级脉复位冲波形
     * 格式：Pwr-X0(通道)-VoltResetPulse-X1(起始电压)-X2(最低电压)-X3(低脉冲持续时间)-X4(高脉冲持续时间)
     */
    public static final String EXECUTE_VOLTAGE_RESET_FORM = wholeCombine(PULSE_VOLTAGE, group(NUMBER), group(NUMBER), group(ANY_TIME), group(ANY_TIME));
    /**
     * 作用：执行任意复位脉冲
     * 格式：Pwr-X0(通道)-ArbVoltResetPulse-X1(递变起始电压)-X2(递变终止电压)-X3(恒定电压)-X4(低脉冲持续时间)-X5(高脉冲持续时间)
     */
    public static final String EXECUTE_ARBITRARY_RESET_FORM = wholeCombine(ARBITRARY, group(NUMBER), group(NUMBER), group(NUMBER), group(ANY_TIME), group(ANY_TIME));

    /**
     * 作用：执行中断脉冲波形
     * 格式：Pwr-X0(通道)-InterruptPulse-X1(起始电压)-X4(高脉冲持续时间)
     */
    public static final String EXECUTE_INTERRUPT_RESET_FORM = wholeCombine(INTERRUPT, group(NUMBER), group(ANY_TIME));


    /**
     * 作用：改变电流
     * 格式：Pwr-X0(通道)-CURRENT-X1(电流，A)
     */
    public static final String CHANGE_CURRENT = wholeCombine(CURRENT, group(NUMBER));

}