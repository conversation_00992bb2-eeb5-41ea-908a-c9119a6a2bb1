package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

/**
 * 机械臂动作序列
 */
public class RobotRegexRule extends BaseRegexRule {

    private static final String TOUCH = wholeMatchCaseInsensitive("Touch");
    private static final String LONG_TOUCH = wholeMatchCaseInsensitive("LongTouch");
    private static final String SLIDE = wholeMatchCaseInsensitive("Slide");
    private static final String NO_LIFT = wholeMatchCaseInsensitive("NoLift");

    //Touch-X1
//    public static final String TOUCH = combine(TOUCH_METHOD_NAME, group(WORD));

    //Touch-X1-Random-X2-X3
    public static final String RANDOM_TIMES_TOUCH = wholeCombine(TOUCH, group(WORD), RegexRuleConstants.RANDOM, group(WORD), group(WORD));

    //Touch-Xn1-Xn2-Xn3-Xn
    public static final String TOUCH_MORE = add(TOUCH, group(more(noGroup(add(SPLITTER, WORD)))));

    //LongTouch-X1-X2（ms/S/min/H）
    public static final String PERFORM_LONG_TOUCH = wholeCombine(LONG_TOUCH, group(WORD), group(ANY_TIME));

    //Slide-Xn1-Xn2-Xn3-Xn
    public static final String SLIDE_MORE = add(SLIDE, group(more(noGroup(add(SPLITTER, WORD)))));

    //Slide-Nolift-Xn1-Xn2-Xn3-Xn
    public static final String SLIDE_MORE_NO_LIFT = add(combine(SLIDE, NO_LIFT), group(more(noGroup(add(SPLITTER, WORD)))));

    public static void main(String[] args) {
        System.out.println(RobotRegexRule.TOUCH_MORE);
    }

}
