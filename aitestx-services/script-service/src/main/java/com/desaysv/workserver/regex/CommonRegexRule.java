package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 通用动作序列
 */
public class CommonRegexRule extends BaseRegexRule {

    public static final String WAIT = wholeMatchCaseInsensitive("Wait"); //等待
    public static final String REPEAT = wholeMatchCaseInsensitive("Repeat"); //重复
    public static final String DIALOG = wholeMatchCaseInsensitive("Dialog"); //对话框
    public static final String JUDGE = wholeMatchCaseInsensitive("Judge"); //对话框
    public static final String DEVICE = wholeMatchCaseInsensitive("Device");//设备
    public static final String FLUCTUATION = wholeMatchCaseInsensitive("Fluctuation");//波动
    public static final String START = wholeMatchCaseInsensitive("Start");
    public static final String STOP = wholeMatchCaseInsensitive("Stop");
    public static final String EXECUTE_SCRIPT = wholeMatchCaseInsensitive("ExecuteScript");
    public static final String VALUE = wholeMatchCaseInsensitive("Value");


    public static final String EXECUTE_SCRIPT_COMMAND = wholeCombine(EXECUTE_SCRIPT, group(FILE_NAME_REGEX));


    /**
     * 作用：断开或者重连设备
     * 格式：Device-X0（设备名）-On/Off
     */
    public static final String DEVICE_ON_OFF = wholeCombine(DEVICE, group(NAME_VARIABLE), group(RegexRuleConstants.ON_OFF));

    /**
     * 作用：等待时间
     * 格式：Wait-X0（ms/S/H/Min）
     */
    public static final String WAIT_TIME = wholeCombine(WAIT, group(ANY_TIME));

    /**
     * 作用：对话框提示
     * 格式：Dialog-X0(提示）
     */
    public static final String WARNING_DIALOG = wholeCombine(DIALOG, group(WORD));
    /**
     * 作用：对话框判断
     * 格式：Dialog-Judge-X0(判断内容）
     */
    public static final String CHECK_DIALOG = wholeCombine(DIALOG, JUDGE, group(WORD));

    /**
     * 作用：等待随机时间
     * 格式：Wait-Random-X0（ms/s/h/min）-X1（ms/s/h/min）
     */
    public static final String RANDOM_WAIT = wholeCombine(WAIT, RegexRuleConstants.RANDOM, group(ANY_TIME), group(ANY_TIME));


    /**
     * 作用：改变通道状态（物理含义）
     * 格式：Pwr-X0(通道)-On/Off(开关状态,ON开,OFF关)-X1(物理含义,可省略)
     * 举例：
     * 1.Switch-29-ON-KL30
     * 2.Pwr-1-ON-KL30
     */
    //TODO:如何使WORD_EXCLUDE_PURE_NUMBER可缺省
    public static final String CHANNEL_ON_OFF = wholeCombine(group(RegexRuleConstants.ON_OFF), group(WORD_EXCLUDE_PURE_NUMBER));


    /**
     * 作用：改变通道状态
     * 格式：On/Off(开关状态,ON开,OFF关)
     */
    public static final String OPTIONAL_CHANNEL_ON_OFF = group(RegexRuleConstants.ON_OFF);


    /**
     * 作用：轻量化测试箱改变阻值（带关键字，不带注释）
     * 格式：數值
     * 单位：欧姆
     * 举例:
     * 1.Res-1-5-1-0-154
     */
    public static final String CHANGE_VALUE_WITH_LIGHT_TEST_BOX = wholeCombine(group(NUMBER), group(NUMBER), group(NUMBER), group(NUMBER));


    /**
     * 作用：改变值（带注释）
     * 格式：數值
     * 举例:
     * 1.Res-1-154-FuelRes
     */
    public static final String CHANGE_VALUE_WITH_MEANING = wholeCombine(group(NUMBER), group(WORD_EXCLUDE_PURE_NUMBER));

    /**
     * 作用：改变值（带注释）新
     * 格式：數值
     * 举例:
     * 1.Res-1-Value-154-FuelRes
     */
    public static final String CHANGE_VALUE_WITH_MEANING_NEW = wholeCombine(VALUE, group(NUMBER), group(WORD_EXCLUDE_PURE_NUMBER));

    /**
     * 作用：改变值（带通道，不带注释）
     * 格式：數值
     * 举例:
     * 1.Res-1-154
     */
    public static final String CHANGE_VALUE_WITH_CHANNEL = wholeCombine(group(NUMBER));


    /**
     * 作用：改变值（不带通道，不带注释）
     * 格式：數值
     * 举例:
     * 1.Res-154
     */
    public static final String CHANGE_VALUE = wholeCombine("");

    /**
     * 作用：改变值（不带注释）
     * 格式：數值
     * 举例:
     * 1.Res-Value-154
     */
    public static final String CHANGE_VALUE_NEW = wholeCombine(VALUE, group(NUMBER));

    /**
     * 作用：步进(带时间）
     * 格式：Step-X1(起始值)-X2(终止值)-X3(步进值)-X4(持续时间）
     */
    public static final String STEP_RANGE_WITH_TIME = wholeCombine(RegexRuleConstants.STEP, group(NUMBER), group(NUMBER), group(NUMBER), group(ANY_TIME));

    /**
     * 作用：步进
     * 格式：Step-X1(起始值)-X2(终止值)-X3(步进值)
     */
    public static final String STEP_RANGE = wholeCombine(RegexRuleConstants.STEP, group(NUMBER), group(NUMBER), group(NUMBER));

    /**
     * 作用：随机
     * 格式：Random-X1(起始值)-X2(终止值)
     */
    public static final String RANDOM_RANGE = wholeCombine(RegexRuleConstants.RANDOM, group(NUMBER), group(NUMBER));

    /**
     * 作用：电流采集反馈判断
     * 格式：GetCurrent-X1(电流期望下限值)-X2(电流期望上限值)
     */
    public static final String COMPARE_CURRENT = wholeCombine(RegexRuleConstants.GET_CURRENT, group(NUMBER), group(NUMBER));

    /**
     * 作用：电压采集反馈判断
     * 格式：GetVoltage-X1(电压期望下限值)-X2(电压期望上限值)
     */
    public static final String COMPARE_VOLTAGE = wholeCombine(RegexRuleConstants.GET_VOLTAGE, group(NUMBER), group(NUMBER));
    /**
     * 作用：电流采集波动判断开始
     * 格式：GetCurrent-Fluctuation-Start
     */
    public static final String COMPARE_CURRENT_FLUCTUATION_START = wholeCombine(RegexRuleConstants.GET_CURRENT, FLUCTUATION, START);
    /**
     * 作用：电流采集波动判断开始并设置采集频率
     * 格式：GetCurrent-Fluctuation-Start-X0(采集频率)
     */
    public static final String COMPARE_CURRENT_FLUCTUATION_START_INTERVAL = wholeCombine(RegexRuleConstants.GET_CURRENT, FLUCTUATION, START, group(ANY_TIME));

    /**
     * 作用：电流采集波动判断结束并输出结果
     * 格式：GetCurrent-Fluctuation-Stop-X0(基准值,A)-X1(波动值±n,A)
     */
    public static final String COMPARE_CURRENT_FLUCTUATION_STOP = wholeCombine(RegexRuleConstants.GET_CURRENT, FLUCTUATION, STOP, group(NUMBER), group(NUMBER));

    /**
     * 获取所有动作
     *
     * @return 所有动作
     */
    public static Set<String> getCollections() {
        try {
            return new HashSet<>(Arrays.asList(lower("Wait"), lower("Repeat"), lower("Check"), lower("Cycle"), lower("Device")));
        } catch (Exception e) {
            return new HashSet<>();
        }
    }

    public static void main(String[] args) {
        HashSet<String> hashSet = new HashSet<>(Arrays.asList(WAIT, REPEAT));
        System.out.println(hashSet);
        System.out.println(hashSet.contains("Wait"));
        System.out.println(hashSet.contains("Wait".toLowerCase()));
        System.out.println("Wait".toLowerCase());
        System.out.println(hashSet.contains(wholeMatchCaseInsensitive("Wait")));
    }
}
