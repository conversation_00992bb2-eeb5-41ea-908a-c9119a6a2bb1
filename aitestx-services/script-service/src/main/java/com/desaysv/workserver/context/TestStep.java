package com.desaysv.workserver.context;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/4/7 10:58
 * @description :
 * @modified By :
 * @since : 2023/4/7
 **/
@Data
@NoArgsConstructor
public class TestStep implements Serializable {
//    @JSO<PERSON>ield(serialize = false)
    @JSONField(ordinal = 1)
    private String uuid;
    @JSONField(ordinal = 2)
    private String testStep; //序列
    @JSONField(ordinal = 3)
    private String type;
}
