package com.desaysv.workserver.parser;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.context.TestStep;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 动作序列解析服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Lazy
public class ActionSequenceParserService {

    @Autowired
    private ActionSequenceResolver actionSequenceResolver;

    private final ActionSequenceParserFactory actionSequenceParserFactory;

    private String[] sentenceSplit(String sequenceSentence) {
        return sequenceSentence.split(BaseRegexRule.SPLITTER);
    }

    private String[] paragraphSplit(String sequenceParagraph) {
        return sequenceParagraph.split("\n+");
    }

    /**
     * 清洗动作序列
     *
     * @param sequenceSentence 动作序列
     * @return
     */
    private static String cleanActionSequence(String sequenceSentence) {
        sequenceSentence = sequenceSentence.replaceAll(BaseRegexRule.SPLITTER, BaseRegexRule.NORMAL_SPLITTER);
        return sequenceSentence;
    }

    /**
     * 检查语法
     *
     * @param wordList
     * @param sequenceSentence
     * @return
     */
    private ActionSequenceUnit checkGrammarStructure(String[] wordList, String sequenceSentence) {
        if (wordList.length > 0) {
            //解析动作序列语法
            return actionSequenceParserFactory.parseActionSequenceUnit(sequenceSentence);
        } else {
            return new ActionSequenceUnit().error("动作序列没有包含-或者——分隔符");
        }
    }

    /**
     * 解析单行动作序列语句
     *
     * @param sequenceSentence 单行动作序列语句
     * @return
     */
    private ActionSequenceUnit parseActionSequenceSentence(String sequenceSentence) {
        log.info(">>>解析动作序列:{}", sequenceSentence);
        //数据清洗
        sequenceSentence = cleanActionSequence(sequenceSentence);
        //数据分割
        String[] wordList = sentenceSplit(sequenceSentence);
        return checkGrammarStructure(wordList, sequenceSentence);
    }


    /**
     * 解析动作序列段落
     *
     * @param sequenceParagraph 动作序列语法段落
     * @return
     */
    public List<ActionSequence> parseActionSequenceParagraph(String sequenceParagraph) {
//        sequenceParagraph = StrUtil.strip(sequenceParagraph, "\"");
        List<ActionSequence> actionSequenceList = new ArrayList<>();
        String[] sentenceList = paragraphSplit(sequenceParagraph);
        for (String sentence : sentenceList) {
            actionSequenceList.add(parseActionSequenceSentence(sentence.trim()));
        }
        //处理groups
        resolveGroups(actionSequenceList);
        return actionSequenceList;
    }

    public List<ActionSequence> parseActionSequenceList(List<TestStep> testStepList) {
        List<ActionSequence> actionSequenceList = new ArrayList<>();
        if (testStepList == null)
            return actionSequenceList;
        for (TestStep testStep : testStepList) {
            ActionSequenceUnit actionSequenceUnit = parseActionSequenceSentence(testStep.getTestStep().trim());
            actionSequenceUnit.setTestStepUUID(testStep.getUuid());
            actionSequenceList.add(actionSequenceUnit);
        }
        //处理groups
        resolveGroups(actionSequenceList);
        return actionSequenceList;
    }

    private void resolveGroups(List<ActionSequence> actionSequenceList) {
        actionSequenceResolver.resolveGroupedActionSequenceMap(actionSequenceList);
    }

    public boolean isActionSequenceListValid(List<ActionSequence> actionSequenceList) {
        return actionSequenceList.stream().allMatch(ActionSequence::isCheckOk);
    }


    public static void main(String[] args) {
//        String command = "\"pwr-supplyPower-random-4-12.3\npwr-supplyPower-22\"";
//        System.out.println(Arrays.toString(command.split("\\n+")));
//        System.out.println(Arrays.toString("pwr-1-test".split("pwr-1")));
        ActionSequenceParserService sequenceParserService = new ActionSequenceParserService(new ActionSequenceParserFactory());
        String sequenceParagraph = "1.Pwr-2-SupplyPower-13.5\n" +
                "2.Pwr-1-SupplyPower-Random-5.0-13.5";
//        System.out.println(sequenceParserService.parseParagraph(sequenceParagraph));
        sequenceParserService.parseActionSequenceSentence("Pwr-2-SupplyPower-13.5");
        sequenceParserService.parseActionSequenceSentence("Pwr-55-SupplyPower-Random-5.0-13.5");
        sequenceParserService.parseActionSequenceSentence("6.CANoeCAN-ACU-Sig-sigName-5");
    }
}
