package com.desaysv.workserver.converter;

import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 动作序列语义转换器
 */
public interface SequenceConverter {

    /**
     * 将自然语言句子转换成动作序列
     *
     * @param naturalLanguage 自然语言
     * @return
     */
    String convertLine(String naturalLanguage);

    /**
     * 将自然语言段落转换成动作序列
     *
     * @param naturalLanguage 自然语言
     * @return
     */
    default String convertParagraph(String naturalLanguage) {
        if (StringUtils.isEmpty(naturalLanguage)) {
            return "";
        }
        String[] naturalLanguages = naturalLanguage.split("\n");
        StringBuilder sb = new StringBuilder();
        for (String input : naturalLanguages) {
            String result = convertLine(input);
            sb.append(result).append("\n");
        }
        return sb.toString();
    }

    /**
     * 将多个连续动作序列转换成动作序列列表
     *
     * @param actionSequenceSimpleContext 动作序列上下文
     * @return
     */
    List<String> convertAll(ActionSequenceSimpleContext actionSequenceSimpleContext);

}
