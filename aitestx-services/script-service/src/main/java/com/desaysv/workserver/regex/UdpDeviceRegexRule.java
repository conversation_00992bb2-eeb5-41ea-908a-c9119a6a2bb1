package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.GET_CURRENT;
import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.GET_VOLTAGE;

public class UdpDeviceRegexRule extends BaseRegexRule {
    private static final String SET_DATA = wholeMatchCaseInsensitive("setData");
    private static final String GET_DATA = wholeMatchCaseInsensitive("getData");
    private static final String SET_PWM = wholeMatchCaseInsensitive("setPWM");
    private static final String GET_PWM = wholeMatchCaseInsensitive("getPWM");
    private static final String GET_RESISTANCE = wholeMatchCaseInsensitive("getResistance");
    private static final String SET_VOLTAGE = wholeMatchCaseInsensitive("setVoltage");
    private static final String SET_DUTY = wholeMatchCaseInsensitive("setDuty");
    private static final String DUTY_VALUE = wholeMatchCaseInsensitive("setDutyValue");
    private static final String PING = wholeMatchCaseInsensitive("Ping");
    private static final String RECONNECT = wholeMatchCaseInsensitive("Reconnect");
    private static final String SET_DID = wholeMatchCaseInsensitive("setDID");
    private static final String GET_DID = wholeMatchCaseInsensitive("getDID");

    public static final String SEND_RECONNECT_TIMES_COMMAND = wholeCombine(RECONNECT, group(ALL_NUMBER));
    public static final String SEND_UDP_PING_COMMAND = wholeCombine(PING, group(ALL_NUMBER), group(ALL_NUMBER));
    public static final String SEND_UDP_COMMAND = wholeCombine(SET_DATA, group(ECU_NAME), group(ALL_NUMBER_WITH_HEX));
    public static final String READ_UDP_DATA = wholeCombine(GET_DATA, group(ECU_NAME), group(ALL_NUMBER_WITH_HEX));
    public static final String GET_VOLTAGE_HL_LEVEL = wholeCombine(GET_VOLTAGE, group(ECU_NAME), group(NUMBER));
    public static final String GET_VOLTAGE_RANDOM_DATA = wholeCombine(GET_VOLTAGE, group(ECU_NAME), RegexRuleConstants.RANDOM, group(VOLTAGE), group(VOLTAGE));
    public static final String GET_VOLTAGE_RANGE_DATA = wholeCombine(GET_VOLTAGE, group(ECU_NAME), group(VOLTAGE), RegexRuleConstants.RANGE, group(VOLTAGE));
    public static final String GET_RESISTANCE_RANGE_DATA = wholeCombine(GET_RESISTANCE, group(ECU_NAME), group(NUMBER), RegexRuleConstants.RANGE, group(NUMBER));
    public static final String GET_CURRENT_RANDOM_DATA = wholeCombine(GET_CURRENT, group(ECU_NAME), RegexRuleConstants.RANDOM, group(CURRENT), group(CURRENT));
    public static final String GET_CURRENT_RANGE_DATA = wholeCombine(GET_CURRENT, group(ECU_NAME), group(CURRENT), RegexRuleConstants.RANGE, group(CURRENT));
    public static final String SET_PWM_DATA = wholeCombine(SET_PWM, group(ECU_NAME), group(FREQUENCY), group(ECU_NAME), RegexRuleConstants.PERCENT);
    public static final String GET_PWM_RANGE_DATA = wholeCombine(GET_PWM, group(ECU_NAME), group(FREQUENCY), group(FREQUENCY), group(ECU_NAME), RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);
    public static final String SET_DUTY_DATA = wholeCombine(SET_DUTY, group(ECU_NAME), RegexRuleConstants.PERCENT);
    public static final String SET_DUTY_VALUE = wholeCombine(DUTY_VALUE, group(ECU_NAME), RegexRuleConstants.PERCENT);
    public static final String SET_DID_INFO = wholeCombine(SET_DID, group(HEX_NUMBER), group(ECU_NAME));
    public static final String GET_DID_INFO = wholeCombine(GET_DID, group(HEX_NUMBER), group(ECU_NAME));

//    private static final String GET_PWM = wholeMatchCaseInsensitive("getPWM");
//    private static final String GET_VOLTAGE = wholeMatchCaseInsensitive("getVoltage");
//    public static final String SEND_UDP_COMMAND = wholeCombine(SET_DATA, group(ECU_NAME), group(ALL_NUMBER_WITH_HEX));
//    public static final String READ_UDP_DATA = wholeCombine(GET_DATA, group(ECU_NAME), group(ALL_NUMBER_WITH_HEX));
//    public static final String GET_VOLTAGE_RANDOM_DATA = wholeCombine(GET_VOLTAGE, group(ECU_NAME), RegexRuleConstants.RANDOM, BaseRegexRule.VOLTAGE_GROUP, BaseRegexRule.VOLTAGE_GROUP);
//    public static final String GET_VOLTAGE_RANGE_DATA = wholeCombine(GET_VOLTAGE, group(ECU_NAME), BaseRegexRule.VOLTAGE_GROUP, RegexRuleConstants.RANGE, BaseRegexRule.VOLTAGE_GROUP);
}
