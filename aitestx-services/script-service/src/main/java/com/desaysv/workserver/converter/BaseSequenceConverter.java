package com.desaysv.workserver.converter;

import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Lazy
public class BaseSequenceConverter {

    @Autowired
    private BuiltinRuleSequenceConverter builtinRuleSequenceConverter;

    @Autowired
    private LLMSequenceConverter llmSequenceConverter;


    public List<String> convert(ActionSequenceSimpleContext actionSequenceContext) {
        if (actionSequenceContext.getConvertMethod().equalsIgnoreCase("python")) {
            return builtinRuleSequenceConverter.convertAll(actionSequenceContext);
        } else {
            return llmSequenceConverter.convertAll(actionSequenceContext);
        }
    }
}
