package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class ProcedureRegexRule extends BaseRegexRule {

    public static final String ONE_LINE_CHECK = combine(BaseRegexRule.RegexRuleConstants.CHECK, group(CHECK_MORE_NUMBER));

    public static final String REPEAT_MULTI_LINES = combine(BaseRegexRule.RegexRuleConstants.REPEAT, group(CHECK_MORE_NUMBER), group(CHECK_MORE_NUMBER), group(NUMBER));

    public static final String CYCLE_CHECK_MULTI_LINES_WITH_TIME = combine(RegexRuleConstants.CYCLE, group(ANY_TIME), group(CHECK_MORE_NUMBER));


}
