package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

/**
 * LIN动作序列语法
 */
public class LinRegexRule extends BusRegexRule {

    /**
     * 功能：改变LIN信号值，固定
     * 格式：LIN-Sig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-X3(信号值)
     */
    public static final String CHANGE_LIN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：改变LIN信号值，固定
     * 格式：LIN-Sig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-X3(信号值， 十六进制)
     */
    public static final String CHANGE_LIN_SIGNAL_HEX = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(HEX_NUMBER));


    /**
     * 功能：改变LIN信号值，随机
     * 格式：LIN-Sig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-Random-X3(随机下限)-X4(随机上限)
     */
    public static final String RANDOM_LIN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(ALL_NUMBER), group(ALL_NUMBER));


    /**
     * 功能：改变LIN信号值，步进（带ECU名称）, 不带间隔时间
     * 格式：LIN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Step-X4(信号起始值)-X5(信号终止值)-X6(步进值)
     */
    public static final String STEP_LIN_SIGNAL_WITHOUT_TIME = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
            RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER));

    /**
     * 功能：改变LIN信号值，步进（带ECU名称）
     * 格式：LIN-X0(通道)-Sig-X1(ECU名称)-X2(报文名)-X3(信号名)-Step-X4(信号起始值)-X5(信号终止值)-X6(步进值)-X7(步进间隔时间)
     */
    public static final String STEP_LIN_SIGNAL = wholeCombine(SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
            RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));

    /**
     * 功能：改变LIN信号值，步进（不带ECU名称）
     * 格式：LIN-X0(通道)-Sig-X1(报文名)-X2(信号名)-Step-X3(信号起始值)-X4(信号终止值)-X5(步进值)-X7(步进间隔时间)
     */
    public static final String STEP_LIN_SIGNAL_WITHOUT_ECU = wholeCombine(SIG, group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID),
            RegexRuleConstants.STEP, group(ALL_NUMBER), group(ALL_NUMBER), group(ALL_NUMBER), group(ANY_TIME));

    /**
     * 功能：LIN单个报文的丢失和恢复
     * 格式：LIN-Msg-X0(ECU节点名称)-X1(报文ID)-X2(CAN报文状态, 0/1, 0：Inactive, 1：Active)
     */
    public static final String CHANGE_LIN_MSG = wholeCombine(MSG, group(ECU_NAME), group(HEX_NUMBER), group(NUMBER));

    /**
     * 功能：LIN ECU所有报文的丢失和恢复
     * 格式：LIN-Ecu-X0(ECU节点名称)-X1(报文状态, 0/1, 0：Inactive, 1：Active)
     */
    public static final String CHANGE_ALL_ECU_LIN_MSG = wholeCombine(ECU, group(ECU_NAME), group(NUMBER));

    /**
     * 功能：LIN所有报文的丢失和恢复
     * 格式：LIN-Channel-X0(报文状态, 0/1, 0：Inactive, 1：Active)
     */
    public static final String CHANGE_ALL_CHANNEL_LIN_MSG = wholeCombine(CHANNEL, group(NUMBER));

    /**
     * 功能：接收LIN信号期望值：固定
     * 格式：LIN-GetSig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-X3(信号期望值)
     */
    public static final String COMPARE_LIN_SIGNAL = wholeCombine(GET_SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ALL_NUMBER));

    /**
     * 功能：接收LIN信号期望值：固定
     * 格式：LIN-GetSig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-X3(信号期望值, 十六进制)
     */
    public static final String COMPARE_LIN_SIGNAL_HEX = wholeCombine(GET_SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), group(HEX_NUMBER));

    /**
     * 功能：接收LIN信号期望值：在一定范围内
     * 格式：LIN-GetSig-X0(ECU节点名称)-X1(报文名字)-X2(信号名字)-Random-X3(信号期望值下限)-X4(信号期望值上限)
     */
    public static final String COMPARE_LIN_SIGNAL_RANGE = wholeCombine(GET_SIG, group(ECU_NAME), group(MESSAGE_OR_SIGNAL_NAME_ID), group(MESSAGE_OR_SIGNAL_NAME_ID), RegexRuleConstants.RANDOM, group(ALL_NUMBER), group(ALL_NUMBER));


    public static final class PTS {
        /**
         * 功能：PTS写入（带ECU/报文id）
         * 格式：LIN-X0(通道)-PTSTX-X1(ECU名称)-X2(报文id)-X3(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_LIN_PTS = wholeCombine(PTSTX, group(ECU_NAME), group(HEX_NUMBER), group(BYTE_X_COMMAND));
        /**
         * 功能：PTS写入（只带ECU）
         * 格式：LIN-X0(通道)-PTSTX-X1(ECU名称)-X2(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_LIN_PTS_WITH_ECU = wholeCombine(PTSTX, group(ECU_NAME), group(BYTE_X_COMMAND));

        /**
         * 功能：PTS写入（不带ECU/报文id）
         * 格式：LIN-X0(通道)-PTSTX-X1(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_LIN_PTS_WITHOUT_ECU = wholeCombine(PTSTX, group(BYTE_X_COMMAND));

        /**
         * 功能：PTS写入（带报文id）
         * 格式：LIN-X0(通道)-PTSTX-X1(报文id)-X2(PTS字节的指令，可加空格)
         */
        public static final String CHANGE_LIN_PTS_BY_MESSAGE_ID = wholeCombine(PTSTX, group(HEX_NUMBER), group(BYTE_X_COMMAND));
        /**
         * 功能：PTS期望反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(PTS字节的指令)
         */
        public static final String COMPARE_LIN_PTS = wholeCombine(PTSRX, group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望反馈指令（带报文id）
         * 格式：LIN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)
         */
        public static final String COMPARE_LIN_PTS_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND));

        /**
         * 功能：PTS期望反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(PTS字节的指令)-byteX(需要检查的范围的字节)-X2(标准值)-X3(幅度)
         */
        public static final String COMPARE_LIN_PTS_HEX_BYTE = wholeCombine(PTSRX, group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(HEX_NUMBER), group(HEX_NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(PTS字节的指令)-byteX(需要检查的范围的字节)-X2(字节下限)-X3(字节上限)
         */
        public static final String COMPARE_LIN_PTS_BYTE = wholeCombine(PTSRX, group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(NUMBER), group(NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)-byteX(需要检查的范围的字节)-X3(字节下限)-X4(字节上限)
         */
        public static final String COMPARE_LIN_PTS_BYTE_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(NUMBER), group(NUMBER));

        /**
         * 功能：PTS期望反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(报文id)-X2(PTS字节的指令)-byteX(需要检查的范围的字节)-X3(标准值)-X4(幅度)
         */
        public static final String COMPARE_LIN_PTS_HEX_BYTE_WITH_MESSAGE_ID = wholeCombine(PTSRX, group(HEX_NUMBER), group(BYTE_X_COMMAND), RegexRuleConstants.BYTE_LOCATION, group(HEX_NUMBER), group(HEX_NUMBER));

        /**
         * 功能：PTS期望无反馈指令
         * 格式：LIN-X0(通道)-PTSRX-X1(PTS字节的指令)
         */
        public static final String COMPARE_LIN_PTS_NA = wholeCombine(PTSRX, BaseRegexRule.NA);

        /**
         * 功能：Lin唤醒功能
         * 格式：LIN-X0(通道)-WakeUp-0 (0代表启动唤醒， 1代表关闭唤醒)
         */
        public static final String LIN_WAKE_UP = wholeCombine(WAKEUP,  group(NUMBER));

        /**
         * 功能：期望总线上出现某个报文id
         * 格式：LIN-X0(通道)-CheckMsg-Exist-X1(报文id)
         */
        public static final String EXIST_LIN_MESSAGE_ID = wholeCombine(CHECK_MSG, EXIST, group(HEX_NUMBER));

        /**
         * 功能：期望总线上不出现某个报文id
         * 格式：LIN-X0(通道)-CheckMsg-NotExist-X1(报文id)
         */
        public static final String NOT_EXIST_LIN_MESSAGE_ID = wholeCombine(CHECK_MSG, NOT_EXIST, group(HEX_NUMBER));

        /**
         * 功能：持续100ms检测总线上出现某个报文id
         * 格式：LIN-X0(通道)-CheckMsg-Exist-X1(报文id)-last-100ms
         */
        public static final String LAST_CHECK_EXIST_LIN_MESSAGE_ID = wholeCombine(CHECK_MSG, EXIST, group(HEX_NUMBER), LAST, MILLI_TIME);

        /**
         * 功能：持续100ms检测总线上出现某个报文id
         * 格式：LIN-X0(通道)-CheckMsg-Exist-X1(报文id)-last-100ms
         */
        public static final String LAST_CHECK_NOT_EXIST_LIN_MESSAGE_ID = wholeCombine(CHECK_MSG, NOT_EXIST, group(HEX_NUMBER), LAST, MILLI_TIME);

    }

}
