package com.desaysv.workserver.result;

import cn.hutool.core.util.ArrayUtil;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.components.DeviceActionSequenceHeader;
import com.desaysv.workserver.context.ActionSequenceContext;
import com.desaysv.workserver.context.TestStep;
import com.desaysv.workserver.executor.ActionSequenceExecutorService;
import com.desaysv.workserver.finder.ActionSequenceDeviceFinderFactory;
import com.desaysv.workserver.parser.ActionSequenceParserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@Lazy
public class ActionSequenceCheckService {

    @Autowired
    private ActionSequenceExecutorService actionSequenceExecutorService;

    @Autowired
    private ActionSequenceDeviceFinderFactory actionSequenceDeviceFinderFactory;

    @Autowired
    private ActionSequenceParserService actionSequenceParserService;

    private static final Pattern CHECK_LINE_PATTERN = Pattern.compile("-?check-.*", Pattern.CASE_INSENSITIVE);

//    @Deprecated
//    public ActionSequenceCheckResult checkActionSequenceParagraphGrammar(String actionSequenceParagraph, List<String> resultCheckMarker) {
//        if (StrUtils.isEmpty(actionSequenceParagraph)) {
//            return new ActionSequenceCheckResult().setCheckOk(true);
//        }
//        List<ActionSequence> actionSequenceList = actionSequenceParserService.parseActionSequenceParagraph(actionSequenceParagraph);
//        for (ActionSequence actionSequence : actionSequenceList) {
//            if (actionSequence instanceof ActionSequenceUnit) {
//                if (actionSequence.isCheckOk()) {
//                    ActionSequenceUnit actionSequenceUnit = (ActionSequenceUnit) actionSequence;
//                    //继续下一步正则表达式检查
//                    if (!actionSequenceExecutorService.check(actionSequenceUnit, resultCheckMarker)) {
//                        actionSequenceUnit.error(String.format("\"%s\"无法匹配到正则表达式", actionSequenceUnit.getRegexRuleApplyExpression()));
//                    }
//                }
//            } else {
//                //暂不检查ActionSequenceGroup
//            }
//        }
//        return new ActionSequenceCheckResult().setActionSequenceList(actionSequenceList);
//    }

    /**
     * 附加动作序列参数
     *
     * @param actionSequenceList         动作序列列表
     * @param expectedActionSequenceList 期望结果动作序列列表
     */
    private void attachActionSequences(List<ActionSequence> actionSequenceList, List<ActionSequence> expectedActionSequenceList) {
        actionSequenceList.forEach(actionSequence -> {
            if (actionSequence.getRawExpression() != null) {
                //修复了其他关键字中有Send也会被影响到，例如："SendEventMsg"，会走进判断里面导致映射到接口时参数匹配有问题。因此增加-PTSTX-或者-Send-来匹配这两种类型
                if (actionSequence.getRawExpression().toUpperCase().contains(String.format("-%s-", BaseRegexRule.PTS_TX_CONSTANT.toUpperCase())) ||
                        actionSequence.getRawExpression().toUpperCase().contains(String.format("-%s-", BaseRegexRule.SERIAL_TX_CONSTANT.toUpperCase()))) {
                    List<String> checkResultIds = actionSequence.getOptions().getCheckResultIds();
                    if (ArrayUtil.isEmpty(checkResultIds)) {
                        //没有显式check
                        ActionSequence targetActionSequence = expectedActionSequenceList.isEmpty() ? null : expectedActionSequenceList.get(0);
                        if (targetActionSequence != null) {
                            actionSequence.getOptions().setCheckedContext(targetActionSequence.getRawExpression());
                        } else {
                            actionSequence.getOptions().setCheckedContext("NA");
                        }
                    } else {
                        if (checkResultIds.contains(BaseRegexRule.CHECK_ALL)) {
                            if (!expectedActionSequenceList.isEmpty()) {
                                actionSequence.getOptions().setCheckedContext(expectedActionSequenceList.get(0).getRawExpression());
                            } else {
                                actionSequence.getOptions().setCheckedContext("NA");
                            }
                        } else {
                            Optional<ActionSequence> anyTargetActionSequence = expectedActionSequenceList.stream().filter(s ->
                                    //FIXME，只支持第一个checkId
                                    Objects.equals(s.getUserSequenceOrder(), checkResultIds.get(0))).findAny();
                            if (anyTargetActionSequence.isPresent()) {
                                ActionSequence targetActionSequence = anyTargetActionSequence.get();
                                actionSequence.getOptions().setCheckedContext(targetActionSequence.getRawExpression());
                            } else {
                                actionSequence.getOptions().setCheckedContext("NA");
                            }
                        }
                    }
                }
            }
        });

    }

    public ActionSequenceCheckResult checkActionSequenceParagraphGrammar(List<TestStep> testStepList,
                                                                         List<String> resultCheckMarker,
                                                                         List<ActionSequence> expectedActionSequenceList) {
        //空序列检测
        if (CollectionUtils.isEmpty(testStepList)) {
            return new ActionSequenceCheckResult().setCheckOk(true);
        }
        List<ActionSequence> actionSequenceList = actionSequenceParserService.parseActionSequenceList(testStepList);
        if (expectedActionSequenceList != null) {
            //动作序列附加信息
            attachActionSequences(actionSequenceList, expectedActionSequenceList);
        }
        //序号重复性检查
        orderNoCheck(actionSequenceList);
        //语法检查
        for (ActionSequence actionSequence : actionSequenceList) {
            if (actionSequence instanceof ActionSequenceUnit) {
                ActionSequenceUnit actionSequenceUnit = (ActionSequenceUnit) actionSequence;
                if (actionSequence.isCheckOk()) {
                    //继续下一步正则表达式检查
                    if (!actionSequenceExecutorService.check(actionSequenceUnit, resultCheckMarker)) {
                        String error = String.format("\"%s\"语法检查错误：%s", actionSequenceUnit.getRawExpression(), actionSequence.getActionSequenceError().getErrorMessage());
                        log.warn(error);
                        actionSequenceUnit.error(error);
                    } else {
                        log.info("\"{}\"动作序列语法检查正确", actionSequenceUnit.getRawExpression());
                        //增加设备未连接检测
                        //增加设备连接检测，如果未连接也要放到error
                        if (actionSequenceUnit.getActionSequenceHeader() instanceof DeviceActionSequenceHeader) {
                            //设备方法
                            OperationTarget operationTarget = actionSequenceDeviceFinderFactory.findBySequenceDeviceName(actionSequenceUnit.getActionSequenceHeader());
                            if (operationTarget == null) {
                                if (actionSequenceUnit.getActionSequenceHeader() instanceof DeviceActionSequenceHeader) {
                                    actionSequence.setCheckOk(false);
                                    String errorMessage = String.format("序列：%s，错误原因：设备\"%s(#%d)\"没有连接成功", actionSequenceUnit.getRawExpression(),
                                            actionSequenceUnit.getActionSequenceHeader().getExecutorName(), ((DeviceActionSequenceHeader) actionSequenceUnit.getActionSequenceHeader()).getDeviceOrder());
                                    actionSequenceUnit.error(errorMessage);
                                }
                            }
                        }
                    }
                } else {
                    actionSequenceUnit.error(String.format("错误: 该序列单元格中的序号\"%s\"已存在，不允许重复出现!", actionSequenceUnit.getUserSequenceOrder()));
                }
            } else {
                //暂不检查ActionSequenceGroup
            }
        }
        return new ActionSequenceCheckResult().setActionSequenceList(actionSequenceList);
    }

    /**
     * 序号重复性检查
     *
     * @param actionSequenceList 动作序列列表
     */
    private static void orderNoCheck(List<ActionSequence> actionSequenceList) {
        Map<String, ActionSequence> actionSequenceMap = new HashMap<>();
        for (ActionSequence actionSequence : actionSequenceList) {
            if (StringUtils.isNotEmpty(actionSequence.getRawExpression())) {
                if (StringUtils.isNotEmpty(actionSequence.getUserSequenceOrder())
                        && actionSequenceMap.containsKey(actionSequence.getUserSequenceOrder())) {
                    actionSequence.setCheckOk(false);
                } else {
                    actionSequenceMap.put(actionSequence.getUserSequenceOrder(), actionSequence);
                }
            }
        }
    }


    /**
     * 提取期望结果序号
     *
     * @param expectResult 期望结果字符串
     * @return
     */
    private List<String> extractResultCheckMarker(String expectResult) {
        List<ActionSequence> actionSequenceList = actionSequenceParserService.parseActionSequenceParagraph(expectResult);
        return actionSequenceList.stream().map(ActionSequence::getUserSequenceOrder).collect(Collectors.toList());
    }

    /**
     * 提取期望结果序号
     *
     * @param expectTestStepList 期望结果步骤列表
     * @return
     */
    private List<String> extractResultCheckMarker(List<TestStep> expectTestStepList) {
        List<ActionSequence> actionSequences = actionSequenceParserService.parseActionSequenceList(expectTestStepList);
        return actionSequences.stream().map(ActionSequence::getUserSequenceOrder).collect(Collectors.toList());
    }

    public ActionSequenceCheckReporter checkActionSequenceGrammar(ActionSequenceContext actionSequenceContext) {
        //提取结果检查序号
        List<String> resultCheckMarker = new ArrayList<>();
        if (!CollectionUtils.isEmpty(actionSequenceContext.getExpectResult())) {
            log.info("--------------------------------------------------");
            resultCheckMarker.addAll(extractResultCheckMarker(actionSequenceContext.getExpectResult()));
            log.info("收集结果检查序号:{}", resultCheckMarker);
            log.info("--------------------------------------------------");
        }
        //检查动作序列语法
        log.info("~~~检查动作序列语法~~~");
        ActionSequenceCheckReporter actionSequenceCheckReporter = new ActionSequenceCheckReporter();
        //TODO:判断是否含有check语法
        log.info("*预处理期望结果语法*");
        List<ActionSequence> expectationSequenceList = actionSequenceParserService.parseActionSequenceList(actionSequenceContext.getExpectResult());

        if (actionSequenceContext.getSequenceType() == ActionSequenceContext.PRECONDITION) {
            log.info("*检查前提条件语法*");
            actionSequenceCheckReporter.setPreconditionCheckResult(checkActionSequenceParagraphGrammar(actionSequenceContext.getPrecondition(), resultCheckMarker, expectationSequenceList));
        } else if (actionSequenceContext.getSequenceType() == ActionSequenceContext.OPERATION_STEP) {
            log.info("*检查操作步骤语法*");
            //针对Operation的这列，没有check语法，默认最后一行加上语法为check all
            if (actionSequenceContext.isSingleCase() && actionSequenceContext.isCancelSingleTestCheckAny()) {
                actionSequenceContext.setOperationStep(removeCheckLine(actionSequenceContext.getOperationStep()));
            }
            if (!actionSequenceContext.isSingleCase() || !actionSequenceContext.isCancelSingleTestCheckAll()) {
                actionSequenceContext.setOperationStep(ensureCheckKeywordExistence(actionSequenceContext.getOperationStep()));
            }
            actionSequenceCheckReporter.setOperationStepCheckResult(checkActionSequenceParagraphGrammar(actionSequenceContext.getOperationStep(), resultCheckMarker, expectationSequenceList));
        } else if (actionSequenceContext.getSequenceType() == ActionSequenceContext.EXPECT_RESULT) {
        } else {
            log.info("*检查前提条件语法*");
            actionSequenceCheckReporter.setPreconditionCheckResult(checkActionSequenceParagraphGrammar(actionSequenceContext.getPrecondition(), resultCheckMarker, expectationSequenceList));
            log.info("*检查操作步骤语法*");
            //针对Operation的这列，没有check语法，默认最后一行加上语法为check all
            actionSequenceContext.setOperationStep(ensureCheckKeywordExistence(actionSequenceContext.getOperationStep()));
            actionSequenceCheckReporter.setOperationStepCheckResult(checkActionSequenceParagraphGrammar(actionSequenceContext.getOperationStep(), resultCheckMarker, expectationSequenceList));
        }
        log.info("*检查期望结果语法*");
        actionSequenceCheckReporter.setExpectResultCheckResult(checkActionSequenceParagraphGrammar(actionSequenceContext.getExpectResult(), resultCheckMarker, expectationSequenceList));  //单击单元格，也能执行check
        return actionSequenceCheckReporter.report();
    }

    //TestStep中的字符串截掉testStepList的所有CHECK_LINE_PATTERN语法
    private List<TestStep> removeCheckLine(List<TestStep> testStepList) {
        return testStepList.stream()
                .map(testStep -> {
                    testStep.setTestStep(testStep.getTestStep().replaceAll(CHECK_LINE_PATTERN.pattern(), ""));
                    return testStep;
                })
                .collect(Collectors.toList());
    }

    private List<TestStep> ensureCheckKeywordExistence(List<TestStep> testStepList) {
        if (!CollectionUtils.isEmpty(testStepList)) {
            boolean containsCheck = testStepList.stream()
                    .anyMatch(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                            CHECK_LINE_PATTERN.matcher(testStep.getTestStep()).find());
            if (!containsCheck) {
                TestStep lastTestStep = testStepList.stream()
                        .reduce((step1, step2) -> step2)
                        .orElse(null);
                if (lastTestStep != null) {
                    String newTestStep = String.format("%s%s", lastTestStep.getTestStep(), "-check-all");
                    lastTestStep.setTestStep(newTestStep);
                }
            }
        } else {
            if (testStepList == null) {
                testStepList = new ArrayList<>();
            }
            TestStep checkStep = new TestStep();
            checkStep.setUuid("0");
            checkStep.setTestStep("check-all");
            testStepList.add(checkStep);
        }
        return testStepList;
    }

}
