package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.RANGE;

public class ElectronicLoadRegexRule  extends BaseRegexRule {
    public static final String INPUT_STATUS_CONSTANTS = wholeMatchCaseInsensitive("setInputState");

    public static final String SET_CC = wholeCombine(RegexRuleConstants.SET_CURRENT, group(CURRENT));
    public static final String SET_CV = wholeCombine(RegexRuleConstants.SET_VOLTAGE, group(VOLTAGE));
    public static final String GET_CC = wholeCombine(RegexRuleConstants.GET_CURRENT, group(CURRENT));
    public static final String GET_CV = wholeCombine(RegexRuleConstants.GET_VOLTAGE, group(VOLTAGE));
    public static final String GET_CC_RANGE = wholeCombine(RegexRuleConstants.GET_CURRENT, group(CURRENT), RANGE, group(CURRENT));
    public static final String GET_CV_RANGE = wholeCombine(RegexRuleConstants.GET_VOLTAGE, group(VOLTAGE), RANGE, group(VOLTAGE));
    public static final String SET_INPUT_STATUS_ON_OFF = wholeCombine(INPUT_STATUS_CONSTANTS, group(RegexRuleConstants.ON_OFF));
}
