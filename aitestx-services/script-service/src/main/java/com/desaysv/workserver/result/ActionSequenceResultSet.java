package com.desaysv.workserver.result;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import lombok.Data;

@Data
public class ActionSequenceResultSet {

    private int sumCycle;

    private int failCycle;

    private String newlyTestResultData = "NA";

    private String resultSequenceExpression = "NA";

    private boolean exception;

    public void addSumCycle() {
        sumCycle++;
    }

    public void addFailCycle() {
        failCycle++;
    }

    public void addNewlyTestData(Object actualExpectedResult) {
        if (actualExpectedResult instanceof ActualExpectedResult) {
            newlyTestResultData = String.valueOf(actualExpectedResult);
        } else {
            newlyTestResultData = "NA";
        }
    }

    public boolean isPass() {
        return failCycle == 0;
    }

    public String conclusion() {
        if (exception) {
            return "Exception";
        }
        return isPass() ? "Pass" : "Fail";
    }

    public String failRate() {
        return String.format("(%d/%d)", failCycle, sumCycle);
    }

    public void exception() {
        exception = true;
    }

    @Override
    public String toString() {
//        return String.format("%s-%s-%s-%s", resultSequenceExpression, conclusion(), newlyTestResultData, failRate());
        return String.format("%s-%s-%s", resultSequenceExpression, newlyTestResultData, failRate());
    }


}
