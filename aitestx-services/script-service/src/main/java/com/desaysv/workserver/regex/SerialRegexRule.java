package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class SerialRegexRule extends BaseRegexRule {
    public static final String NAME = wholeMatchCaseInsensitive("Name");//指令名称
    public static final String CHECK_MSG = wholeMatchCaseInsensitive("CheckMsg");//指令名称
    public static final String SEND_CONSTANT = wholeMatchCaseInsensitive(SERIAL_TX_CONSTANT);
    public static final String RECV_CONSTANT = wholeMatchCaseInsensitive(SERIAL_RX_CONSTANT);
    public static final String SEND = wholeCombine(SEND_CONSTANT, group(BYTE_X_COMMAND_AUTO));
    public static final String SEND_BY_INTERPRETATION = wholeCombine(SEND_CONSTANT, NAME, group(MESSAGE_OR_SIGNAL_NAME_ID));
    public static final String SEND_PARENTHESIS = wholeCombine(SEND_CONSTANT, group(PARENTHESIS_COMMAND));
    public static final String SEND_CHECK_SUM = wholeCombine(SEND_CONSTANT, group(BYTE_X_COMMAND_AUTO), group(MESSAGE_OR_SIGNAL_NAME_ID));
    public static final String SEND_CHECK_INTERNAL = wholeCombine(SEND_CONSTANT, group(BYTE_X_COMMAND_AUTO), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ANY_TIME));
    public static final String RECV_PARENTHESIS = wholeCombine(RECV_CONSTANT, group(PARENTHESIS_COMMAND));
    public static final String RECV = wholeCombine(RECV_CONSTANT, group(BYTE_X_COMMAND_AUTO));
    public static final String RECV_WAIT = wholeCombine(RECV_CONSTANT, group(BYTE_X_COMMAND_AUTO), group(ANY_TIME));
    public static final String RECV_CHECK = wholeCombine(RECV_CONSTANT, group(BYTE_X_COMMAND_AUTO), group(MESSAGE_OR_SIGNAL_NAME_ID));
    public static final String RECV_CHECK_WAIT = wholeCombine(RECV_CONSTANT, group(BYTE_X_COMMAND_AUTO), group(MESSAGE_OR_SIGNAL_NAME_ID), group(ANY_TIME));
    public static final String SERIAL_CHECK_MSG = wholeCombine(CHECK_MSG,group(MESSAGE_OR_SIGNAL_NAME));
}
