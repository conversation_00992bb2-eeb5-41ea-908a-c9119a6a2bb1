package com.desaysv.workserver.context;

import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.parser.ActionSequenceParserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SoundSequenceHandler {

    private static final Pattern CHECK_LINE_PATTERN = Pattern.compile(".*-Check-.*", Pattern.CASE_INSENSITIVE);
    private static final Pattern SOUND_LINE_PATTERN = Pattern.compile(".*-AMP-.*", Pattern.CASE_INSENSITIVE);
    private static final Pattern SOUND_RECORD_LINE_PATTERN = Pattern.compile(".*-Record-On$", Pattern.CASE_INSENSITIVE);

    @Autowired
    private ActionSequenceParserService actionSequenceParserService;

    /**
     * 增加对声音处理
     * 前后增加 Sound-0-Record-ON，Sound-0-Record-OFF-check-1
     */
    public void addSoundStepsForChecks(ActionSequenceContext context) {
        List<TestStep> expectResult = context.getExpectResult();
        List<TestStep> operationStep = context.getOperationStep();
        
        if (expectResult == null || !containsSoundCheck(expectResult)) {
            return;
        }

        // 提取声音设备信息
        Integer channel = extractSoundChannel(expectResult);
        Integer deviceId = extractDeviceId(expectResult);

        // 创建录音相关步骤
        TestStep recordOnStep = createRecordStep(deviceId, channel, "On");
        TestStep waitTimeStep = createWaitStep(2000);
        TestStep recordOffStep = createRecordStep(deviceId, channel, "Off");

        // 处理不同情况下的录音步骤添加
        if (operationStep == null) {
            handleEmptyOperationSteps(context, recordOnStep, waitTimeStep, recordOffStep);
        } else if (!containsRecordStep(operationStep)) {
            if (!containsCheckStep(operationStep)) {
                handleNoCheckOperationSteps(operationStep, recordOnStep, waitTimeStep, recordOffStep);
            } else {
                handleCheckOperationSteps(operationStep, expectResult, recordOnStep, waitTimeStep, deviceId, channel);
            }
        }
    }

    /**
     * 检查期望结果中是否包含声音检查
     */
    private boolean containsSoundCheck(List<TestStep> steps) {
        return steps.stream()
                .anyMatch(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        SOUND_LINE_PATTERN.matcher(step.getTestStep()).find());
    }

    /**
     * 提取声音通道号
     */
    private Integer extractSoundChannel(List<TestStep> steps) {
        return steps.stream()
                .filter(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        SOUND_LINE_PATTERN.matcher(step.getTestStep()).find())
                .findFirst()
                .map(step -> {
                    Matcher matcher = Pattern.compile("(?<=-)(\\d+)(?=-AMP-)", Pattern.CASE_INSENSITIVE)
                            .matcher(step.getTestStep());
                    if (matcher.find()) {
                        try {
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            return 1;
                        }
                    }
                    return 1;
                })
                .orElse(1);
    }

    /**
     * 提取设备ID
     */
    private Integer extractDeviceId(List<TestStep> steps) {
        return steps.stream()
                .filter(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        SOUND_LINE_PATTERN.matcher(step.getTestStep()).find())
                .findFirst()
                .map(step -> {
                    Matcher matcher = Pattern.compile("Sound#(\\d+)-", Pattern.CASE_INSENSITIVE)
                            .matcher(step.getTestStep());
                    if (matcher.find()) {
                        try {
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            return 1;
                        }
                    }
                    return 1;
                })
                .orElse(1);
    }

    /**
     * 创建录音步骤
     */
    private TestStep createRecordStep(Integer deviceId, Integer channel, String action) {
        TestStep step = new TestStep();
        step.setTestStep("Sound#" + deviceId + "-" + channel + "-Record-" + action);
        return step;
    }

    /**
     * 创建等待步骤
     */
    private TestStep createWaitStep(int milliseconds) {
        TestStep step = new TestStep();
        step.setTestStep("Wait-" + milliseconds + "ms");
        return step;
    }

    /**
     * 检查操作步骤中是否包含录音步骤
     */
    private boolean containsRecordStep(List<TestStep> steps) {
        return steps.stream()
                .anyMatch(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        SOUND_RECORD_LINE_PATTERN.matcher(step.getTestStep()).find());
    }

    /**
     * 检查操作步骤中是否包含检查步骤
     */
    private boolean containsCheckStep(List<TestStep> steps) {
        return steps.stream()
                .anyMatch(step -> StringUtils.isNotEmpty(step.getTestStep()) &&
                        CHECK_LINE_PATTERN.matcher(step.getTestStep()).find());
    }

    /**
     * 处理空操作步骤的情况
     */
    private void handleEmptyOperationSteps(ActionSequenceContext context, TestStep recordOnStep, 
                                          TestStep waitTimeStep, TestStep recordOffStep) {
        List<TestStep> newOperationSteps = new ArrayList<>();
        newOperationSteps.add(recordOnStep);
        newOperationSteps.add(waitTimeStep);
        newOperationSteps.add(recordOffStep);
        context.setOperationStep(newOperationSteps);
    }

    /**
     * 处理没有检查步骤的操作步骤
     */
    private void handleNoCheckOperationSteps(List<TestStep> operationStep, TestStep recordOnStep, 
                                            TestStep waitTimeStep, TestStep recordOffStep) {
        operationStep.add(recordOnStep);
        operationStep.add(waitTimeStep);
        operationStep.add(recordOffStep);
    }

    /**
     * 处理包含检查步骤的操作步骤
     */
    private void handleCheckOperationSteps(List<TestStep> operationStep, List<TestStep> expectResult,
                                          TestStep recordOnStep, TestStep waitTimeStep, 
                                          Integer deviceId, Integer channel) {
        for (int i = 0; i < operationStep.size(); i++) {
            TestStep step = operationStep.get(i);
            String testStepStr = step.getTestStep();

            if (!testStepStr.trim().toLowerCase().contains("check")) {
                continue;
            }

            if (testStepStr.trim().toLowerCase().contains("-check-all")) {
                i = handleCheckAllStep(operationStep, i, recordOnStep, waitTimeStep, deviceId, channel);
                return;
            }

            List<String> checkPartStr = extractCheckNumber(testStepStr);
            if (checkPartStr.isEmpty()) {
                continue;
            }
            boolean isAmpCheck = false;
            for (String checkPartStrItem : checkPartStr){
                ActionSequence checkedSequence = findMatchingActionSequence(expectResult, checkPartStrItem);
                if (checkedSequence == null) {
                    continue;
                }
                TestStep checkStep = findMatchingTestStep(expectResult, checkedSequence);
                if (checkStep == null) {
                    continue;
                }
                if (checkStep.getTestStep().toLowerCase().contains("amp")) {
                    isAmpCheck = true;
                    break;
                }
            }
            if (isAmpCheck){
                i = handleAmpCheckStep(operationStep, i, recordOnStep, waitTimeStep, deviceId, channel);
            }
        }
    }

    /**
     * 处理Check-All步骤
     */
    private int handleCheckAllStep(List<TestStep> operationStep, int index, TestStep recordOnStep,
                                  TestStep waitTimeStep, Integer deviceId, Integer channel) {
        TestStep updatedStep = operationStep.get(index);
        String updatedTestStepStr = updatedStep.getTestStep();

        Pattern pattern = Pattern.compile("-Check-.*", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(updatedTestStepStr);
        
        if (matcher.find()) {
            String replacedPart = matcher.group();
            String newStepStr = updatedTestStepStr.replaceFirst("(?i)-check-.*", "").trim();
            updatedStep.setTestStep(newStepStr);

            TestStep soundOffStep = new TestStep();
            soundOffStep.setTestStep("Sound#" + deviceId + "-" + channel + "-Record-Off" + replacedPart);
            soundOffStep.setType(updatedStep.getType());

            operationStep.add(index + 1, recordOnStep);
            operationStep.add(index + 2, waitTimeStep);
            operationStep.add(index + 3, soundOffStep);
            return index + 3;
        }
        
        return index;
    }

    /**
     * 处理包含AMP的Check步骤
     */
    private int handleAmpCheckStep(List<TestStep> operationStep, int index, TestStep recordOnStep,
                                  TestStep waitTimeStep, Integer deviceId, Integer channel) {
        TestStep updatedStep = operationStep.get(index);
        String updatedTestStepStr = updatedStep.getTestStep();

        Pattern pattern = Pattern.compile("-Check-.*", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(updatedTestStepStr);
        
        if (matcher.find()) {
            String replacedPart = matcher.group();
            String newStepStr = updatedTestStepStr.replaceFirst("(?i)-check-.*", "").trim();
            updatedStep.setTestStep(newStepStr);

            TestStep soundOffStep = new TestStep();
            soundOffStep.setTestStep("Sound#" + deviceId + "-" + channel + "-Record-Off" + replacedPart);
            soundOffStep.setType(updatedStep.getType());

            operationStep.add(index + 1, recordOnStep);
            operationStep.add(index + 2, waitTimeStep);
            operationStep.add(index + 3, soundOffStep);
            return index + 3;
        }
        
        return index;
    }

    /**
     * 提取Check后面的序号
     */
    private List<String> extractCheckNumber(String testStepStr) {
        // 匹配 -check- 或 -Check- 等形式后的内容
        String pattern = "(?i).*?-Check-(.*)";
        java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher m = p.matcher(testStepStr);

        if (m.matches()) {
            String content = m.group(1); // 获取 -Check- 后的内容
            return java.util.Arrays.asList(content.split("-")); // 按照 '-' 分割成列表
        }

        return new java.util.ArrayList<>(); // 如果没匹配到返回空列表
    }

    /**
     * 查找匹配的ActionSequence
     */
    private ActionSequence findMatchingActionSequence(List<TestStep> expectResult, String checkPartStr) {
        List<ActionSequence> actionSequences = actionSequenceParserService.parseActionSequenceList(expectResult);
        return actionSequences.stream()
                .filter(s -> s.getUserSequenceOrder().equals(checkPartStr))
                .findFirst()
                .orElse(null);
    }

    /**
     * 查找匹配的TestStep
     */
    private TestStep findMatchingTestStep(List<TestStep> expectResult, ActionSequence checkedActionSequence) {
        return expectResult.stream()
                .filter(s -> s.getUuid().equals(checkedActionSequence.getTestStepUUID()))
                .findFirst()
                .orElse(null);
    }
}
