package com.desaysv.workserver.executor.procedure;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class ProcedureHandler extends OperationTarget implements IProcedure {

    @Override
    public void repeatMultiLines(int startNo, int endNo, int repetitions) {

    }

    @Override
    public void check(String checkMarker) {

    }

    @Override
    public void checkMultiLinesWithTime(String time, String checkMarker) {

    }

}
