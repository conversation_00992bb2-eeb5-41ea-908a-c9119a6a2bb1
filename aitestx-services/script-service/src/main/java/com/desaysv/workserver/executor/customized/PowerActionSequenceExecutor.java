package com.desaysv.workserver.executor.customized;

import com.desaysv.workserver.executor.BaseActionSequenceExecutor;
import com.desaysv.workserver.devices.power.base.interfaces.IPowerDevice;

/**
 * ActionSequenceExecutor注解范例，用来做特殊自定义执行器逻辑，
 * 普通用途可以不用重写BaseActionSequenceExecutor
 */
//@ActionSequenceExecutor(type = SequenceDeviceCollections.POWER)
//@Service
public class PowerActionSequenceExecutor extends BaseActionSequenceExecutor {


    @Override
    public Class<?> getExecutorClass() {
        return IPowerDevice.class;
    }
}
