package com.desaysv.workserver.components;

import lombok.Data;

@Data
public abstract class ActionSequence {

    private String testStepUUID; //序列UUID

    private String userSequenceOrder; //用户定义的序列序号

    private String rawExpression; //原始格式表达式

    private final ActionSequenceOptions options = new ActionSequenceOptions(); //动作序列选项

    private final ActionSequenceError actionSequenceError = new ActionSequenceError();  //序列语法错误

    private boolean checkOk; //是否编译通过

    private boolean executed;//是否被执行

    private boolean executeOk; //是否执行成功

}
