package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class TcpServerRegexRule extends BaseRegexRule {

    public static final String SEND_CONSTANT = wholeMatchCaseInsensitive(TCP_SEND);
    public static final String RECV_CONSTANT = wholeMatchCaseInsensitive(TCP_RECEIVE);

    public static final String SEND = wholeCombine(SEND_CONSTANT, group(PARENTHESIS_COMMAND));
    public static final String RECV = wholeCombine(RECV_CONSTANT, group(PARENTHESIS_COMMAND));
}
