package com.desaysv.workserver.result;

import com.desaysv.workserver.context.ActionSequenceContext;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

@Data
public class ActionSequenceCheckReporter {

    private ActionSequenceCheckResult preconditionCheckResult;

    private ActionSequenceCheckResult operationStepCheckResult;

    private ActionSequenceCheckResult expectResultCheckResult;

    @Setter(AccessLevel.NONE)
    private boolean checkOk;

    @Setter(AccessLevel.NONE)
    private boolean executed;

    @Setter(AccessLevel.NONE)
    private boolean executeOk;

    private boolean terminated;

    private String errorMessage;

    private ActionSequenceCheckResults actualResult = new ActionSequenceCheckResults();
    private ActionSequenceContext actionSequenceContext;

    public boolean isCheckOk(ActionSequenceCheckResult actionSequenceCheckResult) {
        return actionSequenceCheckResult == null || actionSequenceCheckResult.isCheckOk();
    }

    public boolean isExecuted(ActionSequenceCheckResult actionSequenceCheckResult) {
        return actionSequenceCheckResult == null || actionSequenceCheckResult.isExecuted();
    }

    public boolean isExecuteOk(ActionSequenceCheckResult actionSequenceCheckResult) {
        if (actionSequenceCheckResult == null || actionSequenceCheckResult.getActionSequenceList().isEmpty()) {
            return true;
        }
        if (actionSequenceCheckResult.isExecuted()) {
            return actionSequenceCheckResult.isExecuteOk();
        }
        return isCheckOk();
    }

    public boolean isCheckOk() {
        return isCheckOk(preconditionCheckResult) && isCheckOk(operationStepCheckResult) && isCheckOk(expectResultCheckResult);
    }

    public boolean isExecuted() {
        return isExecuted(preconditionCheckResult) || isExecuted(operationStepCheckResult) || isExecuted(expectResultCheckResult);
    }

    public boolean isExecuteOk() {
        return isExecuteOk(preconditionCheckResult) && isExecuteOk(operationStepCheckResult) && isExecuteOk(expectResultCheckResult);
    }

    public ActionSequenceCheckReporter report() {
        if (preconditionCheckResult != null) {
            preconditionCheckResult.report();
        }
        if (operationStepCheckResult != null) {
            operationStepCheckResult.report();
        }
        if (expectResultCheckResult != null) {
            expectResultCheckResult.report();
        }
        checkOk = isCheckOk();
        executed = isExecuted();
        executeOk = isExecuteOk();
        return this;
    }

}
