package com.desaysv.workserver.components;

import lombok.Data;

import java.util.List;

@Data
public class ActionSequenceOptions {

    private int repetitions = 1;

    private List<String> checkResultIds;

    private List<String> cycleCheckResultIds;

    private boolean enable = true; //默认启用

    private boolean grouped = false; //默认不组合

    private float cycleTime;

    private SequenceWaitTime afterSequenceWaitTime = new SequenceWaitTime();

    private SequenceWaitTime beforeSequenceWaitTime = new SequenceWaitTime();

    private String checkedContext; //待检查的上下文

}
