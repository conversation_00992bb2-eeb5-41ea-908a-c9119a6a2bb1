package com.desaysv.workserver.executor;

import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.result.ActionSequenceCheckResults;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 动作序列执行上下文 - 在执行过程中传递关键数据
 * 包括:
 * - 是否为模拟执行
 * - 期望的动作序列列表
 * - 检查结果集合
 */
@Data
public class ActionSequenceExecutorContext {
    private boolean executionFailed = false; // 新增执行状态标志
    private boolean simulated = false;

    private List<ActionSequence> expectedActionSequenceList;

    private ActionSequenceCheckResults actionSequenceCheckResults;

    public static ActionSequenceExecutorContext build() {
        ActionSequenceExecutorContext actionSequenceExecutorContext = new ActionSequenceExecutorContext();
        actionSequenceExecutorContext.setExpectedActionSequenceList(new ArrayList<>());
        actionSequenceExecutorContext.setActionSequenceCheckResults(new ActionSequenceCheckResults());
        return actionSequenceExecutorContext;
    }

    public static ActionSequenceExecutorContext build(List<ActionSequence> expectedActionSequenceList,
                                                      ActionSequenceCheckResults actionSequenceCheckResults) {
        ActionSequenceExecutorContext actionSequenceExecutorContext = new ActionSequenceExecutorContext();
        actionSequenceExecutorContext.setExpectedActionSequenceList(expectedActionSequenceList);
        actionSequenceExecutorContext.setActionSequenceCheckResults(actionSequenceCheckResults);
        return actionSequenceExecutorContext;
    }

}
