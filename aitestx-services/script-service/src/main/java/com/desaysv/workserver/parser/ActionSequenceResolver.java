package com.desaysv.workserver.parser;

import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.components.ActionSequenceOptions;
import com.desaysv.workserver.components.ActionSequenceUnit;
import com.desaysv.workserver.components.OrderedActionSequence;
import com.desaysv.workserver.regex.ProcedureRegexRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Lazy
public class ActionSequenceResolver {

    @Deprecated
    public List<ActionSequence> resolveRepeatActionSequences(List<ActionSequence> actionSequenceList) {
        Pattern pattern = Pattern.compile(ProcedureRegexRule.REPEAT_MULTI_LINES);
        return actionSequenceList.stream().filter(actionSequence -> {
            if (actionSequence instanceof ActionSequenceUnit) {
                ActionSequenceUnit actionSequenceUnit = (ActionSequenceUnit) actionSequence;
                Matcher matcher = pattern.matcher(actionSequenceUnit.getRawExpression());
                boolean match = matcher.find();
                if (match) {
                    actionSequence.getOptions().setGrouped(true);
                }
                return match;
            }
            return false;
        }).collect(Collectors.toList());
    }

    private Map<String, OrderedActionSequence> resolveOrderedActionSequenceMap(List<ActionSequence> actionSequenceList) {
        Map<String, OrderedActionSequence> orderedActionSequenceMap = new LinkedHashMap<>();
        for (int order = 0; order < actionSequenceList.size(); order++) {
            ActionSequence actionSequence = actionSequenceList.get(order);
            orderedActionSequenceMap.put(actionSequence.getUserSequenceOrder(), new OrderedActionSequence(order, actionSequence));
        }
        return orderedActionSequenceMap;
    }

    public void resolveGroupedActionSequenceMap(List<ActionSequence> actionSequenceList) {
        Map<String, OrderedActionSequence> orderedActionSequenceMap = resolveOrderedActionSequenceMap(actionSequenceList);
        for (int index = 0; index < actionSequenceList.size(); index++) {
            ActionSequence actionSequence = actionSequenceList.get(index);

            ActionSequenceUnit actionSequenceUnit = (ActionSequenceUnit) actionSequence;
            if (StringUtils.isEmpty(actionSequenceUnit.getRawExpression()))
                continue;
            Pattern pattern = Pattern.compile(ProcedureRegexRule.REPEAT_MULTI_LINES);
            Matcher matcher = pattern.matcher(actionSequenceUnit.getRawExpression());

            if (matcher.find()) {
                String headUserOrder = matcher.group(1);
                OrderedActionSequence headActionSequenceUnit = orderedActionSequenceMap.get(headUserOrder);
                if (headActionSequenceUnit == null) {
                    actionSequenceUnit.setCheckOk(false);
                    actionSequenceUnit.error(String.format("无法找到循环开始用户索引\"%s\"", headUserOrder));
                    continue;
                }
                String tailUserOrder = matcher.group(2);
                OrderedActionSequence tailActionSequenceUnit = orderedActionSequenceMap.get(tailUserOrder);
                if (tailActionSequenceUnit == null) {
                    actionSequenceUnit.setCheckOk(false);
                    actionSequenceUnit.error(String.format("无法找到循环结束用户索引\"%s\"", tailUserOrder));
                    continue;
                }
                ActionSequenceGroup actionSequenceGroup = new ActionSequenceGroup();
                actionSequenceGroup.setUserSequenceOrder(actionSequence.getUserSequenceOrder());
                actionSequenceGroup.setLoopCycle(Integer.parseInt(matcher.group(3)));
                actionSequenceGroup.setRawExpression(actionSequence.getRawExpression());
                for (int orderNumber = headActionSequenceUnit.getOrderNumber(); orderNumber <= tailActionSequenceUnit.getOrderNumber(); orderNumber++) {
                    actionSequenceGroup.addToGroup(actionSequenceList.get(orderNumber));
                }
                actionSequenceList.set(index, actionSequenceGroup);
            }
        }
    }

    @Deprecated
    public void markDisabledActionSequences(List<ActionSequence> actionSequenceList, Set<Integer> disabledActionSequenceIndexes) {
        for (int index = 0; index < actionSequenceList.size(); index++) {
            if (disabledActionSequenceIndexes.contains(index)) {
                ActionSequenceOptions actionSequenceOptions = actionSequenceList.get(index).getOptions();
                if (!actionSequenceOptions.isGrouped()) {
                    actionSequenceOptions.setEnable(false);
                }
            }
        }
    }

}
