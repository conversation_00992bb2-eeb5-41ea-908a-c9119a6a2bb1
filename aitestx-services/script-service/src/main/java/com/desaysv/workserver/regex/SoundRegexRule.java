package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.AMP;

public class SoundRegexRule extends BaseRegexRule {

    public static final String SOUND_CONSTANT = wholeMatchCaseInsensitive("Record");
    private final static String FREQUENCY = wholeMatchCaseInsensitive("Frequency");
    private final static String PERIOD = wholeMatchCaseInsensitive("Period");
    private final static String MAX = wholeMatchCaseInsensitive("Max");
    private final static String MIN = wholeMatchCaseInsensitive("Min");
    private final static String LOUD = wholeMatchCaseInsensitive("Loud");//持续蜂鸣关键词
    private final static String MUTE = wholeMatchCaseInsensitive("Mute");//静音关键词

    /**
     * 作用：开关录音
     * 格式：Sound-X0(通道)-Record-On/Off(开关状态,ON开,OFF关)
     */
    public static final String SOUND_RECORD_ON_OFF =  wholeCombine(SOUND_CONSTANT, group(RegexRuleConstants.ON_OFF));
    /**
     * 作用：声音有无达到范围值（ON到OFF）
     * 格式：Sound-X0(通道)-AMP-X1(范围起始)
     */
    public static final String SOUND_START = wholeCombine(AMP, group(NUMBER));
    /**
     * 作用：声音有无达到范围值（ON到OFF）
     * 格式：Sound-X0(通道)-AMP-X1(范围起始)-X2(范围终止)
     */
    public static final String SOUND_START_TO_END = wholeCombine(AMP, group(NUMBER), group(NUMBER));
    /**
     * 作用：声音在持续时间有无达到范围值（起始到终止）
     * 格式：Sound-X0(通道)-AMP-X1(范围起始)-X2(范围终止)-X8(持续时间)
     */
    public static final String SOUND_START_TO_END_TIME = wholeCombine(AMP, group(NUMBER), group(NUMBER), group(ANY_TIME));
    /**
     * 作用：声音持续蜂鸣（所有音量达到阈值）
     * 格式：Sound-X0(通道)-AMP-Loud-X1(蜂鸣阈值 )
     */
    public static final String SOUND_LOUD = wholeCombine(AMP, LOUD, group(NUMBER));
    /**
     * 作用：声音持续静音（所有音量低于阈值）
     * 格式：Sound-X0(通道)-AMP-Mute-X1(静音阈值)
     */
    public static final String SOUND_MUTE = wholeCombine(AMP, MUTE, group(NUMBER));
    /**
     * 作用：声音在默认时间检测周期
     * 格式：Sound-X0(通道)-Period-X1(蜂鸣阈值)-X2(目标周期)（以S为单位）
     */
    public static final String SOUND_CYCLE = wholeCombine(PERIOD, group(NUMBER), group(NUMBER));
    /**
     * 作用：声音在给定时间检测周期
     * 格式：Sound-X0(通道)-Period-X1(蜂鸣阈值)-X2(目标周期)-X3(录音时间)（以S为单位）
     */
    public static final String SOUND_CYCLE_TIME = wholeCombine(PERIOD, group(NUMBER), group(NUMBER), group(NUMBER));
    /**
     * 作用：声音在默认时间检测频率
     * 格式：Sound-X0(通道)-Frequency-X1(最小频率)-X2(最大频率)
     */
    public static final String SOUND_FREQUENCY = wholeCombine(FREQUENCY, group(NUMBER), group(NUMBER));
    /**
     * 作用：声音在给定时间检测频率
     * 格式：Sound-X0(通道)-Frequency-X1(最小频率)-X2(最大频率)-X3(录音时间)
     */
    public static final String SOUND_FREQUENCY_TIME = wholeCombine(FREQUENCY, group(NUMBER), group(NUMBER), group(NUMBER));
    /**
     * 作用：声音设定时间内持续蜂鸣（所有音量达到阈值）
     * 格式：Sound-X0(通道)-AMP-Loud-X1(蜂鸣阈值)-X2(设定时间)
     */
    public static final String SOUND_LOUD_TIME = wholeCombine(AMP, LOUD, group(NUMBER), group(ANY_TIME));
    /**
     * 作用：声音设定时间内持续静音（所有音量低于阈值）
     * 格式：Sound-X0(通道)-AMP-Mute-X1(蜂鸣阈值)-X2(设定时间)
     */
    public static final String SOUND_MUTE_TIME = wholeCombine(AMP, MUTE, group(NUMBER), group(ANY_TIME));

}
