package com.desaysv.workserver.converter;

import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.context.ActionSequenceSimpleContext;
import com.desaysv.workserver.utils.StrUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

@Component
@Lazy
@Slf4j
public class LLMSequenceConverter implements SequenceConverter {
    private static final String ORIGINAL_BASE_URL = "http://10.219.78.136:8822/v1/chat/completions";
    //http://10.133.5.106:8000/forward
    private static final String BASE_URL = StrUtils.isEmpty(SystemEnv.getLocalLlmUrl()) ? ORIGINAL_BASE_URL : SystemEnv.getLocalLlmUrl();
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private final OkHttpClient client;
    int TIMEOUT = 60;

    public LLMSequenceConverter() {
        this.client = new OkHttpClient.Builder()
                .followRedirects(true)  // 启用重定向跟随
                .connectTimeout(Duration.ofSeconds(TIMEOUT))
                .readTimeout(Duration.ofSeconds(TIMEOUT))
                .writeTimeout(Duration.ofSeconds(TIMEOUT))
                .build();
    }

    // 请求数据类
    static class RequestData {
        String input;
        String task;
        boolean stream;

        public RequestData(String input, String task, boolean stream) {
            this.input = input;
            this.task = task;
            this.stream = stream;
        }
    }

    public String sendChatCompletionRequest(String input, String task, boolean stream) {
        Gson gson = new Gson();
        RequestData data = new RequestData(input, task, stream);
        // 序列化 JSON 对象
        String jsonBody = gson.toJson(data);

        RequestBody body = RequestBody.create(JSON, jsonBody);

        Request request = new Request.Builder()
                .url(BASE_URL)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "HTTP request failed with status code: " + response.code() +
                        ", message: " + response.message();
            }
            assert response.body() != null;
            return extractContentFromJson(response.body().string());
        } catch (IOException e) {
            return "HTTP request failed with exception: " + e.getMessage();
        }
    }

    public String extractContentFromJson(String jsonString) {
        try {
            // 创建ObjectMapper实例
            ObjectMapper mapper = new ObjectMapper();
            // 将JSON字符串解析为JsonNode
            JsonNode rootNode = mapper.readTree(jsonString);
            // 导航到"choices"数组的第一个元素
            JsonNode choicesNode = rootNode.path("choices").get(0);
            // 从"message"对象中提取"content"字段
            return choicesNode.path("message").path("content").asText();
        } catch (Exception e) {
            // 异常处理
            log.error(e.getMessage(), e);
            return "Error parsing JSON: " + e.getMessage();
        }
    }

    @Override
    public String convertLine(String naturalLanguage) {
        return "";
    }

    @Override
    public List<String> convertAll(ActionSequenceSimpleContext actionSequenceContext) {
        List<String> actionSequenceList = new ArrayList<>();
        boolean stream = false;
        actionSequenceList.add(sendChatCompletionRequest(actionSequenceContext.getPrecondition(), "A", stream));
        actionSequenceList.add(sendChatCompletionRequest(actionSequenceContext.getOperationStep(), "B", stream));
        actionSequenceList.add(sendChatCompletionRequest(actionSequenceContext.getExpectResult(), "C", stream));
        return actionSequenceList;
    }

    public static void main(String[] args) {
        System.out.println(new LLMSequenceConverter().sendChatCompletionRequest("KL30打开", "A", false));
    }
}
