package com.desaysv.workserver.result;

import com.desaysv.workserver.components.ActionSequence;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Data
public class ActionSequenceCheckResult {

    private List<ActionSequence> actionSequenceList = new ArrayList<>();

    @Setter(AccessLevel.NONE)
    private boolean checkOk;

    @Setter(AccessLevel.NONE)
    private boolean executed;

    @Setter(AccessLevel.NONE)
    private boolean executeOk;

    private int executeState; //TODO：增加->未执行、执行成功、执行失败

    public boolean isCheckOk() {
        return actionSequenceList.stream().allMatch(ActionSequence::isCheckOk);
    }

    public boolean isExecuted() {
        return actionSequenceList.stream().anyMatch(ActionSequence::isExecuted);
    }


    public boolean isExecuteOk() {
        if (!isExecuted()) {
            return false;
        }

        for (ActionSequence actionSequence : actionSequenceList) {
            if (actionSequence.isExecuted() && !actionSequence.isExecuteOk()) {
                return false;
            }
        }
        return true;
    }

    public boolean isValid() {
        return actionSequenceList.size() > 0 && isCheckOk();
    }

    public ActionSequenceCheckResult setActionSequenceList(List<ActionSequence> actionSequenceList) {
        this.actionSequenceList = actionSequenceList;
        return this;
    }

    public ActionSequenceCheckResult setCheckOk(boolean checkOk) {
        this.checkOk = checkOk;
        return this;
    }

    public void report() {
        checkOk = isCheckOk();
        executed = isExecuted();
        executeOk = isExecuteOk();
    }

    public static void main(String[] args) {
        System.out.println(new ActionSequenceCheckResult().isValid());
    }

}