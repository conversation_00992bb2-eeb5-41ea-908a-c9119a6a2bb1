package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class AutoClickerRegexRule extends BaseRegexRule {

    private static final String CLICK = wholeMatchCaseInsensitive("Click");
    private static final String TOUCH = wholeMatchCaseInsensitive("Touch");
    private static final String RELEASE = wholeMatchCaseInsensitive("Release");
    /**
     * 功能：点击
     * 格式：Click-X0(通道)-X1(按下时间)-X2(间隔时间)-X3(次数)
     */
    public static final String CLICK_ONE_CHANNEL = wholeCombine(CLICK, group(INTEGER_NUMBER), group(ANY_TIME), group(ANY_TIME), group(INTEGER_NUMBER));
    /**
     * 功能：按下
     * 格式：Touch-X0(通道)
     */
    public static final String CLICK_TOUCH = wholeCombine(TOUCH, group(INTEGER_NUMBER));
    /**
     * 功能：释放
     * 格式：Release-X0(通道)
     */
    public static final String CLICK_RELEASE = wholeCombine(RELEASE, group(INTEGER_NUMBER));


}
