package com.desaysv.workserver.components;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.utils.ReflectUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 动作序列设备集
 */
public class SequenceDeviceKeywords extends BaseRegexRule {

    //输出
    public static final String POWER = "pwr"; //电源
    public static final String ROBOT = lower("robot"); //机械臂
    public static final String VISION = lower("vision"); //视觉
    public static final String CAPTURE = lower("capture"); //视频采集卡
    public static final String SERIAL = lower("serial");  //串口
    public static final String RELAY_BOARD = lower("switch"); //继电器板卡
    public static final String RESISTOR = lower("res"); //电阻板卡
    public static final String PWM_OUT = lower("pwmOut"); //PWM输出板卡
    //    public static final String TRI_STATE_OUTPUT = "hl"; //三态输出板卡
    public static final String TRI_STATE_OUTPUT = lower("VoltOut"); //三态输出板卡
    public static final String CAN = lower("CAN"); //CAN
    public static final String LIN = lower("LIN"); //LIN
    //    public static final String FLEX_RAY = lower("FLEXRAY"); //FLEXRAY
    public static final String ETHERNET = lower("ETH"); //以太网
    public static final String ADS_TOOL = lower("ADSTool"); //ADSTool
    public static final String QNX = lower("qnx"); //QNX仪表

    //输入
//    public static final String PWM_IN = lower("pwmIn"); //PWM采集反馈
    //    public static final String COLLECT_VOLTAGE = lower("collectV"); //模拟采集电压反馈
    public static final String COLLECT_VOLTAGE = lower("VoltGet"); //电压采集卡
    public static final String PWM_IN = lower("PWMGet"); //PWM采集卡
    public static final String BUZZER = lower("buzzer"); //蜂鸣器采集卡
    public static final String SOUND = lower("sound"); //声音采集卡
    public static final String UDP = lower("UDP");//UDP设备
    public static final String TCP = lower("TCP");//TCP客户端设备
    public static final String ELE_LOAD = lower("ELoad"); //电子负载
    public static final String OSCILLOSCOPE = lower("Osc"); //示波器
    public static final String SIGNAL_GENERATOR = lower("Sig"); //信号发生器
    public static final String DC_COLLECTOR = lower("DC"); //直流采集器

    public static final String DAQ = lower("daq");

    public static final String ADB = lower("adb"); //HUD_ADB设备
    public static final String TCP_SERVER = lower("TcpServer"); //TCP服务器
    public static final String AUTO_CLICKER = lower("clicker"); //点击器

    public static Set<String> getCollections() {
        try {
            return new HashSet<>(ReflectUtils.<String>getStaticProperties(SequenceDeviceKeywords.class).values());
        } catch (Exception e) {
            return new HashSet<>();
        }
    }

    public static void main(String[] args) {
        System.out.println(SequenceDeviceKeywords.getCollections());
    }
}
