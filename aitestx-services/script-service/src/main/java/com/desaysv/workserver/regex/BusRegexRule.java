package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class BusRegexRule extends BaseRegexRule {

    public static final String GET_SIG = wholeMatchCaseInsensitive("GetSig");
    public static final String SIG = wholeMatchCaseInsensitive("Sig");
    public static final String MSG = wholeMatchCaseInsensitive("Msg");
    public static final String ECU = wholeMatchCaseInsensitive("Ecu");
    public static final String CHANNEL = wholeMatchCaseInsensitive("Channel");
    public static final String SET_UDS_LOG_METHOD_NAME = wholeMatchCaseInsensitive("UDSLog");
    public static final String SET_UDS_LOG_NAME_METHOD_NAME = wholeMatchCaseInsensitive("UDSLogName");

    public static final String PTSTX = wholeMatchCaseInsensitive(PTS_TX_CONSTANT);
    public static final String PTSRX = wholeMatchCaseInsensitive(PTX_RX_CONSTANT);
    public static final String CHECK_MSG = wholeMatchCaseInsensitive("CheckMsg");
    public static final String LAST = wholeMatchCaseInsensitive("last");
    public static final String EXIST = wholeMatchCaseInsensitive("Exist");
    public static final String NOT_EXIST = wholeMatchCaseInsensitive("NotExist");
    public static final String WAKEUP = wholeMatchCaseInsensitive("WakeUp");
    public static final String SET_CAN_LOG_METHOD_NAME = wholeMatchCaseInsensitive("CANLog");
    public static final String SET_CAN_LOG_NAME_METHOD_NAME = wholeMatchCaseInsensitive("CANLogName");

}
