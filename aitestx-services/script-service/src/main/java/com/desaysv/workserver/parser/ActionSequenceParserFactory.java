package com.desaysv.workserver.parser;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.components.*;
import com.desaysv.workserver.parser.customized.PowerActionSequenceParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.RANDOM_WAIT_LINE;
import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.WAIT_LINE;
import static com.desaysv.workserver.action_sequence.BaseRegexRule.SPLITTER;

/**
 * 动作序列解析工厂
 */
@Service
@Slf4j
@Lazy
public class ActionSequenceParserFactory implements ApplicationListener<ContextRefreshedEvent>, ActionSequenceParserStrategy {
    private final static Map<String, ActionSequenceParserStrategy> MAP = new HashMap<>();
    private final Pattern splitterPattern = Pattern.compile(BaseRegexRule.SPLITTER + BaseRegexRule.END_INDEX);

    public static void addStrategyManually() {
        MAP.put(SequenceDeviceKeywords.POWER, new PowerActionSequenceParser());
    }

    /**
     * 过滤动作序列时间等待部分
     *
     * @param actionSequenceUnit 动作序列
     * @return
     */
    private ActionSequenceUnit filterActionSequenceWaitTime(ActionSequenceUnit actionSequenceUnit) {
        SequenceWaitTime afterSequenceWaitTime = actionSequenceUnit.getOptions().getAfterSequenceWaitTime();
        //后缀等待时间
        ActionSequenceUnit unit = extractWaitTime(actionSequenceUnit, afterSequenceWaitTime, SPLITTER + WAIT_LINE, SPLITTER + RANDOM_WAIT_LINE);
        SequenceWaitTime beforeSequenceWaitTime = actionSequenceUnit.getOptions().getBeforeSequenceWaitTime();
        //前缀等待时间
        return extractWaitTime(unit, beforeSequenceWaitTime, WAIT_LINE + SPLITTER, RANDOM_WAIT_LINE + SPLITTER);
    }

    private ActionSequenceUnit extractWaitTime(ActionSequenceUnit actionSequenceUnit, SequenceWaitTime sequenceWaitTime, String waitTimeRegex, String randomWaitTimeRegex) {
        Pattern pattern = Pattern.compile(waitTimeRegex);
        Pattern randomPattern = Pattern.compile(randomWaitTimeRegex);
        String rawExpression = actionSequenceUnit.getRawExpression();
        if (rawExpression != null) {
            Matcher matcher = pattern.matcher(rawExpression);
            Matcher randomMatcher = randomPattern.matcher(rawExpression);
            if (matcher.find()) {
                sequenceWaitTime.setWaitTime(BaseRegexRule.getSecondsOfDefaultMills(matcher.group(1)));
                rawExpression = rawExpression.replaceAll(waitTimeRegex, "");
                actionSequenceUnit.setRawExpression(rawExpression);
                log.info("等待时间前后缀过滤:{}", actionSequenceUnit.getOptions());
            } else if (randomMatcher.find()) {
                Float lowerSeconds = BaseRegexRule.getSecondsOfDefaultMills(randomMatcher.group(1));
                Float upperSeconds = BaseRegexRule.getSecondsOfDefaultMills(randomMatcher.group(2));
                sequenceWaitTime.setWaitTimeRandomEnabled(true);
                sequenceWaitTime.setLowerTime(lowerSeconds);
                sequenceWaitTime.setUpperTime(upperSeconds);
                rawExpression = rawExpression.replaceAll(randomWaitTimeRegex, "");
                actionSequenceUnit.setRawExpression(rawExpression);
                log.info("随机等待时间前后缀过滤:{}", actionSequenceUnit.getOptions());
            }
        }
        return actionSequenceUnit;
    }

    /**
     * 构建协议字段
     *
     * @param sequenceSentence 动作序列语句
     * @return
     */
    private ActionSequenceUnit regexSplitActionSequenceUnit(String sequenceSentence) {
        ActionSequenceUnit actionSequenceUnit = new ActionSequenceUnit();
        actionSequenceUnit.setCheckOk(true); //默认通过
        actionSequenceUnit.setRawExpression(sequenceSentence);
        //过滤等待时间
        filterActionSequenceWaitTime(actionSequenceUnit);
        //重复设置整行匹配句
        sequenceSentence = actionSequenceUnit.getRawExpression();

//        Pattern pattern = Pattern.compile(
//                String.format("(?:(?<userSequenceOrder>\\d+)\\.)?(?<executeName>\\d*[A-Za-z]\\w*)#?(?<deviceOrder>\\d+)?%s?(?<deviceChannel>\\d+)?%s?",
//                        BaseRegexRule.SPLITTER, BaseRegexRule.SPLITTER));
        Pattern pattern = Pattern.compile(
                String.format("(?:(?<userSequenceOrder>[A-Za-z0-9]+)\\.)?(?<executeName>\\d*[A-Za-z]\\w*)#?(?<deviceOrder>\\d+)?%s?(?<deviceChannel>\\d+)?%s?",
                        BaseRegexRule.SPLITTER, BaseRegexRule.SPLITTER));
        Matcher m = pattern.matcher(sequenceSentence);
        if (m.find()) {
            ActionSequenceHeader actionSequenceHeader;
            String userSequenceOrder = m.group("userSequenceOrder");
            if (userSequenceOrder != null) {
                actionSequenceUnit.setUserSequenceOrder(userSequenceOrder);
            }

            String executeName = m.group("executeName");
            if (!SequenceDeviceKeywords.getCollections().contains(executeName.toLowerCase())) {
                //不存在设备序列号
                actionSequenceHeader = new CommonActionSequenceHeader();
                actionSequenceUnit.setHeaderExpression(userSequenceOrder == null ? executeName : userSequenceOrder +BaseRegexRule.DOT + executeName);

                if (userSequenceOrder != null) {
                    Pattern p = Pattern.compile("^" + Pattern.quote(userSequenceOrder + BaseRegexRule.DOT) + "(.*)$");
                    Matcher matcher = p.matcher(sequenceSentence);
                    if (matcher.find()) {
                        actionSequenceUnit.setExtraSequenceExpression(matcher.group(1));
                    } else {
                        // 如果模式不匹配，记录错误或使用原始句子
                        log.warn("无法匹配序号模式: userSequenceOrder={}, sequenceSentence={}", userSequenceOrder, sequenceSentence);
                        actionSequenceUnit.setExtraSequenceExpression(sequenceSentence);
                    }
                } else {
                    actionSequenceUnit.setExtraSequenceExpression(sequenceSentence);
                }
//                System.out.println("Common:");
//                if (!CommonRegexRule.getCollections().contains(executeName.toLowerCase())) {
//                    String info = String.format("无法找到通用关键字\"%s\"", executeName);
//                    log.warn(info);
//                    actionSequenceUnit.error(info);
//                }
            } else {
                //存在设备序列号
                DeviceActionSequenceHeader deviceActionSequenceHeader = new DeviceActionSequenceHeader();
                actionSequenceHeader = deviceActionSequenceHeader;
                actionSequenceUnit.setHeaderExpression(m.group());
                String[] sequenceList = sequenceSentence.split(actionSequenceUnit.getHeaderExpression());
                if (sequenceList.length > 0) {
                    //存在除序列头外的语句
                    String extraActionSequenceSentence = sequenceList[sequenceList.length - 1];
                    actionSequenceUnit.setExtraSequenceExpression(extraActionSequenceSentence);
                }
                String deviceOrder = m.group("deviceOrder");
                String deviceChannel = m.group("deviceChannel");
                if (deviceOrder != null) {
                    deviceActionSequenceHeader.setDeviceOrder(Integer.parseInt(deviceOrder));
                }
                if (deviceChannel != null) {
                    deviceActionSequenceHeader.setDeviceChannel(Integer.parseInt(deviceChannel));
                    if (!splitterPattern.matcher(actionSequenceUnit.getHeaderExpression()).find()) {
                        actionSequenceUnit.error(String.format("设备通道后面没有跟着%s", BaseRegexRule.NORMAL_SPLITTER));
                    }
                }
            }
            actionSequenceUnit.setActionSequenceHeader(actionSequenceHeader);
            actionSequenceUnit.getActionSequenceHeader().setExecutorName(executeName);
//            System.out.println("sequenceSentence:" + sequenceSentence);
//            System.out.println("userSequenceOrder:" + userSequenceOrder);
//            System.out.println("executeName:" + executeName);
//            System.out.println("actionSequenceHeader:" + actionSequenceHeader);
//            System.out.println("----------------------");

//            return actionSequenceUnit;
        }
        return actionSequenceUnit;
    }


    public ActionSequenceUnit parseActionSequenceUnit(String sequenceSentence) {
        ActionSequenceUnit actionSequenceUnit = regexSplitActionSequenceUnit(sequenceSentence);
        if (actionSequenceUnit.getActionSequenceHeader() == null) {
            //不符合协议
            return new ActionSequenceUnit().error("动作序列协议头信息缺失");
        }
        return parse(actionSequenceUnit);
    }


    /**
     * 解析动作序列
     *
     * @param actionSequenceUnit 动作序列
     * @return
     */
    @Override
    public ActionSequenceUnit parse(ActionSequenceUnit actionSequenceUnit) {
        ActionSequenceParserStrategy strategy = MAP.get(actionSequenceUnit.getActionSequenceHeader().getExecutorName().toLowerCase());
        if (strategy != null) {
            actionSequenceUnit = strategy.parse(actionSequenceUnit);
        } else {
            actionSequenceUnit = filter(actionSequenceUnit);
        }
//        return new ActionSequence().error(String.format("无法查找到符合\"%s\"的协议解析器", actionSequenceHeader.getHeader()));
        return actionSequenceUnit;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        // 获取bean工厂带LiveViewCode注解的service实例
        Map<String, Object> beansWithAnnotation = contextRefreshedEvent.getApplicationContext().getBeansWithAnnotation(ActionSequenceParser.class);
        beansWithAnnotation.forEach((key, value) -> {
            // 获取实现类注解里的type
//            ActionSequenceParser annotation = AnnotationUtils.findAnnotation(value.getClass(), ActionSequenceParser.class);
            // cglib代理无法获取到注解，这里需要使用spring自带的工具类来操作
            ActionSequenceParser annotation = value.getClass().getAnnotation(ActionSequenceParser.class);
            String type = annotation.type().toLowerCase();
            boolean flag = annotation.flag();
            if (flag) {
                if (!MAP.containsKey(type)) {
                    MAP.put(type, (ActionSequenceParserStrategy) value);
                } else {
                    log.error("type[{}]出现重复，请检查注解type绑定", type);
                }
            } else {
                log.info("策略类[{}]标注失效，不注册策略工厂", value);
            }
        });
    }

}
