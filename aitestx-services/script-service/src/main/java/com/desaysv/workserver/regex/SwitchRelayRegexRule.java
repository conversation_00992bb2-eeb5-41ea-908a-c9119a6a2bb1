package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class SwitchRelayRegexRule extends BaseRegexRule {
    /**
     * 作用：改变所有地址的所有通道状态
     * 格式：On/Off(开关状态,ON开,OFF关)
     * switch-all-ON
     */
    public static final String ALL_ADDRESS_CHANNEL_ON_OR_OFF = wholeCombine(BaseRegexRule.RegexRuleConstants.ALL, group(BaseRegexRule.RegexRuleConstants.ON_OFF));

    /**
     * 1.Switch-1-2-ON（带从站地址-通道号，不带注释）
     */
    public static final String SWITCH_ON_OR_OFF = wholeCombine(group(NUMBER), group(RegexRuleConstants.ON_OFF));


    /**
     * 作用：改变某个地址所有通道状态
     * 格式：On/Off(开关状态,ON开,OFF关)
     * switch-1-all-32-ON
     */
    public static final String ALL_CHANNEL_ON_OR_OFF = wholeCombine(RegexRuleConstants.ALL, group(NUMBER), group(RegexRuleConstants.ON_OFF));
}
