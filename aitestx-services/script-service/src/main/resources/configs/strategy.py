# coding: utf-8

import re

patterns = [
    {
        "pattern": re.compile(r"(?:等待|持续|等|延迟)(\d+)(秒|秒钟|s|S)"),
        "strategyClass": "TimeConversionStrategy"
    },
    {
        "pattern": re.compile(r"(\w+)=(\d+)"),
        "strategyClass": "SignalConversionStrategy"
    },
    {
        "pattern": re.compile(r"测试电压(\d+\.?\d*)V"),
        "strategyClass": "VoltageConversionStrategy"
    },
    {
        "pattern": re.compile(r"(\w+)(?:报文)?(有效|无效|恢复|丢失)"),
        "strategyClass": "MessageValidConversionStrategy"
    },
    {
        "pattern": re.compile(r"电压(\d+\.?\d*)V"),
        "strategyClass": "VoltageConversionStrategy"
    },
    {
        "pattern": re.compile(r"(\\p{IsHan}+.*?)指示灯(?:保持)?(?:常)?(亮|灭|不亮)"),
        "strategyClass": "com.example.WarningLightConversionStrategy"
    },
    {
        "pattern": re.compile(r"(屏蔽|开启)(\\p{IsHan}+.*?)"),
        "strategyClass": "com.example.WarningLightConversionStrategy2"
    }
    #     {
    #         "pattern": re.compile(r"([\u4e00-\u9fff]+.*?)指示灯(?:保持)?(?:常)?(亮|灭|不亮)"),
    #         "strategyClass": "WarningLightConversionStrategy"
    #     },
    #     {
    #         "pattern": re.compile(r"(屏蔽|开启)([\u4e00-\u9fff]+.*?)"),
    #         "strategyClass": "WarningLightConversionStrategy2"
    #     }
]


class TimeConversionStrategy:
    def convert(self, matcher):
        time_in_seconds = int(matcher.group(1))
        time_in_millis = time_in_seconds * 1000
        return "Wait-{}".format(time_in_millis)


class SignalConversionStrategy:
    def convert(self, matcher):
        signal_name = matcher.group(1)
        signal_value = matcher.group(2)
        # Assume dbcFilePath is available as a global variable or through some configuration
        # Implement DBC reading logic if required
        return "CAN-Sig-{}-{}".format(signal_name, signal_value)


class VoltageConversionStrategy:
    def convert(self, matcher):
        voltage = matcher.group(1)
        return "Pwr-SupplyPower-{}".format(voltage)


class MessageValidConversionStrategy:
    def convert(self, matcher):
        message = matcher.group(1)
        valid = matcher.group(2)
        return "CAN-Msg-{}-{}".format(message, '1' if valid in ['有效', '恢复'] else '0')


class WarningLightConversionStrategy:
    def convert(self, matcher):
        pattern_name = matcher.group(1).replace('(', '(').replace(')', ')')
        state = matcher.group(2)
        if state == '亮':
            return "Vision-Pattern-{}".format(pattern_name)
        else:
            return "Vision-Pattern-NoExist-{}".format(pattern_name)


class WarningLightConversionStrategy2:
    def convert(self, matcher):
        enabled = matcher.group(1)
        pattern_name = matcher.group(2).replace('(', '(').replace(')', ')').replace('指示灯功能', '').replace('指示灯',
                                                                                                              '')
        if enabled == '开启':
            return "Vision-Pattern-{}".format(pattern_name)
        else:
            return "Vision-Pattern-NoExist-{}".format(pattern_name)
