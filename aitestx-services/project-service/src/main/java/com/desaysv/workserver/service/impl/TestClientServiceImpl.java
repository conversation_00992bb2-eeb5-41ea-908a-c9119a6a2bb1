package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.TestClientMapper;
import com.desaysv.workserver.model.TestClient;
import com.desaysv.workserver.service.TestClientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-2 17:53
 * @description :
 * @modified By :
 * @since : 2022-8-2
 */
@Service
@Lazy
public class TestClientServiceImpl implements TestClientService {

    @Autowired
    private TestClientMapper testClientMapper;

    @Override
    public Integer addClient(TestClient client) {
        return testClientMapper.insert(client);
    }

    @Override
    public TestClient getClientByName(String clientName) {
        return testClientMapper.selectByName(clientName);
    }

    @Override
    public TestClient getClientById(Integer clientId) {
        return testClientMapper.selectBy<PERSON>rimary<PERSON><PERSON>(clientId);
    }
}
