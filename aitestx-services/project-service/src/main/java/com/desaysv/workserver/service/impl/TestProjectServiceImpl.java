package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.TestProjectMapper;
import com.desaysv.workserver.model.Department;
import com.desaysv.workserver.model.TestProject;
import com.desaysv.workserver.service.TestProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
//no lazy
public class TestProjectServiceImpl implements TestProjectService {

    private TestProjectMapper testProjectMapper;

    @Autowired
    public void setTestProjectMapper(TestProjectMapper testProjectMapper) {
        this.testProjectMapper = testProjectMapper;
    }

    @Override
    @Async
    public void initDB() {
//        log.info("testProjectMapper初始化db");
        TestProject publicProject = getPublicProject();
        if (publicProject == null) {
            publicProject = new TestProject();
            publicProject.setName("__PUBLIC_PROJECT__");
            publicProject.setCommunal(true);
            testProjectMapper.insert(publicProject);
        }
    }

    @Override
    public TestProject getPublicProject() {
        return testProjectMapper.selectPublicProject();
    }

    @Override
    public Integer deleteProject(TestProject testProject) {
        return testProjectMapper.deleteByPrimaryKey(testProject.getId());
    }

    @Override
    public Integer addProject(TestProject testProject) {
        return testProjectMapper.insert(testProject);
    }

    @Override
    public Integer updateProject(TestProject testProject) {
        return testProjectMapper.updateByPrimaryKey(testProject);
    }

    @Override
    public TestProject getProjectById(Integer projectId) {
        return testProjectMapper.selectByPrimaryKey(projectId);
    }

    @Override
    public TestProject getProjectByName(String projectName) {
        return testProjectMapper.selectByName(projectName);
    }

    @Override
    public List<Department> getDepartments(TestProject testProject) {
        return testProjectMapper.selectDepartments(testProject);
    }

    @Override
    public List<TestProject> getAllProjects() {
        return testProjectMapper.selectAll();
    }


}
