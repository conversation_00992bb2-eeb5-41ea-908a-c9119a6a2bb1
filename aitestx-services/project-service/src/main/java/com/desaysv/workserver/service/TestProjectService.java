package com.desaysv.workserver.service;

import com.desaysv.workserver.model.Department;
import com.desaysv.workserver.model.TestProject;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 16:08
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
public interface TestProjectService {

    /**
     * 获取公共项目
     *
     * @return 公共项目
     */
    TestProject getPublicProject();

    /**
     * 删除测试项目
     *
     * @param testProject 测试项目
     * @return 受影响的行数
     */
    Integer deleteProject(TestProject testProject);

    /**
     * 保存测试项目
     *
     * @param testProject 测试项目
     * @return 项目ID
     */
    Integer addProject(TestProject testProject);

    /**
     * 更新测试项目
     *
     * @param testProject 测试项目
     * @return 受影响的行数
     */
    Integer updateProject(TestProject testProject);

    /**
     * 查找测试项目
     *
     * @param projectId 测试项目id
     * @return 指定id的测试项目
     */
    TestProject getProjectById(Integer projectId);

    /**
     * 根据测试项目名查询测试项目
     *
     * @param projectName 测试项目名
     * @return 测试项目
     */
    TestProject getProjectByName(String projectName);

    /**
     * 查找事业单元
     *
     * @param testProject 测试项目
     * @return
     */
    List<Department> getDepartments(TestProject testProject);

    /**
     * 查找所有测试项目
     *
     * @return 所有测试项目
     */
    List<TestProject> getAllProjects();

    void initDB();

}
