package com.desaysv.workserver.entity;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-30 13:16
 * @description : 图片
 * @modified By :
 * @since : 2022-3-30
 */
@Getter
@Setter
public class Image extends OperationTarget {

    private String name;
    private TemplateImageConfig templateImageConfig;

    public Image() {

    }

    public Image(TemplateImageConfig templateImageConfig) {
        this.templateImageConfig = templateImageConfig;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "name=" + getName() +
                '}';
    }
}
