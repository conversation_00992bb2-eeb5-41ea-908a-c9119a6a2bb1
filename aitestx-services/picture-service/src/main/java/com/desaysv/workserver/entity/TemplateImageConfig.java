package com.desaysv.workserver.entity;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import com.desaysv.workserver.utils.AnnotationUtils;
import lombok.Data;

import java.util.Arrays;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 13:44
 * @description :
 * @modified By :
 * @since : 2022-3-18
 */
@Data
public class TemplateImageConfig {

    private String templateName;
    private String algorithm;
    private boolean mustExist;
    private Float threshold;
    private Double timeout;
    private ScaledRoiRect roi;
    private RectSize imageSize;
    private OperationTarget guideDevice; //引导设备
    private boolean colorMatchEnabled; //是否启用颜色匹配
    private double recognizedDuration; //识别持续时间
    private float recognizedInterval; //识别帧间隔
    private String matchedText; //识别文字
    private boolean distanceEnabled; //启用距离检测

    @Override
    public String toString() {
        if (roi == null) {
            return super.toString();
        }
        return String.format(
                "%s|%f%%%s",
                String.format("(%f|%f|%f|%f)",
                        roi.getPointStart().getX(),
                        roi.getPointStart().getY(),
                        roi.getPointEnd().getX(),
                        roi.getPointEnd().getY()),
                threshold * 100,
                timeout > 0 ? String.format("|%fs", timeout) : "");
    }

    public static TemplateImageConfig buildByJsonObject(Object object) {
        return JSON.to(TemplateImageConfig.class, object);
    }

    public static void main(String[] args) {
        System.out.println(Arrays.toString(AnnotationUtils.getAllFields(TemplateImageConfig.class, 0)));
    }

}
