package com.desaysv.workserver.service;

import com.desaysv.workserver.mapper.roi.CoordinatesRoiMapper;
import com.desaysv.workserver.model.roi.CoordinatesRoi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Lazy
public class CoordinatesRoiServiceImpl implements CoordinatesRoiService {

    @Autowired
    private CoordinatesRoiMapper coordinatesRoiMapper;

    @Override
    public CoordinatesRoi insertOrUpdate(CoordinatesRoi coordinatesRoi) {
        CoordinatesRoi roi = coordinatesRoiMapper.selectByCoordinatesUUID(coordinatesRoi.getCoordinatesUUID());
        if (roi != null) {
            //更新Roi
            coordinatesRoi.setUpdateTime(new Date());
            coordinatesRoiMapper.updateByCoordinatesUUID(coordinatesRoi);
        } else {
            //增加Roi到数据库
            coordinatesRoiMapper.insert(coordinatesRoi);
        }
        roi = coordinatesRoiMapper.selectByCoordinatesUUID(coordinatesRoi.getCoordinatesUUID());
        roi.setDeviceType(coordinatesRoi.getDeviceType());
        return roi;
    }

    @Override
    public CoordinatesRoi getRoiByCoordinatesUUID(String coordinatesUUID) {
        return coordinatesRoiMapper.selectByCoordinatesUUID(coordinatesUUID);
    }


    @Override
    public Integer deleteByCoordinatesUUID(String coordinatesUUID) {
        return coordinatesRoiMapper.deleteByCoordinatesUUID(coordinatesUUID);
    }
}
