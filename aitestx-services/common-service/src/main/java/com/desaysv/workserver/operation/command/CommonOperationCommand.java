package com.desaysv.workserver.operation.command;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationCommand;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.operation.handlers.CommonOperationHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-7 17:19
 * @description :
 * @modified By :
 * @since : 2022-7-7
 */
@Slf4j
@Component
@Lazy
public class CommonOperationCommand implements OperationCommand {

    @Autowired
    private CommonOperationHandler commonOperationHandler;

    @Override
    public OperationResult execute(ExecutionContext executionContext, Operation operation) {
        return callOperationMethod(commonOperationHandler, operation);
    }

}
