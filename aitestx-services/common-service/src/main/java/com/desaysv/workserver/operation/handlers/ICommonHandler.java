package com.desaysv.workserver.operation.handlers;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.base.variable.JavaLanguageEngine;
import com.desaysv.workserver.text.TextWebSocketManager;
import com.desaysv.workserver.utils.sse.SseConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.constants.AppConstants.RANDOM_VALUE_SEPARATOR;

public interface ICommonHandler {

    Logger log = LogManager.getLogger(ICommonHandler.class.getSimpleName());


    /**
     * 执行脚本
     *
     * @param scriptName 脚本名称
     * @return
     */
    boolean executeScript(String scriptName);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).EXECUTE_SCRIPT_COMMAND"})
    default ActualExpectedResult executeScriptActionSequence(String scriptName) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = executeScript(scriptName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行脚本名称为:{}, 结果:{},共耗时:{}毫秒", scriptName, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("executeScriptActionSequence", pass, "");
        return actualExpectedResult;
    }

    /**
     * 动作序列专用函数（默认ms）
     *
     * @param timeWithUnit
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).WAIT_TIME"})
    default boolean waitTime(String timeWithUnit) {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(timeWithUnit);
        if (seconds != null) {
            return waitTime(seconds);
        }
        return false;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).WARNING_DIALOG"})
    default boolean showWarningDialog(String warnings) {
        String clientId = SseConstants.DIALOG;
        // 等待客户端响应
        try {
            String response = TextWebSocketManager.sendAndRecv(clientId, warnings, -1);
            return Boolean.parseBoolean(response);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return false;
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHECK_DIALOG"})
    default boolean checkDialog(String judgeText) {
        String clientId = SseConstants.DIALOG_JUDGE;
        // 等待客户端响应
        try {
            String response = TextWebSocketManager.sendAndRecv(clientId, judgeText, -1);
            return Boolean.parseBoolean(response);
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 动作序列专用函数（默认ms）
     *
     * @param lowerTimeWithUnit
     * @param upperTimeWithUnit
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).RANDOM_WAIT"})
    default boolean randomWait(String lowerTimeWithUnit, String upperTimeWithUnit) {
        log.info("随机等待时间{}到{}", lowerTimeWithUnit, upperTimeWithUnit);
        Float lowerSeconds = BaseRegexRule.getSecondsOfDefaultMills(lowerTimeWithUnit);
        Float upperSeconds = BaseRegexRule.getSecondsOfDefaultMills(upperTimeWithUnit);
        if (lowerSeconds != null && upperSeconds != null) {
            float seconds = (float) RandomUtil.randomDouble(lowerSeconds, upperSeconds);
            return waitTime(seconds);
        }
        return false;
    }

    /**
     * flyTest前端专用随机等待函数（默认s）
     *
     * @param timeWithUnit
     * @return
     */
    default boolean waitTimeWithUnit(String timeWithUnit) throws OperationFailNotification {
        float seconds;
        if (JavaLanguageEngine.getInstance().isScriptLanguage(timeWithUnit)) {
            Object result = JavaLanguageEngine.getInstance().getValue(timeWithUnit);
            if (result == null) {
                throw new OperationFailNotification(String.format("表达式解析失败:%s", timeWithUnit));
            }
            timeWithUnit = String.valueOf(result);
        }

        if (NumberUtil.isNumber(timeWithUnit)) {
            seconds = BaseRegexRule.getSeconds(timeWithUnit);
        } else if (timeWithUnit.contains(RANDOM_VALUE_SEPARATOR)) {
            String[] segments = timeWithUnit.split(RANDOM_VALUE_SEPARATOR);
            if (segments.length != 2) {
                throw new OperationFailNotification(String.format("等待时间指令格式失败:%s", timeWithUnit));
            }
            String lowerSecondUnit = segments[0];
            String upperSecondUnit = segments[1];
            boolean isFloat = lowerSecondUnit.contains(".") || upperSecondUnit.contains(".");
            Float lowerSeconds = BaseRegexRule.getSeconds(lowerSecondUnit);
            Float upperSeconds = BaseRegexRule.getSeconds(upperSecondUnit);
            if (lowerSeconds == null || upperSeconds == null) {
                throw new OperationFailNotification(String.format("等待时间指令格式失败:%s", timeWithUnit));
            }
            log.info("等待随机时间:{}~{}", lowerSecondUnit, upperSecondUnit);
            if (isFloat) {
                seconds = (float) RandomUtil.randomDouble(lowerSeconds, upperSeconds);
            } else {
                seconds = RandomUtil.randomInt(lowerSeconds.intValue(), upperSeconds.intValue() + 1);
            }
        } else {
            throw new OperationFailNotification(String.format("等待时间指令格式失败:%s", timeWithUnit));
        }
        return waitTime(seconds);
    }


    boolean waitTime(float seconds);

}
