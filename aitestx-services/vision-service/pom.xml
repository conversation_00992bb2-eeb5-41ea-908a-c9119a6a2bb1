<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>AITestXServer</artifactId>
        <groupId>com.desaysv</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>vision-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.hik</groupId>
            <artifactId>hikCamera</artifactId>
            <scope>system</scope>
            <version>1.0</version>
            <systemPath>${pom.basedir}/../../libs/MvCameraControlWrapper.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-common</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.larksuite.oapi</groupId>
            <artifactId>oapi-sdk</artifactId>
            <version>2.3.6</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-core</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>websocket-service</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.5.3</version> <!-- 请检查是否有更新的版本 -->
        </dependency>
        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jcommon</artifactId>
            <version>1.0.23</version> <!-- 请检查是否有更新的版本 -->
        </dependency>

    </dependencies>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

</project>