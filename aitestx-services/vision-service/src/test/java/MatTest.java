import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.Mat;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class MatTest {
    // Mat内存重用
    private final OpenCVFrameConverter.ToMat converter;
    private Mat reuseOriginalMat;
    private Mat reuseTemplateMat;

    public MatTest() {
        converter = new OpenCVFrameConverter.ToMat();
        reuseOriginalMat = new Mat();
        reuseTemplateMat = new Mat();
    }

    public void test() throws IOException {
        BufferedImage image1 = ImageIO.read(new File("D:\\uidp4666\\My Pictures\\Camera Roll\\v2-3be4c4a1a3813954b0d8c236953ad7c6_r.jpg"));
        BufferedImage image2 = ImageIO.read(new File("D:\\uidp4666\\My Pictures\\Camera Roll\\v2-04e07ad0ac3d971c7bb3e4c273c10424_r.jpg"));
        reuseOriginalMat = converter.convertToMat(Java2DFrameUtils.toFrame(image1));
        System.out.println(System.identityHashCode(reuseOriginalMat));
        reuseTemplateMat = converter.convertToMat(Java2DFrameUtils.toFrame(image2));
        System.out.println(System.identityHashCode(reuseTemplateMat));
    }

    public static void main(String[] args) throws IOException {
        MatTest matTest = new MatTest();
        for (int i = 0; i < 10; i++) {
            matTest.test();
            System.out.println("----------------");
        }
    }

}
