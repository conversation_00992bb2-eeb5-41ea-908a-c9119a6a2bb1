import com.desaysv.workserver.algorithm.base.ImageMatchingFactory;
import com.desaysv.workserver.entity.VisionResult;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;

import javax.imageio.ImageIO;
import java.io.File;

import static org.bytedeco.opencv.global.opencv_imgcodecs.imwrite;
import static org.bytedeco.opencv.global.opencv_imgproc.TM_CCOEFF_NORMED;
import static org.bytedeco.opencv.global.opencv_imgproc.rectangle;
import static org.junit.jupiter.api.Assertions.*;

@Slf4j
public class ImageMatchingFactoryDemo {

    //    @Test
    public static void testPerformTemplateMatching() {
        // 创建 ImageMatchingFactory 实例
        ImageMatchingFactory factory = new ImageMatchingFactory();

        try {
            // 读取源图像和模板图像
            String sourceImagePath = "D:\\uidp4666\\Desktop\\业务开展\\5_问题分析\\图片\\zxb\\b.png";  // 替换为实际的源图像路径
            String templateImagePath = "D:\\uidp4666\\Desktop\\业务开展\\5_问题分析\\图片\\zxb\\a.png";  // 替换为实际的模板图像路径

            Mat sourceImage = Java2DFrameUtils.toMat(ImageIO.read(new File(sourceImagePath)));
            Mat templateImage = Java2DFrameUtils.toMat(ImageIO.read(new File(templateImagePath)));
            opencv_imgproc.cvtColor(sourceImage, sourceImage, opencv_imgproc.COLOR_BGR2GRAY);
            opencv_imgproc.cvtColor(templateImage, templateImage, opencv_imgproc.COLOR_BGR2GRAY);
            // 验证图像是否成功加载
            assertNotNull(sourceImage, "源图像加载失败");
            assertNotNull(templateImage, "模板图像加载失败");
            assertFalse(sourceImage.empty(), "源图像为空");
            assertFalse(templateImage.empty(), "模板图像为空");


            // 执行模板匹配
            VisionResult result = factory.performTemplateMatching(
                    sourceImage,
                    templateImage,
                    TM_CCOEFF_NORMED,
                    null
            );

            // 验证结果
            assertNotNull(result, "匹配结果不应为空");
            assertNotNull(result.getBestMatch(), "最佳匹配区域不应为空");
            assertNotNull(result.getCenterPoint(), "中心点不应为空");

            // 输出匹配结果
            log.info("匹配得分: {}", result.getScore());
            log.info("最佳匹配区域: {}", result.getBestMatch());
            log.info("中心点: {}", result.getCenterPoint());

            // 可选：显示结果
            // 在源图像上绘制匹配结果
            Mat imgDisplay = sourceImage.clone();
            rectangle(
                    imgDisplay,
                    result.getBestMatch().toRect(),
                    new Scalar(0, 255, 0, 0),  // 使用绿色绘制框
                    2,           // 线条粗细
                    0,          // 线条类型
                    0           // 位移
            );

            // 保存带有标识框的图片
            String outputImagePath = "D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\AITestXServer\\aitestx-services\\vision-service\\src\\test\\java\\output_with_box.png";
            imwrite(outputImagePath, imgDisplay);
            log.info("带有标识框的图片已保存至: {}", outputImagePath);

            // 显示结果（可选）
//            factory.showResult(imgDisplay);

        } catch (Exception e) {
            log.error("测试过程中发生错误", e);
            fail("测试失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        testPerformTemplateMatching();
    }
}