package com.desaysv.workserver.entity;

import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import lombok.Data;
import org.bytedeco.javacv.Frame;

import java.util.Queue;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 13:20
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
@Data
public class VisionAlgorithm {
    public final static String DEFAULT_ALGORITHM = AlgorithmSets.cvTemplateMatching;

    private String algorithmName; //算法名称

    private String templateName; //前端传入的模板名，包含&|

    private Frame liveFrame; //实时图像

    private Frame templateFrame; //FIXME：改为Mat

    private boolean mustExist; //是否必须存在

    private double timeout; //识别超时时间/s

    private double preWaitTime = 0; //前置等待时间/s

    private float threshold; //对比阈值百分数（0-1）

    private boolean onlyTestSimilarity = false; //是否只测试相似度

    private boolean colorMatchEnabled; //是否启用颜色匹配

    private boolean reduceNoise; //是否启用降噪

    private double recognizedDuration; //识别持续时间/s

    private AbsoluteRoiRect roiRect; //ROI边框

    private String matchedText; //识别文字

    private boolean preProcessEnabled; //是否启用预处理

    private ImageAlgorithmConfig algorithmConfig; //算法配置

    private float recognizedInterval = 0.1f;//对比帧间隔/s

    private Float roiEnlargePercent; //ROI扩大百分比

    private VisionEventHandler visionEventHandler; //事件处理器

    private Queue<Frame> frameQueue;//帧队列

    private double frameRate;//摄像头帧率

    private double targetSimilarity;//目标相似度

    private String videoFailFilePath;//错误文件保存路径(视频用)

    private String videoFailROIFilePath;//错误ROI文件保存路径(视频用)

    public void setThreshold(Float threshold) {
        this.threshold = threshold == null ? 0 : threshold;
    }

    public void setTimeout(Double timeout) {
        this.timeout = timeout == null ? 0 : timeout;
    }

}