package com.desaysv.workserver.handler;

import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.algorithm.ocr.OcrMatching;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.chain.IVisionAlgorithmHandleChain;
import com.desaysv.workserver.chain.IVisionAlgorithmHandler;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * OCR匹配算法处理
 */
@Service
@Lazy //TODO: 分析Lazy失效 https://blog.csdn.net/qq_18297675/article/details/103267125
public class OcrAlgorithmHandler implements IVisionAlgorithmHandler {

    @Autowired
    private OcrMatching ocrMatching;

    @Override
    public VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm,
                                        VisionEventHandler visionEventHandler,
                                        IVisionAlgorithmHandleChain handleChain) throws OperationFailNotification {
        VisionResult visionResult;
        if (visionAlgorithm.getAlgorithmName().equalsIgnoreCase(AlgorithmSets.ocrMatching)) {
            visionResult = ocrMatching.applyAlgorithm(visionAlgorithm, visionEventHandler);
        } else {
            visionResult = handleChain.handleAlgorithm(visionAlgorithm, visionEventHandler);
        }
        return visionResult;
    }

}
