package com.desaysv.workserver.algorithm.base;


import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.device.DeviceUnknownException;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import org.bytedeco.javacv.Frame;

public interface VisionEventHandler {

    Frame captureLiveFrame() throws FrameGrabberException;

    Frame getTemplateFrame(String templateName) throws OperationFailNotification;

    AbsoluteRoiRect getAbsoluteRoiRect(String templateName) throws OperationFailNotification;
}
