package com.desaysv.workserver.utils;

import java.awt.*;
import java.awt.image.BufferedImage;

public class HistogramUtils {

    private static int[][] calculateHSVHistogram(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        int[][] histograms = new int[3][256];

        for (int i = 0; i < width; i++) {
            for (int j = 0; j < height; j++) {
                Color color = new Color(image.getRGB(i, j));
                float[] hsv = Color.RGBtoHSB(color.getRed(), color.getGreen(), color.getBlue(), null);
                int h = (int) (hsv[0] * 255);
                int s = (int) (hsv[1] * 255);
                int v = (int) (hsv[2] * 255);

                histograms[0][h]++;
                histograms[1][s]++;
                histograms[2][v]++;
            }
        }
        return histograms;
    }

    public static double calculateLocalHSVHistogramSimilarity(BufferedImage img1, BufferedImage img2) {
        int width = img1.getWidth();
        int height = img1.getHeight();
        int numRegions = 32; // 将图像分成32个区域
        int regionWidth = width / numRegions;
        int regionHeight = height / numRegions;
        double totalSimilarity = 0.0;

        for (int y = 0; y < numRegions; y++) {
            for (int x = 0; x < numRegions; x++) {
                BufferedImage subImg1 = img1.getSubimage(x * regionWidth, y * regionHeight, regionWidth, regionHeight);
                BufferedImage subImg2 = img2.getSubimage(x * regionWidth, y * regionHeight, regionWidth, regionHeight);
                double similarity = calculateHSVHistogramSimilarity(subImg1, subImg2);
                totalSimilarity += similarity;
            }
        }

        // 平均相似度
        return totalSimilarity / (numRegions * numRegions);
    }

    private static double calculateHSVHistogramSimilarity(BufferedImage img1, BufferedImage img2) {
        int[][] hist1 = calculateHSVHistogram(img1);
        int[][] hist2 = calculateHSVHistogram(img2);
        int totalPixels = img1.getWidth() * img1.getHeight();
        double sum = 0.0;

        // 使用卡方距离
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 256; j++) {
                double value1 = hist1[i][j];
                double value2 = hist2[i][j];
                if (value1 + value2 > 0) {
                    sum += Math.pow(value1 - value2, 2) / (value1 + value2);
                }
            }
        }
        // 归一化到0-1之间
        return 1.0 / (1.0 + sum / (3 * totalPixels));
    }
}
