package com.desaysv.workserver.algorithm.strict;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.base.StrictMatching;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.ImageDimensionException;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;

/**
 * 逐像素图像模板匹配（根据ROI）
 */
@Service
@Slf4j
@Lazy
public class PerPixelTemplateMatching extends ImageMatchingBaseTemplate implements StrictMatching {
    public static final String FRIENDLY_ALGORITHM_NAME = "逐像素匹配";

    @Override
    public String getFriendlyName() {
        return FRIENDLY_ALGORITHM_NAME;
    }

    private static double diff(Mat srcMat, Mat dstMat) throws ImageDimensionException {
        // 检查两张图片的尺寸是否一致
        if (srcMat.cols() != dstMat.cols() || srcMat.rows() != dstMat.rows()) {
            throw new ImageDimensionException(String.format("图像尺寸不一致，%s,%s", srcMat.size(), dstMat.size()));
        }
        try (Mat graySrcMat = new Mat();
             Mat grayDstMat = new Mat();
             Mat diff = new Mat();
             Mat thresholdMat = new Mat()) {
            opencv_imgproc.cvtColor(srcMat, graySrcMat, opencv_imgproc.COLOR_BGR2GRAY);
            opencv_imgproc.cvtColor(dstMat, grayDstMat, opencv_imgproc.COLOR_BGR2GRAY);

            // 计算两张图片差异
            opencv_core.absdiff(graySrcMat, grayDstMat, diff); //TODO: 打印diff看非0数量
            // 阈值处理
            opencv_imgproc.threshold(diff, thresholdMat, 0, 255, opencv_imgproc.THRESH_OTSU);

            // 计算像素差异总和
            Scalar sum = opencv_core.sumElems(thresholdMat);
            return sum.get(0) + sum.get(1) + sum.get(2);
        }
    }

    private static double calculateSimilarity(double diffValue, int totalPixels) {
        // 计算差异值与图像像素总数的比例
        double similarity = 1.0 - (diffValue / (255.0 * totalPixels));
        // 转换为百分比
        return similarity * 100.0;
    }

    public static double calSimilarity(Mat srcMat, Mat dstMat) throws ImageDimensionException {
        double diffValue = diff(srcMat, dstMat);
        int totalPixels = srcMat.cols() * srcMat.rows();
        return calculateSimilarity(diffValue, totalPixels);
    }

    @Override
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) throws ImageTemplateMatchingException {
        VisionResult visionResult = new VisionResult();
//        Frame cropFrame = ImageUtils.crop(visionAlgorithm.getLiveFrame(), visionAlgorithm.getRoiRect());
        double score;
        Frame liveFrame = visionAlgorithm.getLiveFrame();
        Frame templateFrame = visionAlgorithm.getTemplateFrame();
        try (Frame croppedFrame = ImageUtils.crop(liveFrame, visionAlgorithm.getRoiRect());
             Mat mat1 = Java2DFrameUtils.toMat(croppedFrame);
             Mat mat2 = Java2DFrameUtils.toMat(templateFrame)
        ) {
            score = calSimilarity(mat1, mat2);
        } catch (ImageDimensionException e) {
            throw new ImageTemplateMatchingException(e);
        }
        visionResult.setBestMatch(Rectangle.fromRect(visionAlgorithm.getRoiRect()));
        visionResult.setScore(score / 100.0f);
        return visionResult;
    }

    public static void main(String[] args) throws IOException, ImageDimensionException {
        String folder = "D:\\uidp4666\\Desktop\\ADS\\对比图片\\";
        Mat src = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "粉红图片.png")));
        Mat dst1 = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "1.png")));
        Mat dst2 = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "2.png")));
        Mat dst3 = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "3.png")));
        Mat dst4 = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "4.png")));
        Mat dst5 = Java2DFrameUtils.toMat(ImageIO.read(new File(folder + "5.png")));

        System.out.println("dst1:" + PerPixelTemplateMatching.calSimilarity(src, src));
        System.out.println("dst1:" + PerPixelTemplateMatching.calSimilarity(src, dst1));
        System.out.println("dst2:" + PerPixelTemplateMatching.calSimilarity(src, dst2));
        System.out.println("dst3:" + PerPixelTemplateMatching.calSimilarity(src, dst3));
        System.out.println("dst4:" + PerPixelTemplateMatching.calSimilarity(src, dst4));
        System.out.println("dst5:" + PerPixelTemplateMatching.calSimilarity(src, dst5));
    }

}
