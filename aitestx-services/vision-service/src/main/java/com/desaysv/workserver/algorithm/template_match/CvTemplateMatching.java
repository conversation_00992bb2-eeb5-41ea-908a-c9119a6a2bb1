package com.desaysv.workserver.algorithm.template_match;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.base.ImageMatchingFactory;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


/**
 * OpenCV模板匹配
 */
@Slf4j
@Service
@Lazy
public class CvTemplateMatching extends ImageMatchingBaseTemplate {

    @Autowired
    private ImageMatchingFactory imageMatchingFactory;

    @Override
    public String getFriendlyName() {
        return "模板匹配";
    }

    @Override
    public void preProcessing(VisionAlgorithm visionAlgorithm) {

    }

    @Override
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        return imageMatchingFactory.templateMatching(visionAlgorithm);
    }

    @Override
    public void afterProcessing(VisionAlgorithm visionAlgorithm) {

    }
}
