package com.desaysv.workserver.algorithm.color;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.base.StrictMatching;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


/**
 * 颜色匹配
 */
@Slf4j
@Service
@Lazy
public class ColorMatching extends ImageMatchingBaseTemplate implements StrictMatching {

    @Override
    public String getFriendlyName() {
        return "颜色匹配";
    }

    @Override
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        VisionResult visionResult = new VisionResult();
        Frame originFrame = visionAlgorithm.getLiveFrame();
        Frame templateFrame = visionAlgorithm.getTemplateFrame();
        try (Frame cropFrame = ImageUtils.crop(originFrame, visionAlgorithm.getRoiRect())) {
            double pearsonRatio = ColorCalculate.diff(cropFrame, templateFrame);
            //FIXME：ssimRatio改成全像素匹配
            //double ssimRatio = StrictTemplateMatching.calSSIM(cropFrame, templateFrame);
            //double score = ColorUtils.avgMerge(pearsonRatio, ssimRatio);
            //log.info("颜色匹配信息: pearsonRatio:{}, ssimRatio:{}", pearsonRatio, ssimRatio);
            log.info("颜色匹配信息: pearsonRatio->{} ", pearsonRatio);
            visionResult.setScore(pearsonRatio);
            visionResult.setBestMatch(Rectangle.fromRect(visionAlgorithm.getRoiRect()));
            return visionResult;
        }
    }

}
