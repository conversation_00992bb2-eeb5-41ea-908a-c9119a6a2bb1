package com.desaysv.workserver.algorithm.strict;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.base.StrictMatching;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.utils.ImageUtils;
import com.desaysv.workserver.utils.StructuralSimilarityUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Size;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;

import static org.bytedeco.opencv.global.opencv_imgproc.*;

/**
 * 严格图像模板匹配（根据ROI）
 */
@Service
@Slf4j
@Lazy
public class StrictTemplateMatching extends ImageMatchingBaseTemplate implements StrictMatching {

    protected static final String FRIENDLY_ALGORITHM_NAME = "精准匹配";

    @Override
    public String getFriendlyName() {
        return FRIENDLY_ALGORITHM_NAME;
    }

    @Override
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        return performTemplateMatching(
                visionAlgorithm.getLiveFrame(),
                visionAlgorithm.getTemplateFrame(),
                visionAlgorithm.getRoiRect(),
                visionAlgorithm.isColorMatchEnabled(),
                visionAlgorithm.isReduceNoise()
        );
    }

    private VisionResult performTemplateMatching(Frame liveFrame, Frame templateFrame, AbsoluteRoiRect roiRect, boolean isColorMatchEnabled, boolean isReduceNoise) {
        VisionResult visionResult = new VisionResult();
        try (Frame cropFrame = roiRect == null ? liveFrame : ImageUtils.crop(liveFrame, roiRect)) {
            // 将Frame转换为Mat以进行图像处理
            try (Mat cropMat = Java2DFrameUtils.toMat(cropFrame);
                 Mat templateMat = Java2DFrameUtils.toMat(templateFrame)) {

                if (isReduceNoise) {
                    // 应用降噪算法
                    applyNoiseReduction(cropMat);
                    applyNoiseReduction(templateMat);
                }

                double similarity = StructuralSimilarityUtils.getEnhancedSSIM(cropMat, templateMat, isColorMatchEnabled);
                visionResult.setScore(similarity);
                if (roiRect == null) {
                    visionResult.setBestMatch(new Rectangle(0, 0, liveFrame.imageWidth, liveFrame.imageHeight));
                } else {
                    visionResult.setBestMatch(Rectangle.fromRect(roiRect));
                }
            }
            return visionResult;
        }
    }

    /**
     * 应用降噪算法
     *
     * @param mat 输入图像Mat
     */
    private void applyNoiseReduction(Mat mat) {
        // 1. 高斯模糊 - 平滑图像并减少噪点
        Size gaussianSize = new Size(5, 5);
        GaussianBlur(mat, mat, gaussianSize, 0);

        // 2. 中值滤波 - 对椒盐噪声特别有效
        medianBlur(mat, mat, 3);

        // 3. 双边滤波 - 保留边缘的同时平滑图像
        // 参数: 源图像, 目标图像, 邻域直径, 颜色空间标准差, 坐标空间标准差
        bilateralFilter(mat, mat, 9, 75, 75);
    }

    public static void main(String[] args) throws IOException {
        String img1 = "D:\\uidp4666\\Desktop\\分析\\1.png";
        String img2 = "D:\\uidp4666\\Desktop\\分析\\2.png";
        System.out.println(StructuralSimilarityUtils.getSSIM(Java2DFrameUtils.toFrame(ImageIO.read(new File(img1))), Java2DFrameUtils.toFrame(ImageIO.read(new File(img2))), true));
    }

}