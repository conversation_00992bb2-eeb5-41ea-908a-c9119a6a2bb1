package com.desaysv.workserver.algorithm.feature;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.indexer.FloatIndexer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_calib3d;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.opencv_core.*;
import org.bytedeco.opencv.opencv_features2d.*;
import org.bytedeco.opencv.opencv_xfeatures2d.SURF;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.bytedeco.opencv.global.opencv_core.CV_32F;
import static org.bytedeco.opencv.global.opencv_core.NORM_HAMMING;
import static org.bytedeco.opencv.global.opencv_imgproc.COLOR_BGR2GRAY;
import static org.bytedeco.opencv.global.opencv_imgproc.cvtColor;

@Slf4j
@Service
@Lazy
public class FeatureMatching extends ImageMatchingBaseTemplate {
    // AffineFeature2D.class, BEBLID.class, AffineFeature.class, TEBLID.class
    // AgastFeatureDetector.class, FastFeatureDetector.class, GFTTDetector.class,
    // MSER.class, SimpleBlobDetector.class, BoostDesc.class, BriefDescriptorExtractor.class,
    // DAISY.class, FREAK.class, HarrisLaplaceFeatureDetector.class,
    // LATCH.class, LUCID.class, MSDDetector.class, StarDetector.class, TBMR.class, VGG.class
    private final static List<Class<? extends Algorithm>> featureAlgorithmList = Arrays.asList(ORB.class, SIFT.class, SURF.class, BRISK.class, KAZE.class);


    @Override
    public String getFriendlyName() {
        return "特征匹配";
    }

    private static double useOptimizedFeatureMatching(Mat mat1, Mat mat2) throws ImageTemplateMatchingException {
        log.info("使用优化特征识别{}算法", AKAZE.class.getSimpleName());
        try (AKAZE detector = AKAZE.create();
             KeyPointVector keyPoints1 = new KeyPointVector();
             KeyPointVector keyPoints2 = new KeyPointVector();
             Mat descriptors1 = new Mat();
             Mat descriptors2 = new Mat();
             Mat mask1 = new Mat();
             Mat mask2 = new Mat();
        ) {
            // Detect key points and compute descriptors
            detector.detectAndCompute(mat1, mask1, keyPoints1, descriptors1);
            detector.detectAndCompute(mat2, mask2, keyPoints2, descriptors2);

            // Match descriptors
            try (DescriptorMatcher matcher = DescriptorMatcher.create(DescriptorMatcher.BRUTEFORCE_HAMMING);
                 DMatchVectorVector matches = new DMatchVectorVector()
            ) {
                matcher.knnMatch(descriptors1, descriptors2, matches, 2);

                // Filter matches based on Lowe's ratio test
                float ratioThreshold = 0.4f;
                List<DMatch> goodMatches = new ArrayList<>();

                for (int i = 0; i < matches.size(); i++) {
                    DMatchVector matchList = matches.get(i);

                    if (matchList.size() >= 2) {
                        DMatch match1 = matchList.get(0);
                        DMatch match2 = matchList.get(1);

                        float distanceRatio = match1.distance() / match2.distance();
                        if (distanceRatio < ratioThreshold) {
                            goodMatches.add(match1);
                        }
                    }
                }
                log.info("特征匹配数量:{}->原图：{}，模板：{}", goodMatches.size(), keyPoints1.size(), keyPoints2.size());
                if (goodMatches.isEmpty()) {
                    throw new ImageTemplateMatchingException(String.format("%s匹配数量为0", AKAZE.class.getSimpleName()));
                }
                // Apply RANSAC to further filter matches
                try (Mat srcPointsMat = new Mat(new Size(2, goodMatches.size()), CV_32F);
                     Mat dstPointsMat = new Mat(new Size(2, goodMatches.size()), CV_32F);
                     Mat mask = new Mat();
                     FloatIndexer srcIdx = srcPointsMat.createIndexer();
                     FloatIndexer dstIdx = dstPointsMat.createIndexer();
                ) {
                    for (int i = 0; i < goodMatches.size(); i++) {
                        try (DMatch match = goodMatches.get(i);
                             Point2f pt1 = keyPoints1.get(match.queryIdx()).pt();
                             Point2f pt2 = keyPoints2.get(match.trainIdx()).pt();
                        ) {
                            srcIdx.put(i, 0, pt1.x());
                            srcIdx.put(i, 1, pt1.y());
                            dstIdx.put(i, 0, pt2.x());
                            dstIdx.put(i, 1, pt2.y());
                        }
                    }
                    try (Mat mat = opencv_calib3d.findHomography(srcPointsMat, dstPointsMat, mask, opencv_calib3d.RANSAC, 5.0)) {
                        // Calculate similarity based on the ratio of inliers to total matches
                        int inliers = opencv_core.countNonZero(mask);
                        return (double) inliers / goodMatches.size();
                    }
                }
            }
        }
    }

    // 反射调用类的静态 create() 方法来创建对象
    public static Feature2D createDetector(Class<? extends Algorithm> detectorClass) throws ImageTemplateMatchingException {
        try {
            Method createMethod = detectorClass.getMethod("create");
            return (Feature2D) createMethod.invoke(null);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new ImageTemplateMatchingException(e);
        }
    }


    private static double useFastFeatureMatching(Mat mat1, Mat mat2, List<Class<? extends Algorithm>> algorithms) {
        for (Class<? extends Algorithm> algorithm : algorithms) {
            long matchCount = 0;
            try (Feature2D detector = createDetector(algorithm);
                 KeyPointVector keyPoints1 = new KeyPointVector();
                 KeyPointVector keyPoints2 = new KeyPointVector();
                 Mat descriptors1 = new Mat();
                 Mat descriptors2 = new Mat();
                 Mat mask1 = new Mat();
                 Mat mask2 = new Mat();
            ) {
                log.info("使用快速特征匹配算法\"{}\"", algorithm.getSimpleName());
                // Detect keyPoints and compute descriptors
                detector.detectAndCompute(mat1, mask1, keyPoints1, descriptors1);
                detector.detectAndCompute(mat2, mask2, keyPoints2, descriptors2);
                log.info("keyPoints1:{}，keyPoints2:{}", keyPoints1.size(), keyPoints2.size());
                if (keyPoints1.size() > 0 && keyPoints2.size() > 0) {
                    // Match descriptors
                    try (BFMatcher matcher = new BFMatcher(NORM_HAMMING, true);
                         DMatchVector matches = new DMatchVector();
                    ) {
                        matcher.match(descriptors1, descriptors2, matches);

//                // Draw matches
//                Mat imgMatches = new Mat();
//                drawMatches(mat1, keyPoints1, mat2, keyPoints2, matches, imgMatches);
                        // Calculate similarity (e.g., based on number of matches)
                        matchCount = matches.size();
                    }
                }
                log.info("\"{}\"特征匹配数量:{}->原图：{}，模板：{}", algorithm.getSimpleName(), matchCount, keyPoints1.size(), keyPoints2.size());
                if (matchCount > 0) {
                    return matchCount / (double) Math.min(keyPoints1.size(), keyPoints2.size());
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return 0;
    }

    @Override
    protected VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
//        log.info("进行特征匹配:{}", visionAlgorithm);
        Frame liveFrame = visionAlgorithm.getLiveFrame();
        Frame templateFrame = visionAlgorithm.getTemplateFrame();
        try (Frame cropFrame = ImageUtils.crop(liveFrame, visionAlgorithm.getRoiRect());
             Mat mat1 = Java2DFrameUtils.toMat(cropFrame);
             Mat mat2 = Java2DFrameUtils.toMat(templateFrame)
        ) {
            cvtColor(mat1, mat1, COLOR_BGR2GRAY);
            cvtColor(mat2, mat2, COLOR_BGR2GRAY);
            double similarity;
            try {
                similarity = useOptimizedFeatureMatching(mat1, mat2);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                similarity = useFastFeatureMatching(mat1, mat2, featureAlgorithmList);
            }
            log.info("特征匹配相似度:{}", similarity);
            VisionResult visionResult = new VisionResult();
            visionResult.setScore(similarity);
            return visionResult;
        }
    }

    public static void main(String[] args) throws ImageTemplateMatchingException, IOException {
        BufferedImage image1 = ImageIO.read(new File("D:\\uidp4666\\Desktop\\保存图片20240527084407\\模板图片.png"));
        BufferedImage image2 = ImageIO.read(new File("D:\\uidp4666\\Desktop\\保存图片20240527084407\\实际ROI图片.png"));

//        Frame originalMat = Java2DFrameUtils.toFrame(image1);
//        Frame templateMat = Java2DFrameUtils.toFrame(image2);
//        Mat o = Java2DFrameUtils.toMat(originalMat);
//        Mat t = Java2DFrameUtils.toMat(templateMat);
//        Rect rect = new Rect(383, 31, 48, 48);
//        Frame crop = ImageUtils.crop(image1, rect);
//        Frame crop1 = ImageUtils.crop(image2, rect);
        //保存crop到文件夹
//        ImageIO.write(Java2DFrameUtils.toBufferedImage(crop), "png", new File("D:\\uidp4666\\Desktop\\仪表背景变换专项分析\\84台架\\2\\crop.png"));
//        ImageIO.write(Java2DFrameUtils.toBufferedImage(crop1), "png", new File("D:\\uidp4666\\Desktop\\仪表背景变换专项分析\\84台架\\2\\crop1.png"));
        System.out.println(FeatureMatching.useFastFeatureMatching(Java2DFrameUtils.toMat(image1), Java2DFrameUtils.toMat(image2), featureAlgorithmList));
    }
}
