package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.FloatPointer;
import org.bytedeco.javacpp.IntPointer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.MatVector;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Scalar;
import org.bytedeco.opencv.opencv_quality.QualitySSIM;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import static org.bytedeco.opencv.global.opencv_imgproc.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 19:03
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
//TODO: 加入位置扩大检测
@Slf4j
public class StructuralSimilarityUtils {

    public static Mat cropContour(Mat source) {
        // 转换为灰度图（仅用于检测轮廓）
        try (Mat gray = new Mat();
             // 二值化，使用较低的阈值只检测纯黑色区域
             Mat binary = new Mat();
             // 查找轮廓
             MatVector contours = new MatVector();
             Mat hierarchy = new Mat();
        ) {
            cvtColor(source, gray, COLOR_BGR2GRAY);
            threshold(gray, binary, 5, 255, THRESH_BINARY); // 使用较低的阈值
            findContours(binary, contours, hierarchy, RETR_EXTERNAL, CHAIN_APPROX_SIMPLE);

            // 如果没有找到轮廓，返回原图
            if (contours.empty()) {
                return source;
            }

            // 找到最大轮廓
            double maxArea = 0;
            int maxContourIdx = 0;
            for (int i = 0; i < contours.size(); i++) {
                double area = contourArea(contours.get(i));
                if (area > maxArea) {
                    maxArea = area;
                    maxContourIdx = i;
                }
            }

            // 裁剪原始图像
            try (
                    // 获取边界矩形
                    Rect boundRect = boundingRect(contours.get(maxContourIdx));
                    Mat cropped = new Mat(source, boundRect);
                    // 创建BGRA图像（保持原始颜色）
                    Mat rgba = new Mat();
                    // 创建掩码（基于裁剪图像的灰度图像）
                    Mat croppedGray = new Mat();
                    Mat croppedMask = new Mat();
            ) {
                cvtColor(cropped, rgba, COLOR_BGR2BGRA);
                cvtColor(cropped, croppedGray, COLOR_BGR2GRAY);
                threshold(croppedGray, croppedMask, 5, 255, THRESH_BINARY);

                // 遍历图像像素，设置掩码外的区域为透明
                for (int y = 0; y < rgba.rows(); y++) {
                    for (int x = 0; x < rgba.cols(); x++) {
                        // 仅根据掩码判断透明度
                        if (croppedMask.ptr(y, x).get() == 0) {
                            rgba.ptr(y, x).put(3, (byte) 0); // 设置Alpha通道为0（透明）
                        }
                    }
                }

                return rgba;
            }
        }
    }


    public static double getSSIM(Mat m1, Mat m2) {
        // 验证输入
        if (m1.empty() || m2.empty() ||
                m1.size().width() != m2.size().width() ||
                m1.size().height() != m2.size().height()) {
            throw new IllegalArgumentException("输入图像无效或尺寸不匹配");
        }
        Mat mat1 = null;
        Mat mat2 = null;

        try {

            if (m1.channels() == 3) {
                mat1 = new Mat();
                opencv_imgproc.cvtColor(m1, mat1, opencv_imgproc.COLOR_BGR2GRAY);
            } else {
                mat1 = m1.clone();
            }
            if (m2.channels() == 3) {
                mat2 = new Mat();
                opencv_imgproc.cvtColor(m2, mat2, opencv_imgproc.COLOR_BGR2GRAY);
            } else {
                mat2 = m2.clone();
            }

            // 使用OpenCV内置的SSIM计算方法
            try (QualitySSIM qSSIM = QualitySSIM.create(mat1);
                 Scalar ssim = qSSIM.compute(mat2)) {
                return ssim.get(0);
            }
        } finally {
            if (mat1 != null) {
                mat1.close();
            }
            if (mat2 != null) {
                mat2.close();
            }
        }
    }


    /**
     * 计算ssim（颜色空间）
     *
     * @param mat1
     * @param mat2
     * @return ssim分数
     */
    public static double getColorSSIM(Mat mat1, Mat mat2) {
        // 验证输入
        if (mat1.empty() || mat2.empty() ||
                mat1.size().width() != mat2.size().width() ||
                mat1.size().height() != mat2.size().height()) {
            throw new IllegalArgumentException("输入图像无效或尺寸不匹配");
        }

        // 分离通道
        try (MatVector channels1 = new MatVector(3); // 创建3个通道的向量
             MatVector channels2 = new MatVector(3)) {
            opencv_core.split(mat1, channels1);
            opencv_core.split(mat2, channels2);

            // 计算每个通道的SSIM并求平均
            double ssim = 0;
            int numChannels = (int) channels1.size();
            for (int i = 0; i < numChannels; i++) {
                ssim += getSSIM(channels1.get(i), channels2.get(i));
            }

            // 返回平均值
            return ssim / numChannels;
        }
    }

    /**
     * 计算ssim（LAB空间）
     *
     * @param mat1
     * @param mat2
     * @return ssim分数
     */
    public static double getColorSSIM_LAB(Mat mat1, Mat mat2) {
        // 验证输入
        if (mat1.empty() || mat2.empty() ||
                mat1.size().width() != mat2.size().width() ||
                mat1.size().height() != mat2.size().height()) {
            throw new IllegalArgumentException("输入图像无效或尺寸不匹配");
        }

        try (
                // 转换为LAB色彩空间
                Mat lab1 = new Mat();
                Mat lab2 = new Mat();
                // 分离通道
                MatVector channels1 = new MatVector(3); // 创建3个通道的向量
                MatVector channels2 = new MatVector(3);) {
            opencv_imgproc.cvtColor(mat1, lab1, opencv_imgproc.COLOR_BGR2Lab);
            opencv_imgproc.cvtColor(mat2, lab2, opencv_imgproc.COLOR_BGR2Lab);

            opencv_core.split(lab1, channels1);
            opencv_core.split(lab2, channels2);

            // 计算各通道的SSIM，并赋予不同权重
            // L通道权重较小，ab通道权重较大以突出颜色差异
            double weightL = 0.25;  // 亮度权重
            double weightA = 0.375; // 红绿差异权重
            double weightB = 0.375; // 蓝黄差异权重
            // 计算加权SSIM
            double ssimL = getSSIM(channels1.get(0), channels2.get(0));
            double ssimA = getSSIM(channels1.get(1), channels2.get(1));
            double ssimB = getSSIM(channels1.get(2), channels2.get(2));

            return weightL * ssimL + weightA * ssimA + weightB * ssimB;
        }
    }

    /**
     * 计算ssim（YUV空间）
     *
     * @param mat1
     * @param mat2
     * @return ssim分数
     */
    public static double getColorSSIM_YUV(Mat mat1, Mat mat2) {
        // 验证输入
        if (mat1.empty() || mat2.empty() ||
                mat1.size().width() != mat2.size().width() ||
                mat1.size().height() != mat2.size().height()) {
            throw new IllegalArgumentException("输入图像无效或尺寸不匹配");
        }

        try (// 转换为YUV色彩空间
             Mat yuv1 = new Mat();
             Mat yuv2 = new Mat();
             // 分离通道
             MatVector channels1 = new MatVector(3);
             MatVector channels2 = new MatVector(3);
        ) {
            opencv_imgproc.cvtColor(mat1, yuv1, opencv_imgproc.COLOR_BGR2YUV);
            opencv_imgproc.cvtColor(mat2, yuv2, opencv_imgproc.COLOR_BGR2YUV);

            opencv_core.split(yuv1, channels1);
            opencv_core.split(yuv2, channels2);

            // 计算加权SSIM
            // Y通道权重更大(0.8)，因为亮度对人眼感知影响最大
            // U和V通道各占0.1的权重
            // Y通道
            // U通道
            // V通道
            return getSSIM(channels1.get(0), channels2.get(0)) * 0.8 +  // Y通道
                    getSSIM(channels1.get(1), channels2.get(1)) * 0.1 +  // U通道
                    getSSIM(channels1.get(2), channels2.get(2)) * 0.1;
        }
    }

    private static double compareColorHistograms(Mat mat1, Mat mat2) {
        // 检查输入图像是否为空
        if (mat1.empty() || mat2.empty()) {
            throw new IllegalArgumentException("Input images cannot be empty");
        }

        // 转换到HSV颜色空间
        try (Mat hsv1 = new Mat();
             Mat hsv2 = new Mat();
             Mat hist1 = new Mat();
             Mat hist2 = new Mat()
        ) {
            opencv_imgproc.cvtColor(mat1, hsv1, opencv_imgproc.COLOR_BGR2HSV);
            opencv_imgproc.cvtColor(mat2, hsv2, opencv_imgproc.COLOR_BGR2HSV);

            // 设置直方图参数
            int[] histSize = {30, 10, 10}; // H:30bins, S:10bins, V:10bins
            int[] channels = {0, 1, 2};    // 三个通道

            // 设置取值范围
            float[] ranges = {0, 180, 0, 256, 0, 256}; // 将所有通道的范围放在一个数组中

            // 计算第一张图的直方图
            try (MatVector inputVector1 = new MatVector(hsv1);
                 IntPointer chanPointer1 = new IntPointer(channels);
                 Mat mask1 = new Mat();
                 IntPointer size1 = new IntPointer(histSize);
                 FloatPointer range1 = new FloatPointer(ranges);

                 MatVector inputVector2 = new MatVector(hsv2);
                 IntPointer chanPointer2 = new IntPointer(channels);
                 Mat mask2 = new Mat();
                 IntPointer size2 = new IntPointer(histSize);
                 FloatPointer range2 = new FloatPointer(ranges);
            ) {
                opencv_imgproc.calcHist(
                        inputVector1, // 输入图像
                        chanPointer1, // 通道
                        mask1,              // 掩码
                        hist1,                  // 输出直方图
                        size1, // 每个维度的尺寸
                        range1  // 取值范围
                );

                // 计算第二张图的直方图
                opencv_imgproc.calcHist(
                        inputVector2, // 输入图像
                        chanPointer2, // 通道
                        mask2,              // 掩码
                        hist2,                  // 输出直方图
                        size2, // 每个维度的尺寸
                        range2  // 取值范围
                );

                // 归一化直方图
                try (Mat normalizeMask1 = new Mat();
                     Mat normalizeMask2 = new Mat()) {
                    opencv_core.normalize(hist1, hist1, 0, 1, opencv_core.NORM_MINMAX, -1, normalizeMask1);
                    opencv_core.normalize(hist2, hist2, 0, 1, opencv_core.NORM_MINMAX, -1, normalizeMask2);

                    // Bhattacharyya 距离，需要用 1 减去结果，因为该方法 0 表示最相似
                    return 1 - opencv_imgproc.compareHist(hist1, hist2, opencv_imgproc.CV_COMP_BHATTACHARYYA);
                }
            }
        }
    }

    public static double getEnhancedSSIM(Mat mat1, Mat mat2) {
        return getEnhancedSSIM(mat1, mat2, true);
    }

    public static double getEnhancedSSIM(Mat mat1, Mat mat2, boolean colorDetectionEnabled) {
        // 计算SSIM
        double ssim = getSSIM(mat1, mat2);

        if (!colorDetectionEnabled) {
            return ssim;
        } else {
            // 计算颜色直方图相似度
            double histSimilarity = compareColorHistograms(mat1, mat2);
            log.info("ssim:{}", ssim);
            log.info("histSimilarity:{}", histSimilarity);

            // 组合SSIM和直方图相似度
            double ssimWeight = 0.85;     // SSIM权重
            double histWeight = 0.15;     // 直方图权重

            return ssimWeight * ssim + histWeight * histSimilarity;
        }
    }


    /**
     * 计算ssim（传入BufferImage方法）
     *
     * @param image1
     * @param image2
     * @return ssim分数
     */
    public static double getSSIM(BufferedImage image1, BufferedImage image2, boolean colorDetectionEnabled) {
        if (image1 == null || image2 == null) {
            throw new IllegalArgumentException("输入BufferedImage不能为空");
        }
        try (Mat mat1 = Java2DFrameUtils.toMat(image1);
             Mat mat2 = Java2DFrameUtils.toMat(image2)) {
            return getEnhancedSSIM(mat1, mat2, colorDetectionEnabled);
        }
    }

    /**
     * 计算ssim（传入Frame方法）
     *
     * @param frame1
     * @param frame2
     * @return ssim分数
     */
    public static double getSSIM(Frame frame1, Frame frame2, boolean colorDetectionEnabled) {
        if (frame1 == null || frame2 == null) {
            throw new IllegalArgumentException("输入Frame不能为空");
        }
        try (Mat originalMat = Java2DFrameUtils.toMat(frame1);
             Mat templateMat = Java2DFrameUtils.toMat(frame2)) {
            return getEnhancedSSIM(originalMat, templateMat, colorDetectionEnabled);
        }
    }


    public static void main(String[] args) throws IOException {
//        File file1 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\aabb\\2.jpg");
//        File file2 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\aabb\\3.jpg");
//        File file1 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\123\\1.png");
//        File file2 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\123\\2.png");
//        File file1 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\BSRALbright\\BSRALbright_ROI图片_20241021092119.png");
//        File file2 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\BSRALbright\\BSRALbright_ROI图片_20241021092747.png");
//        File file1 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\VCDbright\\VCDbright_ROI图片_20241015165515.png");
//        File file2 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\VCDbright\\VCDbright_ROI图片_20241016090246.png");
//        File file1 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\电池\\1.jpg");
//        File file2 = new File("D:\\WorkDocs\\00-Code\\05-Web_Backend\\AITestX\\仪表图片待分析\\电池\\2.jpg");
//        File file1 = new File("D:\\uidp4666\\Desktop\\仪表\\图像识别1204\\报错图像1204.png");
//        File file2 = new File("D:\\uidp4666\\Desktop\\仪表\\图像识别1204\\主机2.png");
//        File file1 = new File("D:\\uidp4666\\Desktop\\仪表\\NJ\\1.jpg");
//        File file2 = new File("D:\\uidp4666\\Desktop\\仪表\\NJ\\2.jpg");
        File file1 = new File("D:\\UIDS1050\\Downloads\\20250424-144804.png");
        File file2 = new File("D:\\UIDS1050\\Downloads\\Nall\\Nall_ROI_20250423112745.png");
        BufferedImage image1 = ImageIO.read(file1);
        BufferedImage image2 = ImageIO.read(file2);
        Mat mat1 = Java2DFrameUtils.toMat(image1);
        Mat mat2 = Java2DFrameUtils.toMat(image2);
        double ssim = StructuralSimilarityUtils.getSSIM(mat1, mat2);
        double color = StructuralSimilarityUtils.getColorSSIM(mat1, mat2);
        double lab = StructuralSimilarityUtils.getColorSSIM_LAB(mat1, mat2);
        double yuv = StructuralSimilarityUtils.getColorSSIM_YUV(mat1, mat2);
        double enhancedSSIM = StructuralSimilarityUtils.getEnhancedSSIM(mat1, mat2);
        double compareColorHistograms = StructuralSimilarityUtils.compareColorHistograms(mat1, mat2);


        System.out.println("ssim: " + ssim);
        System.out.println("color: " + color);
        System.out.println("lab: " + lab);
        System.out.println("yuv: " + yuv);
        System.out.println("compareColorHistograms: " + compareColorHistograms);
        System.out.println("enhancedSSIM: " + enhancedSSIM);
    }
}
