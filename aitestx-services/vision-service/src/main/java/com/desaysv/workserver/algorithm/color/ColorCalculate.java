package com.desaysv.workserver.algorithm.color;

import com.desaysv.workserver.utils.StructuralSimilarityUtils;
import com.desaysv.workserver.utils.ColorUtils;
import com.desaysv.workserver.utils.PearsonRatioUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * 颜色计算
 */
@Slf4j
public class ColorCalculate {

    /**
     * 可选值为：1,2,4,8,16,32
     * 当值为64时会抛出异常，此时需要实现64位转10进制
     * radix 64 greater than Character.MAX_RADIX
     */
    private static final int compareLevel = 4;

    @AllArgsConstructor
    @Data
    public static class ColorSet {
        private String origin;
        private String benchmark;
    }

    /**
     * 获取一个数组中两两组合
     *
     * @param list                  数组
     * @param includeTheSameElement 是否包含相同元素
     * @return
     */
    public static List<ColorSet> combineTwoElement(List<String> list, boolean includeTheSameElement) {
        List<ColorSet> resultList = Lists.newArrayListWithExpectedSize(list.size() * list.size() / 2);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        if (list.size() >= 2) {
            for (int j = 0; j < list.size(); j++) {
                resultList.addAll(combine(list.subList(j, list.size())));
            }
        }
        if (includeTheSameElement) {
            for (String element : list) {
                resultList.add(new ColorSet(element, element));
            }
        }
        return resultList;
    }

    private static List<ColorSet> combine(List<String> list) {
        List<ColorSet> resultList = Lists.newArrayListWithExpectedSize(list.size() * list.size() / 2);
        for (int j = 1; j < list.size(); j++) {
            resultList.add(new ColorSet(list.get(0), list.get(j)));
        }
        return resultList;
    }

    private static List<String> getFileNames(String path) {
        try (Stream<Path> paths = Files.walk(Paths.get(path), 1)) {
            List<Path> fileNames = paths
                    .filter(Files::isRegularFile)
                    .collect(Collectors.toList());
            return fileNames.stream().map(Path::getFileName).map(Path::toString).collect(Collectors.toList());

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    private static void test(List<ColorSet> colorSets, String folder) throws IOException {
        for (ColorSet colorSet : colorSets) {
            final String pic1Path = folder + "\\" + colorSet.origin;
            final String pic2Path = folder + "\\" + colorSet.benchmark;
            final List<Double> origin = getPicArrayData(pic1Path);
            final List<Double> benchmark = getPicArrayData(pic2Path);
            double pearsonRatio = PearsonRatioUtils.getPearsonByDim(origin, benchmark);
            double ssim = StructuralSimilarityUtils.getSSIM(ImageIO.read(new File(pic1Path)), ImageIO.read(new File(pic2Path)), false);
//            double d = 0.7 * pearsonRatio + 0.3 * ssim;
//            if (d > 0.9) {
//                System.out.printf("%s VS %s : %f%n", colorSet.origin, colorSet.benchmark, d);
//            }
            double avg = ColorUtils.avgMerge(pearsonRatio, ssim);
            if (avg >= 0.4) {
//            if (pearsonRatio > 0.80) {
                System.out.printf("%s VS %s : %f  || ", colorSet.origin, colorSet.benchmark, pearsonRatio);
                System.out.printf("%s VS %s : %f || ", colorSet.origin, colorSet.benchmark, ssim);
                System.out.println(avg);
//            }
            }
        }
    }

    private static void testSSIM(List<ColorSet> colorSets, String folder) throws IOException {
        for (ColorSet colorSet : colorSets) {
            final String pic1Path = folder + "\\" + colorSet.origin;
            final String pic2Path = folder + "\\" + colorSet.benchmark;
            double ratio = StructuralSimilarityUtils.getSSIM(ImageIO.read(new File(pic1Path)), ImageIO.read(new File(pic2Path)), false);
            if (ratio > 0.80) {
                System.out.printf("%s VS %s : %f%n", colorSet.origin, colorSet.benchmark, ratio);
            }
        }
    }

    public static double diff(Frame origin, Frame benchmark) {
        final List<Double> originList = getPicArrayData(origin);
        final List<Double> benchmarkList = getPicArrayData(benchmark);
        return PearsonRatioUtils.getPearsonByDim(originList, benchmarkList);
    }

    private static List<Double> getPicArrayData(String path) throws IOException {
        BufferedImage bufferedImage = ImageIO.read(new File(path));
        return getPicArrayData(bufferedImage);
    }

    private static List<Double> getPicArrayData(Frame frame) {
        return getPicArrayData(Java2DFrameUtils.toBufferedImage(frame));
    }

    private static List<Double> getPicArrayData(BufferedImage bufferedImage) {
        //初始化集合
        int size = (int) Math.pow(compareLevel, 3);
        final List<Double> picFingerprint = new ArrayList<>(size);
        IntStream.range(0, size).forEach(i -> {
            picFingerprint.add(i, 0.0);
        });
        //遍历像素点
        for (int i = 0; i < bufferedImage.getWidth(); i++) {
            for (int j = 0; j < bufferedImage.getHeight(); j++) {
                Color color = new Color(bufferedImage.getRGB(i, j));
                //对像素点进行计算
                putIntoFingerprintList(picFingerprint, color.getRed(), color.getGreen(), color.getBlue());
            }
        }
        return picFingerprint;
    }

    /**
     * 放入像素的三原色进行计算，得到List的位置
     *
     * @param picFingerprintList picFingerprintList
     * @param r                  r
     * @param g                  g
     * @param b                  b
     * @return
     */
    private static List<Double> putIntoFingerprintList(List<Double> picFingerprintList, int r, int g, int b) {
        //比如r g b是126, 153, 200 且 compareLevel为16进制，得到字符串：79c ,然后转10进制，这个数字就是List的位置
        final int index = Integer.valueOf(getBlockLocation(r) + getBlockLocation(g) + getBlockLocation(b), compareLevel);
        final Double origin = picFingerprintList.get(index);
        picFingerprintList.set(index, origin + 1);
        return picFingerprintList;
    }

    /**
     * 计算当前原色应该分在哪个区块
     *
     * @param colorPoint colorPoint
     * @return
     */
    private static String getBlockLocation(int colorPoint) {
        return IntStream.range(0, compareLevel)
                //以10进制计算分在哪个区块
                .filter(i -> {
                    int areaStart = (256 / compareLevel) * i;
                    int areaEnd = (256 / compareLevel) * (i + 1) - 1;
                    return colorPoint >= areaStart && colorPoint <= areaEnd;
                })
                //如果compareLevel大于10则转为对应的进制的字符串
                .mapToObj(location -> compareLevel > 10 ? Integer.toString(location, compareLevel) : location + "")
                .findFirst()
                .orElseThrow(RuntimeException::new);
    }

    private static void putIntoFingerprintMap(Map<Integer, Integer> picFingerprintMap, int r, int g, int b) {
        final int picFingerprint = Integer.valueOf(getBlockLocation(r) + getBlockLocation(g) + getBlockLocation(b), compareLevel);
        Integer value = picFingerprintMap.containsKey(picFingerprint) ? picFingerprintMap.get(picFingerprint) + 1 : 1;
        picFingerprintMap.put(picFingerprint, value);
    }

    private static List<Double> getPicArrayDataByMap(String path) throws IOException {
        BufferedImage bufferedImage = ImageIO.read(new File(path));
        return getPicArrayDataByMap(bufferedImage);
    }

    private static List<Double> getPicArrayDataByMap(BufferedImage bufferedImage) {
        final Map<Integer, Integer> picFingerprintMap = new HashMap<>();

        for (int i = 0; i < bufferedImage.getWidth(); i++) {
            for (int j = 0; j < bufferedImage.getHeight(); j++) {
                //输出一列数据比对
                Color color = new Color(bufferedImage.getRGB(i, j));
                int r = color.getRed();
                int g = color.getGreen();
                int b = color.getBlue();
                putIntoFingerprintMap(picFingerprintMap, r, g, b);
            }
        }

        final List<Integer> keys = picFingerprintMap.keySet().stream().sorted().collect(Collectors.toList());
        final ArrayList<Double> picFingerprintList = new ArrayList<>(keys.size());
        keys.forEach(key -> picFingerprintList.add(Double.valueOf(picFingerprintMap.get(key))));
        return picFingerprintList;
    }

    public static void main(String[] args) throws IOException {
        String folder = "D:\\uidp4666\\Desktop\\image";
        List<String> fileNames = getFileNames(folder);
        List<ColorSet> colorSets = combineTwoElement(fileNames, false);
        System.out.println(colorSets);
        assert colorSets != null;
        test(colorSets, folder);
//        System.out.println("-----------------");
//        testSSIM(colorSets,folder);
//        final String pic1Path = "D:\\uidp4666\\Desktop\\image\\红色.png";
//        final String pic2Path = "D:\\uidp4666\\Desktop\\image\\绿色.png";
//        final List<Double> origin = getPicArrayData(pic1Path);
//        System.out.println(origin);
//        final List<Double> after = getPicArrayData(pic2Path);
//        System.out.println(after);
//        System.out.println(PearsonDemo.getPearsonBydim(origin, after));
    }

}