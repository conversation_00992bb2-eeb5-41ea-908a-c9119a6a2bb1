package com.desaysv.workserver.algorithm.composites;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.strict.StrictTemplateMatching;
import com.desaysv.workserver.algorithm.template_match.CvTemplateMatching;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * OpenCV智能模板匹配（加纯色）
 */
@Slf4j
@Service
@Lazy
public class IntelligentTemplateMatching extends ImageMatchingBaseTemplate {
    @Autowired
    private CvTemplateMatching cvTemplateMatching;

    @Autowired
    private StrictTemplateMatching strictTemplateMatching;

    @Override
    protected VisionResult templateMatching(VisionAlgorithm visionAlgorithm) throws ImageTemplateMatchingException {
        try (Mat mat = Java2DFrameUtils.toMat(visionAlgorithm.getTemplateFrame())) {
            //检测是否纯色图片
            if (ImageUtils.isSolidColorImage(mat)) {
                log.info("纯色图片，使用严格匹配");
                return strictTemplateMatching.templateMatching(visionAlgorithm);
            }
        }

        return cvTemplateMatching.templateMatching(visionAlgorithm);
    }

    @Override
    public String getFriendlyName() {
        return "智能匹配";
    }
}
