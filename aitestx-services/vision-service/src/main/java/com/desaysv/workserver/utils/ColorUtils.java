package com.desaysv.workserver.utils;

public class ColorUtils {

    public static double avgMerge(double pearsonRatio, double ssimRatio) {
        double ssimPercent;
        if (pearsonRatio > 0.99 && ssimRatio > 0.9) {
            //强相关
            return pearsonRatio;
        }
        if (pearsonRatio > 0.6) {
            //平衡ssim算法
            if (ssimRatio <= 0.85) {
                ssimPercent = 0.65;
            } else if (ssimRatio <= 0.9) {
                ssimPercent = 0.6;
            } else if (ssimRatio <= 0.95) {
                ssimPercent = 0.55;
            } else {
                ssimPercent = 0.5;
            }
            double pearsonPercent = 1 - ssimPercent;
            return pearsonRatio * pearsonPercent + ssimRatio * ssimPercent;
        }
        return pearsonRatio;
    }
}
