package com.desaysv.workserver.entity;

import com.desaysv.workserver.algorithm.base.MatchResultModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 16:08
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
@Data
public class SimpleVisionResult {

    private String templateName;

    private boolean passed;

    private double score;

    private String message;

    private Rectangle bestMatch;

    private PointInt centerPoint;

    private boolean largeImage;

    private List<MatchResultModel> resultModelList;

    private FileVisionResult fileVisionResult;

}
