package com.desaysv.workserver.algorithm.base;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.utils.ImageUtils;
import org.apache.commons.lang3.StringUtils;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_core.Rect;
import org.bytedeco.opencv.opencv_core.Scalar;

import static org.bytedeco.opencv.global.opencv_imgproc.*;

/**
 * 图像模板匹配策略
 */
// 智能算法流程：ssim->rect->ocr->template->ssim
public interface ImageTemplateAlgorithmStrategy extends AlgorithmStrategy {

    /**
     * 识别间隔（毫秒）
     */
    int MIN_MATCHING_INTERVAL = SystemEnv.getImageAcqInterval();

    /**
     * 预处理
     *
     * @param visionAlgorithm 视觉算法
     * @return
     */
    default void preProcessing(VisionAlgorithm visionAlgorithm) {

    }

    default void preProcessingEveryFrame(VisionAlgorithm visionAlgorithm) {

    }

    /**
     * 后处理
     *
     * @param visionAlgorithm 视觉算法
     */
    default void afterProcessing(VisionAlgorithm visionAlgorithm) {

    }

    VisionResult imageRecognize(VisionAlgorithm visionAlgorithm, VisionEventHandler visionEventHandler) throws OperationFailNotification;

    default void printVisionResult(VisionResult visionResult, long startMills) {
        printSplitLine();
        if (StringUtils.isNotEmpty(visionResult.getTemplateName())) {
            log.info("图像模板\"{}\"识别结果如下:", visionResult.getTemplateName());
        }
        log.info("{}测试相似度:{}", getFriendlyName(), visionResult.getPercentScore());
        log.info("{}算法耗时:{}ms", getFriendlyName(), System.currentTimeMillis() - startMills);
        log.info("本次图像识别{}", visionResult.isPassed() ? "成功" : "失败");

        if (visionResult.isFailed()) {
            log.info(visionResult.getMessage());
        }
//        log.info("{}ROI:{}", getFriendlyName(), visionResult.getBestMatch());
        printSplitLine();
    }

    default void handleFailFrame(VisionAlgorithm visionAlgorithm, VisionResult visionResult) {
        if (visionAlgorithm.getRoiRect() == null) {
            return;
        }
        // 正确释放Mat
        Frame liveFrame = visionAlgorithm.getLiveFrame();
        try (Mat imgDisplayMat = Java2DFrameUtils.toMat(liveFrame)) {
            //绘制模板ROI
            float fontSize = 0.3f;
            Scalar greenColor = new Scalar(0, 255, 0, 255);
            Rect roiRect = visionAlgorithm.getRoiRect();
            rectangle(imgDisplayMat, roiRect, greenColor, 2, LINE_8, 0);
            putText(imgDisplayMat, "ROI", new Point(roiRect.x(), roiRect.y() - 5),
                    FONT_HERSHEY_SIMPLEX, fontSize, greenColor, 1, LINE_AA, false);

            //用虚线绘制扩大的ROI
            if (visionResult.getEnlargeRoiRect() != null) {
                Scalar whiteColor = new Scalar(255, 255, 255, 255);
                AbsoluteRoiRect enlargeRoiRect = visionResult.getEnlargeRoiRect();
                ImageUtils.drawDashedRectangle(imgDisplayMat, enlargeRoiRect, whiteColor, 2, 10, 10);
                putText(imgDisplayMat, "Enlarge ROI", new Point(enlargeRoiRect.x(), enlargeRoiRect.y() - 5),
                        FONT_HERSHEY_SIMPLEX, fontSize, whiteColor, 1, LINE_AA, false);
            }
            //绘制最佳匹配
            if (visionResult.getBestMatch() != null) {
                Scalar redColor = new Scalar(0, 0, 255, 255);
                Rect bestMatchRect = visionResult.getBestMatch().toRect();
                rectangle(imgDisplayMat, bestMatchRect, redColor, 2, LINE_8, 0);
                putText(imgDisplayMat, "Actual Match", new Point(bestMatchRect.x(), bestMatchRect.y() - 5),
                        FONT_HERSHEY_SIMPLEX, fontSize, redColor, 1, LINE_AA, false);
            }
            visionResult.setEventFrame(Java2DFrameUtils.toFrame(imgDisplayMat)); //转换Frame
            log.info("处理失败图片:{}", visionAlgorithm.getTemplateName());
        }
    }

    default VisionResult applyAlgorithm(VisionAlgorithm visionAlgorithm, VisionEventHandler visionEventHandler) throws OperationFailNotification {
        VisionResult visionResult;
        preProcessing(visionAlgorithm);
        visionResult = imageRecognize(visionAlgorithm, visionEventHandler);
        visionResult.setRoiRect(visionAlgorithm.getRoiRect());
        visionResult.setOriginalFrame(visionAlgorithm.getLiveFrame());
        if (SystemEnv.isSaveFailImage()) {
            if (visionResult.isFailed()) {
                //失败图片
                handleFailFrame(visionAlgorithm, visionResult);
            }
        } else {
            log.info("用户已设置不保存失败图片");
        }
        afterProcessing(visionAlgorithm);
        return visionResult;
    }

}
