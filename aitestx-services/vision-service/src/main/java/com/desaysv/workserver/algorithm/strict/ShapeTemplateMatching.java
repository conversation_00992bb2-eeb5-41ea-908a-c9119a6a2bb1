package com.desaysv.workserver.algorithm.strict;

import com.desaysv.workserver.algorithm.base.ImageMatchingBaseTemplate;
import com.desaysv.workserver.algorithm.base.StrictMatching;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.IntPointer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.MatVector;
import org.bytedeco.opencv.opencv_core.Size;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.bytedeco.opencv.global.opencv_imgproc.*;

@Service
@Slf4j
@Lazy
public class ShapeTemplateMatching extends ImageMatchingBaseTemplate implements StrictMatching {

    private static final double MAX_SIMILARITY_VALUE = 0.1; // Adjust based on empirical observations
    private static final int BLUR_SIZE = 5;
    private static final int CANNY_THRESHOLD1 = 50;
    private static final int CANNY_THRESHOLD2 = 150;

    @Override
    protected VisionResult templateMatching(VisionAlgorithm visionAlgorithm) throws ImageTemplateMatchingException {
        if (visionAlgorithm.getRoiRect() == null) {
            throw new IllegalArgumentException("ROI信息缺失");
        }
        VisionResult visionResult = new VisionResult();
        Frame liveFrame = visionAlgorithm.getLiveFrame();
        Frame templateFrame = visionAlgorithm.getTemplateFrame();
        try (Frame originFrame = ImageUtils.crop(liveFrame, visionAlgorithm.getRoiRect());
             Mat originalMat = Java2DFrameUtils.toMat(originFrame);
             Mat templateMat = Java2DFrameUtils.toMat(templateFrame)
        ) {
            visionResult.setScore(compareShapes(originalMat, templateMat));
            visionResult.setBestMatch(Rectangle.fromRect(visionAlgorithm.getRoiRect()));
            return visionResult;
        }
    }

    @Override
    public String getFriendlyName() {
        return "形状匹配";
    }


    private static Mat preprocessImage(Mat mat) {
        Mat tempMat = new Mat(); //新建Mat存储
        cvtColor(mat, tempMat, COLOR_BGR2GRAY);
        GaussianBlur(tempMat, tempMat, new Size(BLUR_SIZE, BLUR_SIZE), 0);
        Canny(tempMat, tempMat, CANNY_THRESHOLD1, CANNY_THRESHOLD2);
        return tempMat;
    }

    public static double compareShapes(Mat originalMat, Mat templateMat) {
        if (originalMat.empty() || templateMat.empty()) {
            throw new IllegalArgumentException("Input images cannot be empty.");
        }

        try (Mat grayOriginal = preprocessImage(originalMat);
             Mat grayTemplate = preprocessImage(templateMat);
             Mat hierarchyOriginal = new Mat();
             Mat hierarchyTemplate = new Mat();
             MatVector contoursOriginal = new MatVector();
             MatVector contoursTemplate = new MatVector();
        ) {
            // 计算轮廓
            findContours(grayOriginal, contoursOriginal, hierarchyOriginal, RETR_TREE, CHAIN_APPROX_SIMPLE);
            findContours(grayTemplate, contoursTemplate, hierarchyTemplate, RETR_TREE, CHAIN_APPROX_SIMPLE);

            if (contoursOriginal.empty() || contoursTemplate.empty()) {
                throw new IllegalArgumentException("No contours found in one or both images.");
            }

            // 计算相似度
            double similarity = compareContoursHierarchy(contoursOriginal, hierarchyOriginal, contoursTemplate, hierarchyTemplate);
            return normalizeSimiliarity(similarity);
        }
    }

    private static double compareContoursHierarchy(MatVector contours1, Mat hierarchy1, MatVector contours2, Mat hierarchy2) {
        return compareContoursAtLevel(hierarchy1, hierarchy2, -1, -1, 0);
    }

    private static double compareContoursAtLevel(Mat hierarchy1, Mat hierarchy2, int parentIndex1, int parentIndex2, int level) {
        List<Integer> children1 = getChildrenIndices(hierarchy1, parentIndex1);
        List<Integer> children2 = getChildrenIndices(hierarchy2, parentIndex2);

        int count1 = children1.size();
        int count2 = children2.size();

        // Compare number of contours at this level
        double countSimilarity;
        if (count1 == 0 && count2 == 0) {
            // Both contours have no children, consider them similar
            countSimilarity = 1.0;
        } else if (count1 == 0 || count2 == 0) {
            // One contour has children while the other doesn't, consider them different
            countSimilarity = 0.0;
        } else {
            countSimilarity = 1.0 - Math.abs(count1 - count2) / (double) Math.max(count1, count2);
        }

        // If both contours have no children, we stop here
        if (count1 == 0 && count2 == 0) {
            return countSimilarity;
        }

        // Recursively compare child contours
        double childrenSimilarity = 0.0;
        int comparisons = 0;
        for (int i = 0; i < Math.max(count1, count2); i++) {
            if (i < count1 && i < count2) {
                childrenSimilarity += compareContoursAtLevel(hierarchy1, hierarchy2, children1.get(i), children2.get(i), level + 1);
                comparisons++;
            }
        }

        // Calculate average similarity including both count and children similarities
        double averageSimilarity = (countSimilarity + (comparisons > 0 ? childrenSimilarity / comparisons : 0)) / 2.0;

        System.out.printf("Level %d: Count1=%d, Count2=%d, Similarity=%.2f%n", level, count1, count2, averageSimilarity);

        return averageSimilarity;
    }

    private static List<Integer> getChildrenIndices(Mat hierarchy, int parentIndex) {
        List<Integer> children = new ArrayList<>();

        if (hierarchy.empty() || hierarchy.rows() == 0) {
            return children;
        }

        int numContours = hierarchy.rows();

        for (int i = 0; i < numContours; i++) {
            IntPointer rowPtr = new IntPointer(hierarchy.ptr(i));
            if (rowPtr.get(3) == parentIndex) {
                children.add(i);
            }
        }

        return children;
    }

    private static double normalizeSimiliarity(double similarity) {
        return 1.0 - Math.min(similarity / MAX_SIMILARITY_VALUE, 1.0);
    }

    public static void main(String[] args) throws IOException {
        BufferedImage image1 = ImageIO.read(new File("D:\\uidp4666\\Desktop\\仪表\\保存图片20240722084432\\模板图片.png"));
        BufferedImage image2 = ImageIO.read(new File("D:\\uidp4666\\Desktop\\仪表\\保存图片20240722084432\\模板图片 - 副本.png"));
        Mat originalMat = Java2DFrameUtils.toMat(image1);
        Mat templateMat = Java2DFrameUtils.toMat(image2);
        System.out.println(ShapeTemplateMatching.compareShapes(originalMat, templateMat));
//        ImageUtils.saveMat(grayOriginal, "D:\\uidp4666\\Desktop\\仪表\\保存图片\\originalMat-save.png");
//        ImageUtils.saveMat(grayTemplate, "D:\\uidp4666\\Desktop\\仪表\\保存图片\\templateMat-save.png");
    }
}
