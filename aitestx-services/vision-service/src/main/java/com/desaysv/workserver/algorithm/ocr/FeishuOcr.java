package com.desaysv.workserver.algorithm.ocr;

import com.google.gson.JsonParser;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.optical_char_recognition.v1.model.*;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


// SDK 使用文档：https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/server-side-sdk/java-sdk-guide/preparations

/**
 * 飞书ocr
 */
@Slf4j
public class FeishuOcr {
    private static final String clientId = "cli_a8aff39a1a34d013";
    private static final String clientSecret = "we4mW0ohGBJFLlkPnFmtnh5gsB7mqq6R";
    public static void main(String arg[]) throws Exception {
        FeishuOcr ocr = new FeishuOcr();

    }

    public String[] getOcrText(Frame frame) {
        String base64str = "";
        try {
            byte[] fileBytes  = OcrMatching.frameToBytes(frame);
            base64str = Base64.getEncoder().encodeToString(fileBytes);
        } catch (Exception e) {
           log.error("帧转换为字节数组失败{}", e.getMessage());
        }
        // 构建client
        Client client = Client.newBuilder(clientId, clientSecret).build();

        // 创建请求对象
        BasicRecognizeImageReq req = BasicRecognizeImageReq.newBuilder()
                .basicRecognizeImageReqBody(BasicRecognizeImageReqBody.newBuilder()
                        .image(base64str)
                        .build())
                .build();

        try {
            // 发起请求
            BasicRecognizeImageResp resp = client.opticalCharRecognition().v1().image().basicRecognize(req, RequestOptions.newBuilder()
                    .tenantAccessToken(getTenantAccessToken())
                    .build());

            // 处理服务端错误
            if (!resp.success()) {
                System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
                        resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), StandardCharsets.UTF_8)))));
                return null;
            }

            // 业务数据处理
            System.out.println(Jsons.DEFAULT.toJson(resp.getData()));
            return resp.getData().getTextList();
        }catch (Exception e){
            log.error("feishu ocr error:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取飞书云文档的访问令牌
     *
     * @return 访问令牌
     */
    public String getTenantAccessToken() {
        // 1. 定义 API 地址
        String url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";

        // 2. 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // 关键：设置 Content-Type

        // 3. 构建 JSON 请求体
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("app_id", clientId);
        bodyMap.put("app_secret", clientSecret);

        // 4. 封装请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(bodyMap, headers);

        // 5. 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 6. 发送 POST 请求并解析响应
        ResponseEntity<Map> response = restTemplate.postForEntity(
                url,
                requestEntity,
                Map.class
        );

        // 7. 处理响应
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && (Integer) responseBody.get("code") == 0) {
                return (String) responseBody.get("tenant_access_token");
            } else {
                throw new RuntimeException("获取 Token 失败: " + responseBody.get("msg"));
            }
        } else {
            throw new RuntimeException("请求失败，状态码: " + response.getStatusCode());
        }
    }
}
