package com.desaysv.workserver.chain;

import com.desaysv.workserver.utils.ReflectUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 14:12
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */

@Component
@Lazy
@Slf4j
public class VisionAlgorithmHandlerContainer {

    public List<IVisionAlgorithmHandler> getAlgorithmHandlerList() {
        List<IVisionAlgorithmHandler> algorithmHandlers = new ArrayList<>();

        //获取IReceiptHandler接口的实现类
        Set<Class<?>> classList = ReflectUtils.getClassSetBySuper(IVisionAlgorithmHandler.class);
        if (!classList.isEmpty()) {
            for (Class<?> clazz : classList) {
                try {
                    //TODO：算法排序
                    algorithmHandlers.add((IVisionAlgorithmHandler) SpringContextHolder.getBean(clazz));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return algorithmHandlers;
    }

}
