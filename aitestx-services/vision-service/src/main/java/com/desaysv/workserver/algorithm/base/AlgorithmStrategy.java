package com.desaysv.workserver.algorithm.base;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-14 13:28
 * @description :
 * @modified By :
 * @since : 2022-5-14
 */
public interface AlgorithmStrategy {
    Logger log = LogManager.getLogger(AlgorithmStrategy.class.getSimpleName());

    /**
     * 获取算法友好名字
     *
     * @return
     */
    String getFriendlyName();

    /**
     * 应用算法
     *
     * @param visionAlgorithm
     * @param visionEventHandler
     * @return
     * @throws OperationFailNotification
     */
    VisionResult applyAlgorithm(VisionAlgorithm visionAlgorithm, VisionEventHandler visionEventHandler) throws OperationFailNotification;

    /**
     * 预处理
     *
     * @param visionAlgorithm 视觉算法
     * @return
     */
    default void preProcessing(VisionAlgorithm visionAlgorithm) {

    }

    /**
     * 后处理
     *
     * @param visionAlgorithm 视觉算法
     */
    default void afterProcessing(VisionAlgorithm visionAlgorithm) {

    }

    /**
     * 分割线
     */
    default void printSplitLine() {
        log.info("------------------------------");
    }

    /**
     * 处理视觉结果
     *
     * @param visionResult
     * @param visionAlgorithm
     * @return
     */
    default VisionResult handleVisionResult(VisionResult visionResult, VisionAlgorithm visionAlgorithm) {
        if (visionAlgorithm.isOnlyTestSimilarity()) {
            visionResult.setPassed(true);
        } else {
            visionResult.setPassed(isMatchPass(visionResult, visionAlgorithm));
            String desc = getMatchDescription(visionResult, visionAlgorithm);
            if (visionResult.getExtraDesc() != null) {
                visionResult.setMessage(String.format("%s | %s", visionResult.getExtraDesc(), desc));
            } else {
                visionResult.setMessage(desc);
            }
        }
        return visionResult;
    }

    /**
     * 获取匹配描述
     *
     * @param visionResult    视觉结果
     * @param visionAlgorithm 视觉算法
     * @return 匹配描述
     */
    default String getMatchDescription(VisionResult visionResult, VisionAlgorithm visionAlgorithm) {
        return getDescriptionForMatching(visionAlgorithm.getAlgorithmName(), visionResult.getTemplateName(), visionAlgorithm.isMustExist(), visionAlgorithm.getThreshold(), visionResult.getScore());
    }


    /**
     * 计算是否匹配到
     *
     * @param visionResult    视觉结果
     * @param visionAlgorithm 视觉算法
     * @return 是否匹配到
     */
    default boolean isMatchPass(VisionResult visionResult, VisionAlgorithm visionAlgorithm) {
        // return mustExist ? ssim >= threshold : ssim < threshold;
        return visionAlgorithm.isMustExist() ?
                visionResult.getScore() >= visionAlgorithm.getThreshold() :
                visionResult.getScore() < visionAlgorithm.getThreshold();
    }

    /**
     * 获取匹配描述
     *
     * @param algorithm:算法
     * @param templateName 模板名称
     * @param mustExist    是否必须
     * @param threshold    阈值
     * @param score        分数
     * @return 匹配描述
     */
    default String getDescriptionForMatching(String algorithm, String templateName, boolean mustExist, float threshold, double score) {
        return String.format("%s:模板\"%s\"相似度要求%s%.2f，当前相似度为%f",
                algorithm,
                templateName,
                mustExist ? "大于等于" : "小于", threshold, score);
    }

}
