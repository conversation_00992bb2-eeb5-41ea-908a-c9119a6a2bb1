package com.desaysv.workserver.chain;

import com.desaysv.workserver.constants.AlgorithmSets;
import com.desaysv.workserver.algorithm.base.VisionEventHandler;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-17 14:11
 * @description :
 * @modified By :
 * @since : 2022-5-17
 */
@Service
@Slf4j
@Lazy
public class VisionAlgorithmHandleChain implements IVisionAlgorithmHandleChain {

    @Autowired
    private VisionAlgorithmHandlerContainer visionAlgorithmHandlerContainer;

    //记录当前处理者位置
    private int index = 0;
    //处理者集合
    private List<IVisionAlgorithmHandler> algorithmHandlerList;

    @PostConstruct
    public void init() {
        //从容器中获取处理器对象
        algorithmHandlerList = visionAlgorithmHandlerContainer.getAlgorithmHandlerList();
    }

    public void reset() {
        index = 0;
    }

    @Override
    public VisionResult handleAlgorithm(VisionAlgorithm visionAlgorithm, VisionEventHandler visionEventHandler) throws OperationFailNotification {
        VisionResult visionResult = new VisionResult();
        if (!AlgorithmSets.isInAlgorithmSets(visionAlgorithm.getAlgorithmName())) {
            throw new OperationFailNotification(String.format("算法名称\"%s\"不在算法集合中", visionAlgorithm.getAlgorithmName()));
        }
        if (algorithmHandlerList != null && !algorithmHandlerList.isEmpty()) {
            if (index != algorithmHandlerList.size()) {
                IVisionAlgorithmHandler algorithmHandler = algorithmHandlerList.get(index++);
//                log.info("algorithmHandler:{}", algorithmHandler);
                visionResult = algorithmHandler.handleAlgorithm(visionAlgorithm, visionEventHandler, this);
            }
        }
        return visionResult;
    }
}
