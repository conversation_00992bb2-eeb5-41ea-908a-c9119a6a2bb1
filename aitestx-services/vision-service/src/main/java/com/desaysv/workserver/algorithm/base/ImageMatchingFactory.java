package com.desaysv.workserver.algorithm.base;

import cn.hutool.core.lang.Assert;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.entity.Rectangle;
import com.desaysv.workserver.entity.VisionAlgorithm;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.image.ImageTemplateMatchingException;
import com.desaysv.workserver.exceptions.image.VisionProcessException;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacpp.DoublePointer;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.opencv.opencv_core.Mat;
import org.bytedeco.opencv.opencv_core.Point;
import org.bytedeco.opencv.opencv_core.Size;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import static com.desaysv.workserver.utils.ImageUtils.randColor;
import static org.bytedeco.opencv.global.opencv_core.CV_32FC1;
import static org.bytedeco.opencv.global.opencv_core.minMaxLoc;
import static org.bytedeco.opencv.global.opencv_highgui.*;
import static org.bytedeco.opencv.global.opencv_imgproc.*;

/**
 * 模板识别工厂
 */
@Slf4j
@Component
@Lazy
public class ImageMatchingFactory {


    public ImageMatchingFactory() {

    }

    /**
     * 预装载图像算法，主要进行模板预处理，每次图像识别（单次、循环）只会加载一次
     *
     * @param visionAlgorithm 图像算法
     * @return ImageMatchingFactory
     */
    public ImageMatchingFactory prePrepare(VisionAlgorithm visionAlgorithm) {
        return this;
    }


    /**
     * 释放图像资源，主要进行图像资源释放，每次图像识别（单次、循环）只释放一次
     */
    public void release() {

    }

    /**
     * 获取目标
     *
     * @param sourceImageMat 源图片
     * @param threshold      阈值
     * @return VisionResult
     */
    private VisionResult getTarget(VisionResult visionResult, Mat sourceImageMat, float threshold) {
        if (!visionResult.isPassed()) {
            try (Mat imgDisplayMat = new Mat()) {
                sourceImageMat.copyTo(imgDisplayMat);
                rectangle(imgDisplayMat, visionResult.getBestMatch().toRect(), randColor(), 2, 0, 0);
                Size size = imgDisplayMat.size();
                log.info("imgDisplay大小:{}x{}", size.width(), size.height());
            }
        }
        duplicateFix();
        return visionResult;
    }

    private void duplicateFix() {

    }

    /**
     * 模板识别
     *
     * @return VisionResult
     */
    public VisionResult templateMatching(VisionAlgorithm visionAlgorithm) {
        return match(visionAlgorithm);
    }

    /**
     * 验证输入参数
     */
    private void validateInput(VisionAlgorithm visionAlgorithm) {
        if (visionAlgorithm == null) {
            throw new IllegalArgumentException("视觉算法参数不能为空");
        }
        if (visionAlgorithm.getLiveFrame() == null || visionAlgorithm.getLiveFrame().image == null) {
            throw new IllegalArgumentException("相机无法实时采集，请检查相机是否正常连接");
        }
        if (visionAlgorithm.getTemplateFrame() == null) {
            throw new IllegalArgumentException("模板图像不能为空");
        }
    }

    /**
     * 图像匹配
     *
     * @param visionAlgorithm 图像算法
     * @return VisionResult
     */
    public VisionResult match(VisionAlgorithm visionAlgorithm) {
        // 参数校验
        validateInput(visionAlgorithm);

        Frame liveFrame = visionAlgorithm.getLiveFrame();
        Frame templateFrame = visionAlgorithm.getTemplateFrame();
        AbsoluteRoiRect enlargedRoi = null;
        try (  // 使用try-with-resources自动管理OpenCV资源
               Mat sourceImageMat = Java2DFrameUtils.toMat(liveFrame);
               Mat templateImageMat = Java2DFrameUtils.toMat(templateFrame);
        ) {
            if (visionAlgorithm.getRoiEnlargePercent() != null && visionAlgorithm.getRoiEnlargePercent() >= 0) {
                // 获取原始ROI
                AbsoluteRoiRect originalRoi = visionAlgorithm.getRoiRect();
                if (originalRoi != null) {
                    // 计算扩大的像素值
                    log.info("ROI进行比例放大{}%", visionAlgorithm.getRoiEnlargePercent());
                    float ratio = visionAlgorithm.getRoiEnlargePercent() / 100;
                    int enlargePixelsX = (int) (originalRoi.width() * ratio);
                    int enlargePixelsY = (int) (originalRoi.height() * ratio);

                    // 计算新的ROI坐标和尺寸
                    int newX = Math.max(0, originalRoi.x() - enlargePixelsX);
                    int newY = Math.max(0, originalRoi.y() - enlargePixelsY);
                    int newWidth = Math.min(sourceImageMat.cols() - newX,
                            originalRoi.width() + 2 * enlargePixelsX);
                    int newHeight = Math.min(sourceImageMat.rows() - newY,
                            originalRoi.height() + 2 * enlargePixelsY);

                    // 创建扩大后的ROI区域
                    enlargedRoi = new AbsoluteRoiRect(newX, newY, newWidth, newHeight);
                    log.info("扩大后的ROI区域: {}", enlargedRoi);
                }
            } else {
                log.info("使用整图搜索");
            }
            Mat sourceToUse = sourceImageMat;
            Mat templateToUse = templateImageMat;
            try (Mat graySource = visionAlgorithm.isColorMatchEnabled() ? null : new Mat();
                 Mat grayTemplate = visionAlgorithm.isColorMatchEnabled() ? null : new Mat()) {
                if (!visionAlgorithm.isColorMatchEnabled()) {
                    cvtColor(sourceImageMat, graySource, COLOR_BGR2GRAY);
                    cvtColor(templateImageMat, grayTemplate, COLOR_BGR2GRAY);
                    sourceToUse = graySource;
                    templateToUse = grayTemplate;
                }
                // 执行模板匹配
                VisionResult result = performTemplateMatching(
                        sourceToUse,
                        templateToUse,
                        TM_CCOEFF_NORMED,
                        enlargedRoi
                );

                // 记录ROI信息
                logRoiInfo(visionAlgorithm, result);

                return result;
            }
        } catch (Exception e) {
            log.error("图像模板匹配过程发生错误", e);
            throw new VisionProcessException("图像模板匹配失败", e);
        }
    }

    /**
     * 记录ROI信息
     */
    private void logRoiInfo(VisionAlgorithm visionAlgorithm, VisionResult visionResult) {
        if (visionAlgorithm.getRoiRect() != null) {
            log.info("模板图像ROI:{}", visionAlgorithm.getRoiRect().vertextString());
        }
        log.info("实际图像ROI:{}", visionResult.getBestMatch().vertextString());
    }

    /**
     * 执行模板匹配核心逻辑
     */
    public VisionResult performTemplateMatching(Mat sourceImageMat,
                                                Mat templateMat,
                                                int matchMethod,
                                                AbsoluteRoiRect enlargedRoi) throws ImageTemplateMatchingException {
        Assert.notNull(sourceImageMat);
        Assert.notNull(templateMat);

        Mat imageToMatch = sourceImageMat;
        try (Mat roiAreaMat = (enlargedRoi != null) ? new Mat(sourceImageMat, enlargedRoi) : null) {
            if (roiAreaMat != null) {
                imageToMatch = roiAreaMat;
            }

            if (imageToMatch.cols() < templateMat.cols() || imageToMatch.rows() < templateMat.rows()) {
                throw new ImageTemplateMatchingException(String.format("模板尺寸 (%d, %d) 大于源图像/ROI尺寸 (%d, %d)",
                        templateMat.cols(), templateMat.rows(),
                        imageToMatch.cols(), imageToMatch.rows()));
            }

            // 计算结果矩阵大小
            int resultCols = imageToMatch.cols() - templateMat.cols() + 1;
            int resultRows = imageToMatch.rows() - templateMat.rows() + 1;
        /*
            TM_SQDIFF:平方差匹配法；
            TM_SQDIFF_NORMED:归一化平方差匹配法；
            TM_CCORR:相关匹配法；
            TM_CCORR_NORMED:归一化相关匹配法；
            TM_CCOEFF:系数匹配法；
            TM_CCOEFF_NORMED:归一化相关系数匹配法。
         */
            // 执行模板匹配
            try (Mat resultMat = new Mat(resultRows, resultCols, CV_32FC1)) {
                matchTemplate(imageToMatch, templateMat, resultMat, matchMethod);

                // 查找最佳匹配位置
                // 归一
                // normalize(result, result, 0, 1, NORM_MINMAX, -1, new Mat());
                try (
                        DoublePointer minVal = new DoublePointer(1);
                        DoublePointer maxVal = new DoublePointer(1);
                        Point minLoc = new Point();
                        Point maxLoc = new Point();
                ) {
                    minMaxLoc(resultMat, minVal, maxVal, minLoc, maxLoc, null);

                    VisionResult visionResult = new VisionResult();
                    double bestScore;
                    Point bestLoc;

                    // 设置最佳匹配区域
                    // 如果使用cv.TM_SQDIFF或cv.TM_SQDIFF_NORMED作为比较方法，则最小值提供最佳匹配，其余的方法使用最大值进行最佳匹配
                    if (matchMethod == TM_SQDIFF || matchMethod == TM_SQDIFF_NORMED) {
                        bestScore = minVal.get();
                        bestLoc = minLoc;
                        // TM_SQDIFF_NORMED 返回值越接近0越好，为了统一分数标准，这里取反。
                        if (matchMethod == TM_SQDIFF_NORMED) {
                            bestScore = 1 - bestScore;
                        }
                    } else {
                        // TM_CCOEFF_NORMED
                        bestScore = maxVal.get();
                        bestLoc = maxLoc;
                    }

                    visionResult.setScore(bestScore);
                    log.info("匹配分数 - 最小值:{}, 最大值:{}", minVal.get(), maxVal.get());

                    // 将ROI内的坐标转换为原图坐标
                    int originalX = bestLoc.x() + (enlargedRoi != null ? enlargedRoi.x() : 0);
                    int originalY = bestLoc.y() + (enlargedRoi != null ? enlargedRoi.y() : 0);

                    // 设置最佳匹配区域（使用原图坐标）
                    Rectangle bestMatch = new Rectangle(
                            originalX,
                            originalY,
                            templateMat.cols(),
                            templateMat.rows()
                    );
                    visionResult.setEnlargeRoiRect(enlargedRoi);
                    visionResult.setBestMatch(bestMatch);
                    visionResult.setCenterPoint(calculateMatchCenter(templateMat, visionResult));

                    bestLoc.close();
                    return visionResult;
                }
            }
        }
    }


    /**
     * 计算匹配中心点
     *
     * @return 匹配中心坐标
     */
    public PointInt calculateMatchCenter(Mat templateMat, VisionResult visionResult) {
        Rectangle bestMatch = visionResult.getBestMatch();
        return new PointInt(
                (int) (bestMatch.getX() + templateMat.cols() / 2.0),
                (int) (bestMatch.getY() + templateMat.rows() / 2.0)
        );
    }

    /**
     * 窗口展示匹配结果
     */
    public void showResult(Mat imgDisplay) {
        if (imgDisplay != null) {
            imshow("Original marked", imgDisplay);
            waitKey(0);
            destroyAllWindows();
        }
    }

}
