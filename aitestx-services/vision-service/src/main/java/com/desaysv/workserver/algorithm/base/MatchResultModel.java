package com.desaysv.workserver.algorithm.base;

import lombok.Data;

/**
 * 匹配的结果  因为考虑数据比较大，所以属性名得短
 */
@Data
public class MatchResultModel {

    /**
     * 文本
     */
    private String t;

    /**
     * 左上 x
     */
    private float x;

    /**
     * 左上 y
     */
    private float y;

    /**
     * 宽
     */
    private float w;

    /**
     * 高
     */
    private float h;

    /**
     * 角度
     */
    private float r;

    public MatchResultModel(String t, float x, float y, float w, float h) {
        this.t = t;
        this.x = x;
        this.y = y;
        this.w = w;
        this.h = h;
    }

    public MatchResultModel(float x, float y, float w, float h) {
        this.x = x;
        this.y = y;
        this.w = w;
        this.h = h;
    }

    @Override
    public String toString() {
        return "SpaceResultModel{" +
                "t='" + t + '\'' +
                ", x=" + x +
                ", y=" + y +
                ", w=" + w +
                ", h=" + h +
                ", r=" + r +
                '}';
    }
}