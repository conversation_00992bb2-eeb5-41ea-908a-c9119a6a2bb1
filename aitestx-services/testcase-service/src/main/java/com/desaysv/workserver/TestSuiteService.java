package com.desaysv.workserver;

import com.desaysv.workserver.mapper.TestSuiteMapper;
import com.desaysv.workserver.model.TestSuite;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@Lazy
public class TestSuiteService {

    @Autowired
    private TestSuiteMapper testSuiteMapper;

    public TestSuite insertTestSuiteIfNotExist(TestSuite theTestSuite) {
        String testSuiteName = theTestSuite.getTestSuiteName();
        TestSuite testSuite = testSuiteMapper.selectByUniqueRecord(testSuiteName);
        if (testSuite == null) {
            //新增测试套件
            testSuiteMapper.insert(theTestSuite);
            testSuite = testSuiteMapper.selectByUniqueRecord(testSuiteName);
        } else {
            //更新
        }
        return testSuite;
    }

    public TestSuite findTestSuiteByUUID(String uuid) {
        return testSuiteMapper.selectByUUID(uuid);
    }

    public Integer updateTestSuite(TestSuite testSuite) {
        return testSuiteMapper.updateByPrimaryKey(testSuite);
    }
}
