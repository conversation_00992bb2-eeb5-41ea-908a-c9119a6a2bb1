package com.desaysv.workserver;

import com.desaysv.workserver.mapper.TestResultMapper;
import com.desaysv.workserver.model.TestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@Lazy
public class TestResultService {

    @Autowired
    private TestResultMapper testResultMapper;

    public void insertTestResult(TestResult testResult) {
        testResultMapper.insert(testResult);
    }

    public TestResult findTestResultByUUID(String  testResultUUID) {
        return testResultMapper.selectByTestResultUUID(testResultUUID);
    }

    public Integer updateTestResult(TestResult testResult) {
        return testResultMapper.updateByTestResultUUID(testResult);
    }
}
