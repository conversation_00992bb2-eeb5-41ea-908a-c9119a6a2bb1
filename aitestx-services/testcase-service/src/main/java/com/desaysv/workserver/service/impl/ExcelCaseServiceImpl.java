package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.excel.ExcelSheetTable;
import com.desaysv.workserver.mapper.ExcelCaseMapper;
import com.desaysv.workserver.model.ExcelCaseModel;
import com.desaysv.workserver.service.ExcelCaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.desaysv.workserver.utils.ArrayUtils.listToModel;

@Service
@Slf4j
@Lazy
public class ExcelCaseServiceImpl implements ExcelCaseService {
    @Autowired
    ExcelCaseMapper excelCaseMapper;

    @Override
    public void insertExcelCaseTable(Map<String, ExcelSheetTable> testCaseMap) {
        testCaseMap.entrySet().stream().forEach(t -> {
            String sheetName = t.getKey();
            ExcelSheetTable excelSheetTable = t.getValue();
            List<ExcelCaseModel> excelCaseModelList = new ArrayList<>();
            List<HashMap<Integer, String>> tableData = excelSheetTable.getTableData();
            for (HashMap<Integer, String> hashMap : tableData) {
                List<Object> rowDataList = new ArrayList<>();
                rowDataList.add(0);  //id,在数据库中是自增
                rowDataList.addAll(hashMap.values());
                ExcelCaseModel excelCaseModel = new ExcelCaseModel();
                try {
                    listToModel(rowDataList, excelCaseModel);
                    excelCaseModelList.add(excelCaseModel);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
            deleteByTableName(sheetName);
            insertExcelCaseTable(excelCaseModelList);
        });
    }

    @Override
    public Integer insertExcelCaseTable(List<ExcelCaseModel> excelCaseModelList) {
        if (CollectionUtils.isEmpty(excelCaseModelList)) {
            return -1;
        }
        return excelCaseMapper.insertExcelCaseTable(excelCaseModelList);
    }

    @Override
    public Integer updateExcelCase(ExcelCaseModel excelCaseModel) {
        return excelCaseMapper.updateExcelCase(excelCaseModel);
    }

    @Override
    public Integer clearMapColumnData(String tableName) {
        return excelCaseMapper.clearMapColumnData(tableName);
    }

    @Override
    public Integer batchUpdateExcelCase(List<ExcelCaseModel> excelCaseModel) {
        return excelCaseMapper.batchUpdateExcelCase(excelCaseModel);
    }

    @Override
    public List<ExcelCaseModel> findExcelCaseByTableName(String tableName) {
        return excelCaseMapper.findExcelCaseByTableName(tableName);
    }

    @Override
    public ExcelCaseModel findRowCaseInfoByUuid(String uuid) {
        return excelCaseMapper.findRowCaseInfoByUuid(uuid);
    }

    @Override
    public Integer deleteByTableName(String tableName) {
        return excelCaseMapper.deleteByTableName(tableName);
    }

}
