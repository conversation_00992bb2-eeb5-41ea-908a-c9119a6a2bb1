package com.desaysv.workserver.service;

import com.desaysv.workserver.excel.ExcelDataEntity;
import com.desaysv.workserver.excel.ExcelEntity;
import com.desaysv.workserver.excel.ExcelRowDataModel;
import com.desaysv.workserver.excel.ExcelSheetTable;

import java.util.List;
import java.util.Map;

public interface ExcelDataHandleService {
    void insertData(Map<String, Object> dataInfo);

    void updateExcelData(ExcelRowDataModel excelRowDataModel);

    void clearMapColumnData(Map<String, Object> columnDataMap);

    List<ExcelDataEntity> findExcelDataByName(String sheetName);

    Integer deleteExcelDataByName(String sheetName);

    void insertToExcelDataTable(String excelFileName, String sheetName, List<ExcelRowDataModel> excelRowDataModels);

    Map<String, ExcelSheetTable> getDBTestCaseInfo(ExcelEntity excelEntity);

    boolean updateOrderExcelCase(List<ExcelRowDataModel> excelRowDataModels) throws Exception;
}
