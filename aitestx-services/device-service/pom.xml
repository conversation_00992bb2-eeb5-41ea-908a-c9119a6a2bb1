<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>AITestXServer</artifactId>
        <groupId>com.desaysv</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>device-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <usb4java.javax>1.3.0</usb4java.javax>
        <javax.usb.api>1.0.2</javax.usb.api>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jacob</groupId>
            <artifactId>jacob</artifactId>
            <scope>system</scope>
            <version>1.20</version>
            <systemPath>${pom.basedir}/../../libs/jacob.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version> <!-- 请检查是否有更新版本 -->
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-model</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>common-service</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-mapper</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>picture-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.usb4java</groupId>
            <artifactId>usb4java-javax</artifactId>
            <version>${usb4java.javax}</version>
        </dependency>

        <dependency>
            <groupId>javax.usb</groupId>
            <artifactId>usb-api</artifactId>
            <version>${javax.usb.api}</version>
        </dependency>

        <dependency>
            <groupId>javax.usb</groupId>
            <artifactId>usb-api</artifactId>
            <version>${javax.usb.api}</version>
            <classifier>sources</classifier>
        </dependency>

        <dependency>
            <groupId>com.fazecast</groupId>
            <artifactId>jSerialComm</artifactId>
            <version>2.10.4</version>
        </dependency>

        <dependency>
            <groupId>xyz.froud</groupId>
            <artifactId>JVisa</artifactId>
            <version>2.0.0</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.screenClicker</groupId>-->
        <!--            <artifactId>screenClicker</artifactId>-->
        <!--            <scope>system</scope>-->
        <!--            <version>1.20</version>-->
        <!--            <systemPath>${pom.basedir}/../../libs/screenClicker.jar</systemPath>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.desaysv.bus</groupId>
            <artifactId>CanTools</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv.bus</groupId>
            <artifactId>nican</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv.bus</groupId>
            <artifactId>tosuncan</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv.device</groupId>
            <artifactId>usbPlugDevice</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv.device</groupId>
            <artifactId>electricrelayforinstrument</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.desaysv.device</groupId>
            <artifactId>testBoxControlInstrument</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv.device</groupId>
            <artifactId>soundDevice</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>vision-service</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.desaysv.device</groupId>
            <artifactId>ad4704</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.Automation.Bdaq</groupId>
            <artifactId>Automation.Bdaq</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/../../libs/Automation.BDaq.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.13.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.13.0</version>
        </dependency>

    </dependencies>

    <!--        <build>-->
    <!--            <resources>-->
    <!--                <resource>-->
    <!--                    <directory>dlls</directory>-->
    <!--                    <includes>-->
    <!--                        <include>**/*.dll</include>-->
    <!--                    </includes>-->
    <!--                </resource>-->
    <!--            </resources>-->
    <!--        </build>-->


</project>