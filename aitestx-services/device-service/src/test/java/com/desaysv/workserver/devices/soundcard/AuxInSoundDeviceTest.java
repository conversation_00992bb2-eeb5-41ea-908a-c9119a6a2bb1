package com.desaysv.workserver.devices.soundcard;

import com.desaysv.SoundCollectService;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * AuxInSoundDevice单元测试类
 * 主要测试声音监控相关的功能
 */
//@RunWith(MockitoJUnitRunner.class)
public class AuxInSoundDeviceTest {

    @Mock
    private SoundCollectService soundCollectService;

    @Mock
    private ScheduledExecutorService scheduler;

    @Mock
    private ScheduledFuture<?> volumeMonitoringTaskFuture;

    private AuxInSoundDevice auxInSoundDevice;

    @Before
    public void setUp() {
        // 创建测试对象
        auxInSoundDevice = new AuxInSoundDevice(new DeviceOperationParameter()) {
            @Override
            public float fetchVolume(Integer deviceChannel, String currentDirection) {
                // 根据不同通道返回不同的模拟音量值
                if (deviceChannel == 1) {
                    return 75.0f;
                } else if (deviceChannel == 2) {
                    return 45.0f;
                } else if (deviceChannel == 3) {
                    return 95.0f;
                } else {
                    return 50.0f;
                }
            }
        };

        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field soundCollectServiceField = AuxInSoundDevice.class.getDeclaredField("soundCollectService");
            soundCollectServiceField.setAccessible(true);
            soundCollectServiceField.set(auxInSoundDevice, soundCollectService);

            java.lang.reflect.Field schedulerField = AuxInSoundDevice.class.getDeclaredField("scheduler");
            schedulerField.setAccessible(true);
            schedulerField.set(auxInSoundDevice, scheduler);

            java.lang.reflect.Field volumeMonitoringTaskFutureField = AuxInSoundDevice.class.getDeclaredField("volumeMonitoringTaskFuture");
            volumeMonitoringTaskFutureField.setAccessible(true);
            volumeMonitoringTaskFutureField.set(auxInSoundDevice, volumeMonitoringTaskFuture);

            // 设置recordedVolumes字段
            java.lang.reflect.Field recordedVolumesField = AuxInSoundDevice.class.getDeclaredField("recordedVolumes");
            recordedVolumesField.setAccessible(true);
            Map<Integer, List<Float>> recordedVolumes = new HashMap<>();

            // 为通道1添加音量数据
            List<Float> channel1Volumes = new ArrayList<>();
            channel1Volumes.add(70.0f);
            channel1Volumes.add(75.0f);
            channel1Volumes.add(72.0f);
            recordedVolumes.put(1, channel1Volumes);

            // 为通道2添加音量数据
            List<Float> channel2Volumes = new ArrayList<>();
            channel2Volumes.add(40.0f);
            channel2Volumes.add(45.0f);
            channel2Volumes.add(42.0f);
            recordedVolumes.put(2, channel2Volumes);

            // 为通道3添加音量数据
            List<Float> channel3Volumes = new ArrayList<>();
            channel3Volumes.add(90.0f);
            channel3Volumes.add(95.0f);
            channel3Volumes.add(92.0f);
            recordedVolumes.put(3, channel3Volumes);

            recordedVolumesField.set(auxInSoundDevice, recordedVolumes);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testBeginSoundMonitor() {
        // 执行测试方法
        auxInSoundDevice.beginSoundMonitor(1);

        // 验证方法调用
        verify(soundCollectService, times(1)).startRecording();
    }

    @Test
    public void testEndSoundMonitor_SingleCondition() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression(">70");
        soundMonitor.setRequiredNums(1);

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertTrue("应该满足条件，因为通道1和通道3的音量都大于70", result);
        verify(soundCollectService, times(1)).stopRecording();
        verify(volumeMonitoringTaskFuture, times(1)).cancel(false);
    }

    @Test
    public void testEndSoundMonitor_AndCondition() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression(">70&<80");
        soundMonitor.setRequiredNums(1);

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertTrue("应该满足条件，因为通道1的音量在70到80之间", result);
        verify(soundCollectService, times(1)).stopRecording();
    }

    @Test
    public void testEndSoundMonitor_OrCondition() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression("<50|>90");
        soundMonitor.setRequiredNums(2);

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertTrue("应该满足条件，因为通道2的音量小于50，通道3的音量大于90", result);
        verify(soundCollectService, times(1)).stopRecording();
    }

    @Test
    public void testEndSoundMonitor_ComplexCondition() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression("(>70&<80)|(>90&<100)");
        soundMonitor.setRequiredNums(2);

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertTrue("应该满足条件，因为通道1的音量在70到80之间，通道3的音量在90到100之间", result);
        verify(soundCollectService, times(1)).stopRecording();
    }

    @Test
    public void testEndSoundMonitor_NoMatch() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression(">100");
        soundMonitor.setRequiredNums(1);

        // 模拟saveRecordingToFile方法
        doReturn("录音文件保存成功").when(soundCollectService).saveRecordingToFile(anyString(), anyString());

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertFalse("不应该满足条件，因为没有通道的音量大于100", result);
        verify(soundCollectService, times(1)).stopRecording();
    }

    @Test
    public void testEndSoundMonitor_RequiredNumsNotMet() throws OperationFailNotification {
        // 创建测试参数
        SoundMonitor soundMonitor = new SoundMonitor();
        soundMonitor.setExpression(">70");
        soundMonitor.setRequiredNums(3); // 要求3个通道满足条件，但只有2个通道满足

        // 模拟saveRecordingToFile方法
        doReturn("录音文件保存成功").when(soundCollectService).saveRecordingToFile(anyString(), anyString());

        // 执行测试方法
        boolean result = auxInSoundDevice.endSoundMonitor(1, soundMonitor);

        // 验证结果
        assertFalse("不应该满足条件，因为只有2个通道满足条件，但要求3个", result);
        verify(soundCollectService, times(1)).stopRecording();
    }
}
