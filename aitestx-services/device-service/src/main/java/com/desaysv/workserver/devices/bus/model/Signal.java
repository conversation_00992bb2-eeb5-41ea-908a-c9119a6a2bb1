package com.desaysv.workserver.devices.bus.model;

import com.desaysv.workserver.devices.bus.utils.SysConvertUtils;
import lombok.Data;

import java.awt.*;
import java.util.List;
import java.util.Map;

@Data
public class Signal {
    private double maximum; // 最大值
    private double minimum; // 最小值
    private List<Label> labels; // 下拉框选项
    private String signalName; // 信号组件的名称
    private Rectangle signalBounds;
    private String originalValue;// 原始值 16进制
    private double translateScale; //变换比例
    private double offsetTransform;//变换偏移
    private double actualValue;//actualValue 实际值
    private double initialValue;
    private String messageId;
    private String messageName;
    private String label;
    private boolean movedToMainPanel;// 是否已经移动到主面板
    private boolean showLabel;// 显示标签
    private boolean locked;// 锁定信号
    private SignalType signalType;// 信号展示类型
    private Boolean switchState;    // 开关状态
    private Integer pressedValue;   // 按下时的值
    private Integer releasedValue;  // 释放时的值
    private boolean controlRelayEnabled;// 信号控制继电器开关
    private Map<String,Map<String,Boolean>> relayStates; //通道对应状态
    public void resetToInitialValue() {
        actualValue = initialValue;
        originalValue = SysConvertUtils.decimal2Hex(String.valueOf((int) (actualValue / translateScale) - (offsetTransform / translateScale)));
    }

    public enum SignalType {
        SPINNER, SLIDER, COMBOBOX, SWITCH
    }
}
