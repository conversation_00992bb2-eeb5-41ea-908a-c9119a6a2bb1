package com.desaysv.workserver.devices.I2C;

import com.desaysv.workserver.devices.serial.SerialConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: IIC参数设置
 * @date: 2024/4/10 10:26
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class IICConfig extends SerialConfig {
    private String rate;//时钟频率
    private int clkSLevel;//时钟延展
    private int usbIndex;//usb索引
    private byte startBit;//发送前是否加起始位
    private byte stopBit;//发送后是否加停止位
    private int ioNum;//io口选择
    private int ioDir;//io口方向
    private int ioBit;//io口电平
    private int preDivision;//预分频
    private int period;//占空比精度
    private int pulse;//占空比参数
    private int pulseNum;//脉冲数
    private byte adcNum;//ADC通道
}
