package com.desaysv.workserver.manager;

import java.util.concurrent.Semaphore;

public class Pool {

    public static void main(String[] args) throws InterruptedException {
        Pool pool = new Pool();
        pool.items[0] = 123;
        System.out.println(pool.getItem());
        System.out.println(pool.getItem());
    }

    private static final int MAX_AVAILABLE = 100; // 可同时访问资源的最大线程数
    private final Semaphore availableSemaphore = new Semaphore(MAX_AVAILABLE, true);
    protected Object[] items = new Object[MAX_AVAILABLE];   //共享资源
    protected boolean[] used = new boolean[MAX_AVAILABLE];

    public Object getItem() throws InterruptedException {
        availableSemaphore.acquire();
        return getNextAvailableItem();
    }

    public void putItem(Object x) {
        if (markAsUnused(x)) {
            availableSemaphore.release();
        }
    }

    private synchronized Object getNextAvailableItem() {
        for (int i = 0; i < MAX_AVAILABLE; ++i) {
            if (!used[i]) {
                used[i] = true;
                return items[i];
            }
        }
        System.out.println("no next:" + items[0]);
        return null;
    }

    private synchronized boolean markAsUnused(Object item) {
        for (int i = 0; i < MAX_AVAILABLE; ++i) {
            if (item == items[i]) {
                if (used[i]) {
                    used[i] = false;
                    return true;
                } else
                    return false;
            }
        }
        return false;
    }
}