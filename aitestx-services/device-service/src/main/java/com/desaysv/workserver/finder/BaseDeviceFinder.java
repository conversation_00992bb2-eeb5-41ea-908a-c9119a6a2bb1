package com.desaysv.workserver.finder;

import java.util.regex.Pattern;

public class BaseDeviceFinder {

    protected static boolean isForbidden(String serialFriendlyName, String[] forbiddenHardwareName) {
        for (String name : forbiddenHardwareName) {
            if (Pattern.compile(name).matcher(serialFriendlyName).find()) {
                return true;
            }
        }
        return false;
    }

}
