package com.desaysv.workserver.stream.grabber;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.FrameGrabber;
import org.bytedeco.javacv.VideoInputFrameGrabber;

@Slf4j
public class FrameStreamGrabber extends StreamGrabber {

    private FrameGrabber grabber;

    public FrameStreamGrabber(int devicePort) {
        tryLoad();
        try {
            log.info("VideoInputFrameGrabber->连接相机端口:{}", devicePort);
            //TODO:确定索引到底对不对
            grabber = VideoInputFrameGrabber.createDefault(devicePort);

            // 尝试使用不同的后端
            String cameraBackend = System.getenv("camera.backend");
            if (!StrUtils.isEmpty(cameraBackend)) {
                log.info("使用相机后端:{}", cameraBackend);
                grabber.setFormat(cameraBackend);
            }
            // grabber.setFormat("dshow"); // DirectShow
            // grabber.setFormat("msmf"); // Media Foundation
            // grabber.setFormat("vfw"); // Video For Windows
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void tryLoad() {
        try {
            VideoInputFrameGrabber.tryLoad();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public Frame grab() throws FrameGrabberException {
        if (grabber == null) {
            throw new FrameGrabberException("grabber未初始化或初始化失败");
        }
        try {
            return grabber.grab();
        } catch (FrameGrabber.Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    @Override
    public ImageBuffer grabByteBuffer() throws FrameGrabberException {
        ImageBuffer imageBuffer = new ImageBuffer();
        Frame grabFrame = grab();
//        System.out.println("grab:" + grabFrame);
        imageBuffer.setFrame(grabFrame);
//        BufferedImage bufferedImage = Java2DFrameUtils.toBufferedImage(grabFrame);
//        imageBuffer.setData(((DataBufferByte) bufferedImage.getRaster().getDataBuffer()).getData());
        imageBuffer.setWidth(grabFrame.imageWidth);
        imageBuffer.setHeight(grabFrame.imageHeight);
        return imageBuffer;
    }

    @Override
    public synchronized void close() throws FrameGrabberException {
        if (grabber == null) {
            return;
        }
        try {
            grabber.close();
        } catch (FrameGrabber.Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    @Override
    public void setImageWidth(int width) {
        if (grabber != null) {
            grabber.setImageWidth(width);
        }
    }

    @Override
    public void setImageHeight(int height) {
        if (grabber != null) {
            grabber.setImageHeight(height);
        }
    }

    @Override
    public int getImageWidth() {
        return grabber != null ? grabber.getImageWidth() : 0;
    }

    @Override
    public int getImageHeight() {
        return grabber != null ? grabber.getImageHeight() : 0;
    }

    @Override
    public void start() throws FrameGrabberException {
        if (grabber == null) {
            throw new FrameGrabberException("grabber未初始化或初始化失败");
        }
        try {
            grabber.restart();
        } catch (FrameGrabber.Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    @Override
    public void stop() throws Exception {

    }

    @Override
    public void trigger() throws Exception {

    }

    @Override
    public void release() throws Exception {

    }

    @Override
    public double getFrameRate() {
        return grabber != null ? grabber.getFrameRate() : 0.0;
    }

}
