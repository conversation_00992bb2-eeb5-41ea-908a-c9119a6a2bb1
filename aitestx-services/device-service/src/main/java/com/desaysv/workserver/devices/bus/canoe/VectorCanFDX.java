package com.desaysv.workserver.devices.bus.canoe;

import cantools.dbc.DecodedSignal;
import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.UdsModel;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.CanDevice;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.fdx.FdxDataProcessor;
import com.desaysv.workserver.devices.bus.fdx.FdxSocket;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequenceAll;
import com.desaysv.workserver.entity.CanReplyInfo;
import com.desaysv.workserver.entity.NotificationManager;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;
import static com.desaysv.workserver.devices.bus.fdx.FdxSocket.getMirrorFoldCode;
import static com.desaysv.workserver.utils.FileUtils.isValidFileName;
import static com.desaysv.workserver.utils.sse.SseConstants.UDS_LOG_PATH_SUBSCRIBE_ID;

@Slf4j
public class VectorCanFDX extends CanDevice implements ICanSequenceAll {
    private FdxSocket socket;

    public VectorCanFDX(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
//        socket = FdxSocket.getInstance();
        socket = FdxSocket.getInstance();
    }

    @Override
    public boolean sendAllPeriodicCanMessage(Integer deviceChannel) throws BusError {
        return false;
    }

    @Override
    public boolean stopAllCanMessage(Integer deviceChannel) {
        return false;
    }

    @Override
    public boolean pauseAllCanMessage(Integer deviceChannel) {
        return false;
    }

    @Override
    public boolean isFailReTry() {
        return true;
    }

    @Override
    public int getCanMessageCycle(Integer deviceChannel, String messageName) {
        return -1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.VECTOR_CAN;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        try {
            if (!socket.isCANoeRunning()) {
                FdxSocket.setInstanceNULL();
                socket = FdxSocket.getInstance();
                return socket.isCANoeRunning();
            }
            return true;
        } catch (BusError e) {
            log.warn(e.getMessage(), e);
            throw new DeviceOpenException(e);
        }
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public void send(CanMessage message) throws BusError {

    }

    @Override
    protected void stopSend(CanMessage message) {

    }

    @Override
    protected CyclicTask sendCanMessage(Integer deviceChannel, CanMessage message) throws BusError {
        return null;
    }

    @Override
    protected OperationResult canReplay(CanReplyInfo canReplyInfo) throws BusError, InterruptedException, IOException {
        return null;
    }

    @Override
    protected CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
        return null;
    }

    @Override
    protected CyclicTask stopCanMessage(Integer deviceChannel, String messageId) {
        return null;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }

    @Override
    protected boolean isAllowLock() {
        return false;
    }

    @Override
    public boolean sendCanData(Integer deviceChannel, String messageId, byte[] byteData, Integer period, Integer cycle, boolean isCanFd) {
        return false;
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, int messageStatus) {
        return false;
    }

    @Override
    public boolean setCanChannelMessageStatus(Integer deviceChannel, List<String> excludeEcus, int messageStatus) {
        return false;
    }

    @Override
    public String verifyCanMessage(Integer deviceChannel, String messageId, String byteData, Integer count) throws BusError {
        return "";
    }

    @Override
    public boolean wake(Integer deviceChannel, Integer time) {
        return false;
    }

    @Override
    public boolean sleep(Integer deviceChannel, Integer time) {
        return false;
    }

    @Override
    public void sendService(Integer deviceChannel, String messageId, String byteData) {

    }

    @Override
    public boolean sendDatas(Integer deviceChannel, String messageId, String byteData) {
        return false;
    }

    @Override
    public String checkReplyData(Integer deviceChannel, String messageId, String byteData) {
        return "";
    }

    @Override
    public boolean responsiveServices(UdsModel udsModel) {
        return false;
    }


    @Override
    public boolean responsiveService(Integer deviceChannel, String requestId, String responseId, String serviceData, String responseData) {
        return false;
    }

    @Override
    public boolean responsiveService(Integer deviceChannel, String functionId, String serviceData, String responseData) {
        return false;
    }

    @Override
    public boolean responsiveService(Integer deviceChannel, String serviceData, String responseData) {
        return false;
    }

    @Override
    public boolean send3EService(Integer deviceChannel,boolean isOpen) {
        return false;
    }


    @Override
    public String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError {
        String result = "";
        if (expectResult.equalsIgnoreCase("NotResp")) {
            return fetchCanUdsDataExplainFun();
        } else {
            byte[] data = fetchCanUdsFun();
            if (data == null) {
                log.info("获取CAN UDS 数据失败: data == null");
            } else {
                int expectByteLength = expectResult.length() / 2;
                if (data.length != expectByteLength) {
                    log.info("错误原因：字节长度不一致，序列期望UDS的字节长度是{}，CANoe返回UDS数组长度为{}", expectByteLength, data.length);
                } else {
                    result = Hex.encodeHexString(data);
                }
            }
        }
        return result;
    }

    @Override
    public double fetchCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws BusError {
        return fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
    }

    @Override
    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws BusError {
        long sTime = System.currentTimeMillis();
        return fetchCanSignal(deviceChannel, ecuNodeName, messageName, signalName, sTime);
    }

    private double fetchCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long sTime) throws BusError {
        if (isSimulated()) {
            return RandomUtil.randomDouble(1, 10);
        }
        double fetchSignalValue = -1000.0;
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        if (!socket.sendGetCanSignalInfo(deviceChannel, ecuNodeName, messageName, signalName, fetchSignalValue)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        if (!socket.sendGetCanSignalCounter()) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        boolean result = socket.getReturnValue();
        if (!result) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_SIGNAL_LOG, signalName, fetchSignalValue, FAIL, GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchSignalValue;
        }
        return socket.getCanSignalValue();
    }

    @Override
    public boolean setCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long signalRawValue) throws BusError {
        return setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, signalRawValue);
    }

    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue, Integer cycle) throws BusError {
        return false;
    }

    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_SIGNAL_LOG, signalName, signalPhyValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanSignalInfo(deviceChannel, ecuNodeName, messageName, signalName, signalPhyValue)) {
            ActionSequencesLoggerUtil.info(SET_CAN_SIGNAL_LOG, signalName, signalPhyValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanSignalCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_SIGNAL_LOG, signalName, signalPhyValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public void executeCycleStepLogic(String taskId, Integer deviceChannel, String ecuNodeName,
                                      String messageName, String signalName, Integer start,
                                      Integer end, int step, String interval) {
    }


    @Override
    public boolean setCanMessageCSRolling(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MESSAGE_CSROLLING_LOG, messageNameOrID, checksumStatus, rollingCounterStatus, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanMessageCSRollingInfo(deviceChannel, ecuNodeName, StrUtils.hexStrToInt(messageNameOrID), checksumStatus, rollingCounterStatus)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MESSAGE_CSROLLING_LOG, messageNameOrID, checksumStatus, rollingCounterStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanMessageCSRollingCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_MESSAGE_CSROLLING_LOG, messageNameOrID, checksumStatus, rollingCounterStatus, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();

    }

    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_PTS_LOG, byteInstruction, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanPTSInfo(ecuNodeName, messageId, byteInstruction)) {
            ActionSequencesLoggerUtil.info(SET_CAN_PTS_LOG, byteInstruction, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanPTSCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_PTS_LOG, byteInstruction, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError {
        return false;
    }

    @Override
    public boolean setCanSingleMsgStatus(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_SINGLE_MSG_STATUS_LOG, messageNameOrId, messageStatus, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanSingleMsgStatusInfo(deviceChannel, ecuNodeName, messageNameOrId, messageStatus)) {
            ActionSequencesLoggerUtil.info(SET_CAN_SINGLE_MSG_STATUS_LOG, messageNameOrId, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanSingleMsgStatusCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_SINGLE_MSG_STATUS_LOG, messageNameOrId, messageStatus, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }


    @Override
    public boolean setCanEcuAllMsgStatus(Integer deviceChannel, String ecuNodeName, int messageStatus) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanEcuAllMsgStatusInfo(deviceChannel, ecuNodeName, messageStatus)) {
            ActionSequencesLoggerUtil.info(SET_CAN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanEcuAllMsgStatusCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_ECU_ALL_MSG_STATUS_LOG, messageStatus, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setCanMessageDLC(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_DLC_LOG, messageNameOrId, dlc, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanMsgDLCInfo(ecuNodeName, messageNameOrId, dlc)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_DLC_LOG, messageNameOrId, dlc, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanMsgDLCCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_DLC_LOG, messageNameOrId, dlc, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }


    @Override
    public boolean setCanMessageCycleTime(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_CYCLE_TIME_LOG, cycleTime, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanMsgCycleTimeInfo(ecuNodeName, messageNameOrId, cycleTime)) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_CYCLE_TIME_LOG, cycleTime, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanCycleTimeCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_MSG_CYCLE_TIME_LOG, cycleTime, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

//    @Override
//    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double lowerSignal, double upperSignal) throws SimulatedDeviceNotification, BusError {
//        Supplier<Double> fetchCanSignalPhyValueSupplier = () -> {
//            try {
//                return fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
//            } catch (BusError e) {
//                log.error("compareCanSignal:", e);
//                throw new RuntimeException(e);
//            }
//        };
//        Supplier<Boolean> resultConsistent = () -> {
//            double currentResult = fetchCanSignalPhyValueSupplier.get();
//            return lowerSignal <= currentResult && currentResult <= upperSignal;
//        };
//        return getSupplierWithTimeout(fetchCanSignalPhyValueSupplier, GET_SUPPLIER_VALUE_TIMEOUT, TimeUnit.MILLISECONDS, resultConsistent);
//    }

//    @Override
//    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) throws BusError {
//        Supplier<Double> fetchCanSignalPhyValueSupplier = () -> {
//            try {
//                return fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
//            } catch (BusError e) {
//                log.error("compareCanSignal:", e);
//                throw new RuntimeException(e);
//            }
//        };
//        Supplier<Boolean> resultConsistent = () -> {
//            double lastResult = signalPhyValue;
//            double currentResult = fetchCanSignalPhyValueSupplier.get();
//            return lastResult == currentResult;
//        };
//        return getSupplierWithTimeout(fetchCanSignalPhyValueSupplier, GET_SUPPLIER_VALUE_TIMEOUT, TimeUnit.MILLISECONDS, resultConsistent);
//    }

    @Override
    public String fetchCanPTS(Integer deviceChannel, String messageId) throws BusError {
        byte[] data = socket.SendGetCanPTSInfo();
        String canPTSRx = FdxDataProcessor.parseCanPTSRxValue(data);
        log.info("fetchCanPTS: {}", canPTSRx);
        return canPTSRx;
    }

    @Override
    public boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return lastCheckCanMsgID(deviceChannel, messageId, exist, 2000);
    }

    @Override
    public boolean lastCheckCanMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendLastCheckMsgIDInfo(deviceChannel, messageId, exist, milliSecond)) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetStartCheckMsg()) {
            ActionSequencesLoggerUtil.info(LAST_CHECK_MSG_ID_FAIL, messageId, exist, FAIL, SET_XCP_VAR_BTN_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public String notificationUpgrade(int fileType) throws BusError {
        long sTime = System.currentTimeMillis();
        String ptsSwVersion = null;
        if (!socket.sendSetPtsSwVersion()) {
            ActionSequencesLoggerUtil.info(SET_PTS_VERSION_INIT_LOG, PTS_SW_VERSION_INIT_VALUE, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return null;
        }
        if (!socket.sendSetUpgrade()) {
            ActionSequencesLoggerUtil.info(SET_APP_FLASHDRIVER_LOG, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return null;
        }
        if (!socket.sendSetUpgradeCounter()) {
            ActionSequencesLoggerUtil.info(SET_UPGRADE_COUNTER_LOG, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return null;
        }
        ptsSwVersion = socket.getPtsSwVersion();
        ActionSequencesLoggerUtil.info(RETURN_PTS_VERSION_LOG, ptsSwVersion, System.currentTimeMillis() - sTime);
        return ptsSwVersion;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        long sTime = System.currentTimeMillis();
        String version = NotificationManager.getInstance().getVersion();
        boolean result = version.equals(ptsSwVersion);
        ActionSequencesLoggerUtil.info(COMPARE_VERSION_LOG, version, ptsSwVersion, System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return result;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) throws BusError {
        long sTime = System.currentTimeMillis();
        double getXcpValue = -1000;
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, xcpName, getXcpValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return getXcpValue;
        }
        if (!socket.sendGetCanXCPInfo(String.format("%s::%s", "XCP", ecuNodeName), xcpName, -1000)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, xcpName, getXcpValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return getXcpValue;
        }
        if (!socket.sendGetCanXCPCounter()) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, xcpName, getXcpValue, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return getXcpValue;
        }
        if (!socket.getReturnValue()) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, xcpName, getXcpValue, FAIL, GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return getXcpValue;
        }
        return FdxDataProcessor.parseCanXCPValue(socket.SendGetXcpData());
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, xcpName, xcpValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanXCPInfo(String.format("%s::%s", "XCP", ecuNodeName), xcpName, xcpValue)) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, xcpName, xcpValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanXCPCounter()) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, xcpName, xcpValue, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }


    @Override
    public boolean setCanUDSFun(String address, String sendUdsData) throws BusError {
        log.info("---------setCanUDS:{}-------", sendUdsData);
        long sTime = System.currentTimeMillis();
        if (!socket.setAddress(address.equals("Fun") ? 2 : 1)) {
            ActionSequencesLoggerUtil.info(SET_DOCAN_FAIL,
                    address.equals("2") ? "Fun" : "Phy", sendUdsData, FAIL, SET_ADDRESS_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.getUDSFinishFlag()) {
            log.info("---------start getUDSFinishFlag-------");
            ActionSequencesLoggerUtil.info(GET_FINISH_FLAG_FAIL, FAIL, VectorUtils.SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendUdsData(sendUdsData)) {
            ActionSequencesLoggerUtil.info(SET_DOCAN_FAIL,
                    address, sendUdsData, FAIL, VectorUtils.SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getUDSFinishFlag();
    }

    @Override
    public boolean setCanUDS27ServerWithSeedKey(String address, String udsCaseIdString) throws BusError {
        log.info("---------setCanUDS27ServerFun:{}-------", udsCaseIdString);
        byte[] udsKey = socket.getUDSKey();
        log.info("udsKey:{}", Arrays.toString(udsKey));
        StringBuilder sb = new StringBuilder();
        sb.append(udsCaseIdString);
        for (int i = 0; i < udsKey.length; i++) {
            sb.append(String.format("%02X", udsKey[i]));
        }
        log.info("sendUdsData:{}", Arrays.toString(udsKey));
        return setCanUDSFun(address, sb.toString());
    }


    @Override
    public boolean setCanUdsUdp(String udsUdpCaseIdString) throws BusError {
        log.info("---------setCanUdsUdp:{}-------", udsUdpCaseIdString);
        return socket.executeTestCase(udsUdpCaseIdString) && socket.getTestResult();
    }

    @Override
    public boolean setCanDataLink(String dataLinkCaseIdString) throws BusError {
        log.info("---------setCanDataLink:{}-------", dataLinkCaseIdString);
        return socket.executeTestCase(dataLinkCaseIdString) && socket.getTestResult();
    }

    @Override
    public boolean setCanTP(String tpCaseIdString) throws BusError {
        log.info("---------setCanTP:{}-------", tpCaseIdString);
        return socket.executeTestCase(tpCaseIdString) && socket.getTestResult();
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        socket.setCanLogName(canLogName.replaceAll("\\*", "-"));
        return true;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        log.info("---------setCanLog commandId:{}, canLogName:{}-------", commandId, socket.getCanLogName());
        if (commandId == 1) {
            socket.setCANLogName();
        }
        return commandId == 1 ? socket.setCANStartLogging() : socket.setCANStopLogging();

    }

    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpFunSwitch(int commandId) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_XCP_FUN_SWITCH_LOG, commandId, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetXcpSwitchInfo(commandId)) {
            ActionSequencesLoggerUtil.info(SET_XCP_FUN_SWITCH_LOG, commandId, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setXcpVar(String varName, int xcpValue) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, varName, xcpValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanXCPVarInfo(varName, xcpValue)) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, varName, xcpValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetCanXCPBtn()) {
            ActionSequencesLoggerUtil.info(SET_CAN_XCP_LOG, varName, xcpValue, FAIL, SET_XCP_VAR_BTN_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setXcpSwitchAndVar(int switchCommand, String varName, int varValue) throws BusError {
        boolean setXcpFunSwitchPass = setXcpFunSwitch(switchCommand);
        if (!setXcpFunSwitchPass) return false;
        return setXcpVar(varName, varValue);
    }

    @Override
    public boolean setKeyPosition(int commandId) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_KEY_POSITION_LOG, commandId, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetKeyPositionInfo(commandId)) {
            ActionSequencesLoggerUtil.info(SET_KEY_POSITION_LOG, commandId, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setKeyButton(int commandId) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_KEY_BUTTON_LOG, commandId, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendSetKeyButtonInfo(commandId)) {
            ActionSequencesLoggerUtil.info(SET_KEY_BUTTON_LOG, commandId, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue(GET_TEST_RESULT_TIMEOUT);
    }

    @Override
    public boolean setRDefogSts(int commandId) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_SETRDEFOGSTS_LOG, commandId, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendRDefogStsInfo(commandId)) {
            ActionSequencesLoggerUtil.info(SET_SETRDEFOGSTS_LOG, commandId, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setMirrorFoldSTS(String command) throws BusError {
        long sTime = System.currentTimeMillis();
        int commandCode = getMirrorFoldCode(command);
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_MIRROR_FOLDSTS_LOG,
                    command, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setMirrorFoldSTS(commandCode)) {
            ActionSequencesLoggerUtil.info(SET_MIRROR_FOLDSTS_LOG,
                    command, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setLampSwitch(String command) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SET_LAMP_SWITCH_LOG,
                    command, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setLampSwitch(command)) {
            ActionSequencesLoggerUtil.info(SET_LAMP_SWITCH_LOG,
                    command, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean checkTurnLamp(String turnLampType, int workTime, int checkPeriod) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(CHECK_TURN_LAMP_LOG, turnLampType, workTime, checkPeriod, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendCheckTurnLampInfo(turnLampType, workTime, checkPeriod)) {
            ActionSequencesLoggerUtil.info(CHECK_TURN_LAMP_LOG, turnLampType, workTime, checkPeriod, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendStartCheckTurnLamp()) {
            ActionSequencesLoggerUtil.info(CHECK_TURN_LAMP_LOG, turnLampType, workTime, checkPeriod, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        int timeout = GET_RETURN_VALUE_TIMEOUT + workTime * checkPeriod * 2;  //canoe需要等功能触发后才开始计算周期，实际检测需要2.8s，因此超时时间公式：2s+周期*周期时间*2
        boolean result = socket.getReturnValue(timeout);
        if (!result) {
            ActionSequencesLoggerUtil.info(CHECK_TURN_LAMP_LOG, turnLampType, workTime, checkPeriod, FAIL, GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return true;
    }

    @Override
    public boolean checkFourDoor(String lockStatusCommand) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(CHECK_FOUR_DOOR_LOG,
                    lockStatusCommand, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendCheckFourDoor(lockStatusCommand)) {
            ActionSequencesLoggerUtil.info(CHECK_FOUR_DOOR_LOG,
                    lockStatusCommand, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendStartCheckFourDoor()) {
            ActionSequencesLoggerUtil.info(CHECK_FOUR_DOOR_LOG, lockStatusCommand, FAIL, SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean sendEventMsg(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(SEND_EVENT_MSG_ID_FAIL, MsgID, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendEventMsgInfo(deviceChannel, MsgID, msgTime, msgCounter, msgData)) {
            ActionSequencesLoggerUtil.info(SEND_EVENT_MSG_ID_FAIL, MsgID, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(CHECK_VOLTAGE_ID_FAIL, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.checkVoltage(pinNumber, pinAliveTime, pinNoAliveTime, workCycleNumber)) {
            ActionSequencesLoggerUtil.info(CHECK_VOLTAGE_ID_FAIL, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendStartCheckPinVoltage()) {
            ActionSequencesLoggerUtil.info(CHECK_VOLTAGE_ID_FAIL, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        int timeout = GET_RETURN_VALUE_TIMEOUT + (pinAliveTime + pinNoAliveTime) * workCycleNumber;  //canoe需要等功能触发后才开始计算周期，因此超时时间公式：2s+（激活时间+不激活时间）*周期
        return socket.getReturnValue(timeout);
    }

    @Override
    public double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchXcpVar(String varName) throws BusError {
        long sTime = System.currentTimeMillis();
        int fetchXcpValue = 0;
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, varName, fetchXcpValue, FAIL, SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchXcpValue;
        }
        if (!socket.sendGetXcpVarInfo(varName, fetchXcpValue)) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, varName, fetchXcpValue, FAIL, SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return fetchXcpValue;
        }
        if (!socket.sendGetCanXCPBtn()) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, varName, fetchXcpValue, FAIL, CHECK_XCP_VAR_BTN_FAIL, System.currentTimeMillis() - sTime);
            return fetchXcpValue;
        }
        boolean result = socket.getReturnValue();
        if (!result) {
            ActionSequencesLoggerUtil.info(FETCH_CAN_XCP_LOG, varName, fetchXcpValue, FAIL, GET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return fetchXcpValue;
        }
        return socket.getCanXcpVarValue();
    }

    @Override
    public boolean checkFindKeyOrNoKey(boolean findKey, int findKeyTime) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("checkFindKeyOrNoKey=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    findKey ? "检测天线找到钥匙" : "检测天线找不到钥匙", "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setFindKeyAndTime(findKey, findKeyTime)) {
            ActionSequencesLoggerUtil.info("checkFindKeyOrNoKey=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    findKey ? "检测天线找到钥匙" : "检测天线找不到钥匙", "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue(35000);  //因为CAPL需要处理逻辑需要大于31s
    }

    @Override
    public boolean setCanUdsLogName(String udsLogName) {
        log.info("---------setCanUdsLogName udsLogName:{}-------", udsLogName);
        boolean validFileName = isValidFileName(udsLogName);
        if (!isValidFileName(udsLogName)) {
            ActionSequencesLoggerUtil.info(String.format("录制Log文件名：%s不符合规则", udsLogName));
            return false;
        }
        socket.setUdsLogName(udsLogName.replaceAll("\\*", "-"));
        return true;
    }

    @Override
    public boolean setCanUdsLog(int commandId) throws BusError {
        log.info("---------setCanUdsLog commandId:{}, udsLogName:{}-------", commandId, socket.getUdsLogName());
        if (commandId == 0) {
            socket.setUDSLogName();
            return socket.setUSDStartLogging();
        } else {
            //读取到测试路径
            String udsLogPath = socket.getUDSLogPath();
            if (!udsLogPath.equals(UDS_LOG_FINISH)) {
                SseUtils.pubMsg(UDS_LOG_PATH_SUBSCRIBE_ID, udsLogPath);
                return socket.setUDSStopLogging();
            }
            return false;
        }
    }

    @Override
    public boolean fetchCanNodeM(String nodeMCaseIdString) throws BusError {
        log.info("---------fetchCanNodeM:{}-------", nodeMCaseIdString);
        return socket.getTestResult();
    }

    @Override
    public boolean fetchCanFBL(String fblCaseIdString) throws BusError {
        log.info("---------fetchCanFBL:{}-------", fblCaseIdString);
        return socket.getTestResult();
    }

    @Override
    public boolean fetchCanNM(String nmCaseIdString) throws BusError {
        log.info("---------fetchCanNM:{}-------", nmCaseIdString);
        return socket.getTestResult();
    }

    @Override
    public boolean fetchCanDataLink(String dataLinkCaseIdString) throws BusError {
        log.info("---------fetchCanDataLink:{}-------", dataLinkCaseIdString);
        return socket.getTestResult();
    }

    @Override
    public boolean fetchCanTP(String tpCaseIdString) throws BusError {
        log.info("---------fetchCanTP:{}-------", tpCaseIdString);
        return socket.getTestResult();
    }

    public byte[] fetchCanUdsFun() throws BusError {
        log.info("---------fetchCanUdsFun-------");
        return socket.getUDSFinishFlag() ? socket.getUDSRecUdsData() : null;
    }

    @Override
    public String fetchCanUdsDataExplainFun() throws BusError {
        return socket.getUDSFinishFlag() ? socket.getUDSRecUdsDataDescribe() : null;
    }

    @Override
    public String fetchCanUds22ExplainFun() throws BusError {
        log.info("---------fetchCanUdsExplainFun:-------");
        return socket.getUDSFinishFlag() ? socket.getUDSRecUdsDataDescribe22() : null;
    }

    @Override
    public boolean fetchCanUdsUdp(String udsUdpCaseIdString) throws BusError {
        log.info("---------fetchCanUdsUdp:{}-------", udsUdpCaseIdString);
        return socket.getTestResult();
    }

    @Override
    public boolean setStartUdsTest(int flag) throws BusError {
        log.info("---------setStartUdsTest:{}-------", flag);
        return socket.setStartUdsTestFlag(flag);
    }

    @Override
    public boolean setResetCharge(int flag) throws BusError {
        log.info("---------setResetCharge:{}-------", flag);
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("setResetCharge: flag=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒", flag, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setResetChargeFlag(flag)) {
            ActionSequencesLoggerUtil.info("setResetCharge: flag=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒", flag, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setTemperature(int mode, double value) throws BusError {
        log.info("---------setTemperature: mode=={}, value=={}-------", mode, value);
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("setTemperature: mode=={}, value=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    mode, value, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setTemperature(mode, value)) {
            ActionSequencesLoggerUtil.info("setTemperature: mode=={}, value=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    mode, value, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setTemperatureCounter()) {
            ActionSequencesLoggerUtil.info("setTemperatureCounter, 返回结果:{}, 原因:{}, 执行时长:{}毫秒", "失败", SET_COUNTER_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean setPowerState(String powerState) throws BusError {
        if (isSimulated()) {
            return true;
        }
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("setPowerState powerState=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    powerState, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setPowerState(powerState)) {
            ActionSequencesLoggerUtil.info("setPowerState: powerState =={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    powerState, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue(5000);  //因为电源设置，CAN
    }

    @Override
    public boolean setBoardCardInit(String initType) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("setBoardCardInit initType=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    initType, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setBoardCardInit(initType)) {
            ActionSequencesLoggerUtil.info("setBoardCardInit: initType =={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    initType, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public boolean stopSendPeriodicCanData(Integer deviceChannel, String messageId) {
        return false;
    }

    @Override
    public boolean setCanFBL(String fblCaseIdString) throws BusError {
        log.info("---------setCanFBL:{}-------", fblCaseIdString);
        return socket.executeTestCase(fblCaseIdString, GET_FBL_TEST_RETURN_TIMEOUT) && socket.getTestResult();
    }

    @Override
    public boolean setCanNodeM(String nodeMCaseIdString) throws BusError {
        log.info("---------setCanNodeM:{}-------", nodeMCaseIdString);
        return socket.executeTestCase(nodeMCaseIdString) && socket.getTestResult();
    }

    @Override
    public boolean setCanNM(String nmCaseIdString) throws BusError {
        log.info("---------setCanNM:{}-------", nmCaseIdString);
        return socket.executeTestCase(nmCaseIdString) && socket.getTestResult();
    }

    @Override
    public boolean setCanUdsKeepSession(String status) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!socket.setReturnValue(NOT_RESPONSE)) {
            ActionSequencesLoggerUtil.info("setCanUdsKeepSession status=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    status, "失败", SET_RETURN_VALUE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.setKeepSessionStatus(status)) {
            ActionSequencesLoggerUtil.info("setCanUdsKeepSession: status =={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒",
                    status, "失败", SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getReturnValue();
    }

    @Override
    public Map<String, DecodedSignal> fetchAllCanSignalValue(Integer deviceChannel, String messageName) throws BusError {
        return Collections.emptyMap();
    }

}
