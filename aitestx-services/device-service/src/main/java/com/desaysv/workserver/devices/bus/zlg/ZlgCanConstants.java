package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.utils.ReflectUtils;

import java.util.HashMap;
import java.util.Map;

public class ZlgCanConstants {

    public static final int ZCAN_PCI5121 = 1;
    public static final int ZCAN_PCI9810 = 2;
    public static final int ZCAN_USBCAN1 = 3;
    public static final int ZCAN_USBCAN2 = 4;
    public static final int ZCAN_PCI9820 = 5;
    public static final int ZCAN_CAN232 = 6;
    public static final int ZCAN_PCI5110 = 7;
    public static final int ZCAN_CANLITE = 8;
    public static final int ZCAN_ISA9620 = 9;
    public static final int ZCAN_ISA5420 = 10;
    public static final int ZCAN_PC104CAN = 11;
    public static final int ZCAN_CANETUDP = 12;
    public static final int ZCAN_CANETE = 12;
    public static final int ZCAN_DNP9810 = 13;
    public static final int ZCAN_PCI9840 = 14;
    public static final int ZCAN_PC104CAN2 = 15;
    public static final int ZCAN_PCI9820I = 16;
    public static final int ZCAN_CANETTCP = 17;
    public static final int ZCAN_PCIE_9220 = 18;
    public static final int ZCAN_PCI5010U = 19;
    public static final int ZCAN_USBCAN_E_U = 20;
    public static final int ZCAN_USBCAN_2E_U = 21;
    public static final int ZCAN_PCI5020U = 22;
    public static final int ZCAN_EG20T_CAN = 23;
    public static final int ZCAN_PCIE9221 = 24;
    public static final int ZCAN_WIFICAN_TCP = 25;
    public static final int ZCAN_WIFICAN_UDP = 26;
    public static final int ZCAN_PCIe9120 = 27;
    public static final int ZCAN_PCIe9110 = 28;
    public static final int ZCAN_PCIe9140 = 29;
    public static final int ZCAN_USBCAN_4E_U = 31;
    public static final int ZCAN_CANDTU_200UR = 32;
    public static final int ZCAN_CANDTU_MINI = 33;
    public static final int ZCAN_USBCAN_8E_U = 34;
    public static final int ZCAN_CANREPLAY = 35;
    public static final int ZCAN_CANDTU_NET = 36;
    public static final int ZCAN_CANDTU_100UR = 37;
    public static final int ZCAN_PCIE_CANFD_100U = 38;
    public static final int ZCAN_PCIE_CANFD_200U = 39;
    public static final int ZCAN_PCIE_CANFD_400U = 40;
    public static final int ZCAN_USBCANFD_200U = 41;
    public static final int ZCAN_USBCANFD_100U = 42;
    public static final int ZCAN_USBCANFD_MINI = 43;
    public static final int ZCAN_CANFDCOM_100IE = 44;
    public static final int ZCAN_CANSCOPE = 45;
    public static final int ZCAN_CLOUD = 46;
    public static final int ZCAN_CANDTU_NET_400 = 47;
    public static final int ZCAN_CANFDNET_TCP = 48;
    public static final int ZCAN_CANFDNET_200U_TCP = 48;
    public static final int ZCAN_CANFDNET_UDP = 49;
    public static final int ZCAN_CANFDNET_200U_UDP = 49;
    public static final int ZCAN_CANFDWIFI_TCP = 50;
    public static final int ZCAN_CANFDWIFI_100U_TCP = 50;
    public static final int ZCAN_CANFDWIFI_UDP = 51;
    public static final int ZCAN_CANFDWIFI_100U_UDP = 51;
    public static final int ZCAN_CANFDNET_400U_TCP = 52;
    public static final int ZLG_CANFDDTU_400UEWGR = 64;
    public static final int ZCAN_CANFDNET_400U_UDP = 53;
    public static final int ZCAN_CANFDBLUE_200U = 54;
    public static final int ZCAN_CANFDNET_100U_TCP = 55;
    public static final int ZCAN_CANFDNET_100U_UDP = 56;
    public static final int ZCAN_CANFDNET_800U_TCP = 57;
    public static final int ZCAN_CANFDNET_800U_UDP = 58;
    public static final int ZCAN_USBCANFD_800U = 59;
    public static final int ZCAN_PCIE_CANFD_100U_EX = 60;
    public static final int ZCAN_PCIE_CANFD_400U_EX = 61;
    public static final int ZCAN_PCIE_CANFD_200U_MINI = 62;
    public static final int ZCAN_PCIE_CANFD_200U_M2 = 63;
    public static final int ZCAN_CANFDDTU_400_TCP = 64;
    public static final int ZCAN_CANFDDTU_400_UDP = 65;
    public static final int ZCAN_CANFDWIFI_200U_TCP = 66;
    public static final int ZCAN_CANFDWIFI_200U_UDP = 67;
    public static final int ZCAN_CANFDDTU_800ER_TCP = 68;
    public static final int ZCAN_CANFDDTU_800ER_UDP = 69;
    public static final int ZCAN_CANFDDTU_800EWGR_TCP = 70;
    public static final int ZCAN_CANFDDTU_800EWGR_UDP = 71;
    public static final int ZCAN_CANFDDTU_600EWGR_TCP = 72;
    public static final int ZCAN_CANFDDTU_600EWGR_UDP = 73;
    public static final int ZCAN_USBCAN_I_MINI = 3;

    public static final int STATUS_ERR = 0;
    public static final int STATUS_OK = 1;
    public static final int STATUS_ONLINE = 2;
    public static final int STATUS_OFFLINE = 3;
    public static final int STATUS_UNSUPPORTED = 4;

    public static final byte SEND_ECHO = 0x20;//发送回显

    public static Map<String, Object> getConstantsMap() {
        try {
            return ReflectUtils.getStaticProperties(ZlgCanConstants.class);
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public static void main(String[] args) {
        System.out.println(ZlgCanConstants.getConstantsMap());
    }

}