package com.desaysv.workserver.devices.robot.type.mg400.sockets;

import com.desaysv.workserver.devices.robot.base.CoordinateSystem;
import com.desaysv.workserver.devices.robot.base.RobotPose;
import com.desaysv.workserver.devices.robot.type.mg400.entity.IOEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 18:17
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
public class Dashboard extends DobotCommunication {

    private static final Integer DEFAULT_PORT = 29999;

    @Override
    public int getDefaultPort() {
        return DEFAULT_PORT;
    }

    @Override
    public int getSoTimeout() {
        return 0;
    }

    public RobotReply enableRobot() {
        return sendCmdToRobot("EnableRobot()", 20000);
    }

    public RobotReply disableRobot() {
        return sendCmdToRobot("DisableRobot()", 20000);
    }


    public RobotReply setPayLoad(float weight) {
        return sendCmdToRobot(String.format("PayLoad(%f)", weight), 20000);
    }

    /**
     * 获取位置姿态
     *
     * @return
     */
    public RobotPose getPose(CoordinateSystem coordinateSystem) {
        RobotReply reply = sendCmdToRobot(String.format("GetPose(%d,%d)", coordinateSystem.getUser(), coordinateSystem.getTool()),
                5000, false);
        if (reply.isFailed()) {
            return new RobotPose();
        }
        Pattern bracketPattern = Pattern.compile("\\{(.*)}");
        Matcher bracketMatcher = bracketPattern.matcher(reply.getMessage());
        if (bracketMatcher.find()) {
            reply.setMessage(bracketMatcher.group(1));
        }
        Pattern pattern = Pattern.compile("[-+]?\\d*\\.?\\d+");
        Matcher matcher = pattern.matcher(reply.getMessage());
        List<Double> matchList = new ArrayList<>();
        while (matcher.find()) {
            matchList.add(Double.valueOf(matcher.group()));
        }
        RobotPose robotPose = new RobotPose();
        if (matchList.size() == 6) {
            robotPose.setX(matchList.get(0));
            robotPose.setY(matchList.get(1));
            robotPose.setZ(matchList.get(2));
            robotPose.setR(matchList.get(3));
            robotPose.setSlideRail(matchList.get(4));
            robotPose.setRetain(matchList.get(5));
            robotPose.setUser(coordinateSystem.getUser());
        }
        return robotPose;
    }

    public RobotReply setSpeedFactor(int factor) {
        return sendCmdToRobot(String.format("SpeedFactor(%d)", factor), 5000);
    }

    public RobotReply clearError() {
        return sendCmdToRobot("ClearError()", 5000);
    }

    public RobotReply setUserCoordinate(int userIndex) {
        return sendCmdToRobot(String.format("User(%d)", userIndex), 5000);
    }

    public RobotReply setAccL(int ratio) {
        return sendCmdToRobot(String.format("AccL(%d)", ratio), 5000);
    }

    public RobotReply resetRobot() {
        return sendCmdToRobot("ResetRobot()", 5000);
    }

    public RobotReply stopDrag() {
        return sendCmdToRobot("StopDrag()", 5000);
    }

    public RobotReply getErrorID() {
        return sendCmdToRobot("GetErrorID()", 5000);
    }

    public RobotReply setDO(int index, int status) {
        return sendCmdToRobot(String.format("DO(%d,%d)", index, status), 5000);
    }
    public RobotReply setDOGroup(List<IOEntity> ioEntityList) {
        String cmd = ioEntityList.stream()
                .map(ioEntity -> String.format("%d,%d", ioEntity.getIndex(), ioEntity.getValue()))
                .collect(Collectors.joining(",", "DOGroup(", ")"));
        return sendCmdToRobot(cmd, 5000);
    }


}
