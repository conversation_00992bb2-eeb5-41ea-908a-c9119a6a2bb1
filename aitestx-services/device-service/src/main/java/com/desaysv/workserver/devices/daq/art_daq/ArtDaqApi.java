package com.desaysv.workserver.devices.daq.art_daq;

import com.sun.jna.Pointer;
import com.sun.jna.ptr.IntByReference;
import com.sun.jna.ptr.PointerByReference;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class ArtDaqApi {
    private static final ArtDaq.Clibrary cliInst = ArtDaq.Clibrary.INSTANCE;
    private Pointer taskHandle;

    public static class ArtDaqError extends Exception {

        public ArtDaqError(int error) {
            super(getErrorString(error));
        }
    }

    public String getDeviceName() {
        byte[] attribute = new byte[2048];
        int error = cliInst.ArtDAQ_GetSystemAttribute(ArtDaq.ArtDAQ_Sys_DevNames, attribute, attribute.length);
        if (error < 0) {
            String errorMsg = getErrorString(error);
            log.error("获取设备名称失败，错误码：{}，错误信息：{}", error, errorMsg);
            return "";
        }
        // 替换连续空字符为逗号，并去除末尾空值
        String deviceNamesStr = new String(attribute, StandardCharsets.UTF_8).trim();
        deviceNamesStr = deviceNamesStr.replaceAll("\\u0000+", ","); // 替换多个空字符为逗号
        deviceNamesStr = deviceNamesStr.replaceAll(",$", ""); // 去除末尾逗号
        return deviceNamesStr;
    }

    public boolean init(String deviceName) {
        // 增加设备名称校验
        if (!deviceName.matches("^Dev\\d+$")) {
            log.error("无效的设备名称格式: {}", deviceName);
            return false;
        }
        // 增加设备存在性检查
        if (!getAllDevices().contains(deviceName)) {
            log.error("设备 {} 不存在", deviceName);
            return false;
        }
        if (!deviceName.isEmpty()) {
            try {
                // 在关键步骤添加详细日志
                log.info("开始初始化设备: {}", deviceName);

                taskHandle = createTask();
                log.debug("任务句柄创建成功: {}", taskHandle);

                byte[] chanName = getChanName(deviceName);
                log.debug("通道名称: {}", new String(chanName));

                createChannel(taskHandle, chanName);
                log.info("模拟输入通道创建成功");

                configClock(taskHandle);
                log.info("采样时钟配置成功");

                configTrigger(taskHandle, chanName);
                log.info("触发配置成功");

                startTask(taskHandle);
                log.info("任务启动成功");

                return true;
            } catch (ArtDaqError e) {
                log.error("初始化过程中出现错误", e);
                cliInst.ArtDAQ_ClearTask(taskHandle);
                return false;
            }
        }
        return false;
    }

    private List<String> getAllDevices() {
        byte[] devices = new byte[2048];
        int error = cliInst.ArtDAQ_GetSystemAttribute(ArtDaq.ArtDAQ_Sys_DevNames, devices, devices.length);
        return Arrays.stream(new String(devices).split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    public void release() {
        cliInst.ArtDAQ_StopTask(taskHandle);
        cliInst.ArtDAQ_ClearTask(taskHandle);
    }

    private static String getErrorString(int errorCode) {
        if (errorCode < 0) {
            byte[] errorString = new byte[2048];
            cliInst.ArtDAQ_GetExtendedErrorInfo(errorString, 2048);
            return new String(errorString);
        }
        return null;
    }

    private byte[] getChanName(String deviceName) {
        String strChannelName = deviceName.split(",")[0].trim() + "/" + "ai0\0";
        return strChannelName.getBytes();
    }

    private Pointer createTask() {
        // 修改任务名为符合NI规范的名称
        String strTaskName = "MyDAQTask_01"; // 移除特殊字符和终止符
        byte[] TaskName = strTaskName.getBytes(StandardCharsets.US_ASCII);

        PointerByReference taskPointer = new PointerByReference();
        int error = cliInst.ArtDAQ_CreateTask(TaskName, taskPointer);

        if (error < 0) {
            String errorMsg = getErrorString(error);
            log.error("任务创建失败 (Error {}): {}", error, errorMsg);
            return null;
        }
        return taskPointer.getValue();
    }

    private void createChannel(Pointer taskHandle, byte[] chanName) throws ArtDaqError {
        String strNameToAssignToChannel = "";
        byte[] nameToAssignToChannel = strNameToAssignToChannel.getBytes();
        int error = cliInst.ArtDAQ_CreateAIVoltageChan(taskHandle, chanName, nameToAssignToChannel,
                ArtDaq.ArtDAQ_Val_Diff, -10.0, 10.0, ArtDaq.ArtDAQ_Val_Volts, null);
        getErrorString(error);
        if (error < 0) {
            throw new ArtDaqError(error);
        }
    }

    private void configClock(Pointer taskHandle) throws ArtDaqError {
        // 降低采样率和缓冲区大小
        double samplingRate = 1000.0;  // 从10kHz降到1kHz
        int bufferSize = 1000;         // 缓冲区调整为1秒数据量

        int error = cliInst.ArtDAQ_CfgSampClkTiming(
                taskHandle,
                "".getBytes(),
                samplingRate,
                ArtDaq.ArtDAQ_Val_Rising,
                ArtDaq.ArtDAQ_Val_ContSamps,
                bufferSize
        );

        if (error < 0) {
            log.error("时钟配置失败 (Rate: {}Hz, Buffer: {})", samplingRate, bufferSize);
            throw new ArtDaqError(error);
        }
    }

    private void configTrigger(Pointer taskHandle, byte[] chanName) throws ArtDaqError {
        // 调整触发参数
        double triggerLevel = 1.0; // 将触发阈值从0.01调整到1.0V
        int error = cliInst.ArtDAQ_CfgAnlgEdgeStartTrig(
                taskHandle,
                chanName,
                ArtDaq.ArtDAQ_Val_Rising,
                triggerLevel
        );

        if (error < 0) {
            log.error("触发配置失败 (Level: {}V)", triggerLevel);
            throw new ArtDaqError(error);
        }
    }

    private void startTask(Pointer taskHandle) throws ArtDaqError {
        int error = cliInst.ArtDAQ_StartTask(taskHandle);
        getErrorString(error);
        if (error < 0) {
            throw new ArtDaqError(error);
        }
        try {
            Thread.sleep(500); // 等待500ms确保任务开始
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public double read() throws ArtDaqError {
        int arraySizeInSamples = 1000;
        double[] data = new double[arraySizeInSamples];
        IntByReference samplesPerChanRead = new IntByReference(0);
        IntByReference reserved = new IntByReference(0);

        int error = cliInst.ArtDAQ_ReadAnalogF64(taskHandle, 1000, 100.0, ArtDaq.ArtDAQ_Val_GroupByScanNumber,
                data, arraySizeInSamples, samplesPerChanRead, reserved);
        if (error < 0) {
            throw new ArtDaqError(error);
        }
        int samples = samplesPerChanRead.getValue();
        if (samples <= 0) {
            throw new ArtDaqError(-200284);
        }
        System.out.printf("Acquired %d samples\n", samples);
        for (int k = 0; k < samples; k++) {
            System.out.printf("  %.04f  ", data[k]);
        }
        System.out.println("\n");

        // 计算有效值（RMS）
        double sum = 0.0;
        for (int i = 0; i < samples; i++) {
            sum += data[i] * data[i]; // 累加平方值
        }
        // 计算均方根
        return Math.sqrt(sum / samples);
    }
}