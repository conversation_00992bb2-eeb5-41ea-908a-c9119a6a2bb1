package com.desaysv.workserver.devices.testbox.socket;

import java.io.*;
import java.net.Socket;
import java.util.Arrays;
import java.util.concurrent.ConcurrentLinkedQueue;

public class TCPSocketClient1 {
    private Socket socket;
    private OutputStream out;
    private InputStream in;
    private ConcurrentLinkedQueue<byte[]> commandQueue;
    private ConcurrentLinkedQueue<byte[]> responseQueue;

    public TCPSocketClient1(String serverAddress, int serverPort) throws IOException {
        socket = new Socket(serverAddress, serverPort);
        out = socket.getOutputStream();
        in = socket.getInputStream();
        commandQueue = new ConcurrentLinkedQueue<>();
        responseQueue = new ConcurrentLinkedQueue<>();

        // Start a thread to handle incoming responses
        new Thread(this::listenForResponses).start();
    }

    // Method to send a command (16-bit hex byte array)
    public void sendCommand(byte[] command) {
        commandQueue.offer(command); // Store the command
        try {
            out.write(command); // Send the command
            out.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // Method to get the response for the last sent command
    public byte[] getResponse() {
        return responseQueue.poll();
    }

    // Thread to listen for responses from the server
    private void listenForResponses() {
        try {
            byte[] buffer = new byte[1024]; // Adjust buffer size as needed
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                byte[] response = Arrays.copyOf(buffer, bytesRead); // Extract the actual response
                byte[] command = commandQueue.poll(); // Get the corresponding command
                if (command != null) {
                    responseQueue.offer(response); // Store the response
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // Close the connection
    public void close() throws IOException {
        in.close();
        out.close();
        socket.close();
    }


    public static void main(String[] args) {
        try {
            TCPSocketClient1 client = new TCPSocketClient1("127.0.0.1", 12345);

            // Simulate different interfaces sending commands
            byte[] command1 = hexStringToByteArray("01020304"); // Example: 01 02 03 04
            client.sendCommand(command1);
            byte[] response1 = client.getResponse();
            System.out.println("Response for command1: " + byteArrayToHexString(response1));

            byte[] command2 = hexStringToByteArray("05060708"); // Example: 05 06 07 08
            client.sendCommand(command2);
            byte[] response2 = client.getResponse();
            System.out.println("Response for command2: " + byteArrayToHexString(response2));

            client.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // Helper method to convert hex string to byte array
    public static byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    // Helper method to convert byte array to hex string
    public static String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
}
