package com.desaysv.workserver.devices.power.kikusui;

import com.desaysv.workserver.common.port.CheckPointInfo;
import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 菊水电压触发设置
 * @date: 2024/7/7 19:18
 */
@Data
public class VoltageTrigger {
    private int symbol;
    private Double voltage;
    private int currentSymbol;
    private float current;
    private int actionOperator;
    private String coordinateName;
    private String portName;
    private int attempts;
    private int interval;
    private CheckPointInfo checkPointInfo;
    private List<String> coordinateNameList;

    public VoltageTrigger() {
        checkPointInfo = new CheckPointInfo();
    }
}
