package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.devices.power.base.ChannelPowerData;
import com.desaysv.workserver.devices.power.base.PowerDataDecoder;
import com.desaysv.workserver.exceptions.device.DeviceDataDecodeException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 电源SCPI协议
 */
@Getter
@Slf4j
public class PowerSCPIProtocol implements PowerProtocol {

    private final PortDevice device;

    public PowerSCPIProtocol(PortDevice device) {
        this.device = device;
    }

    public boolean sendCommand(String command) {
        try {
            return device.send(command);
        } catch (DeviceSendException e) {
            log.error("发送命令{}失败:{}", command, e.getMessage());
            return false;
        }
    }

    public String sendCommandAndReceive(String command) {
        try {
            return device.sendAndReceiveString(command + "\n");
        } catch (DeviceSendException e) {
            log.error("发送命令{}失败:{}", command, e.getMessage());
            return null;
        }
    }

    @Override
    public boolean setRemoteMode(boolean isRemote) {
        return sendCommand(isRemote ? ScpiCommandConstants.REMOTE : ScpiCommandConstants.LOCAL);
    }

    @Override
    public boolean fetchOutput() {
        return Objects.equals(sendCommandAndReceive(ScpiCommandConstants.OUTPUT_STATUS), "1");
    }

    @Override
    public boolean selectChannel(Integer channel) {
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand(isOutput ? ScpiCommandConstants.OUTPUT_ON : ScpiCommandConstants.OUTPUT_OFF);
    }

    @Override
    public boolean setVoltageMax(Integer channel, float voltage) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.SET_VOLTAGE_MAX);
    }

    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        return selectChannel(channel) &&
                sendCommand(String.format(ScpiCommandConstants.SET_VOLTAGE + " %fV", voltage));
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        return selectChannel(channel) &&
                sendCommand(String.format(ScpiCommandConstants.SET_CURRENT + " %fA", current));
    }

    @Override
    public boolean readData() {
        return false;
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_VOLTAGE);
    }

    @Override
    public boolean readCurrent(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_CURRENT);
    }

    @Override
    public int getDataLength() {
        return 0;
    }

    @Override
    public void decodeData(byte[] data, PowerDataDecoder powerDataDecoder) throws DeviceDataDecodeException {
        String dataString = new String(data).trim();
        ChannelPowerData powerData = new ChannelPowerData();
        try {
            powerData.setDataCH1(Float.parseFloat(dataString));
        } catch (NumberFormatException e) {
            log.error(e.getMessage(), e);
//            throw new DeviceDataDecodeException(e);
        }
        powerDataDecoder.decodeData(powerData);
    }
}
