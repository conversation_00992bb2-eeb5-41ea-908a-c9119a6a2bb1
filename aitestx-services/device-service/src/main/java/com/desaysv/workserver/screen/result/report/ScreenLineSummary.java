package com.desaysv.workserver.screen.result.report;

import com.desaysv.workserver.screen.result.mist.BackTracePoint;
import com.desaysv.workserver.screen.result.mist.TestPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 画线报点报告
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScreenLineSummary extends TouchSummary {

    private TestPoint testPoint; //实际测试点

    private BackTracePoint reportPoint; //失败测试报点

//    private Double angleDeviation; //报点和实际测试角度偏差

    private Double linearDeviation; //线性度偏差

    private Double distanceDeviation;


}
