package com.desaysv.workserver.devices.robot.interfaces;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.robot.type.mg400.entity.CirclePoint;
import com.desaysv.workserver.devices.robot.type.mg400.entity.LongTouchRobotCoordinate;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.devices.robot.type.mg400.entity.SwipeQuicklyPoint;
import com.desaysv.workserver.model.SpeedCoordinates;

import java.util.List;

public interface IRobotDevice {

    //    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).TOUCH"})
    RobotReply touch(String coordinateName) throws OperationFailNotification;

    RobotReply touch(SpeedCoordinates speedCoordinates) throws OperationFailNotification;

    RobotReply touch(String coordinateName, String projectName) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).RANDOM_TIMES_TOUCH"})
    RobotReply randomTouch(String coordinateName, int lowerTouchCount, int upperTouchCount) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).TOUCH_MORE"})
    RobotReply touchMore(List<String> coordinatesArray) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).PERFORM_LONG_TOUCH"})
    RobotReply longTouch(String coordinateName, String timeWithUnit) throws OperationFailNotification;

    RobotReply longTouch(LongTouchRobotCoordinate longTouchRobotCoordinate) throws OperationFailNotification;

    RobotReply quickSwipe(SwipeQuicklyPoint swipeQuicklyPoint) throws OperationFailNotification;

    RobotReply circle(CirclePoint circlePoint) throws OperationFailNotification;

    RobotReply circle(String coordinateName, Double radius) throws OperationFailNotification;

    RobotReply quickSwipe(String coordinateName, Integer direction, Integer dynamics) throws OperationFailNotification;

    RobotReply longTouch(String coordinateName, float milliseconds) throws OperationFailNotification;

    RobotReply pressTouch(String coordinateName) throws OperationFailNotification;

    RobotReply releaseTouch(String coordinateName) throws OperationFailNotification;

    RobotReply pressTouch(String coordinateName, String projectName) throws OperationFailNotification;

    RobotReply releaseTouch(String coordinateName, String projectName) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).SLIDE_MORE"})
    RobotReply slideMore(List<String> coordinatesArray) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.RobotRegexRule).SLIDE_MORE_NO_LIFT"})
    RobotReply slideMoreWithoutLift(List<String> coordinatesArray) throws OperationFailNotification;
}
