package com.desaysv.workserver.devices.robot.type.mg400.sockets;

import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.devices.robot.type.mg400.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 13:20
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
public class DobotMove extends DobotCommunication {

    private static final Integer DEFAULT_PORT = 30003;

    @Override
    public int getDefaultPort() {
        return DEFAULT_PORT;
    }

    @Override
    public int getSoTimeout() {
        return 5000;
    }

    /**
     * 关节点动运动，不固定距离运动
     *
     * @param command 点动运动轴
     * @return 返回执行结果的描述信息
     */
    public RobotReply moveJog(String command) {
        log.info("moveJog:{}", command);
        String cmd;
        if (command == null || command.isEmpty()) {
            cmd = "MoveJog()";
        } else {
            String strPattern = "^(J[1-6][+-])|([XYZR][+-])$";
            if (command.matches(strPattern)) {
                cmd = "MoveJog(" + command + ")";
            } else {
                cmd = null;
            }
        }
        return sendCmdToRobot(cmd, 5000);
    }

    /**
     * 停止关节点动运动
     *
     * @return 返回执行结果的描述信息
     */
    public RobotReply stopMoveJog() {
        return moveJog(null);
    }

    public RobotReply movJ(MoveEntity moveEntity) {
//        log.info("MovJ:{}", moveEntity);
        StringBuilder str = new StringBuilder();
        str.append("MovJ(").
                append(moveEntity.getX()).append(",").
                append(moveEntity.getY()).append(",").
                append(moveEntity.getZ()).append(",").
                append(moveEntity.getR());
        if (moveEntity.getUser() != null) {
            str.append(",User=").append(moveEntity.getUser());
        }
        if (moveEntity.getTool() != null) {
            str.append(",Tool=").append(moveEntity.getTool());
        }
        if (moveEntity.getSpeed() != null) {
            str.append(",SpeedJ=").append(moveEntity.getSpeed());
        }
        if (moveEntity.getAcc() != null) {
            str.append(",AccJ=").append(moveEntity.getAcc());
        }
        if (moveEntity.getCp() != null) {
            str.append(",CP=").append(moveEntity.getCp());
        }
        str.append(")");
        return sendCmdToRobot(str.toString(), 5000);
    }

    public RobotReply movL(MoveEntity moveEntity) {
        return sendCmdToRobot(buildMovLCmd(moveEntity).toString(), 5000);
    }

    public RobotReply movLQuickly(List<MoveEntity> moveEntityList) {
        StringBuilder cmd = new StringBuilder();
        for (MoveEntity moveEntity : moveEntityList) {
            cmd.append(buildMovLCmd(moveEntity));
        }
        return sendCmdToRobot(cmd.toString(), 5000);
    }

    public StringBuilder buildMovLCmd(MoveEntity moveEntity) {
        StringBuilder str = new StringBuilder();
        str.append("MovL(").
                append(moveEntity.getX()).append(",").
                append(moveEntity.getY()).append(",").
                append(moveEntity.getZ()).append(",").
                append(moveEntity.getR());
        if (moveEntity.getUser() != null) {
            str.append(",User=").append(moveEntity.getUser());
        }
        if (moveEntity.getTool() != null) {
            str.append(",Tool=").append(moveEntity.getTool());
        }
        if (moveEntity.getSpeed() != null) {
            str.append(",SpeedL=").append(moveEntity.getSpeed());
        }
        if (moveEntity.getAcc() != null) {
            str.append(",AccL=").append(moveEntity.getAcc());
        }
        if (moveEntity.getCp() != null) {
            str.append(",CP=").append(moveEntity.getCp());
        }
        str.append(")");
        return str;
    }

    public RobotReply jointMovJ(JointMovJEntity jointMovJEntity) {
//        log.info("JointMovJ:{}", jointMovJEntity);
        StringBuilder str = new StringBuilder();
        str.append("JointMovJ(").append(jointMovJEntity.getJ1()).append(",")
                .append(jointMovJEntity.getJ2())
                .append(",")
                .append(jointMovJEntity.getJ3())
                .append(",")
                .append(jointMovJEntity.getJ4());
        if (jointMovJEntity.getUser() != null) {
            str.append(",User=").append(jointMovJEntity.getUser());
        }
        if (jointMovJEntity.getTool() != null) {
            str.append(",Tool=").append(jointMovJEntity.getTool());
        }
        if (jointMovJEntity.getSpeedJ() != null) {
            str.append(",SpeedJ=").append(jointMovJEntity.getSpeedJ());
        }
        if (jointMovJEntity.getAccJ() != null) {
            str.append(",AccJ=").append(jointMovJEntity.getAccJ());
        }
        str.append(")");
        return sendCmdToRobot(str.toString(), 5000);
    }

    public RobotReply movJExt(MovJExtEntity movJExtEntity) {
        StringBuilder str = new StringBuilder();
        str.append("MovJExt(").append(movJExtEntity.getDistanceOrAngle());
        if (movJExtEntity.getAccE() != null) {
            str.append(",SpeedE=").append(movJExtEntity.getSpeedE());
        }
        if (movJExtEntity.getAccE() != null) {
            str.append(",AccE=").append(movJExtEntity.getAccE());
        }
        str.append(",Sync=").append(movJExtEntity.getSync()).append(")");
        return sendCmdToRobot(str.toString(), 5000);
    }

    public RobotReply arc(ArcEntity arcEntity) {
        StringBuilder str = new StringBuilder();
        str.append("Arc(")
                .append(arcEntity.getX1())
                .append(",")
                .append(arcEntity.getY1())
                .append(",")
                .append(arcEntity.getZ1())
                .append(",")
                .append(arcEntity.getR1())
                .append(",")
                .append(arcEntity.getX2())
                .append(",")
                .append(arcEntity.getY2())
                .append(",")
                .append(arcEntity.getZ2())
                .append(",")
                .append(arcEntity.getR2());
        if (arcEntity.getUser() != null) {
            str.append(",User=").append(arcEntity.getUser());
        }
        if (arcEntity.getTool() != null) {
            str.append(",Tool=").append(arcEntity.getTool());
        }
        if (arcEntity.getSpeedL() != null) {
            str.append(",SpeedJ=").append(arcEntity.getSpeedL());
        }
        if (arcEntity.getAccL() != null) {
            str.append(",AccJ=").append(arcEntity.getAccL());
        }
        str.append(")");
        return sendCmdToRobot(str.toString(), 5000);

    }

    public RobotReply circle(List<MoveEntity> moveEntityList) {
        StringBuilder cmd = new StringBuilder();
        cmd.append("Circle(1,");
        StringBuilder coordinateToString = new StringBuilder();
        for (int i = 0; i < moveEntityList.size(); i++) {
            MoveEntity moveEntity = moveEntityList.get(i);
            if (i == 0) {
                coordinateToString.append("{").append(moveEntity.getX()).append(",")
                        .append(moveEntity.getY()).append(",").append(moveEntity.getZ())
                        .append(",").append(moveEntity.getR()).append("}");
            } else {
                coordinateToString.append(",{").append(moveEntity.getX()).append(",")
                        .append(moveEntity.getY()).append(",").append(moveEntity.getZ())
                        .append(",").append(moveEntity.getR()).append("}");
            }
        }
        cmd.append(coordinateToString);
        cmd.append(")");
        return sendCmdToRobot(cmd.toString(), 5000);
    }

    public RobotReply sync() {
//        return RobotReply.ok();
        return sendCmdToRobot("Sync()", 10000); //持续阻塞10s
    }

    public RobotReply syncAll() {
        return sendCmdToRobot("SyncAll()", 0); //持续阻塞
    }

    public static void main(String[] args) {
        DobotMove dobotMove = new DobotMove();
        dobotMove.connect(DeviceParams.defaultMG400IP);
        dobotMove.sync();
    }
}
