package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLCANFDExtFrameData extends Structure {
    public int mBTRExtArb; // 包含TSEG1-1, TSEG2-1, <PERSON><PERSON><PERSON>, Quartz Frequency
    public int mBTRExtData;

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mBTRExtArb", "mBTRExtData");
    }
    
}
