package com.desaysv.workserver.devices.robot.interfaces;

import com.alibaba.fastjson2.JSONArray;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.robot.type.mg400.entity.LongTouchRobotCoordinate;
import com.desaysv.workserver.screen.events.ArcTouchEvent;
import com.desaysv.workserver.screen.events.ClickEvent;
import com.desaysv.workserver.screen.events.LineTouchEvent;

import java.util.List;

public interface IRobotWithTouchPoint {

    OperationResult autoScreenCalibration(String serialAliasName);

    OperationResult touchAndCheckTouchPoint(String coordinatesName);

    OperationResult swipeAndCheckTouchPoint(JSONArray swipeNames);

    OperationResult touchAndCheckTouchPoint(ClickEvent clickEvent);

    OperationResult swipeAndCheckTouchPoint(LineTouchEvent lineTouchEvent);

    OperationResult longTouchAndCheckTouchPoint(LongTouchRobotCoordinate longTouchRobotCoordinate);

    OperationResult randomAndCheckTouchPoint(List<String> robotCoordinatesRandomList);

    OperationResult arcAndCheckTouchPoint(ArcTouchEvent arcTouchEvent);

}
