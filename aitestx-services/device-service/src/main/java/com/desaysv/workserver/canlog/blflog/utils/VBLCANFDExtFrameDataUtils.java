package com.desaysv.workserver.canlog.blflog.utils;

public class VBLCANFDExtFrameDataUtils {
    // 位掩码定义
    private static final int TSEG1_MINUS_1_MASK = 0xFF; // Bit 0 - 7
    private static final int TSEG2_MINUS_1_MASK = 0xFF00; // Bit 8 - 15
    private static final int PRESCALER_MASK = 0xFFF0000; // Bit 16 - 27
    private static final int QUARTZ_FREQUENCY_MASK = 0xF0000000; // Bit 28 - 31

    // 提取TSEG1-1的值
    public static int getTSEG1Minus1(int value) {
        return (value & TSEG1_MINUS_1_MASK);
    }

    // 提取TSEG2-1的值
    public static int getTSEG2Minus1(int value) {
        return (value & TSEG2_MINUS_1_MASK) >>> 8;
    }

    // 提取Prescaler的值
    public static int getPrescaler(int value) {
        return (value & PRESCALER_MASK) >>> 16;
    }

    // 提取Quartz Frequency的值
    public static int getQuartzFrequency(int value) {
        return (value & QUARTZ_FREQUENCY_MASK) >>> 28;
    }
}
