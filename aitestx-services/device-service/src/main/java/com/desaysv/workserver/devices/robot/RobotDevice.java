package com.desaysv.workserver.devices.robot;

import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.robot.config.RobotConfig;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 机械臂设备
 */
@Getter
@Slf4j
public abstract class RobotDevice extends ConfigurableDevice<RobotConfig> {

    private final String deviceType = DeviceType.DEVICE_ROBOT;

    public static final List<String> IP_ADDRESS = new ArrayList<>(Collections.singletonList(DeviceParams.defaultMG400IP));

    public RobotDevice() {
        super();
    }

    public RobotDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public Class<RobotConfig> getDeviceConfigClass() {
        return RobotConfig.class;
    }

    //用以反射确定具体泛型
    @Override
    public void updateConfig(RobotConfig deviceConfig) {
        super.updateConfig(deviceConfig);
    }
}
