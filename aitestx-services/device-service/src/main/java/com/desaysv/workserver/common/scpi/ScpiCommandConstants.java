package com.desaysv.workserver.common.scpi;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-11 11:22
 * @description :
 * @modified By :
 * @since : 2022-7-11
 */
public class ScpiCommandConstants {
    public static final String REMOTE = "SYSTem:REMote"; //设置电源为远程操作状态
    public static final String LOCAL = "SYSTem:LOCal"; //设置电源为本地操作状态

    public static final String OUTPUT_STATUS = "OUTP?"; //获取电源的当前通道状态
    public static final String OUTPUT_ON = "OUTP ON"; //设置电源的当前通道为开启状态
    public static final String OUTPUT_OFF = "OUTP OFF"; //设置电源的当前通道为关闭状态
    public static final String CHANNEL_OUTPUT_ON = "CHANnel:OUTPut ON"; //设置电源的当前通道为打开状态

    public static final String CHANNEL_OUTPUT_OFF = "CHANnel:OUTPut OFF"; //设置电源的当前通道为关闭状态
    public static final String SET_VOLTAGE = "VOLTage"; //设置电源的当前通道的输出电压值
    public static final String SET_VOLTAGE_MAX = "VOLTage MAX"; //设置电源的当前通道的输出电压值最大
    public static final String SET_CURRENT = "CURRent";//设置电源的当前通道的输出电流值
    public static final String MEASURE_VOLTAGE = "MEASure:VOLTage?"; //读取实际输出电压
    public static final String MEASURE_CURRENT = "MEASure:CURRent?"; //读取实际输出电流
    public static final String MEASURE_CURRENT_MPS3610H = "MEAS:CURR?";//MPS3610H读取电流命令
    public static final String MEASURE_VOLTAGE_MPS3610H = "MEAS:VOLT?";//MPS3610H读取电压命令

    public static final String SET_CHANNEL = "INSTrument ";
    public static final String SET_VOLTAGE_DC = "SOUR:VOLT:DC"; //设置电源的当前通道的输出电压值
    public static final String MEASURE_VOLTAGE_DC_VALUE = "SOUR:VOLT:DC?"; //读取实际输出电压
    public static final String SET_CURRENT_POS = "SOUR:CURR:POS";//设置源电流给定值
    public static final String MEASURE_CURRENT_POS = "SOUR:CURR:POS?"; //读取源电流给定值
    public static final String MEASURE_VOLTAGE_EFFECTIVE_VALUE = "MEASure:VOLTage?"; //读取电压有效值
    public static final String MEASURE_CURRENT_EFFECTIVE_VALUE = "MEASure:CURRent?"; //读取电流有效值
    public static final String SET_LOAD_CURRENT = "SOUR:CURR:NEG";   //设置载电流给定值
    public static final String MEASURE_LOAD_CURRENT = "SOUR:CURR:NEG";  //读取载电流给定值


    //Keysight
    public static final String CONFIG_VOLTAGE_AC = "CONFIGURE:VOLTage:AC";
    public static final String CONFIG_VOLTAGE_DC = "CONFIGURE:VOLTage:DC";
    public static final String CONFIG_CURRENT_AC = "CONFIGURE:CURRent:AC";
    public static final String CONFIG_CURRENT_DC = "CONFIGURE:CURRent:DC";

    public static final String READ = "READ?";

    public static final String MEASURE_VOLTAGE_DC = "MEASure:VOLTage:DC? AUTO";
    public static final String MEASURE_VOLTAGE_AC = "MEASure:VOLTage:AC? AUTO";

    public static final String MEASURE_CURRENT_DC = "MEASure:CURRent:DC? AUTO";
    public static final String MEASURE_CURRENT_AC = "MEASure:CURRent:AC? AUTO";

    public static final String CRLF = "\r\n";
    public static final String LF = "\n";
    public static final String COMPATIBLE_LINE_ENDING = "\n\r\n";// 兼容"\n" + "\r\n"

    public static final String END = "\n";

    public static String selectChannel(int channel, boolean channelNumber) {
        String channelCommand;
        switch (channel) {
            case 2:
                channelCommand = channelNumber ? "CH2" : "SECOnd";
                break;
            case 3:
                channelCommand = channelNumber ? "CH3" : "THIrd";
                break;
            case 1:
            default:
                channelCommand = channelNumber ? "CH1" : "FIRst";
        }
        return SET_CHANNEL + channelCommand;
    }

}
