package com.desaysv.workserver.devices.robot;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.robot.base.CoordinateSystem;
import com.desaysv.workserver.devices.robot.base.DefaultDobotDevice;
import com.desaysv.workserver.devices.robot.base.RobotPose;
import com.desaysv.workserver.devices.robot.config.RobotConfig;
import com.desaysv.workserver.devices.robot.interfaces.IDobotMagician;
import com.desaysv.workserver.devices.robot.type.magician.DobotApi;
import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Random;

@Slf4j
public class DobotMagician extends DefaultDobotDevice implements IDobotMagician {

    @Getter
    @JSONField(serialize = false)
    private DobotApi dobotApi;

    private boolean connected;

    public DobotMagician() {
        this(new DeviceOperationParameter());
    }

    public DobotMagician(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        if (!isSimulated()) {
            dobotApi = new DobotApi();
        }
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Robot.DOBOT_MAGICIAN;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (connected) {
            throw new DeviceOpenRepeatException();
        }
        connected = dobotApi.connect(getDevicePortName());
        return connected;
    }

    @Override
    public void returnHome() {
        dobotApi.returnHome();
    }

    @Override
    public RobotPose fetchPose(CoordinateSystem coordinateSystem) {
        return dobotApi.getPose();
    }

    @Override
    public RobotReply air(boolean pressure) {
        return null;
    }

    //FIXME：防止客户端不断新建线程来调用该函数（验证）
    @Override
    public RobotPose fetchFeedbackData() {
        if (isSimulated()) {
            RobotPose robotPose = new RobotPose();
            RobotConfig robotConfig = getDeviceConfig();
            Random random = new Random();
            robotPose.setX(random.nextDouble() * 10 + 100);
            robotPose.setY(random.nextDouble() * 20 + 10);
            robotPose.setZ(random.nextDouble() * 30 + 10);
            robotPose.setR(random.nextDouble());
            robotPose.setSlideRail((double) 0);
            robotPose.setUser(robotConfig.getParameters().getUserCoordinate());
            return robotPose;
        }
//        FeedbackData feedbackData = new FeedbackData();
        //        System.out.println("robotPose:" + robotPose);
//        feedbackData.setX(robotPose.getX());
//        feedbackData.setY(robotPose.getY());
//        feedbackData.setZ(robotPose.getZ());
//        feedbackData.setR(robotPose.getR());
        return fetchPose(null);
    }

    @Override
    public RobotReply moveJog(String command) {
        log.info("Dobot魔术师 {}:点动:{}", getDeviceName(), command);
        dobotApi.moveJog(command);
        return RobotReply.ok();
    }

    @Override
    public RobotReply stopMoveJog() {
        dobotApi.stopMoveJog();
        return RobotReply.ok();
    }

    @Override
    public RobotReply moveLine(MoveEntity moveEntity, boolean moveDurationCalc) {
        log.info("Dobot魔术师 {}:直线移动到坐标:{}", getDeviceName(), moveEntity);
        dobotApi.moveLine(moveEntity);
        return RobotReply.ok();
    }

    @Override
    public RobotReply moveLineQuickly(List<MoveEntity> moveEntityList) {
        return null;
    }

    @Override
    public RobotReply moveJoint(MoveEntity moveEntity) {
        log.info("Dobot魔术师 {}:关节运动到{}", getDeviceName(), moveEntity);
        dobotApi.moveJoint(moveEntity);
        return RobotReply.ok();
    }

    @Override
    public RobotReply circle(List<MoveEntity> moveEntityList) {
        return null;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        if (!isConnected()) {
            return true;
        }
        //TODO:使用异步方式断开连接
        super.close();
        log.info("Dobot魔术师 {}断开连接", getDeviceName());
        dobotApi.disconnect();
        return true;
    }

    public static void main(String[] args) {
        DobotMagician dobotMagician = new DobotMagician(null);
        try {
            dobotMagician.open();
            dobotMagician.getDobotApi().jump(null);
        } catch (DeviceOpenException | DeviceOpenRepeatException e) {
            log.error(e.getMessage(), e);
        }
    }
}
