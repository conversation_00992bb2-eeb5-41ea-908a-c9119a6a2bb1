package com.desaysv.workserver.stream.rtsp;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.videocapture.VideoCaptureDevice;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.stream.VideoCaptureStreamProducer;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;

import javax.imageio.ImageIO;
import java.io.File;

@Slf4j
public class RtspVideoCaptureStreamProducer extends VideoCaptureStreamProducer {
    private final String deviceName;

    public RtspVideoCaptureStreamProducer(int w, int h, String deviceName) {
        super(w, h);
        this.deviceName = deviceName;
    }

    @Override
    protected void startStream(String deviceModel, String serialNumber) {
        try (Frame frame = grabFrame()) {
            setWidth(frame.imageWidth);
            setHeight(frame.imageHeight);
        } catch (FrameGrabberException e) {
            log.warn(e.getMessage(), e);
        }
    }

    @Override
    public String pushStream(String deviceModel, String deviceName) throws Exception {
        return deviceName;
    }

    @Override
    public Frame grabFrame() throws FrameGrabberException {
        DeviceRegisterManager deviceManager = SpringContextHolder.getBean(DeviceRegisterManager.class);
        VideoCaptureDevice videoCaptureDevice = (VideoCaptureDevice) deviceManager.getDevice(deviceName);
        synchronized (this) {
            log.info("采集VideoCapture图像");
            try {
                String path = videoCaptureDevice.videoCaptureScreenShoot();
                Frame frame = Java2DFrameUtils.toFrame(ImageIO.read(new File(path)));
                if (frame != null && getWidth() == 0 && getHeight() == 0) {
                    setWidth(frame.imageWidth);
                    setHeight(frame.imageHeight);
                }
                return frame;
            } catch (Exception e) {
                log.warn("采集VideoCapture图像出错:", e);
                throw new FrameGrabberException(e);
            }
        }
    }

    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {

    }

    @Override
    public RectSize getDefaultSize() {
        return null;
    }

    @Override
    public void outputDynamicFrame(boolean isOutput) {

    }

    @Override
    public void setExposureAuto(CameraSettings cameraSettings) {

    }

    @Override
    public void setReverseX(boolean reverseX) {
    }

    @Override
    public void setReverseY(boolean reverseY) {

    }

    @Override
    public void grabberSwitch(boolean status) {

    }

    @Override
    public CameraSettings getCameraSettings() {
        return null;
    }

    @Override
    public double getGrabberFrameRate() {
        return 0;
    }

}
