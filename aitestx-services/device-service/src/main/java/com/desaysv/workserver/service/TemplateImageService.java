package com.desaysv.workserver.service;

import com.desaysv.workserver.bo.TemplateImageBo;
import com.desaysv.workserver.bo.TemplateRoiInfo;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.model.TemplatePicture;
import org.bytedeco.javacv.Frame;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 19:12
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
public interface TemplateImageService {

    TemplateImageBo saveTemplate(TemplateImageBo templatePicture) throws IOException;

    TemplateRoiInfo saveTemplateROI(TemplateRoiInfo templateRoiInfo) throws IOException;

    TemplatePicture getTemplate(String templateName, String deviceUniqueCode, String projectName);

    Frame getTemplateFrame(Device device, String templateName, String projectName) throws  FileNotFoundException;

    File getTemplateFile(Device device, String templateName, String projectName) ;

    List<TemplatePicture> getAllTemplates();

}
