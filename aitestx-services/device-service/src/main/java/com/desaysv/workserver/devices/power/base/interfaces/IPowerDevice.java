package com.desaysv.workserver.devices.power.base.interfaces;

import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.action_sequence.IControllableAction;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.IFetchDevice;
import com.desaysv.workserver.devices.power.base.CriticalVoltage;
import com.desaysv.workserver.devices.power.base.StepVoltage;
import com.desaysv.workserver.devices.power.kikusui.*;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import xyz.froud.jvisa.JVisaException;

public interface IPowerDevice extends IFetchDevice, IControllableAction {
    Logger log = LogManager.getLogger(IPowerDevice.class.getSimpleName());
    String CURRENT = "current";
    String VOLTAGE = "voltage";

    /**
     * 获取输出状态
     *
     * @param
     * @return 输出状态
     */
    boolean fetchOutput();


    /**
     * 打开输出
     *
     * @param deviceChannel 通道
     * @return 是否打开输出成功
     */
    boolean outputOn(Integer deviceChannel);


    /**
     * 关闭输出
     *
     * @param deviceChannel 通道
     * @return 是否关闭输出成功
     */
    boolean outputOff(Integer deviceChannel);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).OPTIONAL_CHANNEL_ON_OFF"})
    default void output(Integer deviceChannel, String command) {
        output(deviceChannel, command, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANNEL_ON_OFF"})
    default void output(Integer deviceChannel, String command, String physicalMeaning) {
        boolean isOpen = command.equalsIgnoreCase("ON");
//        log.info("电源通道{}{}{}", deviceChannel,
//                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
//                isOpen ? "打开" : "关闭");
        if (isOpen) {
            outputOn(deviceChannel);
        } else {
            outputOff(deviceChannel);
        }
    }

    /**
     * 设置电压
     *
     * @param voltage 电压
     * @return 是否设置电压成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).CHANGE_VOLTAGE"})
    boolean setVoltage(Integer deviceChannel, float voltage);

    /**
     * 设置电压（自动计算）
     *
     * @param deviceChannel
     * @param voltageWithUnit
     * @return
     */
    boolean setVoltageWithUnit(Integer deviceChannel, String voltageWithUnit) throws OperationFailNotification;

    /**
     * 设置随机电压
     *
     * @param lowerVoltage 电压下限值
     * @param upperVoltage 电压上限值
     * @return 是否设置随机电压成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).RANDOM_VOLTAGE"})
    default boolean setRandomVoltage(Integer deviceChannel, float lowerVoltage, float upperVoltage) {
        float voltage = (float) RandomUtil.randomDouble(lowerVoltage, upperVoltage);
        log.info("电源通道{}输出随机电压:{}~{}V", deviceChannel, lowerVoltage, upperVoltage);
        boolean isPass = setVoltage(deviceChannel, voltage);
        ActionSequencesLoggerUtil.info(String.format("电源通道%d输出随机电压:%s~%sV--%s", deviceChannel, lowerVoltage, upperVoltage, isPass ? "成功" : "失败"));
        return isPass;
    }

    default boolean setStepVoltage(Integer deviceChannel, StepVoltage stepVoltage) {
        return setStepVoltage(deviceChannel, stepVoltage.getStartVoltage(), stepVoltage.getEndVoltage(), stepVoltage.getStepVoltage(), stepVoltage.getStepInterval() + "s");
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).STEP_VOLTAGE"})
    default boolean setStepVoltage(Integer deviceChannel, float startVoltage, float endVoltage, float stepVoltage, String stepInterval) {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(stepInterval);
        long milliseconds = (long) (seconds * 1000);
        log.info("电源通道{}输出步进电压:{}~{}V, 步长:{}V, 间隔:{}s", deviceChannel, startVoltage, endVoltage, stepVoltage, seconds);
        if (startVoltage <= endVoltage) {
            for (float voltage = startVoltage; voltage <= endVoltage; voltage += stepVoltage) {
                while (isPause()) {
                    log.info("电压步进已暂停");
                    try {
                        synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                            ActionSequenceLock.getInstance().getPauseLock().wait();
                        }
                    } catch (InterruptedException e) {
                        break;
                    }
                }
                setVoltage(deviceChannel, voltage);
                try {
                    Thread.sleep(milliseconds);
                } catch (InterruptedException e) {
                    break;
                }
            }
        } else {
            for (float voltage = startVoltage; voltage >= endVoltage; voltage -= stepVoltage) {
                while (isPause()) {
                    log.info("电压步进已暂停");
                    try {
                        synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                            ActionSequenceLock.getInstance().getPauseLock().wait();
                        }
                    } catch (InterruptedException e) {
                        break;
                    }
                }
                setVoltage(deviceChannel, voltage);
                try {
                    Thread.sleep(milliseconds);
                } catch (InterruptedException e) {
                    break;
                }
            }
        }
        setVoltage(deviceChannel, endVoltage);
        return true;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).CONSTANT_VOLTAGE"})
    default boolean setConstantVoltage(Integer deviceChannel, float voltage, String interval) {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        log.info("电源通道{}输出恒定电压:{}V, 间隔:{}s", deviceChannel, voltage, seconds);
        boolean ok = setVoltage(deviceChannel, voltage);
        BaseRegexRule.sleepSeconds(seconds.longValue());
        return ok;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).RAMP_VOLTAGE"})
    default boolean setRampVoltage(Integer deviceChannel, float endVoltage, String interval) throws DeviceReadException {
        return setRampVoltage(deviceChannel, fetchVoltage(deviceChannel), endVoltage, interval);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).RAMP_VOLTAGE_START_TO_END"})
    default boolean setRampVoltage(Integer deviceChannel, float startVoltage, float endVoltage, String interval) {
        float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);  //不带单位，代表ms
        log.info("电源通道{}输出斜坡电压:{}V, 间隔:{}s", deviceChannel, endVoltage, seconds);
        boolean ok = false;
        float stepInterval = 0.1f; //最小间隔时间，秒
        float segment = seconds / stepInterval; //间隔数量
        float stepVoltage = Math.abs((endVoltage - startVoltage) / segment);
        long startMills = System.currentTimeMillis();
        if (startVoltage <= endVoltage) {
            for (float voltage = startVoltage; voltage <= endVoltage; voltage += stepVoltage) {
                ok = setVoltage(deviceChannel, voltage);
                try {
                    Thread.sleep((long) (stepInterval * 1000));
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } else {
            for (float voltage = startVoltage; voltage >= endVoltage; voltage -= stepVoltage) {
                ok = setVoltage(deviceChannel, voltage);
                try {
                    Thread.sleep((long) (stepInterval * 1000));
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        setVoltage(deviceChannel, endVoltage);
        log.info("斜坡电压耗时:{}s", (System.currentTimeMillis() - startMills) / 1000.0f);
        return ok;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_WAVE_FORM"})
    default boolean executeWaveForm(Integer memoryPosition) throws DeviceSendException {
        if (this instanceof IKikusui) {
            return ((IKikusui) this).loadWavePulse(memoryPosition);
        }
        return false;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_START_RESET_FORM"})
    default boolean executeStartForm(Integer deviceChannel, double fromVoltage, double toVoltage, double fromTime, double toTime, Integer fromCycle, Integer toCycle) throws DeviceSendException, JVisaException {
        if (this instanceof IKikusui) {
            StartPulse startPulse = new StartPulse();
            startPulse.setMemoryPosition(1);
            startPulse.setConstantRandom(false);
            startPulse.setFromVoltage(fromVoltage);
            startPulse.setToVoltage(toVoltage);
            startPulse.setFromTime(fromTime / 1000);
            startPulse.setToTime(toTime / 1000);
            startPulse.setFromCycle(fromCycle);
            startPulse.setToCycle(toCycle);
            return ((IKikusui) this).writeStartWavePulse(startPulse);
        } else {
            boolean result = true;
            int maxAllowedCycles = 1000;
            long maxSleepMs = 30000;

            if (fromCycle < 0 || toCycle < 0) {
                throw new IllegalArgumentException("无效的循环范围：[" + fromCycle + "," + toCycle + ")");
            }
            int randomCycle = Math.min(RandomUtil.randomInt(fromCycle, toCycle), maxAllowedCycles);
            for (int i = 0; i < randomCycle && !Thread.currentThread().isInterrupted(); i++) {
                try {
                    result &= setRandomVoltage(deviceChannel, (float) fromVoltage, (float) toVoltage);
                    long sleepTime = (long) Math.min(
                            RandomUtil.randomDouble(fromTime, toTime),
                            maxSleepMs
                    );
                    Thread.sleep(sleepTime);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
            return result;
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_TIME_RESET_FORM"})
    default boolean executeTimeForm(Integer deviceChannel, double highVoltage, double lowVoltage, double highPulseDuration, double lowPulseDuration) throws DeviceSendException, JVisaException {
        if (this instanceof IKikusui) {
            TimeLevelResetPulse timeLevelResetPulse = new TimeLevelResetPulse();
            timeLevelResetPulse.setMemoryPosition(1);
            timeLevelResetPulse.setHighVoltage(highVoltage);
            timeLevelResetPulse.setLowVoltage(lowVoltage);
            timeLevelResetPulse.setHighPulseDuration(highPulseDuration / 1000);
            timeLevelResetPulse.setLowPulseDuration(lowPulseDuration / 1000);
            return ((IKikusui) this).writeTimeLevelResetPulse(timeLevelResetPulse);
        } else {
            boolean result = false;
            final int MAX_CYCLES = 10000;
            final long MAX_SLEEP_MS = 30000L;
            for (int i = 100; i < lowPulseDuration && i < MAX_CYCLES; i++) { // 双条件控制
                try {
                    setVoltage(deviceChannel, (float) highVoltage);
                    Thread.sleep(Math.min((long) highPulseDuration, MAX_SLEEP_MS)); // 限制休眠时间
                    boolean currentResult = setVoltage(deviceChannel, (float) lowVoltage);
                    result = currentResult;
                    Thread.sleep(Math.min(i + 1, MAX_SLEEP_MS)); // 限制递增休眠时间
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
            return result;
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_VOLTAGE_RESET_FORM"})
    default boolean executeVoltageForm(Integer deviceChannel, double highVoltage, double lowVoltage, double lowPulseDuration, double highPulseDuration) throws DeviceSendException, JVisaException {
        if (this instanceof IKikusui) {
            VoltageLevelResetPulse voltageLevelResetPulse = new VoltageLevelResetPulse();
            voltageLevelResetPulse.setMemoryPosition(1);
            voltageLevelResetPulse.setHighVoltage(highVoltage);
            voltageLevelResetPulse.setLowVoltage(lowVoltage);
            voltageLevelResetPulse.setLowPulseDuration(lowPulseDuration / 1000);
            voltageLevelResetPulse.setHighPulseDuration(highPulseDuration / 1000);
            return ((IKikusui) this).writeVoltageLevelResetPulse(voltageLevelResetPulse);
        } else {
            boolean result = false;
            double delta = 0.1;
            int loops = (int) Math.round((highVoltage - lowVoltage) / delta);
            for (int i = 0; i < loops; i++) {
                try {
                    lowVoltage = highVoltage - delta * (i + 1);
                    setVoltage(deviceChannel, (float) highVoltage);
                    Thread.sleep((long) highPulseDuration);
                    result = setVoltage(deviceChannel, (float) lowVoltage);
                    Thread.sleep((long) lowPulseDuration);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
            return result;
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_ARBITRARY_RESET_FORM"})
    default boolean executeArbitraryForm(Integer deviceChannel, double startVoltage, double endVoltage, double constantVoltage, double changeableVoltageDuration, double constantVoltageDuration) throws DeviceSendException, JVisaException {
        if (this instanceof IKikusui) {
            ArbitraryVoltageResetPulse arbitraryVoltageResetPulse = new ArbitraryVoltageResetPulse();
            arbitraryVoltageResetPulse.setMemoryPosition(1);
            arbitraryVoltageResetPulse.setStartVoltage(startVoltage);
            arbitraryVoltageResetPulse.setEndVoltage(endVoltage);
            arbitraryVoltageResetPulse.setChangeableVoltageDuration(changeableVoltageDuration / 1000);
            arbitraryVoltageResetPulse.setConstantVoltage(constantVoltage);
            arbitraryVoltageResetPulse.setConstantVoltageDuration(constantVoltageDuration / 1000);
            return ((IKikusui) this).writeArbitraryVoltageResetPulse(arbitraryVoltageResetPulse);
        } else {
            boolean result = false;
            double delta = 0.1;
            int loops = Math.abs((int) Math.round((endVoltage - startVoltage) / delta));
            double nextChangeableVoltage = startVoltage;
            for (int i = 0; i <= loops; i++) {
                try {
                    setVoltage(deviceChannel, (float) constantVoltage);
                    Thread.sleep((long) constantVoltageDuration);
                    result = setVoltage(deviceChannel, (float) nextChangeableVoltage);
                    Thread.sleep((long) changeableVoltageDuration);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
                if (startVoltage >= endVoltage) {
                    nextChangeableVoltage -= delta;
                } else {
                    nextChangeableVoltage += delta;
                }
            }
            return result;
        }

    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).EXECUTE_INTERRUPT_RESET_FORM"})
    default boolean executeInterruptForm(Integer deviceChannel, double highVoltage, double highPulseDuration) throws DeviceSendException, JVisaException {
        if (this instanceof IKikusui) {
            PowerTemporarilyInterruptPulse powerTemporarilyInterruptPulse = new PowerTemporarilyInterruptPulse();
            powerTemporarilyInterruptPulse.setMemoryPosition(1);
            powerTemporarilyInterruptPulse.setHighVoltage(highVoltage);
            powerTemporarilyInterruptPulse.setHighPulseDuration(highPulseDuration / 1000);
            return ((IKikusui) this).writePowerTemporarilyInterruptPulse(powerTemporarilyInterruptPulse);
        } else {
            boolean result = false;
            double endVoltage = 0;
            double rampInterval = 100 / 1000.0; //100ms
            setVoltage(deviceChannel, (float) highVoltage);
            try {
                Thread.sleep((long) highPulseDuration);
                for (int j = 0; j < 4; j++) {
                    int time = 9;
                    double lowPulseDuration = 0.1 / 1000;  //0.1ms
                    if (j == 1) lowPulseDuration = 1f / 1000; //1ms
                    if (j == 2) lowPulseDuration = 10f / 1000; //10ms
                    if (j == 3) {
                        lowPulseDuration = 100f / 1000; //100ms
                        time = 20;
                    }
                    for (int i = 0; i < time; i++) {
                        setRampVoltage(deviceChannel, (float) highVoltage, (float) endVoltage, String.valueOf(rampInterval));
                        setVoltage(deviceChannel, (float) endVoltage);
                        Thread.sleep((long) (lowPulseDuration * (i + 1)));
                        setRampVoltage(deviceChannel, (float) endVoltage, (float) highVoltage, String.valueOf(rampInterval));
                        result = setVoltage(deviceChannel, (float) highVoltage);
                        Thread.sleep((long) highPulseDuration * (i + 1));
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
            return result;
        }
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_CURRENT"})
    default ActualExpectedResult compareCurrent(Integer deviceChannel, float lowerCurrent, float upperCurrent) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("电源通道{}判断电流:{}~{}A", deviceChannel, lowerCurrent, upperCurrent);
        float value = fetchCurrent(deviceChannel);
        log.info("电源通道{}当前电流:{}A", deviceChannel, value);
        boolean pass = value >= lowerCurrent && value <= upperCurrent;
        String output = String.format("电源通道%d电流:%fA, 检测%s", deviceChannel, value, pass ? "通过" : "失败");
        log.info(output);
        ActionSequencesLoggerUtil.info(output);
        actualExpectedResult.put(CURRENT, pass, value);
        return actualExpectedResult;
    }

    //COMPARE_VOLTAGE
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_VOLTAGE"})
    default ActualExpectedResult compareVoltage(Integer deviceChannel, float lowerVoltage, float upperVoltage) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("电源通道{}判断电压:{}~{}V", deviceChannel, lowerVoltage, upperVoltage);
        float value = fetchVoltage(deviceChannel);
        log.info("电源通道{}当前电压:{}V", deviceChannel, value);
        boolean pass = value >= lowerVoltage && value <= upperVoltage;
        String output = String.format("电源通道%d电压:%fV, 检测%s", deviceChannel, value, pass ? "通过" : "失败");
        ActionSequencesLoggerUtil.info(output);
        actualExpectedResult.put(VOLTAGE, pass, value);
        return actualExpectedResult;
    }

    /**
     * 设置电流
     *
     * @param current 电流
     * @return 是否设置电流成功
     */
//    boolean setCurrent(Integer deviceChannel, float current);
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.PowerRegexRule).CHANGE_CURRENT"})
    boolean setCurrent(Integer deviceChannel, float current);

    /**
     * 搜索最小临界电压
     *
     * @param criticalVoltage 临界电压范围
     * @return 最小临界电压
     */
    CriticalVoltage searchMinimumCriticalVoltage(Integer deviceChannel, CriticalVoltage criticalVoltage) throws DeviceReadException;


}
