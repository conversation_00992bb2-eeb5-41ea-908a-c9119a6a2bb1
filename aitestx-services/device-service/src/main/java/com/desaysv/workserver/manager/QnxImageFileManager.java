package com.desaysv.workserver.manager;

import lombok.Getter;

import java.io.File;

public class QnxImageFileManager extends ImageFileManager {
    @Override
    protected String getImageFolderName() {
        return "qnx";
    }
    @Getter
    private File screenshotFile;


    public QnxImageFileManager(String projectName) {
        super(projectName);
    }

    public QnxImageFileManager(String projectName, String dynamicFolderName) {
        super(projectName, dynamicFolderName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        super.initSubPaths(dynamicFolderName);
        screenshotFile = createFolder(imageConstants.basePath, "screenshot");
    }
}
