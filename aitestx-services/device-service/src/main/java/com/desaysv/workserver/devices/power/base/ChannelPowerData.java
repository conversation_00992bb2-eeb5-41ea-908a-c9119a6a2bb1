package com.desaysv.workserver.devices.power.base;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-11 14:27
 * @description :
 * @modified By :
 * @since : 2022-7-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelPowerData extends PowerData {

    private Float dataCH1;

    private Float dataCh2;

    private Float dataCh3;

    @Override
    public void clear() {
        dataCH1 = null;
        dataCh2 = null;
        dataCh3 = null;
    }
}
