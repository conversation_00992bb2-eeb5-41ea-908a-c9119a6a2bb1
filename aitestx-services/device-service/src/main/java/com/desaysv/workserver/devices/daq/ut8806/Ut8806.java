package com.desaysv.workserver.devices.daq.ut8806;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.base.DaqDevice;
import com.desaysv.workserver.devices.daq.base.IDaqCompare;
import com.desaysv.workserver.devices.daq.base.IVoltCurrDaqAcquireHandler;
import com.desaysv.workserver.entity.CurrentDirection;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.monitor.MeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

@Slf4j
public class Ut8806 extends DaqDevice implements MeasurementComparator, IVoltCurrDaqAcquireHandler, IDaqCompare {

    public JVisaInstrument jVisaInstrument;
    private static final int TIMEOUT = 20000; // 20秒超时
    private static final int COMMAND_DELAY = 500;// 500ms延迟
    private JVisaResourceManager rm;

    @Override
    public String getDeviceModel() {
        return DeviceModel.Daq.UT8806;
    }

    public Ut8806() {
        this(new DeviceOperationParameter());
    }

    public Ut8806(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            rm = new JVisaResourceManager();
        } catch (JVisaException e) {
            log.warn(e.getMessage(), e);
        }
    }

    public boolean init() throws JVisaException {
        try {
            jVisaInstrument.setTimeout(TIMEOUT);
            // 初始化设备
            jVisaInstrument.clear();
            Thread.sleep(COMMAND_DELAY);
        } catch (JVisaException e) {
            log.error("连接设备失败: {}", String.valueOf(e));
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("设备初始化被中断");
            return false;
        }
        return true;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (rm != null) {
            try {
                jVisaInstrument = rm.openInstrument(getDeviceName());
                init();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }

        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        if (jVisaInstrument != null) {
            try {
                jVisaInstrument.close();
            } catch (JVisaException e) {
                log.error("关闭设备失败：{}", e.getMessage());
            }
        }
        return super.close();
    }

    @Override
//    @JsonIgnore
    public float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        float current;
        if (currentDirection != null && currentDirection.equals(CurrentDirection.DC.name())) {
            current = getCurrentDC();
            log.info("{}获取直流电流:{}A", getDeviceName(), current);
        } else {
            current = getCurrentAC();
            log.info("{}获取交流电流:{}A", getDeviceName(), current);
        }
        return current;
    }

    @Override
    @JSONField(serialize = false)
    public float getCurrentDC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return (float) measureCurrentDCValue();
    }

    @Override
    @JSONField(serialize = false)
    public float getCurrentAC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return (float) measureCurrentACValue();
    }

    @Override
    public float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        float voltage;
        if (currentDirection != null && currentDirection.equals(CurrentDirection.DC.name())) {
            voltage = getVoltageDC();
            log.info("{}获取直流电压:{}V", getDeviceName(), voltage);
        } else {
            voltage = getVoltageAC();
            log.info("{}获取交流电压:{}V", getDeviceName(), voltage);
        }
        return voltage;
    }

    @Override
    @JSONField(serialize = false)
    public float getVoltageDC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return (float) measureVoltageDCValue();
    }

    @Override
    @JSONField(serialize = false)
    public float getVoltageAC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return (float) measureVoltageACValue();
    }


    private float getRandomValue() {
        return (float) RandomUtil.randomDouble(1, 10);
    }


    protected void set(String command){
        try {
            jVisaInstrument.clear();
            jVisaInstrument.write(command);
            Thread.sleep(COMMAND_DELAY);
        } catch (JVisaException e) {
            log.error("设置失败：{}", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("命令执行被中断");
        }
    }

    protected String query(String command)  {
        try {
            jVisaInstrument.clear();
            Thread.sleep(COMMAND_DELAY);
            String response = jVisaInstrument.queryString(command);
            Thread.sleep(COMMAND_DELAY);
            return response;
        } catch (JVisaException e) {
            log.error("测量失败：{}", e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("测量被中断");
        }
        return null;
    }

    private double parseResponse(String response) {
        String[] values = response.trim().split("[\n\r ]+");
        String lastValue = values[values.length - 1];
        return Double.parseDouble(lastValue);
    }

    public double measureVoltageDCValue() {
        set("CONFigure:VOLTage:DC");
        String response = query("READ?");
        return parseResponse(response);
    }

    public double measureVoltageACValue() {
        set("CONFigure:VOLTage:AC");
        String response = query("READ?");
        return parseResponse(response);
    }

    public double measureCurrentDCValue() {
        set("CONFigure:CURRent:DC");
        String response = query("READ?");
        return parseResponse(response);
    }

    public double measureCurrentACValue()  {
        set("CONFigure:CURRent:AC");
        String response = query("READ?");
        return parseResponse(response);
    }

    public void disconnect() throws JVisaException {
        if (jVisaInstrument != null) {
            try {
                jVisaInstrument.clear();
                Thread.sleep(COMMAND_DELAY);
                jVisaInstrument.close();
            } catch (JVisaException e) {
                throw new JVisaException("断开连接失败：" + e.getMessage());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new JVisaException("断开连接被中断");
            }
        }
    }

    public static void main(String[] args) throws JVisaException{
        Ut8806 ut8806 = new Ut8806();
        try(JVisaResourceManager rm = new JVisaResourceManager()){
            ut8806.open();
            System.out.println("直流电压读数：" + ut8806.measureVoltageDCValue()+" V");
            System.out.println("交流电压读数：" + ut8806.measureVoltageACValue()+" V");
            System.out.println("直流电流读数：" + ut8806.measureCurrentDCValue() +" A");
            System.out.println("交流电流读数：" + ut8806.measureCurrentACValue() +" A");
            ut8806.disconnect();
        } catch (DeviceOpenRepeatException | DeviceOpenException e) {
            throw new RuntimeException(e);
        }
    }
}
