package com.desaysv.workserver.devices.power.kikusui;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.GlobalConfigHolder;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.nodes.NodeExecutor;
import com.desaysv.workserver.base.nodes.base.LoopBreakNotification;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.DeviceOperationMethod;
import com.desaysv.workserver.base.operation.method.MethodCollector;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.base.operation.method.PairedCustomizeFunction;
import com.desaysv.workserver.common.port.CheckPointInfo;
import com.desaysv.workserver.common.utils.CommandRunner;
import com.desaysv.workserver.common.utils.QueueCommandRunner;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.keysight.KeySight34461A;
import com.desaysv.workserver.devices.power.DefaultPower;
import com.desaysv.workserver.devices.power.base.interfaces.IKikusui;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.PowerSCPIProtocol;
import com.desaysv.workserver.devices.robot.base.DefaultDobotDevice;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 菊水电源
 * TODO: json文件加个波形时间
 * TODO：加入暂停停止命令
 * FIXME：设备自动唤醒时无法调用testStart
 * FIXME：电源Pausing状态无法写入脉冲
 */
@Slf4j
public class Kikusui extends DefaultPower implements IKikusui, TestProcessListener {

    interface ExecuteCallback {
        void doWritePulse();
    }

    private class WavePulseExecuteTemplate {

        public synchronized void process(WavePulse wavePulse, ExecuteCallback executeCallback) throws DeviceSendException {
            WavePulse wavePulseInMemory = wavePulseMap.get(wavePulse.getMemoryPosition());
            if (wavePulseInMemory != null && wavePulseInMemory.equals(wavePulse)) {
                //调用内存波形
                loadWavePulse(wavePulse.getMemoryPosition());
            } else {
                //写入波形
                executeCallback.doWritePulse();
                wavePulseMap.put(wavePulse.getMemoryPosition(), wavePulse);
            }
        }

    }

    private final Map<Integer, WavePulse> wavePulseMap;
    //TODO:移动到统一的线程池
    private final Executor stateCheckExecutor;
    private ExecutorService voltageCheckExecutor;
    private Thread executePairedCustomizeFunctionThread;
    private volatile boolean testCompleted;
    private boolean stateQuerying;
    private PairedCustomizeFunction triggerPairedCustomizeFunction;
    private final QueueCommandRunner queueCommandRunner;
    private final static String TRANSITION_STEP = "Step";
    private final static String TRANSITION_RAMP = "Ramp";
    private final static String AC_ON = "on";
    private final static String AC_OFF = "off";
    private final static String robotName = "Dobot_MG400#1";
    private final static String multimeterName = "KEYSIGHT_34461A#1";
    private ExecutorService executorService = null;
    private final AtomicBoolean monitoring = new AtomicBoolean(false);


    public Kikusui() {
        this(new DeviceOperationParameter());
    }

    public Kikusui(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        //设置通讯协议RS232还是USB
        setCommProtocol(CommProtocol.USB);
        //设置SCPI还是命令
        setPowerProtocol(new PowerSCPIProtocol(this));
        stateCheckExecutor = Executors.newSingleThreadExecutor();
        wavePulseMap = new HashMap<>();
        queueCommandRunner = new QueueCommandRunner(new CommandRunner() {
            @Override
            public Object sendAndReceive(String command) throws DeviceSendException {
                if (getInstrument() != null) {
                    return sendAndReceiveString(command);
                } else {
                    throw new DeviceSendException("菊水仪器未连接");
                }
            }

            @Override
            public void send(String command) throws DeviceSendException {
                try {
                    if (getInstrument() != null) {
                        getInstrument().write(command);
                    } else {
                        throw new DeviceSendException("菊水仪器未连接");
                    }
                } catch (JVisaException e) {
                    throw new DeviceSendException(e);
                }
            }
        });
    }

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(getDeviceName(), this);
        }
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        TestProcessManager.removeTestProcessListener(getDeviceName());
        return super.close();
    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.KIKUSUI;
    }

    @Override
    public boolean send(String command) throws DeviceSendException {
        queueCommandRunner.sendCommand(command, false);
        return true;
    }

    private String sendAndReceive(String command) throws DeviceSendException {
        return (String) queueCommandRunner.sendCommand(command, true);
    }

    private String getProgramExecutionState() throws DeviceSendException {
        return sendAndReceive("PROG:EXEC?");
    }

    @JSONField(serialize = false)
    private float getVoltage() throws DeviceSendException {
        String voltage = sendAndReceive("MEASure:VOLTage?");
        if (voltage == null) {
            throw new DeviceSendException("菊水电压获取失败");
        }
        return Float.parseFloat(voltage);
    }

    private boolean setVoltage(float voltage) throws DeviceSendException {
        log.info("设定恒定电压:{}V", voltage);
//        send("OUTPUT:ON"); 无效
        return send(String.format("VOLTAGE:%f", voltage));
    }

    @Override
    public float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        String current;
        try {
            current = sendAndReceive("MEASure:CURRent?");
        } catch (DeviceSendException e) {
            throw new DeviceReadException(e);
        }
        if (current == null) {
            throw new DeviceReadException("菊水电流获取失败");
        }
        return Float.parseFloat(current);
    }

    private void executePairedCustomizeFunction(PairedCustomizeFunction customizeFunction) throws LoopBreakNotification {
        NodeExecutor protocolInterpreter = SpringContextHolder.getBean(NodeExecutor.class);
        protocolInterpreter.executeByNode(customizeFunction.getNode());
    }

    private boolean isWithinTriggerVoltages(float voltage, List<Float> triggerVoltages) {
        for (float volt : triggerVoltages) {
            if (voltage >= volt) {
                return true;
            }
        }
        return false;
    }

    private boolean isTriggerVoltage(float voltage, float triggerVoltage) {
        return voltage >= triggerVoltage;
    }

    private void checkWavePulseVoltageTrigger() {
        //FIXME: 改成上升沿触发
        if (triggerPairedCustomizeFunction == null) {
            return;
        }
        if (voltageCheckExecutor == null || voltageCheckExecutor.isShutdown()) {
            //新建线程池
            voltageCheckExecutor = Executors.newSingleThreadExecutor();
        } else {
            //存在线程池，不需要重复提交线程
            return;
        }
        log.info("扫描到菊水电压触发函数:{}", triggerPairedCustomizeFunction.getParentOperation());
        List<Float> triggerVoltages = ((JSONArray) triggerPairedCustomizeFunction.getParentOperation().getOperationObject()).toJavaList(Float.class);
        Optional<Float> triggerVoltageOptional = triggerVoltages.stream().min(Float::compareTo);
        float triggerVoltage;
        if (!triggerVoltageOptional.isPresent()) {
            return;
        }
        triggerVoltage = triggerVoltageOptional.get();
        log.info("执行菊水电压检查:{}V", triggerVoltage);
        voltageCheckExecutor.execute(() -> {
            //FIXME：无法暂停，建议使用wait，被pause回调，后续统一抽离成一个线程类，接受测试执行单元管理
            //子线程添加到线程池，先进先出
            boolean checkPassport = true; //检查通行证
            while (!testCompleted) {
                try {
                    if (Thread.currentThread().isInterrupted()) {
                        voltageCheckExecutor.shutdown();
                        break;
                    }
                    float voltage = getVoltage();
                    log.info("当前菊水电压:{}V", voltage);
                    if (!checkPassport) {
                        //没有通行证，等待电压下降到触发电压以下
                        if (voltage < triggerVoltage) {
                            //下降到触发电压以下，重新发放通行证
                            log.info("当前菊水电压比触发电压{}V还低，等待下次函数触发", triggerVoltage);
                            checkPassport = true;
                        }
                    }
                    if (checkPassport && isTriggerVoltage(voltage, triggerVoltage)) {
                        if (executePairedCustomizeFunctionThread != null && executePairedCustomizeFunctionThread.isAlive()) {
                            log.info("电压触发时，检测到上次检测函数仍在运行，中断上次运行");
                            executePairedCustomizeFunctionThread.interrupt();
                        }
                        //获取到通行证，并且在触发电压范围
                        checkPassport = false; //暂时取消通行证，不重复执行自定义函数
                        log.info("触发自定义菊水电压检查函数，触发电压为{}V", voltage);
                        executePairedCustomizeFunctionThread = new Thread(() -> {
                            try {
                                executePairedCustomizeFunction(triggerPairedCustomizeFunction);
                            } catch (LoopBreakNotification e) {
                                log.error(e.getMessage(), e);
                            }
                        });
                        executePairedCustomizeFunctionThread.setName("kikusui-trigger-thread");
                        System.out.println("kikusui thread:" + Thread.currentThread());
                        executePairedCustomizeFunctionThread.setDaemon(true);
                        executePairedCustomizeFunctionThread.start();
                    }
                    TimeUnit.MILLISECONDS.sleep(300);
                } catch (DeviceSendException | InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        });

    }

    private void runAndWaitFinish() throws DeviceSendException {
        runAndWaitFinish(1);
    }


    private void runAndWaitFinish(int loop) throws DeviceSendException {
        log.info("运行菊水脉冲，循环:{}次", loop);
        send(String.format("PROG:EDIT:LOOP %d", loop));
        send("PROG:EXEC:STAT RUN");
        checkWavePulseVoltageTrigger();
        waitForWavePulseFinish();
    }

    private void waitForWavePulseFinish() {
        if (isSimulated()) {
            return;
        }
        synchronized (stateCheckExecutor) {
            stateCheckExecutor.notifyAll();
        }
        if (!stateQuerying) {
            //未在获取状态，进行状态获取
            log.info("运行菊水电源状态获取线程");
            stateCheckExecutor.execute(() -> {
                while (!testCompleted && !Thread.currentThread().isInterrupted()) {
                    try {
                        stateQuerying = true;//标识获取电源状态中
                        String receiveString = getProgramExecutionState();
                        System.out.printf("%s菊水电源状态: %s%n", getDeviceName(), receiveString);
                        if (receiveString != null && receiveString.contains("STOP")) {
                            log.info("收到菊水脉冲运行结束信号");
                            synchronized (this) {
                                //通知运行下一个循环
                                notifyAll();
                            }
                            synchronized (stateCheckExecutor) {
                                stateCheckExecutor.wait();
                            }
                        }
                    } catch (DeviceSendException e) {
                        log.error("stateCheckExecutor->DeviceSendException", e);
                    } catch (InterruptedException e) {
                        log.error("stateCheckExecutor->InterruptedException", e);
                        break;
                    }
                    try {
                        TimeUnit.MILLISECONDS.sleep(1500);
                    } catch (InterruptedException e) {
                        log.error("stateCheckExecutor->DeviceSendException", e);
                        break;
                    }
                }

                testCompleted = true;
                stateQuerying = false; //取消获取电源状态标识
            });
        }
        //等待当前脉冲运行完成
        synchronized (this) {
            try {
                wait();
            } catch (InterruptedException e) {
                clear();
                log.warn("菊水脉冲被人为终止");
            }
        }
    }

    private void checkRunStatus() throws DeviceSendException {
        String receiveString = getProgramExecutionState();
        log.info("菊水当前运行状态:{}", receiveString);
        if (receiveString != null && receiveString.contains("RUN")) {
            //脉冲还在运行中
            log.warn("菊水脉冲还在运行，强制停止");
            stopWavePulse();
            synchronized (this) {
                notifyAll();
            }
        }
    }

    private void loadWavePulse(int memory, int loop) throws DeviceSendException {
        send(String.format("PROG:NAME \"%d\"", memory));
        runAndWaitFinish(loop);
    }

    @Override
    public boolean loadWavePulse(int memory) throws DeviceSendException {
        try {
            checkRunStatus();
            log.info("加载菊水脉冲内存位置:{}", memory);
            int loop = 1;
            loadWavePulse(memory, loop);
            log.info("运行菊水内存{}脉冲已完成", memory);
        } catch (DeviceSendException e) {
            throw e;
        }
        return true;
    }

    @Override
    public void pauseWavePulse() throws DeviceSendException {
        log.info("{}菊水电源脉冲->发送暂停运行命令", getDeviceName());
        send("PROG:EXEC:STAT PAUSE");
    }

    @Override
    public void continueWavePulse() throws DeviceSendException {
        log.info("{}菊水电源脉冲->发送继续运行命令", getDeviceName());
        send("PROG:EXEC:STAT CONT");
    }

    @Override
    public void stopWavePulse() throws DeviceSendException {
        log.info("{}菊水电源脉冲->发送停止运行命令", getDeviceName());
        send("PROG:EXEC:STAT STOP");
    }

    @Override
    public void loadConstantVoltage() {

    }

    private void writeConstantVoltage() throws JVisaException {
        if (GlobalConfigHolder.getGlobalConfig().isAutoLoadConstantWaveVoltage()) {
            log.info("自动加载12V恒压");
            writeStep(12, 0.1);
        }
    }

    private void afterWritePulse(WavePulse wavePulse) throws JVisaException, DeviceSendException {
        wavePulseMap.put(wavePulse.getMemoryPosition(), wavePulse);
        writeConstantVoltage();
        runAndWaitFinish();
    }

    @Override
    public boolean writeCustomizeWavePulse(CustomizedWavePulse customizedWavePulse) throws DeviceSendException, JVisaException {
        WavePulse wavePulseInMemory = wavePulseMap.get(customizedWavePulse.getMemoryPosition());
        if (wavePulseInMemory != null && wavePulseInMemory.equals(customizedWavePulse)) {
            //调用内存波形
            loadWavePulse(customizedWavePulse.getMemoryPosition());
        } else {
            //重新写入波形
            log.info("内存{}写入自定义脉冲:{}", customizedWavePulse.getMemoryPosition(), customizedWavePulse);
            checkRunStatus();
            resetAndInitialize(customizedWavePulse.getMemoryPosition());
            for (CustomizedPulseSegment pulseSegment : customizedWavePulse.getCustomizedPulseSegmentList()) {
                if (Objects.equals(pulseSegment.getTransition(), TRANSITION_STEP))
                    writeStep(pulseSegment.getVoltage(), pulseSegment.getInterval());
                else if (Objects.equals(pulseSegment.getTransition(), TRANSITION_RAMP))
                    writeRamp(pulseSegment.getStartVoltage(), pulseSegment.getVoltage(), pulseSegment.getInterval());
                if (Objects.equals(pulseSegment.getAC(), AC_ON)) {
                    getInstrument().write("PROGram:EDIT:STEP:VOLTAGE:AC:Sweep " + pulseSegment.getStartVpp() + " ;:PROGram:EDIT:STEP:VOLTAGE:AC " + pulseSegment.getEndVpp() + ",Sweep");
                    getInstrument().write("PROGram:EDIT:STEP:Freq:SWEep LINear," + pulseSegment.getStartHz() + " ;:PROGram:EDIT:STEP:Freq " + pulseSegment.getEndHz() + ",Sweep");
                }
            }
            afterWritePulse(customizedWavePulse);
        }
        return true;
    }

    @Override
    public boolean writeStartWavePulse(StartPulse startPulse) throws DeviceSendException, JVisaException {
        //重新写入波形
        log.info("内存{}写入启动脉冲:{}", startPulse.getMemoryPosition(), startPulse);
        checkRunStatus();
        List<Float> constantValues = startPulse.getConstantValues();
        double formVoltage = startPulse.getFromVoltage();
        double toVoltage = startPulse.getToVoltage();
        double fromTime = startPulse.getFromTime();
        double toTime = startPulse.getToTime();
        int cycle = getRandomNum(startPulse.getFromCycle(), startPulse.getToCycle());
        resetAndInitialize(startPulse.getMemoryPosition());
        for (int i = 0; i < cycle; i++) {
            if (testCompleted) {
                return false;
            }
            if (startPulse.isConstantRandom()) {
                writeStep(constantValues.get(i % constantValues.size()), getRandomNum(fromTime, toTime));
            } else {
                writeStep(getRandomNum(formVoltage, toVoltage), getRandomNum(fromTime, toTime));
            }
        }
        afterWritePulse(startPulse);
        return true;
    }

    @Override
    public boolean writeTimeLevelResetPulse(TimeLevelResetPulse timeLevelResetPulse) throws DeviceSendException, JVisaException {
        WavePulse wavePulseInMemory = wavePulseMap.get(timeLevelResetPulse.getMemoryPosition());
        if (wavePulseInMemory != null && wavePulseInMemory.equals(timeLevelResetPulse)) {
            //调用内存波形
            log.info("复用上次时间级复位脉冲内存位置{}", timeLevelResetPulse.getMemoryPosition());
            loadWavePulse(timeLevelResetPulse.getMemoryPosition());
        } else {
            //重新写入波形
            log.info("内存{}写入时间级复位脉冲:{}", timeLevelResetPulse.getMemoryPosition(), timeLevelResetPulse);
            checkRunStatus();
            double highVoltage = timeLevelResetPulse.getHighVoltage();
            double lowVoltage = timeLevelResetPulse.getLowVoltage();
            double highPulseDuration = timeLevelResetPulse.getHighPulseDuration();
            // 1ms一次
            double times = timeLevelResetPulse.getLowPulseDuration() * 1000;
            resetAndInitialize(timeLevelResetPulse.getMemoryPosition());
            for (int i = 0; i < times; i++) {
                if (testCompleted) {
                    return false;
                }
                writeStep(highVoltage, highPulseDuration);
                writeStep(lowVoltage, (0.001 * (i + 1)));
            }
            afterWritePulse(timeLevelResetPulse);
        }
        return true;
    }

    @Override
    public boolean writeVoltageLevelResetPulse(VoltageLevelResetPulse voltageLevelResetPulse) throws DeviceSendException, JVisaException {
        WavePulse wavePulseInMemory = wavePulseMap.get(voltageLevelResetPulse.getMemoryPosition());
        if (wavePulseInMemory != null && wavePulseInMemory.equals(voltageLevelResetPulse)) {
            //调用内存波形
            log.info("复用上次电压级复位脉冲内存位置{}", voltageLevelResetPulse.getMemoryPosition());
            loadWavePulse(voltageLevelResetPulse.getMemoryPosition());
        } else {
            //重新写入波形
            log.info("内存{}写入电压级复位脉冲:{}", voltageLevelResetPulse.getMemoryPosition(), voltageLevelResetPulse);
            checkRunStatus();
            double highVoltage = voltageLevelResetPulse.getHighVoltage();
            double lowVoltage = voltageLevelResetPulse.getLowVoltage();
            double highPulseDuration = voltageLevelResetPulse.getHighPulseDuration();
            double lowPulseDuration = voltageLevelResetPulse.getLowPulseDuration();
            double delta = 0.1;
            int loops = (int) Math.round((highVoltage - lowVoltage) / delta);
            log.info("脉冲预计交替循环次数:{}", loops);
            resetAndInitialize(voltageLevelResetPulse.getMemoryPosition());
            for (int i = 0; i < loops; i++) {
                if (testCompleted) {
                    return false;
                }
                lowVoltage = highVoltage - delta * (i + 1);
                writeStep(highVoltage, highPulseDuration);
                writeStep(lowVoltage, lowPulseDuration);
            }
            afterWritePulse(voltageLevelResetPulse);
        }
        return true;
    }

    @Override
    public boolean writeArbitraryVoltageResetPulse(ArbitraryVoltageResetPulse arbitraryVoltageResetPulse) throws DeviceSendException, JVisaException {
        WavePulse wavePulseInMemory = wavePulseMap.get(arbitraryVoltageResetPulse.getMemoryPosition());
        if (wavePulseInMemory != null && wavePulseInMemory.equals(arbitraryVoltageResetPulse)) {
            //调用内存波形
            log.info("复用上次任意电压级复位脉冲内存位置{}", arbitraryVoltageResetPulse.getMemoryPosition());
            loadWavePulse(arbitraryVoltageResetPulse.getMemoryPosition());
        } else {
            //重新写入波形
            log.info("内存{}写入任意电压级复位脉冲:{}", arbitraryVoltageResetPulse.getMemoryPosition(), arbitraryVoltageResetPulse);
            checkRunStatus();
            double endVoltage = arbitraryVoltageResetPulse.getEndVoltage();
            double startVoltage = arbitraryVoltageResetPulse.getStartVoltage();
            double changeableDuration = arbitraryVoltageResetPulse.getChangeableVoltageDuration();

            double constantVoltage = arbitraryVoltageResetPulse.getConstantVoltage();
            double constantDuration = arbitraryVoltageResetPulse.getConstantVoltageDuration();
            double delta = 0.1;
            int loops = Math.abs((int) Math.round((endVoltage - startVoltage) / delta));
            resetAndInitialize(arbitraryVoltageResetPulse.getMemoryPosition());
            double nextChangeableVoltage = startVoltage;
            for (int i = 0; i <= loops; i++) {
                if (testCompleted) {
                    return false;
                }
                writeStep(constantVoltage, constantDuration);
                writeStep(nextChangeableVoltage, changeableDuration);
                if (startVoltage >= endVoltage) {
                    nextChangeableVoltage -= delta;
                } else {
                    nextChangeableVoltage += delta;
                }
            }
            afterWritePulse(arbitraryVoltageResetPulse);
        }
        return true;
    }

    @Override
    public boolean writePowerTemporarilyInterruptPulse(PowerTemporarilyInterruptPulse powerTemporarilyInterruptPulse) throws DeviceSendException, JVisaException {
        WavePulse wavePulseInMemory = wavePulseMap.get(powerTemporarilyInterruptPulse.getMemoryPosition());
        if (wavePulseInMemory != null && wavePulseInMemory.equals(powerTemporarilyInterruptPulse)) {
            //调用内存波形
            log.info("复用上次电源短暂中断脉冲内存位置{}", powerTemporarilyInterruptPulse.getMemoryPosition());
            loadWavePulse(powerTemporarilyInterruptPulse.getMemoryPosition());
        } else {
            //重新写入波形
            log.info("内存{}写入电源短暂中断脉冲:{}", powerTemporarilyInterruptPulse.getMemoryPosition(), powerTemporarilyInterruptPulse);
            checkRunStatus();
            double startVoltage = powerTemporarilyInterruptPulse.getHighVoltage();
            double endVoltage = 0;
            double highPulseDuration = powerTemporarilyInterruptPulse.getHighPulseDuration();
            double rampInterval = 100 / 1000.0; //100ms
            resetAndInitialize(powerTemporarilyInterruptPulse.getMemoryPosition());
            writeStep(startVoltage, highPulseDuration);
            for (int j = 0; j < 4; j++) {
                int time = 9;
                double lowPulseDuration = 0.1 / 1000;  //0.1ms
                if (j == 1) lowPulseDuration = 1f / 1000; //1ms
                if (j == 2) lowPulseDuration = 10f / 1000; //10ms
                if (j == 3) {
                    lowPulseDuration = 100f / 1000; //100ms
                    time = 20;
                }
                for (int i = 0; i < time; i++) {
                    if (testCompleted) {
                        return false;
                    }
                    writeRamp(startVoltage, endVoltage, rampInterval);
                    writeStep(endVoltage, lowPulseDuration * (i + 1));
                    writeRamp(endVoltage, startVoltage, rampInterval);
                    writeStep(startVoltage, highPulseDuration);
                }
            }
            afterWritePulse(powerTemporarilyInterruptPulse);
        }
        return true;
    }

    /**
     * 监测电压并在达到值时执行操作
     *
     * @param voltageTrigger 电压触发设置
     */
    public synchronized boolean kikusuiTriggerAction(VoltageTrigger voltageTrigger) {
        if (monitoring.get()) {
            return false;
        }
        monitoring.set(true);
        if (executorService == null || executorService.isShutdown()) {
            executorService = Executors.newSingleThreadExecutor();
        }
        String projectName = getExecutionContext().get().getProjectName();
        Future<Boolean> future = executorService.submit(new Callable<Boolean>() {
            @Override
            public Boolean call() throws Exception {
                try {
                    double triggerVoltage = voltageTrigger.getVoltage();
                    int triggerMode = voltageTrigger.getSymbol();
                    int intervalTime = voltageTrigger.getInterval();
                    int attempts = voltageTrigger.getAttempts();
                    int attemptCount = 0;
                    while (monitoring.get() && attemptCount < attempts) {
                        float currentVoltage = getVoltage();
                        log.info("当前菊水电压:{}V", currentVoltage);
                        boolean triggerConditionMet = isTriggerConditionMet(triggerMode, currentVoltage, triggerVoltage);
                        if (triggerConditionMet) {
                            return executeTriggerAction(voltageTrigger, projectName);
                        }
                        attemptCount++;
                        Thread.sleep(intervalTime);
                    }
                    if (attemptCount >= attempts) {
                        log.info(String.format("尝试%d次后未满足条件，停止判断，请检查是否正确", attempts));
                        return false;
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.info("电压监控被中断。");
                } catch (DeviceSendException | DeviceReadException | OperationFailNotification e) {
                    log.error("设备异常", e);
                } finally {
                    monitoring.set(false);
                    log.info("监控停止");
                }
                return false;
            }
        });
        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            log.error("获取执行结果时发生错误", e);
            return false;
        }
    }

    private boolean isTriggerConditionMet(int triggerMode, double currentVoltage, double triggerVoltage) {
        if (triggerMode == 0) {
            return currentVoltage >= triggerVoltage;
        } else if (triggerMode == 1) {
            return currentVoltage <= triggerVoltage;
        } else {
            log.warn("数据传递出现异常，未知的对比类型: {}", triggerMode);
            return false;
        }
    }

    private boolean executeTriggerAction(VoltageTrigger voltageTrigger, String projectName) throws DeviceSendException, DeviceReadException, OperationFailNotification {
        int action = voltageTrigger.getActionOperator();
        float current = voltageTrigger.getCurrent();
        int currentDirection = voltageTrigger.getSymbol();
        if (action == 0) {
            return multimeterTriggerAction(currentDirection, current);
        } else if (action == 1) {
            return robotTriggerAction(voltageTrigger, projectName);
        } else {
            return robotSwipeTriggerAction(voltageTrigger, projectName);
        }
    }

    private boolean robotSwipeTriggerAction(VoltageTrigger voltageTrigger, String projectName) {
        log.info("准备执行机械臂滑动");
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        Device device = deviceFinderManager.findDeviceByAliasName(robotName);
        if (device != null) {
            DefaultDobotDevice defaultDobotDevice = (DefaultDobotDevice) device;
            defaultDobotDevice.swipeByName(voltageTrigger.getCoordinateNameList(), true, projectName);
        }
        return false;
    }

    private boolean multimeterTriggerAction(int currentDirection, float electricityValue) throws DeviceReadException {
        log.info("准备执行电流检测");
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        Device device = deviceFinderManager.findDeviceByAliasName(multimeterName);
        if (device == null) {
            return false;
        }
        KeySight34461A keySight34461A = (KeySight34461A) device;
        float currentDC = keySight34461A.getCurrentDC();
        boolean result;
        if (currentDirection == 0) {
            result = currentDC >= electricityValue;
            log.info(result ? "电流检测到大于等于{}A" : "电流检测到小于{}A", electricityValue);
        } else {
            result = currentDC <= electricityValue;
            log.info(result ? "电流检测到小于等于{}A" : "电流检测到大于{}A", electricityValue);
        }
        log.info(result ? "电流检测通过" : "电流检测未通过");
        return result;
    }

    private boolean robotTriggerAction(VoltageTrigger voltageTrigger, String projectName) throws OperationFailNotification {
        log.info("准备执行机械臂报点检测");
        String portName = voltageTrigger.getPortName();
        CheckPointInfo checkPointInfo = voltageTrigger.getCheckPointInfo();
        String coordinateName = voltageTrigger.getCoordinateName();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        Device device = deviceFinderManager.findDeviceByAliasName(portName);
        if (device != null) {
            SerialPortDevice serialPortDevice = (SerialPortDevice) device;
            Device robotDevice = deviceFinderManager.findDeviceByAliasName(robotName);
            if (robotDevice != null) {
                DefaultDobotDevice defaultDobotDevice = (DefaultDobotDevice) robotDevice;
                checkPointInfo.setTouchType("PRESS");
                checkPointInfo.setEnableCoordinate(true);
                serialPortDevice.pointCheckStart(checkPointInfo);
                defaultDobotDevice.pressTouch(coordinateName, projectName);
                OperationResult operationResult = serialPortDevice.pointCheckStop(checkPointInfo);
                if (!operationResult.isOk()) {
                    return false;
                }
                checkPointInfo.setTouchType("RELEASE");
                checkPointInfo.setEnableCoordinate(false);
                serialPortDevice.pointCheckStart(checkPointInfo);
                defaultDobotDevice.releaseTouch(coordinateName);
                operationResult = serialPortDevice.pointCheckStop(checkPointInfo);
                return operationResult.isOk();
            }
        }
        return false;
    }


    private void clear() {
        testCompleted = true;
        wavePulseMap.clear();
        if (voltageCheckExecutor != null && !voltageCheckExecutor.isTerminated()) {
            voltageCheckExecutor.shutdownNow();
        }
        if (executePairedCustomizeFunctionThread != null && executePairedCustomizeFunctionThread.isAlive()) {
            executePairedCustomizeFunctionThread.interrupt();
        }
        voltageCheckExecutor = null;
        executePairedCustomizeFunctionThread = null;
        log.info("完成测试后执行菊水电源清理工作");
    }

    public void resetAndInitialize(int programName) throws JVisaException {
        if (isSimulated()) {
            return;
        }
        getInstrument().write("PROGram:name '" + programName + "'");
        getInstrument().write("PROGram:EDIT:Del");
    }

    public void createStep() throws JVisaException {
        if (isSimulated()) {
            return;
        }
        getInstrument().write("PROGram:EDIT:ADD 1");
        getInstrument().write("PROGram:EDIT:COUNT?");
        int count = Integer.parseInt(getInstrument().readString());
        getInstrument().write("PROGram:EDIT:STEP:SELECT " + (count + 1) + "\"");
    }

    public int getRandomNum(int minNum, int MaxNum) {
        return (int) (Math.random() * (MaxNum - minNum + 1) + minNum);
    }

    public double getRandomNum(double minNum, double MaxNum) {
        double num = Math.random() * (MaxNum - minNum + 1) + minNum;
        if (num > MaxNum) {
            num = getRandomNum(minNum, MaxNum);
        }
        return num;
    }

    public void writeStep(double voltage, double interval) throws JVisaException {
        if (isSimulated()) {
//            log.info("volt:{}->{}", voltage, interval);
            return;
        }
        createStep();
        getInstrument().write("PROGram:EDIT:STEP:VOLTAGE " + voltage + "v");
        getInstrument().write("PROGram:EDIT:STEP:TIME " + interval + "s");
    }

    public void writeRamp(double startVoltage, double endVoltage, double interval) throws JVisaException {
        if (isSimulated()) {
            return;
        }
        createStep();
        getInstrument().write("PROGram:EDIT:STEP:VOLTAGE:RAMP " + startVoltage + "v;:PROGram:EDIT:STEP:VOLTAGE " + endVoltage + "v,RAMP");
        getInstrument().write("PROGram:EDIT:STEP:TIME " + interval + "s");
    }

    @Override
    public boolean compileCompleted(MethodCollector methodCollector) {
        triggerPairedCustomizeFunction = methodCollector.getFirstPairedCustomizeFunctionByKeyword(
                DeviceOperationMethod.beginVoltageTriggerFunction.getKeyword());
        return true;
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        clear();
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        //FIXME：自动唤醒时无法调用testStart
        log.info("开始运行前执行菊水电源清理工作");
        clear();
        testCompleted = false;
        stateQuerying = false;
    }

    @Override
    public void testPausing() {
        try {
            pauseWavePulse();
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void testFailed(ExecutionContext executionContext, int testCycle) {
        testPausing();
    }

    @Override
    public void testResume() {
        try {
            continueWavePulse();
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void testTerminated() {
        try {
//            clear();
            stopWavePulse();
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        List<Float> floats = new ArrayList<>();
        floats.add(1.0f);
        floats.add(2.0f);
        floats.add(3.0f);
        for (int i = 0; i < 10; i++) {
            System.out.println("add:" + floats.get(i % floats.size()));
        }
    }
}
