package com.desaysv.workserver.screen.distributor;

import com.desaysv.workserver.monitor.data.DataDistributor;
import com.desaysv.workserver.screen.consumers.KeepAliveTouchPointDataConsumer;

public class KeepAliveTouchPointDistributor extends DataDistributor<byte[]> {
    private final KeepAliveTouchPointDataConsumer keepAliveTouchPointDataConsumer;

    public KeepAliveTouchPointDistributor(KeepAliveTouchPointDataConsumer consumer) {
        super(consumer);
        this.keepAliveTouchPointDataConsumer = consumer;
    }
}
