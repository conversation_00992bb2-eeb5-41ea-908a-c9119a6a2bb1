package com.desaysv.workserver.stream.grabber;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import org.bytedeco.javacv.Frame;

public class VideoCaptureStreamGrabber extends StreamGrabber {

    @Override
    public ImageBuffer grabByteBuffer() throws FrameGrabberException {
        return null;
    }

    @Override
    public void start() {

    }

    @Override
    public void stop() {

    }

    @Override
    public void trigger() {

    }

    @Override
    public Frame grab() {
        return null;
    }

    @Override
    public void release() {

    }
}
