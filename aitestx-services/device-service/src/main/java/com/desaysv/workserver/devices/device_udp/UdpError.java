package com.desaysv.workserver.devices.device_udp;

import lombok.Getter;

/**
 * UDP异常
 */
@Getter
public class UdpError extends Exception {
    // 错误代码常量定义
    public static final int SUCCESS = 0xFF;
    public static final int RESERVED = 0xFE;
    public static final int BUSY = 0xFD;
    public static final int RESOURCE_OUT_OF_RANGE = 0xFC;
    public static final int COMMAND_NOT_SUPPORTED = 0xFB;
    public static final int WITHOUT_SUPPORTED = 0xFA;
    public static final int VERIFY_ERROR = 0xF9;
    public static final int ACCESS_LOCKED = 0xF8;
    public static final int REQUEST_SEQUENCE_ERROR = 0xF7;
    public static final int WRONG_BLOCK_TYPE = 0xF6;
    public static final int INVALID_IDENTIFIER = 0xF5;
    public static final int PAGE_MODE_REFUSED = 0xF4;
    private int errorCode;

    // 根据错误代码自动获取描述
    public UdpError(int errorCode) {
        super(getMessage(errorCode));
        this.errorCode = errorCode;
    }

    // 允许自定义描述的构造函数
    public UdpError(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public UdpError(String message) {
        super(message);
    }

    public UdpError(Throwable cause) {
        super(cause);
    }

    private static String getMessage(int errorCode) {
        switch (errorCode) {
            case RESERVED:
                return "保留(Reserved)";
            case BUSY:
                return "系统忙（Busy）";
            case RESOURCE_OUT_OF_RANGE:
                return "未实现（Resource Out of Range）";
            case COMMAND_NOT_SUPPORTED:
                return "错误的命令（Command Not Supported）";
            case WITHOUT_SUPPORTED:
                return "未实现（Without Supported）";
            case VERIFY_ERROR:
                return "验证失败（Verify Error）";
            case ACCESS_LOCKED:
                return "授权不足（Access Locked）";
            case REQUEST_SEQUENCE_ERROR:
                return "请求序列错误（Request Sequence Error）";
            case WRONG_BLOCK_TYPE:
                return "错误的资源类型（Wrong Block Type）";
            case INVALID_IDENTIFIER:
                return "错误的资源标识符（Invalid Identifier）";
            case PAGE_MODE_REFUSED:
                return "未准备好（Page Mode Change Refused）";
            default:
                return "未知错误";
        }
    }

    // 获取十六进制格式的错误代码
    public String getHexErrorCode() {
        return String.format("0x%02X", errorCode);
    }


}
