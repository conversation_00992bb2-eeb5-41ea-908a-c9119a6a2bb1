package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLObjectHeader extends Structure {
    public VBLObjectHeaderBase mBase;
    public int mObjectFlags;
    public short mClientIndex;
    public short mObjectVersion;
    public long mObjectTimeStamp;

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mBase", "mObjectFlags", "mClientIndex", "mObjectVersion", "mObjectTimeStamp");
    }
}
