package com.desaysv.workserver.devices.bus.e2e.xiaomiE2E;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Data
public class E2eXiaomi implements CanMessageEventListener {
    private static final ConcurrentHashMap<Integer, AtomicInteger> counters = new ConcurrentHashMap<>();
    // 初始计数器值
    private final int initialCounterValue = 0;
    private final CanConfig canConfig;
    private final int channel;
    private final int MIN_COUNTER_LENGTH = 0;
    private final int MAX_COUNTER_LENGTH = 14;
    private final CopyOnWriteArrayList<XiaomiE2eData> ve2eList;

    public E2eXiaomi(CanConfig canConfig, int channel) {
        this.canConfig = canConfig;
        this.channel = channel;
        CopyOnWriteArrayList<XiaomiE2eData> tempVe2eList = null;

        try {
            // 获取 E2eTableManager 的单例实例
            E2eTableManager tableManager = E2eTableManager.getInstance();
            if (tableManager == null) {
                log.error("E2eTableManager instance is null");
            } else {
                // 确保从 E2eTableManager 获取的列表内容被正确复制到 CopyOnWriteArrayList
                if (tableManager.getVe2eList() != null) {
                    tempVe2eList = new CopyOnWriteArrayList<>(tableManager.getVe2eList());
                } else {
                    log.error("ve2eList from E2eTableManager is null");
                }
            }
        } catch (Exception e) {
            log.error("E2E配置信息获取失败: {}", e.getMessage());
        }

        // 确保 ve2eList 总是被赋值
        this.ve2eList = (tempVe2eList != null) ? tempVe2eList : new CopyOnWriteArrayList<>();
    }


    public byte[] e2eCalculateXm(int canId, byte[] canData) {
        // 查找所有匹配的规则
        List<XiaomiE2eData> matchingRules = ve2eList.stream()
                .filter(x -> x.getCanId() == canId)
                .collect(Collectors.toList());

        if (!matchingRules.isEmpty()) {
            // 确保计数器存在并初始化
            AtomicInteger counter = counters.computeIfAbsent(canId, k -> new AtomicInteger(initialCounterValue));
            // 获取当前计数值
            AtomicInteger currentCounterValue = counters.get(canId);
            for (XiaomiE2eData matchingRule : matchingRules) {
                try {
                    // 使用当前计数值计算CRC
                    canData = matchingRule.crc(canData, currentCounterValue);
                } catch (Exception e) {
                    log.error("处理CAN ID {}的E2E计算时发生错误: {}", canId, e.getMessage(), e);
                    // 在这里可以选择抛出异常或者返回原始数据，取决于业务需求
                    // throw e;
                    return canData; // 或者 return null; 根据业务需求
                }
            }
            // 更新计数器
            e2eCounter(canId);
        } else {
            log.warn("未找到CAN ID {}对应的E2E规则", canId);
        }

        return canData;
    }

    public void e2eCounter(int canId) {
        AtomicInteger counter = counters.get(canId);
        if (counter != null) {
            int newValue = (counter.incrementAndGet()) % (MAX_COUNTER_LENGTH + 1);
            counter.set(newValue);
        } else {
            log.warn("CAN ID {}计数器跟新失败", canId);
        }
    }

    @Override
    public void onMessageSend(CanMessage message) {
        if (message.getE2eType() == null) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            if ("Xiaomi".equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
        }
        if (!message.isE2eEnabled()) {
            return;
        }
        if ("Xiaomi".equalsIgnoreCase(message.getE2eType())) {
            message.setData(e2eCalculateXm(message.getArbitrationId(), message.getData()));
        }
    }

    public static void main(String[] args) {
        // 测试代码
        // 模拟的 CAN 配置
        CanConfig canConfig = new CanConfig();
        // 假设 CAN 配置中已经配置了 E2E 类型为 "CheryE01"
        canConfig.getE2eTypes().put(1, "Xiaomi");

        // 创建 E2eXiaomi 实例
        E2eXiaomi e2eXiaomi = new E2eXiaomi(canConfig, 1);

        // 测试数据
        int canId1 = 0xF1; // 示例 CAN ID
        byte[] canData1 = new byte[]{0x00, 0x00, (byte) 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0X00, 0X00, 0X00, 0X00, (byte) 0xA0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0X00, 0X00, 0X00, 0X00, 0x00, 0x00, 0x00, 0x00, 0X00, 0X00, (byte) 0X7F, (byte) 0XFE}; // 示例 CAN 数据
        for (int i = 0; i <= 23; i++) {
            byte[] processedData = e2eXiaomi.e2eCalculateXm(canId1, canData1);
            System.out.println("第" + i + "次：" + "Processed CAN Data:");
            for (byte b : processedData) {
                System.out.printf("%02X ", b);
            }
            System.out.println();
        }


    }
}