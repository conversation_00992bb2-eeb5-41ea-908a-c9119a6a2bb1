package com.desaysv.workserver.devices.robot.type.mg400.entity;

import com.desaysv.workserver.model.RobotCoordinates;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 13:27
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Slf4j
@Data
public class MoveEntity implements Cloneable {
    private double x;
    private double y;
    private double z;
    private double r;
    private double slideRail;
    private Integer user;
    private Integer tool;
    private Integer speed;
    private Integer acc;
    private Integer cp;//平滑过渡参数
    private boolean joint = false;

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(x);
            moveEntity.setY(y);
            moveEntity.setZ(z);
            moveEntity.setR(r);
            moveEntity.setSlideRail(slideRail);
            moveEntity.setUser(user);
            moveEntity.setTool(tool);
            moveEntity.setSpeed(speed);
            moveEntity.setAcc(acc);
            moveEntity.setCp(cp);
            return moveEntity;
        }
    }

    public MoveEntity() {

    }

    public MoveEntity(double x, double y, double z, double r) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.r = r;
    }

    public boolean compare(MoveEntity moveEntity, double deviation) {
        return Math.abs(x - moveEntity.x) <= deviation &&
                Math.abs(y - moveEntity.y) <= deviation &&
                Math.abs(z - moveEntity.z) <= deviation;
    }

    public static MoveEntity fromRobotCoordinates(RobotCoordinates robotCoordinates) {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(robotCoordinates.getX());
        moveEntity.setY(robotCoordinates.getY());
        moveEntity.setZ(robotCoordinates.getZ());
        moveEntity.setR(robotCoordinates.getR());
        moveEntity.setSlideRail(robotCoordinates.getSlideRail());
        return moveEntity;
    }

    @Override
    public String toString() {
        return String.format("(%f, %f, %f, %f%s)", x, y, z, r, slideRail == 0 ? "" : String.format(",slideRail=%f", slideRail));
    }
}
