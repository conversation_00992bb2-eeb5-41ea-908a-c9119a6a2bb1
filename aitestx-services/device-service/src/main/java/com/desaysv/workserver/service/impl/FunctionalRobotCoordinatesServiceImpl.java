package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.FunctionalRobotCoordinatesMapper;
import com.desaysv.workserver.model.FunctionalRobotCoordinates;
import com.desaysv.workserver.service.FunctionalRobotCoordinatesService;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Lazy
public class FunctionalRobotCoordinatesServiceImpl implements FunctionalRobotCoordinatesService {

    @Autowired
    private FunctionalRobotCoordinatesMapper functionalRobotCoordinatesMapper;

    @Override
    public Integer addFunctionalCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates) {
        return functionalRobotCoordinatesMapper.insert(functionalRobotCoordinates);
    }

    @Override
    public Integer updateFunctionalCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates) {
        return functionalRobotCoordinatesMapper.updateByFunctionCoordinatesUUID(functionalRobotCoordinates);
    }

    @Override
    public FunctionalRobotCoordinates getFunctionalCoordinatesByUUID(String funcCoordinatesUUID) {
        return functionalRobotCoordinatesMapper.selectByFunctionCoordinatesUUID(funcCoordinatesUUID);
    }

    @Override
    public List<FunctionalRobotCoordinates> getFunctionalCoordinates(FunctionalRobotCoordinatesQueryVo funcCoordinatesQuery) {
        return functionalRobotCoordinatesMapper.selectByCondition(funcCoordinatesQuery);
    }
}
