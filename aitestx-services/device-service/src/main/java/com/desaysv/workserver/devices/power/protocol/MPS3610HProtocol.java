package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.devices.power.base.PowerDevice;

import static com.desaysv.workserver.common.scpi.ScpiCommandConstants.COMPATIBLE_LINE_ENDING;
import static com.desaysv.workserver.common.scpi.ScpiCommandConstants.CRLF;

/**
 * 适用于MPS3610H
 */
public class MPS3610HProtocol extends PowerSCPIProtocol {


    public MPS3610HProtocol(PowerDevice device) {
        super(device);
    }

    @Override
    public boolean selectChannel(Integer channel) {
        if (channel != null) {
            return sendCommand(ScpiCommandConstants.selectChannel(channel, true));
        }
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand((isOutput ? ScpiCommandConstants.OUTPUT_ON : ScpiCommandConstants.OUTPUT_OFF) + CRLF);
    }

    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        voltage = Float.parseFloat(String.format("%.2f", voltage));
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_VOLTAGE + " " + voltage) + " " + COMPATIBLE_LINE_ENDING);
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_CURRENT + " " + current) + " " + COMPATIBLE_LINE_ENDING);
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_VOLTAGE_MPS3610H + CRLF);
    }

    @Override
    public boolean readCurrent(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_CURRENT_MPS3610H + CRLF);
    }

}
