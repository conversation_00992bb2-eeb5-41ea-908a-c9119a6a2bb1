package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLObjectHeaderBase extends Structure {
    public int mSignature;
    public short mHeaderSize;
    public short mHeaderVersion;
    public int mObjectSize;
    public int mObjectType;

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mSignature", "mHeaderSize", "mHeaderVersion", "mObjectSize", "mObjectType");
    }


}

