package com.desaysv.workserver.stream.rtsp;

import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.stream.AndroidStreamProducer;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;

import javax.imageio.ImageIO;
import java.io.File;

@Slf4j
public class RtspAndroidStreamProducer extends AndroidStreamProducer {
    private final String serialNumber;

    public RtspAndroidStreamProducer(int w, int h, String serialNumber) {
        super(w, h);
        this.serialNumber = serialNumber;
    }

    @Override
    protected void startStream(String deviceModel, String serialNumber) {
        try (Frame frame = grabFrame()) {
            setWidth(frame.imageWidth);
            setHeight(frame.imageHeight);
        } catch (FrameGrabberException e) {
            log.warn(e.getMessage(), e);
        }
    }

    @Override
    public String pushStream(String deviceModel, String serialNumber) throws Exception {
//        startStream(deviceModel, serialNumber);
        return serialNumber;
    }

    @Override
    public Frame grabFrame() throws FrameGrabberException {
        DeviceRegisterManager deviceManager = SpringContextHolder.getBean(DeviceRegisterManager.class);
        AndroidDevice androidDevice = (AndroidDevice) deviceManager.getDevice(serialNumber);
        synchronized (this) {
            log.info("采集Android图像");
            try {
                String path = androidDevice.screenshot();
                Frame frame = Java2DFrameUtils.toFrame(ImageIO.read(new File(path)));
                if (frame != null && getWidth() == 0 && getHeight() == 0) {
                    setWidth(frame.imageWidth);
                    setHeight(frame.imageHeight);
                }
                return frame;
            } catch (Exception e) {
                log.warn("采集Android图像出错:", e);
                throw new FrameGrabberException(e);
            }
        }
    }

    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {

    }

    @Override
    public RectSize getDefaultSize() {
        return null;
    }

    @Override
    public void outputDynamicFrame(boolean isOutput) {

    }

    @Override
    public void setExposureAuto(CameraSettings cameraSettings) {

    }

    @Override
    public void setReverseX(boolean reverseX) {
    }

    @Override
    public void setReverseY(boolean reverseY) {

    }

    @Override
    public void grabberSwitch(boolean status) {

    }

    @Override
    public CameraSettings getCameraSettings() {
        return null;
    }

    @Override
    public double getGrabberFrameRate() {
        return 0;
    }

}
