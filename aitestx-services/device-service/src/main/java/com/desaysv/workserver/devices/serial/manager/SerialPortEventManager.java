package com.desaysv.workserver.devices.serial.manager;

import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.serial.listener.SerialPortOpenListener;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * 管理 SerialPortDevice 打开和关闭事件的监听器并进行通知。
 * 同时，它也跟踪当前已打开的 SerialPortDevice 实例。
 */
@Slf4j
public class SerialPortEventManager {

    // 单例实例
    private static final SerialPortEventManager INSTANCE = new SerialPortEventManager();

    private final List<SerialPortOpenListener> listeners = new CopyOnWriteArrayList<>();
    private final List<SerialPortDevice> openSerialPorts = new CopyOnWriteArrayList<>();

    /**
     * 私有构造函数，防止外部实例化。
     */
    private SerialPortEventManager() {
        // 初始化代码（如果需要）
    }

    /**
     * 获取 SerialPortEventManager 的单例实例。
     *
     * @return SerialPortEventManager 实例。
     */
    public static SerialPortEventManager getInstance() {
        return INSTANCE;
    }

    /**
     * 注册一个监听器以接收串口打开/关闭事件。
     *
     * @param listener 要注册的监听器。
     */
    public void registerListener(SerialPortOpenListener listener) {
        if (listener != null && !listeners.contains(listener)) {
            listeners.add(listener);
            log.debug("已注册监听器: {}", listener.getClass().getName());

            // 新增逻辑：通知新注册的监听器关于当前已打开的串口
            log.debug("为新监听器 {} 检查已打开的端口...", listener.getClass().getName());
            for (SerialPortDevice openPort : openSerialPorts) {
                try {
                    log.debug("通知新监听器 {} 关于已打开的端口: {}", listener.getClass().getName(), openPort.getDevicePortName());
                    listener.onSerialPortOpened(openPort);
                } catch (Exception e) {
                    log.error("向新监听器 {} 通知端口 {} 打开事件时发生错误: {}",
                            listener.getClass().getName(), openPort.getDevicePortName(), e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 注销一个监听器。
     *
     * @param listener 要注销的监听器。
     */
    public void unregisterListener(SerialPortOpenListener listener) {
        if (listener != null) {
            listeners.remove(listener);
            log.debug("已注销监听器: {}", listener.getClass().getName());
        }
    }

    /**
     * 通知所有已注册的监听器串口已打开。
     * 将设备添加到已打开的端口列表中。
     *
     * @param device 已打开的 SerialPortDevice。
     */
    public void notifyPortOpened(SerialPortDevice device) {
        if (device == null) return;
        // 确保设备只添加一次，即使 notifyPortOpened 被意外多次调用
        if (openSerialPorts.stream().noneMatch(p -> p == device)) { // 使用对象引用比较，或确保 SerialPortDevice 有合适的 equals/hashCode
            openSerialPorts.add(device);
            log.info("串口设备已打开: {}(别名:{})。正在通知 {} 个监听器。", device.getDevicePortName(), device.getAliasName(), listeners.size());
        } else {
            log.debug("串口设备 {} 已在打开列表中。可能重复通知或状态已更新。", device.getDevicePortName());
        }
        for (SerialPortOpenListener listener : listeners) {
            try {
                listener.onSerialPortOpened(device);
            } catch (Exception e) {
                log.error("通知监听器 {} 关于端口 {} 打开事件时发生错误: {}", listener.getClass().getName(), device.getDevicePortName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 通知所有已注册的监听器串口已关闭。
     * 从已打开的端口列表中移除设备。
     *
     * @param device 已关闭的 SerialPortDevice。
     */
    public void notifyPortClosed(SerialPortDevice device) {
        if (device == null) return;
        boolean removed = openSerialPorts.remove(device);
        if (removed) {
            log.info("串口设备已关闭: {}(别名:{})。正在通知 {} 个监听器。", device.getDevicePortName(), device.getAliasName(), listeners.size());
        } else {
            log.debug("尝试关闭/通知关闭的串口设备 {} 不在打开列表中。", device.getDevicePortName());
        }
        for (SerialPortOpenListener listener : listeners) {
            try {
                listener.onSerialPortClosed(device);
            } catch (Exception e) {
                log.error("通知监听器 {} 关于端口 {} 关闭事件时发生错误: {}", listener.getClass().getName(), device.getDevicePortName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 获取当前已打开的 SerialPortDevice 实例列表。
     *
     * @return 一个包含当前已打开 SerialPortDevice 实例的新列表。
     */
    public List<SerialPortDevice> getOpenSerialPorts() {
        return new CopyOnWriteArrayList<>(openSerialPorts); // 返回副本以防止外部修改
    }

    /**
     * 根据端口名称获取当前已打开的 SerialPortDevice 实例列表。
     *
     * @param portName 用于筛选的端口名称。
     * @return 一个包含匹配的已打开 SerialPortDevice 实例的新列表。
     */
    public List<SerialPortDevice> getOpenSerialPortsByName(String portName) {
        if (portName == null || portName.isEmpty()) {
            return new CopyOnWriteArrayList<>(); // 返回空的可写副本
        }
        return openSerialPorts.stream()
                .filter(device -> portName.equals(device.getDevicePortName()) && device.isConnected()) // 确保设备确实是打开状态
                .collect(Collectors.toList());
    }
} 