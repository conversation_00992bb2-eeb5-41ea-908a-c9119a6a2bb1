package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.bo.TemplateImageBo;
import com.desaysv.workserver.bo.TemplateRoiInfo;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceUnknownException;
import com.desaysv.workserver.manager.ImageFileManager;
import com.desaysv.workserver.mapper.TemplatePictureMapper;
import com.desaysv.workserver.mapper.TestDeviceMapper;
import com.desaysv.workserver.mapper.TestDeviceTypeMapper;
import com.desaysv.workserver.mapper.TestProjectMapper;
import com.desaysv.workserver.model.*;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.model.roi.PercentTemplateRoi;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.TemplateRoi;
import com.desaysv.workserver.service.OriginalPictureService;
import com.desaysv.workserver.service.TemplateImageService;
import com.desaysv.workserver.service.TemplateRoiService;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.stream.StreamService;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

/**
 * 图像模板服务
 */
@Service
@Slf4j
@Lazy
public class TemplateImageServiceImpl implements TemplateImageService {

    @Autowired
    private TemplatePictureMapper templatePictureMapper;

    @Autowired
    private OriginalPictureService originalPictureService;

    @Autowired
    private TemplateRoiService templateRoiService;

    //TODO: 后续考虑移动到service层管理
    @Autowired
    private TestDeviceMapper testDeviceMapper;

    @Autowired
    private TestDeviceTypeMapper testDeviceTypeMapper;

    @Autowired
    private TestProjectMapper testProjectMapper;

    @Autowired
    private StreamService streamService;

    /**
     * Roi比例转换器
     *
     * @param templateImageBo 图像模板信息
     */
    private TemplateImageBo roiPercentTransformer(TemplateImageBo templateImageBo) {
        TestDevice testDevice = testDeviceMapper.selectByUniqueCode(templateImageBo.getDeviceUniqueCode());
        RectSize size = streamService.getImageSize(testDevice);

        double startX = templateImageBo.getStartX();
        double startY = templateImageBo.getStartY();
        double endX = templateImageBo.getEndX();
        double endY = templateImageBo.getEndY();
        templateImageBo.setTemplateRoi(TemplateRoi.of(startX, startY, endX, endY, size, templateImageBo.getRoiTypeId()));
        return templateImageBo;
    }

    /**
     * 保存图像模板
     *
     * @param rawTemplateImageBo 原始图像模板信息
     * @return 图像模板信息
     * @throws IOException
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public TemplateImageBo saveTemplate(TemplateImageBo rawTemplateImageBo) throws IOException {
        if (rawTemplateImageBo.isSimulated()) {
            log.info("设备正在模拟中，不保存模板图片");
            return rawTemplateImageBo;
        }
        try {
            TemplateImageBo templateImageBo = roiPercentTransformer(rawTemplateImageBo);
            TestDevice testDevice = testDeviceMapper.selectByUniqueCode(templateImageBo.getDeviceUniqueCode());
            TestProject testProject = testProjectMapper.selectByName(templateImageBo.getProjectName());
            TestDeviceType testDeviceType = testDeviceTypeMapper.selectByPrimaryKey(testDevice.getTypeId());
            //保存到数据库
            String originalPictureName = templateImageBo.getName();
            templateImageBo.setOriginalPictureName(originalPictureName);
            addToDB(templateImageBo);
            Frame originalFrame;
            if (rawTemplateImageBo.isImageSnapshotAvailable()) {
                // 如果使用的是快照，直接使用快照图像
                originalFrame = streamService.getSnapshotFrameFromCache(templateImageBo.getDeviceUniqueCode());
            } else {
                originalFrame = streamService.grab(testDevice);
            }
            ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(testDevice, testDeviceType.getName(), testProject.getName());
            //保存原图
//            pictureFileManager.saveOriginPictureFile(originalPictureName, originalFrame);
            //保存模板
            AbsoluteRoiRect roiRect = templateImageBo.getTemplateRoi().toRoiRect();
            log.info("templatePictureBo->getRoi:{}", roiRect);
            Frame templateFrame = streamService.cropWithRoi(testDevice, originalFrame, roiRect);
            imageFileManager.saveTemplateImageFile(templateImageBo.getName(), templateFrame);
            imageFileManager.saveRoi(templateImageBo.getName(), templateImageBo.getTemplateRoi());
            //释放Frame内存
//            originalFrame.close();
//            templateFrame.close();
            return templateImageBo;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); //解决注解失效问题
            throw new IOException(e);
        }
    }
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public TemplateRoiInfo saveTemplateROI(TemplateRoiInfo templateRoiInfo) throws IOException {
        try {
            TestDevice testDevice = testDeviceMapper.selectByUniqueCode(templateRoiInfo.getDeviceUniqueCode());
            TestProject testProject = testProjectMapper.selectByName(templateRoiInfo.getProjectName());
            TestDeviceType testDeviceType = testDeviceTypeMapper.selectByPrimaryKey(testDevice.getTypeId());
            ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(testDevice, testDeviceType.getName(), testProject.getName());
            imageFileManager.saveRoi(templateRoiInfo.getTemplateName(), templateRoiInfo.getTemplateRoi());
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); //解决注解失效问题
            throw new IOException(e);
        }
        return templateRoiInfo;
    }
    /**
     * 更新图像模板
     *
     * @param templatePicture 图像模板
     * @param templateImageBo 图像模板信息
     * @param existBefore     是否存在数据库记录
     * @return
     */
    private TemplatePicture updateTemplatePicture(TemplatePicture templatePicture, TemplateImageBo templateImageBo, boolean existBefore) {
        // 更新Roi（比例）
        PercentTemplateRoi percentTemplateRoi = new PercentTemplateRoi();
        percentTemplateRoi.setTypeId(templateImageBo.getRoiTypeId());
        percentTemplateRoi.setStartX(templateImageBo.getStartX());
        percentTemplateRoi.setStartY(templateImageBo.getStartY());
        percentTemplateRoi.setEndX(templateImageBo.getEndX());
        percentTemplateRoi.setEndY(templateImageBo.getEndY());
        percentTemplateRoi.setTemplatePictureUUID(templatePicture.getTemplatePictureUUID());
        templateRoiService.insertOrUpdate(percentTemplateRoi);

        //更新设备
        TestDevice testDevice = testDeviceMapper.selectByUniqueCode(templateImageBo.getDeviceUniqueCode());
        templatePicture.setDeviceId(testDevice.getId());
        templatePicture.setDeviceTypeId(testDevice.getTypeId());
        templatePicture.setDeviceIndex(1);

        //更新原图
        OriginalPicture op = templateImageBo.getOriginalPicture();
        RectSize size = streamService.getImageSize(testDevice);
        op.setWidth(size.getWidth());
        op.setHeight(size.getHeight());
        OriginalPicture originalPicture = originalPictureService.insertOrUpdate(op);
        log.info("更新原图:{}", originalPicture);
        templatePicture.setOriginalPictureUUID(originalPicture.getUuid());

        //更新项目
        TestProject testProject = testProjectMapper.selectByName(templateImageBo.getProjectName());
        templatePicture.setProjectId(testProject.getId());

        return templatePicture;
    }

    /**
     * 保存图像模板到数据库
     *
     * @param templateImageBo 图像模板信息
     * @return 图像模板
     */
    private TemplatePicture addToDB(TemplateImageBo templateImageBo) {
        log.info("图像模板上传信息:{}", templateImageBo);
        //图片名联合项目名查询
        //TODO：区分设备：android和相机
        TemplatePicture templatePicture = templatePictureMapper.selectByName(
                templateImageBo.getName(),
                templateImageBo.getDeviceUniqueCode(),
                templateImageBo.getProjectName());
        if (templatePicture != null) {
            log.info("已存在数据库记录:{}", templatePicture);
            updateTemplatePicture(templatePicture, templateImageBo, true);
            templatePictureMapper.updateByTemplatePictureUUID(templatePicture);
        } else {
            templatePicture = new TemplatePicture();
            templatePicture.setName(templateImageBo.getName());
            String templatePictureUUID = StrUtils.getSaltMD5(templateImageBo.toString(), StrUtils.generateUUID());
            templatePicture.setTemplatePictureUUID(templatePictureUUID);
            updateTemplatePicture(templatePicture, templateImageBo, false);
            log.info("保存到数据库:{}", templatePicture);
            templatePictureMapper.insert(templatePicture);
        }
        return templatePicture;
    }

    /**
     * 获取图像模板
     *
     * @param templateName     图像模板名
     * @param deviceUniqueCode 设备唯一码
     * @param projectName      项目名
     * @return 图像模板
     */
    @Override
    public TemplatePicture getTemplate(String templateName, String deviceUniqueCode, String projectName) {
        return templatePictureMapper.selectByName(templateName, deviceUniqueCode, projectName);
    }

    /**
     * 获取图像模板
     *
     * @param device       设备
     * @param templateName 图像模板名
     * @param projectName  项目名
     * @return 图像模板
     */
    @Override
    public Frame getTemplateFrame(Device device, String templateName, String projectName) throws FileNotFoundException {
        //TODO： 后续deviceType改为enum
        ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
        return imageFileManager.getTemplateImageFrame(templateName);
    }

    /**
     * 获取图像模板文件
     *
     * @param device       设备
     * @param templateName 图像模板名
     * @param projectName  项目名
     * @return 图像模板文件
     * @throws DeviceUnknownException
     */
    @Override
    public File getTemplateFile(Device device, String templateName, String projectName)  {
        ImageFileManager imageFileManager = ImageFileManager.getImageFileManager(device, projectName);
        return imageFileManager.getTemplateImageFile(templateName);
    }

    @Override
    public List<TemplatePicture> getAllTemplates() {
        return null;
    }

}
