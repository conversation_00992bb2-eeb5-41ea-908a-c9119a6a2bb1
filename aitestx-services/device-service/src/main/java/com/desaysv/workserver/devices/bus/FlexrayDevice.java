package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

/**
 * CAN设备
 */
@Getter
public abstract class FlexrayDevice extends BaseCanDevice implements TestProcessListener {

    public FlexrayDevice() {
        this(new DeviceOperationParameter());
    }

    public FlexrayDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(getDeviceName(), this);
        }
    }

    @Override
    public boolean close() throws DeviceCloseException {
        TestProcessManager.removeTestProcessListener(getDeviceName());
        return true;
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        stopAllFrexRayMessage(null);
    }

    /**
     * 发送报文
     *
     * @param message 报文
     */
    public abstract void send(CanMessage message) throws BusError;

    /**
     * 停发报文
     *
     * @param message 报文
     * @return 是否停发报文成功
     */
    protected abstract void stopSend(CanMessage message);

    protected abstract boolean stopAllFrexRayMessage(Integer deviceChannel);

    protected abstract CyclicTask sendCanMessage(Integer deviceChannel, CanMessage message);

    protected abstract CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId);

    protected abstract CyclicTask stopCanMessage(Integer deviceChannel, String messageId);

}
