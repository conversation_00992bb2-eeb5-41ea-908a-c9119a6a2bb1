package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.IT67Protocol;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ReflectUtils;
import xyz.froud.jvisa.JVisaException;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/15 20:54
 * @description : IT67系列电源
 * @modified By :
 * @since : 2023/5/15
 **/
public class IT67xx extends DefaultPower {

    public IT67xx() {
        this(new DeviceOperationParameter());
    }

    public IT67xx(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setCommProtocol(CommProtocol.USB);
        setPowerProtocol(new IT67Protocol(this));
    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT67xx;
    }

    public static void main(String[] args) throws DeviceOpenException, DeviceCloseException, JVisaException, DeviceReadException {
        //FIXME：测试JVISA报错乱码
        //TODO：测试JVISA 官方highlevel代码
        //TODO：测试JVISA对ASRL13::INSTR RS232的getVoltage()支持
        System.out.println(ReflectUtils.getReflectionByMethodName(IT67xx.class, "fetchVoltage"));
    }
}
