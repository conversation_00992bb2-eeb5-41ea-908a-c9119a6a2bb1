package com.desaysv.workserver.devices.autoclicker;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class UsbHidClicker {
    private static final short VENDOR_ID = 1810;
    private static final short PRODUCT_ID = 8226;
    private HidDeviceStructure hidDeviceStructure;

    public UsbHidClicker() {
    }

    public HidDeviceInfoStructure searchDevices() {
        return HidApi.enumerateDevices(VENDOR_ID, PRODUCT_ID);
    }

    public boolean open() {
        return this.open(null);
    }

    public boolean open(String serialNumber) {
        hidDeviceStructure = HidApi.open(VENDOR_ID, PRODUCT_ID, serialNumber);
        return hidDeviceStructure != null;
    }

    public boolean clickChannel(int channel) {
        return this.clickChannel(channel, 20);
    }

    public boolean clickChannel(int channel, int delay) {
        if (hidDeviceStructure == null) {
            return false;
        }
        if (!channelDown(channel)) {
            return false;
        }
        try {
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        return channelUp(channel);
    }

    public boolean channelDown(int channel) {
        if (hidDeviceStructure == null) {
            return false;
        }
        byte[] channelDown = new byte[]{-111, 1, (byte) (channel - 1)};
        int value = HidApi.write(this.hidDeviceStructure, channelDown, 3, (byte) 0);
        return value != -1;
    }

    public boolean channelUp(int channel) {
        if (hidDeviceStructure == null) {
            return false;
        }
        byte[] channelUp = new byte[]{-111, 0, (byte) (channel - 1)};
        int value = HidApi.write(this.hidDeviceStructure, channelUp, 3, (byte) 0);
        return value != -1;
    }


    public boolean close() {
        if (hidDeviceStructure == null) {
            return false;
        }
        HidApi.close(hidDeviceStructure);
        return true;
    }
}
