package com.desaysv.workserver.devices.bus.canoe.com;

import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.BaseLinDevice;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.interfaces.ILinSequence;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.jacob.com.Dispatch;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;
import static com.desaysv.workserver.utils.StrUtils.hexStrToInt;

public class VectorLin extends BaseLinDevice implements ILinSequence {
    private final VectorShareComponent canApp;
    private final Dispatch namespaces;

    public VectorLin(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        canApp = VectorShareComponent.getInstance();
        canApp.setClose(false);
        namespaces = VectorShareComponent.namespaces;
//        canApp = new ActiveXComponent("CANoe.Application");        // 创建 CANoe Application 对象
//        Dispatch system = Dispatch.call(canApp, "System").toDispatch();  // 获取 CAPL 模块对象
//        namespaces = Dispatch.call(system, "Namespaces").toDispatch();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.VECTOR_LIN;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean setLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) {
        long sTime = System.currentTimeMillis();
        Dispatch netWorksNodesVariables = getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolLinVariables = getVariantDispatch(namespaces, "ILcontrolLin", "Variables");
        putVariant(ILcontrolLinVariables, "ILcontrolLinMsgName", messageName);       // 设置LIN报文名称
        putVariant(ILcontrolLinVariables, "ILcontrolLinSigName", signalName);        // 设置LIN信号名称
        putVariant(ILcontrolLinVariables, "ILcontrolLinSigValue", signalValue);      //设置LIN信号值
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolLinVariables, "ILcontrolLinSigNameCounter");//设置LIN信号名称counter
        boolean result = getResult(netWorksNodesVariables);
        log.info("设置LIN报文信号{}的信号值:{}, 返回结果:{}, 执行时长:{}毫秒", signalName, signalValue, result ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return result;
    }

    @Override
    public boolean setLinSingleMsgControl(Integer deviceChannel, String ecuNodeName, String messageID, int messageStatus) {
        long sTime = System.currentTimeMillis();
        Dispatch netWorksNodesVariables = getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolLinVariables = getVariantDispatch(namespaces, "ILcontrolLin", "Variables");
        putVariant(ILcontrolLinVariables, "ILcontrolLinFrameID", hexStrToInt(messageID));      //设置LIN FrameID
        putVariant(ILcontrolLinVariables, "ILcontrolLinFrameStatus", messageStatus);        // 设置LIN FrameStatus
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolLinVariables, "ILcontrolLinFrameIDCounter");
        boolean result = getResult(netWorksNodesVariables);
        log.info("设置messageID:{}, LINFrame的状态:{}, 执行时长：{} 毫秒, 返回结果:{}", messageID, messageStatus, System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return result;
    }

    @Override
    public boolean setLinAllMsgStatus(String ecuNodeName, int messageStatus) {
        long sTime = System.currentTimeMillis();
        Dispatch netWorksNodesVariables = getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolLinVariables = getVariantDispatch(namespaces, "ILcontrolLin", "Variables");
        putVariant(ILcontrolLinVariables, "ILcontrolLinSchedulerStatus", messageStatus);        // 设置LIN schedulerStatus
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolLinVariables, "ILcontrolLinSchedulerStatusCounter");
        boolean result = getResult(netWorksNodesVariables);
        log.info("设置LinScheduler的状态:{}, 执行时长:{} 毫秒, 返回结果:{}", messageStatus, System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return result;
    }

    @Override
    public boolean setLinChannelMsgStatus(Integer deviceChannel, int status) {
        return false;
    }

    @Override
    public double fetchLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) {
        System.out.println("-------------fetchLinSignal--------");
        Dispatch netWorksNodesVariables = getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolLinVariables = getVariantDispatch(namespaces, "ILcontrolLin", "Variables");
        putVariant(ILcontrolLinVariables, "ILcontrolLinMsgName", messageName);       // 设置LIN报文名称
        putVariant(ILcontrolLinVariables, "ILcontrolLinGetSigName", signalName);        // 设置获取LIN信号名称
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return -1;
        }
        putCounter(ILcontrolLinVariables, "ILcontrolLinGetSigNameCounter");     //设置returnValue为255
        if (getResult(netWorksNodesVariables)) {
            return fetchLatestSystemVariant(ILcontrolLinVariables, "ILcontrolLinGetSigValue", 2000);
        }
        return -1;
    }

    @Override
    public boolean setLinPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        return false;
    }

    @Override
    public boolean sendLinMessage(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        return false;
    }

    @Override
    public boolean setLinWakeUp(Integer deviceChannel, int wakeUpCommand) throws BusError {
        return false;
    }

    @Override
    public String readLinDataByIdHex(Integer deviceChannel, String messageId) {
        return null;
    }

    @Override
    public String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError {
        return null;
    }

    @Override
    public boolean fetchLinMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return false;
    }

    @Override
    public boolean lastCheckLinMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        return false;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }


}
