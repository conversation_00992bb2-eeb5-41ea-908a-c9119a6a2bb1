package com.desaysv.workserver.devices.camera.config;

import lombok.Builder;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/11/25 11:45
 */
@Data
@Builder
public class FailVideoConfig {
    private boolean failVideo;
    private int failVideoTime;
    private int failVideoResolution;
    private String failVideoSavePath;

    public FailVideoConfig() {
        this.failVideo = false;
        this.failVideoTime = 0;
        this.failVideoResolution = 0;
        this.failVideoSavePath = "";
    }
    public FailVideoConfig(boolean failVideo, int failVideoTime, int failVideoResolution, String failVideoSavePath) {
        this.failVideo = failVideo;
        this.failVideoTime = failVideoTime;
        this.failVideoResolution = failVideoResolution;
        this.failVideoSavePath = failVideoSavePath;
    }
}
