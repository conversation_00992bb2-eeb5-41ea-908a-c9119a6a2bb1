package com.desaysv.workserver.devices.bus.zlg;

import com.sun.jna.Pointer;

import java.util.HashMap;
import java.util.Map;
public class ZlgUtil {
    // 1. 私有静态实例（volatile保证可见性）
    private static volatile ZlgUtil instance;

    // 存储设备句柄
    private final Map<String, Pointer> deviceHandleMap = new HashMap<>();

    // 2. 私有构造函数
    private ZlgUtil() {}

    // 3. 全局访问点
    public static ZlgUtil getInstance() {
        if (instance == null) {
            synchronized (ZlgUtil.class) {
                if (instance == null) {
                    instance = new ZlgUtil();
                }
            }
        }
        return instance;
    }

    // 将设备句柄添加到设备管理器中
    public void addDeviceHandle(String key, Pointer handle) {
        deviceHandleMap.put(key, handle);
    }

    // 获取已存在的设备句柄
    public Pointer getDeviceHandle(String key) {
        return deviceHandleMap.get(key);
    }

    // 删除已存在的设备句柄
    public void removeDeviceHandle(String key) {
        deviceHandleMap.remove(key);
    }
}

