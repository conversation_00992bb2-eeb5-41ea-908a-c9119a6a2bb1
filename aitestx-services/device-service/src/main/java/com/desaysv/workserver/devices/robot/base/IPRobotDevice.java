package com.desaysv.workserver.devices.robot.base;

import com.desaysv.workserver.devices.robot.RobotDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 10:49
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
public class IPRobotDevice extends RobotDevice {

    public IPRobotDevice() {

    }

    public IPRobotDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public synchronized boolean open() throws DeviceOpenException {
        return false;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return false;
    }

    @Override
    public String getDeviceModel() {
        return null;
    }
}
