package com.desaysv.workserver.common.port;

import com.desaysv.workserver.screen.config.ScreenConfig;
import lombok.Data;


/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/1/25 20:43
 * @version: 1.0
 */
@Data
public class CheckPointInfo {
    private String touchType;
    private ScreenConfig screenConfig;
    private boolean enableTrace; //检查Trace
    private int fingers;
    private boolean enableCoordinate;//检查xy坐标

    public CheckPointInfo() {
        screenConfig = new ScreenConfig();
    }

}
