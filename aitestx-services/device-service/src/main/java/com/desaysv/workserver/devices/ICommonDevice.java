package com.desaysv.workserver.devices;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;

public interface ICommonDevice {

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).DEVICE_ON_OFF"})
    default ActualExpectedResult deviceOnOff(String deviceName, String command) throws DeviceCloseException {
        return deviceOnOff(deviceName, command);
    }

}
