package com.desaysv.workserver.devices.bus.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CANMessage {

    /**
     * DBCIndex
     */
    private int dbcIndex = 1;
    /**
     * 是否选中
     */
    private boolean isChoice;
    /**
     * 名称
     */
    private String name;
    /**
     * ID
     */
    private String id;
    /**
     * ID(HEX)
     */
    private String idHex;
    /**
     * DLC
     */
    private int dlc;
    /**
     * 注释
     */
    private String remark;
    /**c
     * CAN类型  CAN CANFD CANFD加速
     */
    private String canType;
    /**
     * 除开Event类型都认为是周期报文
     */
    private String sendType;
    /**
     * 是否开启E2E
     */
    private boolean openE2E;
    /**
     * e2e类型
     */
    private String e2eType;
    /**
     * 发送次数
     */
    private int sendCount;
    /**
     * 间隔ms
     */
    private int interval;
    /**
     * 状态
     */
    private String status;

    /**
     * 是否显示（二次面板用）
     */
    private boolean displayFlag;

    private String b1;
    private String b2;
    private String b3;
    private String b4;
    private String b5;
    private String b6;
    private String b7;
    private String b8;
    private String b9;
    private String b10;
    private String b11;
    private String b12;
    private String b13;
    private String b14;
    private String b15;
    private String b16;
    private String b17;
    private String b18;
    private String b19;
    private String b20;
    private String b21;
    private String b22;
    private String b23;
    private String b24;
    private String b25;
    private String b26;
    private String b27;
    private String b28;
    private String b29;
    private String b30;
    private String b31;
    private String b32;
    private String b33;
    private String b34;
    private String b35;
    private String b36;
    private String b37;
    private String b38;
    private String b39;
    private String b40;
    private String b41;
    private String b42;
    private String b43;
    private String b44;
    private String b45;
    private String b46;
    private String b47;
    private String b48;
    private String b49;
    private String b50;
    private String b51;
    private String b52;
    private String b53;
    private String b54;
    private String b55;
    private String b56;
    private String b57;
    private String b58;
    private String b59;
    private String b60;
    private String b61;
    private String b62;
    private String b63;
    private String b64;
    private String b65;
    private String b66;
    private String b67;
    private String b68;
    private String b69;
    private String b70;
    private String b71;
    private String b72;
    private String b73;
    private String b74;
    private String b75;
    private String b76;
    private String b77;
    private String b78;
    private String b79;
    private String b80;
    private String b81;
    private String b82;
    private String b83;
    private String b84;
    private String b85;
    private String b86;
    private String b87;
    private String b88;
    private String b89;
    private String b90;
    private String b91;
    private String b92;
    private String b93;
    private String b94;
    private String b95;
    private String b96;
    private String b97;
    private String b98;
    private String b99;
    private String b100;


    /**
     * Can信号
     */
    private List<CANSignal> canSignalList;

    /**
     * 复用信号标志位
     */
    private boolean isMultiplex;

    /**
     * Can复用信号
     */
    private CANMultiplexSignal canMultiplexSignal;

    /**
     * 信号组
     */
    private List<CANSignalGroup> signalGroups;
    public enum StatusEnum {
        Nominal,
        Sending,
        Stop,
        Failed;

        public static String getChineseStatus(StatusEnum status) {
            switch (status) {
                case Nominal:
                    return "无";
                case Sending:
                    return "正在发送";
                case Stop:
                    return "已停止";
                case Failed:
                    return "错误";
                default:
                    return "未知状态";
            }
        }
    }
}
