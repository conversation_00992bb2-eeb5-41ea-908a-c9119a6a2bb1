package com.desaysv.workserver.devices.testbox;

import com.desaysv.device.testbox.ModbusUtil;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.interfaces.rzcu.IRZCUTestBoardBox;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 10:50
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Slf4j
public class RZCUTestBox extends Device implements IRZCUTestBoardBox {
    private ModbusUtil modbusUtil;
    private static final int BAUD_RATE = 9600;
    private final Map<Integer, SlaveAddressModel> switchAddressChannelMap;
    private final Map<Integer, SlaveAddressModel> dcAddressChannelMap;

    public RZCUTestBox() {
        this(new DeviceOperationParameter());
    }

    public RZCUTestBox(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        switchAddressChannelMap = new HashMap<>();
        switchAddressChannelMap.put(1, new SlaveAddressModel(6, 32));
        switchAddressChannelMap.put(2, new SlaveAddressModel(5, 32));
        switchAddressChannelMap.put(3, new SlaveAddressModel(4, 32));
        switchAddressChannelMap.put(4, new SlaveAddressModel(3, 32));
        switchAddressChannelMap.put(5, new SlaveAddressModel(2, 8));
        switchAddressChannelMap.put(6, new SlaveAddressModel(1, 8));
        switchAddressChannelMap.put(7, new SlaveAddressModel(10, 4));
        dcAddressChannelMap = new HashMap<>();
        dcAddressChannelMap.put(1, new SlaveAddressModel(7, 24));
        dcAddressChannelMap.put(2, new SlaveAddressModel(8, 24));
        dcAddressChannelMap.put(3, new SlaveAddressModel(9, 24));
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_TEST_BOX;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.TestBox.RZCU_TEST_BOX;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil == null) {
            modbusUtil = new ModbusUtil();
        }
        return modbusUtil.openSerialPort(getDevicePortName(), BAUD_RATE);
    }

    @Override
    public boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        return modbusUtil.closeSerialPort();
    }


    @Override
    public boolean writeRelaySwitchBoardCard(Integer slaveAddressKey, Integer channelNumber, String command) throws BoardCardTransportException {
        SlaveAddressModel model = switchAddressChannelMap.get(slaveAddressKey);
        int slaveAddress = model.getSlaveAddress();
        return modbusUtil.controlRelay(slaveAddress, channelNumber, command.equals("ON"));
    }

    @Override
    public boolean writeRelaySwitchAll(int slaveAddressKey, int channelTotal, int value) throws BoardCardTransportException {
        SlaveAddressModel model = switchAddressChannelMap.get(slaveAddressKey);
        int slaveAddress = model.getSlaveAddress();
        if (value == 0) {
            modbusUtil.openAllRelays(slaveAddress, channelTotal);
        } else {
            modbusUtil.closeAllRelays(slaveAddress, channelTotal);
        }
        return true;
    }

    @Override
    public boolean writeRelaySwitchAllAddressChannel(int value) throws BoardCardTransportException {
        for (Map.Entry<Integer, SlaveAddressModel> entry : switchAddressChannelMap.entrySet()) {
            SlaveAddressModel model = entry.getValue();
            int slaveAddress = model.getSlaveAddress();
            int channelTotal = model.getChannel();
            if (value == 1) {
                modbusUtil.openAllRelays(slaveAddress, channelTotal);
            } else {
                modbusUtil.closeAllRelays(slaveAddress, channelTotal);
            }
        }
        return true;
    }

    @Override
    public float fetchCurrent(int slaveAddressKey, Integer channelNumber) throws DeviceReadException, ModbusTransportException {
        SlaveAddressModel model = dcAddressChannelMap.get(slaveAddressKey);
        int slaveAddress = model.getSlaveAddress();
        Map<Integer, Float> currentMap = modbusUtil.readCurrent(slaveAddress);
        if (currentMap == null) return -1;
        return currentMap.get(channelNumber);
    }

    @Override
    public float fetchVoltage(int slaveAddressKey, Integer channelNumber) throws DeviceReadException, ModbusTransportException {
        SlaveAddressModel model = dcAddressChannelMap.get(slaveAddressKey);
        int slaveAddress = model.getSlaveAddress();
        Map<Integer, Float> voltageMap = modbusUtil.readVoltage(slaveAddress);
        if (voltageMap == null) return -1;
        return voltageMap.get(channelNumber);
    }
}
