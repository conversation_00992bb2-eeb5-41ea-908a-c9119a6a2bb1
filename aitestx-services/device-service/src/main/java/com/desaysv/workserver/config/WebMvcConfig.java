package com.desaysv.workserver.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.PathResourceResolver;

import java.time.Duration;

/**
 * Web MVC配置，用于处理静态资源和图片服务
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private static final String IMAGE_BASE_PATH = "D:/FlyTest/data/server/system/images/";

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置图像文件直接访问
        registry.addResourceHandler("/direct-images/**")
                .addResourceLocations("file:" + IMAGE_BASE_PATH)
                .setCacheControl(CacheControl.noCache())
                .resourceChain(false)
                .addResolver(new PathResourceResolver());

        // 配置静态资源访问
        registry.addResourceHandler("/**")
                .addResourceLocations("classpath:/static/")
                .setCacheControl(CacheControl.maxAge(Duration.ofDays(1)));
    }
}
