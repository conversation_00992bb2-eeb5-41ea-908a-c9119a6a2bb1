package com.desaysv.workserver.stream.grabber;

import MvCameraControlWrapper.CameraControlException;
import MvCameraControlWrapper.MvCameraControl;
import MvCameraControlWrapper.MvCameraControlDefines;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;
import org.bytedeco.javacv.OpenCVFrameConverter;
import org.bytedeco.opencv.opencv_core.Mat;
import org.opencv.core.CvType;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantReadWriteLock;

import static MvCameraControlWrapper.MvCameraControlDefines.*;
import static org.bytedeco.opencv.global.opencv_core.CV_8UC3;
import static org.bytedeco.opencv.global.opencv_imgproc.COLOR_RGB2BGR;
import static org.bytedeco.opencv.global.opencv_imgproc.cvtColor;

/**
 * 海康相机硬件接口层
 */
@Slf4j
public class HikCameraLayer {

    @Getter
    private MvCameraControlDefines.Handle cameraHandle;
    @Getter
    private HikAttribute hikAttribute;
    @Getter
    private boolean valid;
    private final Mat displayImageMat;
    private final Mat workerImageMat;
    private final OpenCVFrameConverter.ToMat displayMatConverter;
    private final OpenCVFrameConverter.ToMat workerMatConverter;
    private final ImageBuffer workerImageBuffer;
    private final ImageBuffer displayImageBuffer;
    private final AtomicBoolean isGrabbing = new AtomicBoolean(false);
    private final ReentrantReadWriteLock cameraLock = new ReentrantReadWriteLock();

    // 缓存缓冲区以减少内存分配
    private volatile byte[] cachedByteData;
    private volatile byte[] imageByteBuffer;
    private volatile byte[] rgbByteBuffer;

    static {
        System.setProperty("org.bytedeco.javacpp.logger", "slf4jlogger");
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "debug");
    }

    /**
     * 打印设备信息
     *
     * @param stDeviceInfo
     */
    private static void printDeviceInfo(MvCameraControlDefines.MV_CC_DEVICE_INFO stDeviceInfo) {
        if (null == stDeviceInfo) {
            System.out.println("stDeviceInfo is null");
            return;
        }

        if ((stDeviceInfo.transportLayerType == MV_GIGE_DEVICE) || (stDeviceInfo.transportLayerType == MV_GENTL_GIGE_DEVICE)) {
            System.out.println("\tCurrentIp:       " + stDeviceInfo.gigEInfo.currentIp);
            System.out.println("\tModel:           " + stDeviceInfo.gigEInfo.modelName);
            System.out.println("\tUserDefinedName: " + stDeviceInfo.gigEInfo.userDefinedName);
        } else if (stDeviceInfo.transportLayerType == MV_USB_DEVICE) {
            System.out.println("\tUserDefinedName: " + stDeviceInfo.usb3VInfo.userDefinedName);
            System.out.println("\tSerial Number:   " + stDeviceInfo.usb3VInfo.serialNumber);
            System.out.println("\tDevice Number:   " + stDeviceInfo.usb3VInfo.deviceNumber);
        } else if (stDeviceInfo.transportLayerType == MV_GENTL_CAMERALINK_DEVICE) {
            System.out.println("\tUserDefinedName: " + stDeviceInfo.cmlInfo.userDefinedName);
            System.out.println("\tSerial Number:   " + stDeviceInfo.cmlInfo.serialNumber);
            System.out.println("\tDevice Number:   " + stDeviceInfo.cmlInfo.DeviceID);
        } else if (stDeviceInfo.transportLayerType == MV_GENTL_CXP_DEVICE) {
            System.out.println("\tUserDefinedName: " + stDeviceInfo.cxpInfo.userDefinedName);
            System.out.println("\tSerial Number:   " + stDeviceInfo.cxpInfo.serialNumber);
            System.out.println("\tDevice Number:   " + stDeviceInfo.cxpInfo.DeviceID);
        } else if (stDeviceInfo.transportLayerType == MV_GENTL_XOF_DEVICE) {
            System.out.println("\tUserDefinedName: " + stDeviceInfo.xofInfo.userDefinedName);
            System.out.println("\tSerial Number:   " + stDeviceInfo.xofInfo.serialNumber);
            System.out.println("\tDevice Number:   " + stDeviceInfo.xofInfo.DeviceID);
        } else {
            System.err.print("Device is not supported! \n");
        }

        System.out.println("\tAccessible:      "
                + MvCameraControl.MV_CC_IsDeviceAccessible(stDeviceInfo, MV_ACCESS_Exclusive));
        System.out.println();
    }

    public HikCameraLayer(int devicePort) {
        displayMatConverter = new OpenCVFrameConverter.ToMat(); //已加入资源释放
        workerMatConverter = new OpenCVFrameConverter.ToMat(); //已加入资源释放
        displayImageMat = new Mat(); //已加入资源释放
        workerImageMat = new Mat(); //已加入资源释放
        workerImageBuffer = new ImageBuffer();
        displayImageBuffer = new ImageBuffer();
        //创建句柄
        try {
            cameraHandle = createHandle(HikEnumDevices.enumDevices().get(devicePort));
            hikAttribute = new HikAttribute(cameraHandle);
            valid = true;
        } catch (CameraControlException e) {
            log.error(e.getMessage(), e);
            valid = false;
        }
    }

    /**
     * 解析海康相机错误码
     *
     * @param functionName 接口名称
     * @param nRet         错误码
     * @return 错误码
     */
    private int explainErrorCode(String functionName, int nRet) {
        if (MV_OK != nRet) {
            log.error("{}接口执行失败，错误码: [0X{}]\n", functionName, String.format("%X", nRet));
        }
        return nRet;
    }

    /**
     * 创建海康相机句柄
     *
     * @param deviceInfo
     * @return
     * @throws CameraControlException
     */
    public MvCameraControlDefines.Handle createHandle(MvCameraControlDefines.MV_CC_DEVICE_INFO deviceInfo) throws CameraControlException {
        return MvCameraControl.MV_CC_CreateHandle(deviceInfo);
    }

    /**
     * 打开海康相机
     *
     * @return
     */
    public synchronized boolean open() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            // Open device
            int nRet = MvCameraControl.MV_CC_OpenDevice(cameraHandle);
            explainErrorCode("MV_CC_OpenDevice", nRet);
            return nRet == MV_OK;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }

    /**
     * 关闭海康相机
     *
     * @return
     */
    public void close() {
        try {
            stopGrabbing();
            closeCamera();
            destroyHandle();
        } catch (Exception e) {
            log.error("关闭海康相机失败", e);
        } finally {
            releaseResources();
        }
    }

    /**
     * 释放资源
     */
    private void releaseResources() {
        // 释放Mat转换器资源
        displayMatConverter.close();
        workerMatConverter.close();
        // 释放Mat资源
        displayImageMat.close();
        workerImageMat.close();
        // 清理缓冲区
        imageByteBuffer = null;
        rgbByteBuffer = null;
        cachedByteData = null;
    }

    /**
     * 关闭相机
     *
     * @return
     */
    private boolean closeCamera() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            // 先停止采集
            if (isGrabbing.get()) {
                stopGrabbing();
            }
            // 关闭设备
            int nRet = MvCameraControl.MV_CC_CloseDevice(cameraHandle);
            if (MV_OK != nRet) {
                log.error("关闭海康相机失败，错误码: [0x{}]", String.format("%X", nRet));
                return false;
            }
            log.info("海康相机关闭成功");
            return true;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }

    /**
     * 销毁句柄
     *
     * @return
     */
    private boolean destroyHandle() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            // 销毁句柄
            int nRet = MvCameraControl.MV_CC_DestroyHandle(cameraHandle);
            if (MV_OK != nRet) {
                log.error("释放海康相机句柄失败，错误码: [0x{}]", String.format("%X", nRet));
                return false;
            }
            cameraHandle = null;
            valid = false;
            log.info("海康相机句柄释放成功");
            return true;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }

    /**
     * 配置海康相机
     *
     * @return
     */
    public boolean config() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            int nRet1 = MvCameraControl.MV_CC_SetEnumValueByString(cameraHandle, "AcquisitionMode", "Continuous");
            explainErrorCode("MV_CC_SetEnumValueByString AcquisitionMode", nRet1);
            // Make sure that trigger mode is off
            int nRet2 = MvCameraControl.MV_CC_SetEnumValueByString(cameraHandle, "TriggerMode", "Off");
            explainErrorCode("MV_CC_SetEnumValueByString TriggerMode", nRet2);
            return nRet1 == MV_OK && nRet2 == MV_OK;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }


    /**
     * 开启海康相机采流
     *
     * @return
     */
    public synchronized boolean startGrabbing() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        if (isGrabbing.get()) {
            log.warn("相机已经在捕捉图像了。在开始新的捕捉之前，将停止当前的捕捉。");
            stopGrabbing();
        }
        cameraLock.writeLock().lock();
        try {
            int nRet = MvCameraControl.MV_CC_StartGrabbing(cameraHandle);
            explainErrorCode("MV_CC_StartGrabbing", nRet);
            boolean isOk = nRet == MV_OK;
            if (isOk) {
                isGrabbing.set(true);
            }
            return isOk;
        } finally {
            cameraLock.writeLock().unlock();
        }

    }

    /**
     * 停止采流
     *
     * @return
     */
    public synchronized boolean stopGrabbing() {
        if (!isValid() || cameraHandle == null) {
            return false;
        }
        if (!isGrabbing.get()) {
            log.warn("相机当前没有在捕捉图像");
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            int nRet = MvCameraControl.MV_CC_StopGrabbing(cameraHandle);
            explainErrorCode("MV_CC_StopGrabbing", nRet);
            boolean isOk = nRet == MV_OK;
            if (isOk) {
                isGrabbing.set(false);
            }
            return isOk;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }

    @Deprecated
    public Frame grabFrameBuffer() {
        // Get payload size
        MvCameraControlDefines.MVCC_INTVALUE stParam = new MvCameraControlDefines.MVCC_INTVALUE();
        int nRet = MvCameraControl.MV_CC_GetIntValue(cameraHandle, "PayloadSize", stParam);
        explainErrorCode("MV_CC_GetIntValue PayloadSize", nRet);

        MvCameraControlDefines.MV_FRAME_OUT mvFrameOut = new MvCameraControlDefines.MV_FRAME_OUT();
        mvFrameOut.buffer = new byte[(int) stParam.curValue];
        nRet = MvCameraControl.MV_CC_GetImageBuffer(cameraHandle, mvFrameOut, 1000);
        explainErrorCode("MV_CC_GetImageBuffer", nRet);
        try (Mat image = new Mat(mvFrameOut.mvFrameOutInfo.height, mvFrameOut.mvFrameOutInfo.width, CV_8UC3)) {
            image.data().put(mvFrameOut.buffer);
            cvtColor(image, image, COLOR_RGB2BGR); // RGB转BGR
            Frame frame = Java2DFrameUtils.toFrame(image);
            MvCameraControl.MV_CC_FreeImageBuffer(cameraHandle, mvFrameOut);
            return frame;
        }
    }

    /**
     * 打印帧信息
     *
     * @param stFrameInfo
     */
    private static void printFrameInfo(MV_FRAME_OUT_INFO stFrameInfo) {
        if (null == stFrameInfo) {
            System.err.println("stFrameInfo is null");
            return;
        }

        String frameInfo = ("\tFrameNum[" + stFrameInfo.frameNum + "]") +
                "\tWidth[" + stFrameInfo.width + "]" +
                "\tHeight[" + stFrameInfo.height + "]" +
                String.format("\tPixelType[%#x]", stFrameInfo.pixelType.getnValue());

        System.out.println(frameInfo);
    }

    /**
     * 获取或者创建图像缓存
     *
     * @param size
     * @return
     */
    private byte[] getOrCreateImageBuffer(int size) {
        if (imageByteBuffer == null || imageByteBuffer.length < size) {
            imageByteBuffer = new byte[size];
        }
        return imageByteBuffer;
    }

    /**
     * 获取或者创建图像字节数组
     *
     * @param size
     * @return
     */
    private byte[] getOrCreateByteData(int size) {
        if (cachedByteData == null || cachedByteData.length < size) {
            // 如果缓存不存在或大小不够，创建新的数组
            cachedByteData = new byte[size];
        }
        return cachedByteData;
    }

    /**
     * 获取或者创建图像RGB缓存
     *
     * @param rgbSize
     * @return
     */
    private byte[] getOrCreateRgbBuffer(int rgbSize) {
        if (rgbByteBuffer == null || rgbByteBuffer.length < rgbSize) {
            rgbByteBuffer = new byte[rgbSize];
        }
        return rgbByteBuffer;
    }

    /**
     * 获取图像缓存
     *
     * @param postProcessing 是否为图像识别线程
     * @return
     */
    // postProcessing = true表示图像识别
    public ImageBuffer grabImageBuffer(boolean postProcessing) throws FrameGrabberException {
        ImageBuffer imageBuffer = postProcessing ? workerImageBuffer : displayImageBuffer;
        if (!isValid() || cameraHandle == null || !isGrabbing.get()) {
            throw new FrameGrabberException("海康相机句柄报错或者未开启采集接口");
        }

        cameraLock.readLock().lock();
        try {
            // 获取图像大小
            MvCameraControlDefines.MVCC_INTVALUE stParam = new MvCameraControlDefines.MVCC_INTVALUE();
            int nRet = MvCameraControl.MV_CC_GetIntValue(cameraHandle, "PayloadSize", stParam);
            int errorCode = explainErrorCode("MV_CC_GetIntValue PayloadSize", nRet);
            if (errorCode != MV_OK) {
                throw new FrameGrabberException(String.format("海康相机获取相机大小报错，MV_CC_GetIntValue错误码:%d", errorCode));
            }
            // 安全地获取或创建缓冲区
            byte[] currentImageBuffer = getOrCreateImageBuffer((int) stParam.curValue);

            //Get one frame
            //超时
            //获取指定像素格式的帧数据，获取帧数据时上层应用程序需要根据帧率控制好调用该接口的频率
            //采用超时机制获取一帧图片，SDK内部等待直到有数据时返回
            //图像信息结构体
            int timeout = 3000;
            MvCameraControlDefines.MV_FRAME_OUT_INFO stImageInfo = new MvCameraControlDefines.MV_FRAME_OUT_INFO();
            nRet = MvCameraControl.MV_CC_GetOneFrameTimeout(cameraHandle, currentImageBuffer, stImageInfo, timeout);
            errorCode = explainErrorCode("MV_CC_GetOneFrameTimeout", nRet);
            if (errorCode != MV_OK) {
                throw new FrameGrabberException(String.format("海康相机获取图像超时，MV_CC_GetOneFrameTimeout错误码:%d", errorCode));
            }
            imageBuffer.setWidth(stImageInfo.width);
            imageBuffer.setHeight(stImageInfo.height);

            if (stImageInfo.height <= 0 || stImageInfo.width <= 0) {
                throw new FrameGrabberException(String.format("海康相机获取图像大小不正确:%dx%d", stImageInfo.width, stImageInfo.height));
            }
            //转换bayer GB格式
            int dataSizeForRGB = stImageInfo.width * stImageInfo.height * 3; // every RGB pixel takes 3 bytes
            byte[] currentRgbBuffer = getOrCreateRgbBuffer(dataSizeForRGB);
            // Convert pixel type to RGB8_Packed
            MvCameraControlDefines.MV_CC_PIXEL_CONVERT_PARAM_EX stConvertParam = new MvCameraControlDefines.MV_CC_PIXEL_CONVERT_PARAM_EX();
            stConvertParam.width = stImageInfo.ExtendWidth; // image width
            stConvertParam.height = stImageInfo.ExtendHeight; // image height
            stConvertParam.srcData = currentImageBuffer; // input buffer
            stConvertParam.srcDataLen = stImageInfo.frameLen; // input buffer size
            stConvertParam.srcPixelType = stImageInfo.pixelType; // input pixel format
            stConvertParam.dstPixelType = MvCameraControlDefines.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed; // output pixel format
            stConvertParam.dstBuffer = currentRgbBuffer; // output buffer
            stConvertParam.dstBufferSize = dataSizeForRGB; // output buffer size

            nRet = MvCameraControl.MV_CC_ConvertPixelTypeEx(cameraHandle, stConvertParam);
            errorCode = explainErrorCode("MV_CC_ConvertPixelType", nRet);
            if (errorCode != MV_OK) {
                throw new FrameGrabberException(String.format("海康相机转换图像格式错误，MV_CC_ConvertPixelTypeEx错误码:%d", errorCode));
            }

            //限制对sharedImageMat多线程访问
            Mat imageMat = postProcessing ? workerImageMat : displayImageMat;
            //调用 release() 方法并不会释放 Mat 对象本身所占的内存空间，只会释放其内部所保存的数据和缓存空间
            int width = stConvertParam.width;
            int height = stConvertParam.height;
            int channels = currentRgbBuffer.length / (width * height);
            imageMat.create(height, width, CvType.CV_8UC(channels));
            //使用clone，不会直接修改原始的pData数据。这样可以保证数据的独立性和安全性，避免潜在的问题。
            imageMat.data().put(currentRgbBuffer);
            cvtColor(imageMat, imageMat, COLOR_RGB2BGR); // RGB转BGR

//            if (!postProcessing) {
//                //显示线程使用
//                int byteSize = (int) (imageMat.total() * imageMat.elemSize());
//                byte[] byteData = getOrCreateByteData(byteSize);
//                imageMat.data().get(byteData);
//                imageBuffer.setData(byteData);
//                log.info("byteData:{}", System.identityHashCode(byteData));
//            }

            Frame frame = postProcessing ? workerMatConverter.convert(imageMat) : displayMatConverter.convert(imageMat);
            imageBuffer.setFrame(frame);
//            log.info("frame:{}", System.identityHashCode(frame));
//        if (postProcessing) {
//            frame = recognizedMatConverter.convert(imageMat);
//            // 这种方式产生不同的Frame内存
//            // frame = Java2DFrameUtils.toFrame(imageMat);
//        } else {
//            //这种方式复用Frame内存
//            frame = streamMatConverter.convert(imageMat);
//        }
            return imageBuffer;
        } finally {
            cameraLock.readLock().unlock();
        }
    }

    /**
     * 保存图像
     *
     * @param stImageInfo
     * @param pData
     */
    private void saveImage(MvCameraControlDefines.MV_FRAME_OUT_INFO stImageInfo, byte[] pData) {
        int imageLength = stImageInfo.width * stImageInfo.height * 3;    // Every RGB pixel takes 3 bytes
        byte[] imageBuffer = new byte[imageLength];
        // Call MV_CC_SaveImage to save image as JPEG
        MvCameraControlDefines.MV_SAVE_IMAGE_PARAM_EX3 stSaveParam = new MvCameraControlDefines.MV_SAVE_IMAGE_PARAM_EX3();
        stSaveParam.width = stImageInfo.width;                                  // image width
        stSaveParam.height = stImageInfo.height;                                // image height
        stSaveParam.data = pData;                                               // image data
        stSaveParam.dataLen = stImageInfo.frameLen;                             // image data length
        stSaveParam.pixelType = stImageInfo.pixelType;                          // image pixel format
        stSaveParam.imageBuffer = imageBuffer;                                  // output image buffer
        stSaveParam.imageType = MvCameraControlDefines.MV_SAVE_IAMGE_TYPE.MV_Image_Jpeg;               // output image pixel format
        stSaveParam.methodValue = 0;                                            // Interpolation method that converts Bayer format to RGB24.  0-Neareast 1-double linear 2-Hamilton
        stSaveParam.jpgQuality = 60;                                            // JPG endoding quality(50-99]
        cameraLock.writeLock().lock();
        try {
            int nRet = MvCameraControl.MV_CC_SaveImageEx3(cameraHandle, stSaveParam);
            if (MV_OK != nRet) {
                log.error("报错海康相机图片失败，错误码: [0x{}]", String.format("%X", nRet));
            }
        } finally {
            cameraLock.writeLock().unlock();
        }
        // Save buffer content to file
        saveDataToFile(imageBuffer, stSaveParam.imageLen, "SaveImage.jpeg");
    }

    /**
     * 保存数据到文件
     *
     * @param dataToSave
     * @param dataSize
     * @param fileName
     */
    private static void saveDataToFile(byte[] dataToSave, int dataSize, String fileName) {
        try (OutputStream os = Files.newOutputStream(Paths.get(fileName))) {
            os.write(dataToSave, 0, dataSize);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 注册图像获取回调
     *
     * @return
     */
    public boolean registerImageCallback() {
        if (cameraHandle == null) {
            return false;
        }
        cameraLock.writeLock().lock();
        try {
            int nRet = MvCameraControl.MV_CC_RegisterImageCallBack(cameraHandle, (bytes, stImageInfo) -> {
                int imageLen = stImageInfo.width * stImageInfo.height * 3;    // Every RGB pixel takes 3 bytes
                byte[] imageBuffer = new byte[imageLen];

                // Call MV_CC_SaveImage to save image as JPEG
                MvCameraControlDefines.MV_SAVE_IMAGE_PARAM_EX3 stSaveParam = new MvCameraControlDefines.MV_SAVE_IMAGE_PARAM_EX3();
                stSaveParam.width = stImageInfo.width;                                  // image width
                stSaveParam.height = stImageInfo.height;                                // image height
                stSaveParam.data = bytes;                                               // image data
                stSaveParam.dataLen = stImageInfo.frameLen;                             // image data length
                stSaveParam.pixelType = stImageInfo.pixelType;                          // image pixel format
                stSaveParam.imageBuffer = imageBuffer;                                  // output image buffer
                stSaveParam.imageType = MvCameraControlDefines.MV_SAVE_IAMGE_TYPE.MV_Image_Jpeg;               // output image pixel format
                stSaveParam.methodValue = 0;                                            // Interpolation method that converts Bayer format to RGB24.  0-Neareast 1-double linear 2-Hamilton
                stSaveParam.jpgQuality = 60;                                            // JPG endoding quality(50-99]

                int nRet1 = MvCameraControl.MV_CC_SaveImageEx3(cameraHandle, stSaveParam);
                explainErrorCode("MV_CC_SaveImage", nRet1);
                ByteArrayInputStream bais = new ByteArrayInputStream(bytes);
//                    saveDataToFile(imageBuffer, stSaveParam.imageLen, "SaveImage.jpeg");
//                    Frame frame = new AndroidFrameConverter().convert(imageBuffer, stImageInfo.width, stImageInfo.height);
//                    System.out.println("frame:" + frame);
                try {
                    BufferedImage bufferedImage = ImageIO.read(bais);
                    Frame frame = Java2DFrameUtils.toFrame(bufferedImage);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                    return MV_E_ABNORMAL_IMAGE;
                }
                return MV_OK;
            });
            explainErrorCode("MV_CC_RegisterImageCallBack", nRet);
            return nRet == MV_OK;
        } finally {
            cameraLock.writeLock().unlock();
        }
    }


    public static void main(String[] args) throws InterruptedException {
        Mat m = new Mat();
        m.create(3648, 5472, CV_8UC3);
        byte[] data = new byte[5472 * 3648 * 3];
        byte[] byteData = new byte[5472 * 3648 * 3];
        for (int i = 0; i < data.length; i++) {
            data[i] = (byte) new Random().nextInt(255);
        }
        try (OpenCVFrameConverter.ToMat toMat = new OpenCVFrameConverter.ToMat()) {
            for (int i = 0; i < 1000; i++) {
                m.data().put(data);
                m.data().get(byteData);
                Frame frame = toMat.convert(m);
                System.out.println("frame:" + frame);
                TimeUnit.MILLISECONDS.sleep(300);
            }
        }
    }

}
