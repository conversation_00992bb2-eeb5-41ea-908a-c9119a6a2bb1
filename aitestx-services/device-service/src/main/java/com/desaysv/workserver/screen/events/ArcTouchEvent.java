package com.desaysv.workserver.screen.events;

import com.desaysv.workserver.screen.entity.PreciousPoint3D;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ArcTouchEvent extends TouchEvent {

    private PreciousPoint3D centerPoint;
    private BigDecimal radius;
    private double segmentDegree;

}
