package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.N6700Protocol;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * N6700高精度电源
 */
@Slf4j
public class N6700 extends DefaultPower {
    public N6700() {
        this(new DeviceOperationParameter());
    }

    public N6700(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setCommProtocol(CommProtocol.USB);
        setPowerProtocol(new N6700Protocol(this));
    }

    @Override
    public int getNumberChannels() {
        return 2;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.N6700;
    }

}
