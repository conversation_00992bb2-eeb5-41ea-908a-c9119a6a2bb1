package com.desaysv.workserver.devices.bus.base.frexray;

import com.desaysv.workserver.entity.BaseMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * FlexrayMessage报文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlexrayMessage extends BaseMessage {
    private Float timestamp; // 时间戳

    private int slotId; // 报文id

    private Integer channel; // 通道

    private Integer channelMask; // 通道掩码

    private Integer offest;//cycle offset

    private Integer repetitior;//cycle repetition

    private int dlc; // 数据长度

    private byte[] data; // 报文数据

    private int sendTimes; // 发送次数

    private Float duration; // 秒

    private float period; // 秒

    private int framesPerSendNum = 1; //每次发送帧数

    public void setDuration(Float duration) {
        if (duration == null || duration < 0) {
            this.duration = null;
        } else {
            this.duration = duration;
        }
    }

    public FlexrayMessage() {
    }

    public FlexrayMessage(float timestamp, int slotId, Integer channel, Integer channelMask,
                          Integer offest, Integer repetitior, int dlc, byte[] data, int sendTimes, Float period, int framesPerSendNum) {
        this.timestamp = timestamp;
        this.slotId = slotId;
        this.channel = channel;
        this.channelMask = channelMask;
        this.offest = offest;
        this.repetitior = repetitior;
        this.dlc = dlc;
        this.data = data;
        this.sendTimes = sendTimes;
        this.period = period;
        this.framesPerSendNum = framesPerSendNum;
    }


    public int length() {
        return dlc;
    }

    public String getHexId() {
        return String.format("ID: %04X", slotId);
    }

}
