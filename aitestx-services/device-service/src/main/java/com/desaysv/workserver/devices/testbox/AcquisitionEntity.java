package com.desaysv.workserver.devices.testbox;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/8/12 13:33
 * @description :
 * @modified By :
 * @since : 2023/8/12
 **/
@Data
public class AcquisitionEntity {

    private List<Float> voltageAcquisitionOne;
    private List<Float> voltageAcquisitionTwo;
    private List<Float> voltageAcquisitionThree;

    public List<Float> getVoltage(int boardCardNumber) {
        if (boardCardNumber == 1) {
            return voltageAcquisitionOne;
        } else if (boardCardNumber == 2) {
            return voltageAcquisitionTwo;
        } else if (boardCardNumber == 3) {
            return voltageAcquisitionThree;
        } else {
            return new ArrayList<>();
        }

    }
}
