package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLCANFDMessage64 extends Structure {
    public VBLObjectHeader mHeader;
    /**
     * Channel the frame was sent or received.
     */
    public byte mChannel;
    /**
     * Data length code of the frame.
     * DLC Data length in bytes
     * CAN CAN FD
     * 0-8 0-8 0-8
     * 9 8 12
     * 10 8 16
     * 11 8 20
     * 12 8 24
     * 13 8 32
     * 14 8 48
     * 15 8 64
     */
    public byte mDLC;
    /**
     * Valid payload length of mData. The
     * value is 0, if the message was an CAN remote frame.
     */
    public byte mValidDataBytes;
    /**
     * Bits 0 – 3: Number of required
     * transmission attempts
     * Bits 4 – 7: Max number of transmission
     * attempts.
     */
    public byte mTxCount;
    /**
     * Frame identifier.
     */
    public int mID;
    /**
     * Message duration [in ns]. Not including 3
     * interframe-space bit times and by Rxmessages
     * also not including one end-offrame
     * bit time
     */
    public int mFrameLength;
    /**
     * Bit# Meaning
     * 0 (0x0001) Must be 0
     * 1 (0x0002) Reserved, for internal use
     * 2 (0x0004) 1=NERR (1=single wire on low speed CAN)
     * 3 (0x0008) 1=High voltage wake up
     * 4 (0x0010) 1=Remote frame (onlyCAN)
     * 5 (0x0020) Reserved, must be 0
     * 6 (0x0040) 1= Tx Acknowledge
     * 7 (0x0080) 1= Tx Request
     * 8 (0x0100) Reserved, must be 0
     * 9 (0x0200) SRR (CAN FD)
     * 10 (0x0400) R0
     * 11 (0x0800) R1
     * 12 (0x1000) FDF 0: CAN frame 1: CAN FD frame
     * 13 (0x2000) BRS (CAN FD)
     * 14 (0x4000) ESI
     * 15 (0x8000) Reserved, must be 0
     * 16 (0x10000) Reserved, must be 0
     * 17 (0x20000) 1= Frame is part of a burst
     * 18 (0x40000) Single shot mode:Frame could not be transmitted
     * 19 (0x80000) Single shot mode: If bit 18 is set to 1, then this bit reports the Reason.0 = arbitration lost, 1= frame disturbed
     * 20 (0x100000) Reserved, for internal use
     * 21 (0x200000) Reserved for internal
     * 22-31 Reserved, must be 0
     */
    public int mFlags;
    /**
     * CAN- or CAN-FD bit timing
     * configuration for arbitration phase, may
     * be 0, if not supported by hardware/driver
     * Bit 0-7: Quartz frequency im MHz
     * Bit 8-15: Prescaler
     * Bit 16-23: # of time quanta per bit
     * Bit 24-31: Sampling point in percent
     */
    public int mBtrCfgArb;
    /**
     * CAN-FD bit timing configuration for
     * data phase, may be 0, if not supported by
     * hardware/driver. See mBtrCfgArb.
     */
    public int mBtrCfgData;
    /**
     * Time offset of the sampling point of BRS
     * in nanoseconds
     */
    public int mTimeOffsetBrsNs;
    /**
     * Time offset of the sampling point of CRC
     * delimiter in nanoseconds
     */
    public int mTimeOffsetCRCDelNs;
    /**
     * Bit count of the message, exclusive stuff
     * bits.
     */
    public byte mBitCount;
    /**
     * Direction of the message
     */
    public byte mDir;
    /**
     * Offset of the extended event data. Use the
     * macros ‘BLHasExtFrameData’ and
     * ‘BLExtFrameDataPtr’ to get a pointer to
     * mExtFrameData. See example below.
     */
    public byte mExtDataOffset;
    /**
     * CRC of the message.
     * For CAN FD ISO-frames the stuff count
     * and additional flags are stored in the
     * field:
     * Bits Meaning
     * 0-20 CRC
     * 21-26 Reserved, must be 0
     * 27-29 Stuff count field
     * 30 Stuff count field parity
     * 31 ISO format. If set to 1, then the message is
     * in CAN FD ISO format, and the stuff
     * count is valid.
     */
    public int mCRC;
    /**
     * Data bytes of the message. The array size
     * is set to the frame’s data length stored in
     * mValidDataBytes. The maximum value
     * is 64.
     */
    public byte[] mData = new byte[64];  // 根据 mValidDataBytes 调整大小，最大值为 64
     /**
      * The extended format is:
      * Bit 0 – 7: TSEG1-1
      * Bit 8 – 15: TSEG2-1
      * Bit 16 – 27: Prescaler
      * Bit 28 – 31: Quartz Frequency (enumeration). Supported values: 0: 16 MHz, 1: 32 MHz, 2: 80
      * MHz
     */
     public VBLCANFDExtFrameData mExtFrameData;

    public VBLCANFDMessage64(){
        super();
    }
    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mHeader", "mChannel", "mDLC", "mValidDataBytes", "mTxCount", "mID",
                             "mFrameLength", "mFlags", "mBtrCfgArb", "mBtrCfgData", "mTimeOffsetBrsNs", 
                             "mTimeOffsetCRCDelNs", "mBitCount", "mDir", "mExtDataOffset", "mCRC", "mData", 
                             "mExtFrameData");
    }
}
