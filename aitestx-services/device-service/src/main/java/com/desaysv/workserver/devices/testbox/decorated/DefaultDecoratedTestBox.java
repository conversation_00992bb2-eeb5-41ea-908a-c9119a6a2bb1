package com.desaysv.workserver.devices.testbox.decorated;

import com.desaysv.workserver.devices.testbox.PWMEntity;
import com.desaysv.workserver.devices.testbox.TestBox;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/9/7 18:20
 * @description :
 * @modified By :
 * @since : 2023/9/7
 **/
@Slf4j
public class DefaultDecoratedTestBox extends TestBox {

    public DefaultDecoratedTestBox(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public boolean writePWMOutputBoardCard(Integer deviceChannel, PWMEntity pwmEntity) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        long sTime = System.currentTimeMillis();
        super.writePWMOutputBoardCard(deviceChannel, pwmEntity);
        PWMEntity pwmEntityFromRead = fetchPWMOutputBoardCardByChannel(deviceChannel);
        long eTime = System.currentTimeMillis();
        log.info("PWM输出总执行时长：{} 毫秒.", eTime - sTime);
        return pwmEntity.equals(pwmEntityFromRead);
    }

    @Override
    public boolean writeRelaySwitchBoardCard(Integer deviceChannel, int relayValue) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        long sTime = System.currentTimeMillis();
        super.writeRelaySwitchBoardCard(deviceChannel, relayValue);
        Integer relayValueFromRead = fetchRelayBoardCardByChannel(deviceChannel);
        long eTime = System.currentTimeMillis();
        log.info("继电器板卡总执行时长：{} 毫秒.", eTime - sTime);
        return relayValue == relayValueFromRead;
    }

    @Override
    public boolean writeResistanceBoardCard(Integer deviceChannel, int resistance) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        long sTime = System.currentTimeMillis();
        super.writeResistanceBoardCard(deviceChannel, resistance);
        int resistanceFromRead = fetchResistorByChannel(deviceChannel);
        long eTime = System.currentTimeMillis();
        log.info("电阻板卡总执行时长：{} 毫秒.", eTime - sTime);
        return resistance == resistanceFromRead;
    }

    @Override
    public boolean writeTriStateOutputBoardCard(Integer deviceChannel, int state) throws BoardCardTransportException {
        if (isSimulated()) {
            return true;
        }
        long sTime = System.currentTimeMillis();
        super.writeTriStateOutputBoardCard(deviceChannel, state);
        int stateFromRead = fetchTriStateOutputByChannel(deviceChannel);
        long eTime = System.currentTimeMillis();
        log.info("三态输出板卡总执行时长：{} 毫秒.", eTime - sTime);
        return state == stateFromRead;
    }

}
