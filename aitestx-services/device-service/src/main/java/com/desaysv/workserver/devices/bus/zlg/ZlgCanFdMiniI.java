package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ZCAN_USBCANFD_Mini_I底层接口
 */
@Slf4j
public class ZlgCanFdMiniI extends ZlgCan {

    private final ZlgCanAbstractionLayer can;

    public ZlgCanFdMiniI(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        can = new ZlgCanAbstractionLayer();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.ZLG_USBCAN_I_MINI;
    }

    @Override
    public List<Integer> fetchRunningCanMessage(Integer deviceChannel) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if("软件发送".equals(deviceConfig.getSendMode())){
            return super.fetchRunningCanMessage(deviceChannel);
        }else{
            Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
            if (channelMap != null) {
                List<Integer> result = new ArrayList<>();
                for (Map.Entry<Integer, Object> entry : channelMap.entrySet()) {
                    if (entry.getValue() instanceof ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) {
                        ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ) entry.getValue();
                        if (obj.enable == 1) {
                            result.add(entry.getKey());
                        }
                    } else if (entry.getValue() instanceof ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) {
                        ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ obj = (ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ) entry.getValue();
                        if (obj.enable == 1) {
                            result.add(entry.getKey());
                        }
                    }
                }
                return result;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if("软件发送".equals(deviceConfig.getSendMode())){
            super.sendPeriodic(message, period, duration);
        }else{
            can.sendPeriod(getDeviceHandle(), message);
        }
        return null;
    }

    @Override
    public CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if("软件发送".equals(deviceConfig.getSendMode())){
            super.stopCanMessage(deviceChannel, messageId);
        }else{
            ZlgApi.stopCanPeriodMessage(getDeviceHandle(),deviceChannel,messageId);
        }
        log.info("{}停止CAN通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%X", messageId));
        return null;
    }

    @Override
    public boolean stopAllCanMessage(Integer deviceChannel) {
        CanConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if("软件发送".equals(deviceConfig.getSendMode())){
            return super.stopAllCanMessage(deviceChannel);
        }else{
            Map<Integer, Object> channelMap = ZlgApi.getMap().get(deviceChannel);
            if (channelMap != null) {
                for (Map.Entry<Integer, Object> entry : channelMap.entrySet()) {
                    ZlgApi.stopCanPeriodMessage(getDeviceHandle(),deviceChannel,entry.getKey());
                }
                return true;
            }
        }
        log.info("{}停止通道{}所有周期报文", getDeviceName(), deviceChannel);
        return false;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        boolean isSendOk;
        if (timeout != null) {
            //阻塞发送
            isSendOk = can.blockingSend(message, timeout);
        } else {
            //正常发送
            isSendOk = can.send(getChannelHandle(message.getChannel()), message);
        }

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }


    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }
}
