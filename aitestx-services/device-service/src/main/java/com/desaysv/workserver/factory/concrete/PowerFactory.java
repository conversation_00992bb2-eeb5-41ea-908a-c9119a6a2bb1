package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.*;
import com.desaysv.workserver.devices.power.kikusui.Kikusui;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractPowerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-17 10:31
 * @description : 电源工厂
 * @modified By :
 * @since : 2022-3-17
 */
@Component
@Lazy
public class PowerFactory implements AbstractPowerFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        //TODO：使用设计模式，去掉switch
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Power.IT68xx:
                return createIT68xx(deviceRegisterForm);
            case DeviceModel.Power.IT6322:
                return createIT6322(deviceRegisterForm);
            case DeviceModel.Power.IT69xx:
                return createIT69xx(deviceRegisterForm);
            case DeviceModel.Power.IT63xx:
                return createIT63xx(deviceRegisterForm);
            case DeviceModel.Power.IT65xx:
                return createIT65xx(deviceRegisterForm);
            case DeviceModel.Power.IT67xx:
                return createIT67xx(deviceRegisterForm);
            case DeviceModel.Power.POWER_BOX:
                return createPowerBox(deviceRegisterForm);
            case DeviceModel.Power.KIKUSUI:
                return createKikusui(deviceRegisterForm);
            case DeviceModel.Power.N6700:
                return createN6700(deviceRegisterForm);
            case DeviceModel.Power.MPS3610H:
                return createMPS3610H(deviceRegisterForm);
            case DeviceModel.Power.PVD8V50E:
                return createPVD8V50E(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createIT68xx(DeviceRegisterForm deviceRegisterForm) {
        return new IT68xx(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createIT6322(DeviceRegisterForm deviceRegisterForm) {
        return new IT6322(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createIT63xx(DeviceRegisterForm deviceRegisterForm) {
        return new IT63xx(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createIT69xx(DeviceRegisterForm deviceRegisterForm) {
        return new IT69xx(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createIT65xx(DeviceRegisterForm deviceRegisterForm) {
        return new IT65xx(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createIT67xx(DeviceRegisterForm deviceRegisterForm) {
        return new IT67xx(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createPowerBox(DeviceRegisterForm deviceRegisterForm) {
        return new PowerControlBox(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createKikusui(DeviceRegisterForm deviceRegisterForm) {
        return new Kikusui(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createN6700(DeviceRegisterForm deviceRegisterForm) {
        return new N6700(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createMPS3610H(DeviceRegisterForm deviceRegisterForm) {
        return new MPS3610H(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createPVD8V50E(DeviceRegisterForm deviceRegisterForm) {
        return new PVD8V50E(deviceRegisterForm.getDeviceOperationParameter());
    }
}
