package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractOscilloscopeFactory extends AbstractDeviceCreator {
    Device createRigolDHODevice(DeviceRegisterForm deviceRegisterForm);

    Device createSDS5000XDevice(DeviceRegisterForm deviceRegisterForm);

    Device createSDS6000XDevice(DeviceRegisterForm deviceRegisterForm);
}
