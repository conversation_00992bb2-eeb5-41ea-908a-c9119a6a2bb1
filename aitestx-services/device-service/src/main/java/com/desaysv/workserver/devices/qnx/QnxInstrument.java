package com.desaysv.workserver.devices.qnx;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.camera.base.IVisionDevice;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.devices.serial.listener.SerialPortOpenListener;
import com.desaysv.workserver.devices.serial.manager.SerialPortEventManager;
import com.desaysv.workserver.entity.TemplateImageConfig;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.stream.StreamProducer;
import com.desaysv.workserver.stream.StreamService;
import com.desaysv.workserver.stream.rtsp.RtspQnxStreamProducer;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.command.CmdCommand;
import com.desaysv.workserver.utils.command.CommandExecutor;
import com.desaysv.workserver.utils.command.CommandResponse;
import com.desaysv.workserver.utils.command.CommandUtils;
import com.fazecast.jSerialComm.SerialPort;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class QnxInstrument extends QnxDevice implements SerialPortOpenListener {

    private SerialPortDevice externalSerialPortDevice; // 外部串口设备
    private transient SerialPortEventManager serialPortEventManager; // 串口事件管理器
    private boolean isListenerRegistered = false; // 监听器是否已注册
    private String commandStr;
    private RectSize rectSize; // 假定此 rectSize 是固定的，或在首次成功抓取时设置一次

    // 成员变量用于 FFmpegFrameGrabber 管理
    private transient FFmpegFrameGrabber frameGrabberInstance = null;
    private String activeRtmpUrl = null; // 存储首次成功初始化时使用的固定RTMP URL
    private final Object grabberLock = new Object();

    // 添加新的成员变量
    private static final long GRABBER_INIT_TIMEOUT_MS = 60000; // 1分钟超时
    private static final int MAX_RETRY_ATTEMPTS = 3; // 最大重试次数
    private final ScheduledExecutorService reconnectExecutor = Executors.newSingleThreadScheduledExecutor(
            r -> {
                Thread t = new Thread(r, "QnxReconnectThread-" + getDeviceName());
                t.setDaemon(true);
                return t;
            }
    );
    private AtomicBoolean isReconnecting = new AtomicBoolean(false);
    private String lastUsedScriptPath = null; // 存储最后使用的脚本路径

    public QnxInstrument() {
        this(new DeviceOperationParameter());
    }

    public QnxInstrument(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            this.serialPortEventManager = SerialPortEventManager.getInstance();
            if (this.serialPortEventManager != null && !this.isListenerRegistered) {
                this.serialPortEventManager.registerListener(this);
                this.isListenerRegistered = true;
                log.debug("QnxInstrument '{}' 已注册到 SerialPortOpenListener。", getDeviceName());
            }
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 获取 SerialPortEventManager 实例失败。可能无法发布串口打开/关闭事件。", getDeviceName(), e);
            this.serialPortEventManager = null;
        }
    }

    /**
     * 设置用于通信的外部 SerialPortDevice。
     *
     * @param device 要使用的 SerialPortDevice。
     */
    public void setExternalSerialPortDevice(SerialPortDevice device) {
        if (device != null) {
            log.info("QnxInstrument '{}': 设置外部 SerialPortDevice 为 '{}' (端口: {}).",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
        } else if (this.externalSerialPortDevice != null) {
            log.info("QnxInstrument '{}': 清除外部 SerialPortDevice (原为 '{}').",
                    getDeviceName(), this.externalSerialPortDevice.getAliasName());
        }
        this.externalSerialPortDevice = device;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_QNX;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Qnx.QNX_INSTRUMENT;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {

        // 根据实际的打开逻辑返回true或false
        // 此处仅为示例，保持原有逻辑
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        log.info("QnxInstrument '{}': close() 调用.", getDeviceName());

        // 停止视频流
        stopStream();

        synchronized (grabberLock) {
            if (frameGrabberInstance != null) {
                try {
                    log.info("QnxInstrument '{}': 正在停止并释放 FFmpegFrameGrabber (URL: {}).", getDeviceName(), activeRtmpUrl);
                    frameGrabberInstance.stop();
                    frameGrabberInstance.release();
                } catch (FFmpegFrameGrabber.Exception e) {
                    log.error("QnxInstrument '{}': 关闭期间停止/释放 FFmpegFrameGrabber 出错: {}", getDeviceName(), e.getMessage(), e);
                }
                frameGrabberInstance = null;
                activeRtmpUrl = null;
            }
        }

        // 关闭重连执行器
        try {
            reconnectExecutor.shutdown();
            if (!reconnectExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                reconnectExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            reconnectExecutor.shutdownNow();
        }

        // 关闭之前可能打开的QNX相关进程
        killPreviousQnxProcesses();

        if (this.serialPortEventManager != null && this.isListenerRegistered) {
            this.serialPortEventManager.unregisterListener(this);
            this.isListenerRegistered = false;
            log.debug("QnxInstrument '{}' 已从 SerialPortOpenListener 注销.", getDeviceName());
        }
        return true;
    }

    @Override
    public void onSerialPortOpened(SerialPortDevice device) {
        if (device == null) return;
        log.info("QnxInstrument '{}' 收到设备 '{}' (端口: {}) 的 onSerialPortOpened 事件。",
                getDeviceName(), device.getAliasName(), device.getDevicePortName());

        // 检查打开的串口是否是 QnxInstrument 自身配置的串口
        if (this.externalSerialPortDevice == null) {
            log.info("QnxInstrument '{}': 匹配的 SerialPortDevice '{}' (端口: {}) 已打开。设置为外部设备。",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
            setExternalSerialPortDevice(device);
        } else if (this.externalSerialPortDevice == device) {
            // 如果事件对应的设备已经是当前外部设备，则无需操作
            log.debug("QnxInstrument '{}': 收到已设置的外部设备 '{}' 的打开事件。无变化。", getDeviceName(), device.getAliasName());
        } else {
            // 如果一个匹配的串口设备打开了，但是已经设置了另一个外部设备，则记录警告
            log.warn("QnxInstrument '{}': 一个匹配的 SerialPortDevice '{}' 已打开, 但已设置外部设备 '{}'。不进行更改。",
                    getDeviceName(), device.getAliasName(), this.externalSerialPortDevice.getAliasName());
        }
    }

    @Override
    public void onSerialPortClosed(SerialPortDevice device) {
        if (device == null) return;
        log.debug("QnxInstrument '{}' 收到设备 '{}' (端口: {}) 的 onSerialPortClosed 事件。",
                getDeviceName(), device.getAliasName(), device.getDevicePortName());
        // 检查关闭的串口是否是当前使用的外部串口设备
        if (this.externalSerialPortDevice == device) {
            log.info("QnxInstrument '{}': 外部 SerialPortDevice '{}' (端口: {}) 已关闭。清除引用。",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
            setExternalSerialPortDevice(null); // 清除外部串口设备引用
        }
    }

    /**
     * 向串口发送QNX设备初始化命令。
     * 此方法优先使用已设置的 externalSerialPortDevice (如果它已打开)。
     * 否则，它将使用QnxDevice自身的配置通过jSerialComm打开新端口来发送命令。
     *
     * @return 是否发送成功
     */
    public boolean sendQnxInitCommands() {
        String[] commands = {
                "pfctl -d",
                "dtach -a /tmp/android",
                "su",
                "echo peripheral > /sys/devices/platform/soc/a600000.ssusb/mode",
                "su",
                "setprop persist.sv.debug.adb_enable 1",
                "su",
                "setprop persist.sv.enable_adb_install 1",
                "iptables -P INPUT ACCEPT",
                "iptables -P OUTPUT ACCEPT",
                "iptables -P FORWARD ACCEPT",
                "iptables -F",
        };

        if (this.externalSerialPortDevice != null && this.externalSerialPortDevice.getSerialPort() != null && this.externalSerialPortDevice.getSerialPort().isOpen()) {
            try {
                log.info("使用已连接的串口设备 ({}) 发送QNX初始化命令", this.externalSerialPortDevice.getDevicePortName());
                for (String cmd : commands) {
                    log.info("发送命令: {}", cmd);
                    this.externalSerialPortDevice.send((cmd + "\n").getBytes(), false);
                    Thread.sleep(100); // 等待命令执行
                }
                log.info("QNX初始化命令发送完成");
                return true;
            } catch (DeviceSendException e) {
                log.error("通过已连接的串口设备发送QNX初始化命令失败: {}", e.getMessage(), e);
                return false;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                log.error("命令发送被中断", e);
                return false;
            }
        } else {
            // 回退到使用内部串口逻辑
            String serialPortName = getDevicePortName(); // QnxDevice自身配置的串口名
            Integer baudRate = getBaudRate(); // QnxDevice自身配置的波特率
            int currentBaudRate = (baudRate != null) ? baudRate : 115200; // 默认波特率

            if (serialPortName == null || serialPortName.isEmpty()) {
                log.warn("未配置串口名称 (QnxDevice)，无法发送QNX初始化命令");
                return false;
            }

            SerialPort internalSerialPort = null;
            try {
                log.info("正在向串口 {} @ {} 波特率发送QNX初始化命令 (内部回退逻辑)", serialPortName, currentBaudRate);
                internalSerialPort = SerialPort.getCommPort(serialPortName);
                if (!internalSerialPort.isOpen()) {
                    internalSerialPort.setComPortParameters(currentBaudRate, 8, SerialPort.ONE_STOP_BIT, SerialPort.NO_PARITY);
                    internalSerialPort.setComPortTimeouts(SerialPort.TIMEOUT_WRITE_BLOCKING, 0, 5000); // 设置超时
                    if (!internalSerialPort.openPort()) {
                        log.error("无法打开串口: {}", serialPortName);
                        return false;
                    }
                }

                for (String cmd : commands) {
                    log.info("发送QNX命令 (内部): {}", cmd);
                    SerialUtils.sendToPort(internalSerialPort, cmd + "\n");
                    Thread.sleep(200); // 等待命令执行
                }
                log.info("QNX初始化命令发送完成 (内部回退逻辑)");
                return true;
            } catch (Exception e) { // 捕获更广泛的异常，例如打开串口失败
                log.error("发送QNX初始化命令失败 (内部回退逻辑): {}", e.getMessage(), e);
                if (e instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
                return false;
            } finally {
                if (internalSerialPort != null && internalSerialPort.isOpen()) {
                    internalSerialPort.closePort(); // 确保串口关闭
                }
            }
        }
    }

    /**
     * 获取QNX视频流
     *
     * @param filePath 文件路径
     */
    public void startStream(String filePath) {
        startStream(filePath, false);
    }

    /**
     * 停止QNX视频流
     */
    public void stopStream() {
        // 停止命令执行
        if (commandStr != null && !commandStr.isEmpty()) {
            log.info("QnxInstrument '{}': 停止命令执行: {}", getDeviceName(), commandStr);
            CommandExecutor.killByCommand(commandStr);
        }

        // 停止视频流
        try {
            StreamService streamService = SpringContextHolder.getBean(StreamService.class);
            if (streamService.isDistributed(getDeviceUniqueCode())) {
                log.info("QnxInstrument '{}': 停止视频流...", getDeviceName());
                streamService.stopStream(getDeviceUniqueCode());
                log.info("QnxInstrument '{}': 视频流已停止", getDeviceName());
            }
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 停止视频流失败: {}", getDeviceName(), e.getMessage(), e);
        }
    }

    /**
     * 执行QNX命令文件并注册视频流
     *
     * @param filePath 文件路径
     * @param useSystemCmd 是否使用系统命令
     */
    public void startStream(String filePath, boolean useSystemCmd) {
        // 保存脚本路径，用于重连时重新执行
        this.lastUsedScriptPath = filePath;

        // 首先尝试发送初始化命令
        if (sendQnxInitCommands()) {
            log.info("QNX初始化命令发送成功。");
            //等待5s,确保QNX设备已就绪
            try {
                log.info("等待5秒，确保QNX设备已就绪...");
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                log.error("等待QNX设备就绪失败", e);
                Thread.currentThread().interrupt();
            }
        } else {
            log.warn("QNX初始化命令发送失败，可能影响后续QNX命令文件执行");
        }

        log.info("准备执行QNX命令文件，路径: {}", filePath);
        if (useSystemCmd) {
            useSystemCmd(filePath);
        } else {
            useCommandUtils(filePath);
        }

        // 执行完命令后，注册视频流
        registerStream();
    }

    /**
     * 使用CommandUtils执行QNX命令文件
     *
     * @param filePath 文件路径
     */
    private void useCommandUtils(String filePath) {
        try {
            File scriptFile = new File(filePath);
            String scriptDir = scriptFile.getParent();

            if (scriptDir == null) {
                try {
                    // If filePath does not contain path information (e.g., just a filename),
                    // scriptFile.getParent() might return null.
                    // Try to get the current working directory as a fallback.
                    scriptDir = new File(".").getCanonicalPath();
                    log.warn("无法从 '{}' 确定脚本的父目录，将使用备选工作目录 '{}'", filePath, scriptDir);
                } catch (IOException e) {
                    log.error("无法获取备选工作目录，执行可能失败。", e);
                    // Optionally re-throw or set a default known safe path
                    // For now, we'll let it proceed, but executionDirectory might be problematic.
                    // Or, better, return or throw:
                    throw new RuntimeException("无法确定脚本执行目录", e);
                }
            }

            // Command to start a new command prompt window, set its title, working directory,
            // and execute the script, keeping the window open.
            commandStr = filePath;

            CmdCommand cmdCommand = new CmdCommand(commandStr);
            // setShowWindow(true) is important if CommandExecutor needs a hint to allow 'start' to create a new window.
            // The 'start' command itself is responsible for the new window.
            cmdCommand.setShowWindow(true);
            cmdCommand.setWorkDir(getWorkDir(filePath));

            ExecutorService executor = Executors.newSingleThreadExecutor();
            final String finalScriptDir = scriptDir; // Effective final for use in lambda

            executor.submit(() -> {
                try {
                    log.info("准备通过 CommandUtils.executeCommand(CmdCommand) 在新窗口执行命令 (窗口将保持打开): {}", cmdCommand.getCommand());
                    log.info("脚本路径: {}, 脚本将在目录 '{}' 中执行 (通过 /D 参数)", filePath, finalScriptDir);

                    // Execute the command
                    CommandResponse response = CommandUtils.executeCommand(cmdCommand);

                    // Log the response from the 'start' command execution itself.
                    // The actual script's output will be in the new window.
                    log.info("CommandUtils.executeCommand(CmdCommand) 执行完成. Success: {}, Output: {}, Error: {}",
                            response.isOk(), response.getStandardOutput(), response.getStandardError());

                } catch (IOException e) {
                    log.error("使用 CommandUtils.executeCommand(CmdCommand) 执行QNX命令文件 '{}' (命令: {}) 时发生IOException: {}",
                            filePath, cmdCommand.getCommand(), e.getMessage(), e);
                } catch (Exception e) {
                    log.error("执行QNX命令文件 '{}' (命令: {}) 期间发生意外错误 (useCommandUtils lambda): {}",
                            filePath, cmdCommand.getCommand(), e.getMessage(), e);
                }
            });
            executor.shutdown();

        } catch (Exception e) {
            // Catch exceptions from the setup phase (file operations, command string formatting)
            log.error("准备执行QNX命令文件 '{}' (useCommandUtils) 时发生顶层异常: {}", filePath, e.getMessage(), e);
        }
    }

    /**
     * 获取脚本目录
     *
     * @param filePath 脚本路径
     * @return
     */
    private String getWorkDir(String filePath) {
        File scriptFile = new File(filePath);
        String scriptDir = scriptFile.getParent();

        if (scriptDir == null) {
            try {
                // 如果 filePath 不包含路径信息 (例如，只是一个文件名),
                // 则 scriptFile.getParent() 可能返回 null。
                // 尝试获取当前工作目录作为备选。
                scriptDir = new File(".").getCanonicalPath();
                log.warn("无法从 '{}' 确定脚本的父目录，将使用备选工作目录 '{}'", filePath, scriptDir);
            } catch (IOException e) {
                log.error("无法获取备选工作目录，执行可能失败。", e);
                // 可以选择抛出异常或设置一个默认的已知安全路径
                throw new RuntimeException("无法确定脚本执行目录", e);
            }
        }
        return scriptDir;
    }

    /**
     * 使用系统命令执行QNX命令文件
     *
     * @param filePath 文件路径
     */
    private void useSystemCmd(String filePath) {
        try {
            String scriptDir = getWorkDir(filePath);

            // 此命令将由 CommandUtils.executeCommandToString 执行 (假设其内部会添加 "cmd /c")
            // 1. 'start': 启动一个新命令提示符窗口。
            // 2. "Executing [脚本文件名]": 设置新窗口的标题。
            // 3. '/D "[脚本目录]"': 为新窗口中的命令设置工作目录。
            // 4. 'cmd /k "[脚本文件路径]"': 在新窗口中执行脚本，'/k' 参数会使窗口在脚本执行后保持打开状态，便于查看输出或错误。
            String commandForCommandUtils = String.format("start \"Executing %s\" /D \"%s\" cmd /k \"%s\"",
                    new File(filePath).getName(),
                    scriptDir,
                    filePath);

            ExecutorService executor = Executors.newSingleThreadExecutor();
            // CommandUtils 需要一个 File 对象作为工作目录参数
            final File executionDirectory = new File(scriptDir);

            executor.submit(() -> {
                try {
                    log.info("准备通过 CommandUtils 在新窗口执行命令 (窗口将保持打开): {}", commandForCommandUtils);
                    log.info("脚本路径: {}, 脚本将在目录 '{}' 中执行", filePath, executionDirectory.getAbsolutePath());

                    // 假设 CommandUtils.executeCommandToString(cmd, dir) 内部会执行 "cmd /c cmd"
                    // 其中 cmd 是 commandForCommandUtils，dir 是 executionDirectory
                    String result = CommandUtils.executeCommandToString(commandForCommandUtils, executionDirectory);

                    log.info("命令 'start' 已尝试执行。 'start' 命令本身的输出 (如果存在): {}", result);
                    // 脚本的实际输出/错误将显示在弹出的新窗口中。
                } catch (IOException e) {
                    log.error("使用 CommandUtils.executeCommandToString 执行QNX命令文件 '{}' (命令: {}) 时发生IOException: {}", filePath, commandForCommandUtils, e.getMessage(), e);
                } catch (Exception e) {
                    log.error("执行QNX命令文件 '{}' (命令: {}) 期间发生意外错误: {}", filePath, commandForCommandUtils, e.getMessage(), e);
                }
            });
            executor.shutdown();
        } catch (Exception e) {
            // 捕获准备阶段的顶层异常，例如 scriptDir 解析失败时的 RuntimeException
            log.error("准备执行QNX命令文件 '{}' 时发生顶层异常: {}", filePath, e.getMessage(), e);
        }
    }


    @Override
    public OperationResult testSimilarity(TemplateImageConfig templateImageConfig) throws OperationFailNotification {
        if (isSimulated()) {
            log.info("设备模拟中，相似度测试不启用");
            return OperationResult.staticOk();
        }
        return super.testSimilarity(templateImageConfig);
    }

    @Override
    public Frame grabFrame() {
        try {
            StreamService streamService = SpringContextHolder.getBean(StreamService.class);

            // 检查流是否已注册
            if (streamService.isDistributed(getDeviceUniqueCode())) {
                // 从StreamService获取帧
                Frame frame = streamService.grab(getDeviceUniqueCode());
                if (frame != null) {
                    return frame;
                } else {
                    log.warn("QnxInstrument '{}': StreamService.grab返回null", getDeviceName());
                }
            } else {
                // 如果流未注册，尝试注册
                log.info("QnxInstrument '{}': 视频流未注册，尝试注册...", getDeviceName());
                registerStream();

                // 再次尝试获取帧
                if (streamService.isDistributed(getDeviceUniqueCode())) {
                    Frame frame = streamService.grab(getDeviceUniqueCode());
                    if (frame != null) {
                        return frame;
                    } else {
                        log.warn("QnxInstrument '{}': 注册后StreamService.grab返回null", getDeviceName());
                    }
                } else {
                    log.error("QnxInstrument '{}': 注册视频流失败", getDeviceName());
                }
            }

            // 如果从StreamService获取帧失败，尝试使用FFmpegFrameGrabber直接获取
            log.info("QnxInstrument '{}': 尝试使用FFmpegFrameGrabber直接获取帧", getDeviceName());
            return grabFrameDirectly();
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 获取帧失败: {}", getDeviceName(), e.getMessage(), e);
            return createFallbackFrame("获取帧失败: " + e.getMessage());
        }
    }

    /**
     * 直接使用FFmpegFrameGrabber获取帧（备用方法）
     */
    private Frame grabFrameDirectly() {
        synchronized (grabberLock) {
            try {
                if (activeRtmpUrl == null || activeRtmpUrl.isEmpty()) {
                    // 使用固定的RTMP URL格式
                    activeRtmpUrl = "rtmp://127.0.0.1/live?live=1";
                    log.info("QnxInstrument '{}': 使用固定RTMP URL: {}", getDeviceName(), activeRtmpUrl);
                }

                // 初始化或重新初始化FFmpegFrameGrabber
                if (frameGrabberInstance == null) {
                    if (!initializeGrabberWithTimeout()) {
                        return createFallbackFrame("初始化FFmpegFrameGrabber超时");
                    }
                }

                // 获取帧
                Frame frame = frameGrabberInstance.grab();
                if (frame != null) {
                    log.debug("QnxInstrument '{}': 成功直接获取帧，尺寸: {}x{}",
                            getDeviceName(), frame.imageWidth, frame.imageHeight);
                    return frame;
                } else {
                    log.warn("QnxInstrument '{}': 直接获取帧为null，尝试重连...", getDeviceName());
                    return handleGrabFailureWithReconnect("直接获取帧为null");
                }
            } catch (Exception e) {
                log.error("QnxInstrument '{}': 直接获取帧失败: {}", getDeviceName(), e.getMessage(), e);
                return handleGrabFailureWithReconnect("直接获取帧失败: " + e.getMessage());
            }
        }
    }

    /**
     * 带超时的初始化FFmpegFrameGrabber
     * @return 是否成功初始化
     */
    private boolean initializeGrabberWithTimeout() {
        log.info("QnxInstrument '{}': 初始化FFmpegFrameGrabber，URL: {}，超时时间: {}ms",
                getDeviceName(), activeRtmpUrl, GRABBER_INIT_TIMEOUT_MS);

        final CountDownLatch initLatch = new CountDownLatch(1);
        final AtomicBoolean initSuccess = new AtomicBoolean(false);

        Thread initThread = new Thread(() -> {
            try {
                frameGrabberInstance = new FFmpegFrameGrabber(activeRtmpUrl);
                frameGrabberInstance.setOption("stimeout", "5000000"); // 5秒连接超时(微秒)
                frameGrabberInstance.setOption("rtmp_live", "live");   // 指定为直播流
                frameGrabberInstance.start();
                log.info("QnxInstrument '{}': FFmpegFrameGrabber启动成功", getDeviceName());
                initSuccess.set(true);
            } catch (Exception e) {
                log.error("QnxInstrument '{}': 初始化FFmpegFrameGrabber失败: {}", getDeviceName(), e.getMessage(), e);
                cleanUpGrabberAfterFailure();
            } finally {
                initLatch.countDown();
            }
        }, "QnxGrabberInit-" + getDeviceName());

        initThread.setDaemon(true);
        initThread.start();

        try {
            if (!initLatch.await(GRABBER_INIT_TIMEOUT_MS, TimeUnit.MILLISECONDS)) {
                log.error("QnxInstrument '{}': 初始化FFmpegFrameGrabber超时", getDeviceName());
                initThread.interrupt();
                cleanUpGrabberAfterFailure();
                return false;
            }
        } catch (InterruptedException e) {
            log.error("QnxInstrument '{}': 等待初始化FFmpegFrameGrabber被中断", getDeviceName());
            Thread.currentThread().interrupt();
            cleanUpGrabberAfterFailure();
            return false;
        }

        return initSuccess.get();
    }

    private boolean initializeAndStartGrabber(String rtmpUrl, String contextLog) {
        log.info("QnxInstrument '{}': {} FFmpegFrameGrabber (URL: {})...", getDeviceName(), contextLog, rtmpUrl);
        try {
            if (frameGrabberInstance != null) { // 如果因重连调用，先清理旧的
                try {
                    frameGrabberInstance.stop();
                    frameGrabberInstance.release();
                } catch (FFmpegFrameGrabber.Exception e) {
                    log.warn("QnxInstrument '{}': 在 {} 期间清理旧 grabber 实例时出错: {}", getDeviceName(), contextLog, e.getMessage());
                }
            }
            frameGrabberInstance = new FFmpegFrameGrabber(rtmpUrl);
            frameGrabberInstance.setOption("stimeout", "5000000"); // 5 秒连接超时 (微秒)
            frameGrabberInstance.start();
            // activeRtmpUrl 已经在调用此方法前设置或确认
            log.info("QnxInstrument '{}': FFmpegFrameGrabber ({}) 已成功启动。Grabber报告尺寸: {}x{}", getDeviceName(), contextLog, frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight());

            // 如果 this.rectSize 尚未设定 (仅在非常首次的成功启动时)
            if (this.rectSize == null) {
                if (frameGrabberInstance.getImageWidth() > 0 && frameGrabberInstance.getImageHeight() > 0) {
                    this.rectSize = new RectSize(frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight());
                    log.info("QnxInstrument '{}': rectSize 从 grabber ({}) 初始化为: {}x{}", getDeviceName(), contextLog, this.rectSize.getWidth(), this.rectSize.getHeight());
                } else {
                    log.error("QnxInstrument '{}': Grabber ({}) 启动但未提供有效尺寸，且 rectSize 未预设。初始化失败。", getDeviceName(), contextLog);
                    cleanUpGrabberAfterFailure();
                    return false;
                }
            } else {
                // rectSize 已预设，检查是否与 grabber 报告的尺寸一致 (仅日志)
                if (frameGrabberInstance.getImageWidth() > 0 && frameGrabberInstance.getImageHeight() > 0 &&
                        (frameGrabberInstance.getImageWidth() != this.rectSize.getWidth() ||
                                frameGrabberInstance.getImageHeight() != this.rectSize.getHeight())) {
                    log.warn("QnxInstrument '{}': Grabber ({}) 报告的图像尺寸 {}x{} 与预设的 rectSize {}x{} 不符。将优先使用预设的 rectSize。",
                            getDeviceName(), contextLog, frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight(),
                            this.rectSize.getWidth(), this.rectSize.getHeight());
                }
            }
            return true;
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("QnxInstrument '{}': {} FFmpegFrameGrabber (URL: {}) 失败: {}", getDeviceName(), contextLog, rtmpUrl, e.getMessage(), e);
            cleanUpGrabberAfterFailure();
            return false;
        }
    }

    private Frame handleGrabFailureWithReconnect(String failureReason) {
        // 如果已经在重连中，避免重复触发
        if (isReconnecting.getAndSet(true)) {
            log.info("QnxInstrument '{}': 已有重连任务在进行中，不重复触发", getDeviceName());
            return createFallbackFrame(failureReason + " (重连中...)");
        }

        log.info("QnxInstrument '{}': 处理抓取失败 ({})，启动异步重连...", getDeviceName(), failureReason);

        // 创建一个临时的错误帧，立即返回给用户，保持UI响应
        Frame errorFrame = createFallbackFrame(failureReason + " (正在重连...)");

        // 异步执行重连
        reconnectExecutor.submit(() -> {
            try {
                // 清理当前可能已损坏的grabber实例
                cleanUpGrabberAfterFailure();

                // 关闭之前可能打开的命令行窗口
                killPreviousQnxProcesses();

                // 重新执行QNX命令文件
                if (lastUsedScriptPath != null) {
                    log.info("QnxInstrument '{}': 重新执行QNX命令文件: {}", getDeviceName(), lastUsedScriptPath);
                    startStream(lastUsedScriptPath, false);

                    // 等待一段时间让脚本启动
                    Thread.sleep(5000);
                } else {
                    log.warn("QnxInstrument '{}': 无法重新执行QNX命令文件，因为没有记录上次使用的脚本路径", getDeviceName());
                }

                // 尝试重新初始化并启动grabber
                if (initializeGrabberWithTimeout()) {
                    log.info("QnxInstrument '{}': 异步重连成功", getDeviceName());
                } else {
                    log.error("QnxInstrument '{}': 异步重连失败", getDeviceName());
                }
            } catch (Exception e) {
                log.error("QnxInstrument '{}': 异步重连过程中发生异常: {}", getDeviceName(), e.getMessage(), e);
            } finally {
                isReconnecting.set(false);
            }
        });

        return errorFrame;
    }

    /**
     * 关闭之前可能打开的QNX相关进程
     */
    private void killPreviousQnxProcesses() {
        try {
            log.info("QnxInstrument '{}': 尝试关闭之前的QNX相关进程", getDeviceName());

            // 查找并关闭cmd.exe进程中包含"qnx_screen_share"的窗口
            String killCommand = "taskkill /F /FI \"WINDOWTITLE eq *qnx_screen_share*\" /T";
            CmdCommand cmdCommand = new CmdCommand(killCommand);
            cmdCommand.setShowWindow(false);

            CommandResponse response = CommandUtils.executeCommand(cmdCommand);
            log.info("QnxInstrument '{}': 关闭之前QNX进程结果: {}", getDeviceName(),
                    response.isOk() ? "成功" : "失败: " + response.getStandardError());

            // 也可以考虑关闭adb相关进程
            String killAdbCommand = "taskkill /F /IM adb.exe /T";
            CmdCommand adbCommand = new CmdCommand(killAdbCommand);
            adbCommand.setShowWindow(false);

            CommandResponse adbResponse = CommandUtils.executeCommand(adbCommand);
            log.info("QnxInstrument '{}': 关闭ADB进程结果: {}", getDeviceName(),
                    adbResponse.isOk() ? "成功" : "失败: " + adbResponse.getStandardError());

            // 等待进程完全关闭
            Thread.sleep(1000);
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 关闭之前QNX进程失败: {}", getDeviceName(), e.getMessage(), e);
        }
    }

    private void cleanUpGrabberAfterFailure() {
        if (frameGrabberInstance != null) {
            try {
                frameGrabberInstance.release(); // stop可能已在initializeAndStartGrabber中调用或失败时调用
            } catch (FFmpegFrameGrabber.Exception e) {
                log.warn("QnxInstrument '{}': 清理grabber (release) 时出错: {}", getDeviceName(), e.getMessage());
            }
            frameGrabberInstance = null;
        }
        // activeRtmpUrl 不清除，因为它是固定配置
    }

    /**
     * 创建一个带有错误信息的备用帧
     */
    private Frame createFallbackFrame(String errorMessage) {
        try {
            // 创建一个带有错误信息的图像
            BufferedImage image = new BufferedImage(640, 480, BufferedImage.TYPE_3BYTE_BGR);
            Graphics2D g2d = image.createGraphics();
            g2d.setColor(Color.BLACK);
            g2d.fillRect(0, 0, 640, 480);
            g2d.setColor(Color.RED);
            g2d.setFont(new Font("Arial", Font.BOLD, 20));
            g2d.drawString("QNX设备: " + getDeviceName(), 20, 40);
            g2d.drawString("错误: " + errorMessage, 20, 80);
            g2d.drawString("时间: " + new Date(), 20, 120);
            g2d.dispose();

            // 转换为Frame
            return Java2DFrameUtils.toFrame(image);
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 创建备用帧失败: {}", getDeviceName(), e.getMessage(), e);
            return null;
        }
    }

    private void validateFrameSize(Frame frame) {
        if (this.rectSize != null && frame != null &&
                (frame.imageWidth != this.rectSize.getWidth() || frame.imageHeight != this.rectSize.getHeight())) {
            log.warn("QnxInstrument '{}': 抓取到的帧尺寸 {}x{} 与已建立的 rectSize {}x{} 不符! 图像内容可能不符合预期。",
                    getDeviceName(), frame.imageWidth, frame.imageHeight,
                    this.rectSize.getWidth(), this.rectSize.getHeight());
        }
    }

    @Override
    public RectSize getSize() {
        if (rectSize == null) {
            try {
                // 尝试通过grabFrame获取尺寸
                Frame frame = grabFrame();
                if (frame != null && frame.imageWidth > 0 && frame.imageHeight > 0) {
                    rectSize = new RectSize(frame.imageWidth, frame.imageHeight);
                    log.info("QnxInstrument '{}': 从grabFrame获取尺寸: {}x{}",
                            getDeviceName(), rectSize.getWidth(), rectSize.getHeight());
                } else {
                    // 如果无法获取有效帧，使用默认尺寸
                    log.warn("QnxInstrument '{}': 无法从grabFrame获取有效尺寸，使用默认尺寸", getDeviceName());
                    rectSize = new RectSize(1920, 1080);
                }
            } catch (Exception e) {
                log.error("QnxInstrument '{}': 获取尺寸时发生异常: {}", getDeviceName(), e.getMessage(), e);
                rectSize = new RectSize(1920, 1080);
            }
        }
        return rectSize;
    }

    /**
     * 注册QNX设备视频流到StreamService
     */
    private void registerStream() {
        try {
            StreamService streamService = SpringContextHolder.getBean(StreamService.class);

            // 检查流是否已注册
            if (streamService.isDistributed(getDeviceUniqueCode())) {
                log.info("QnxInstrument '{}': 视频流已注册，无需重复注册", getDeviceName());
                return;
            }

            // 准备注册请求
            GrabRequest grabRequest = new GrabRequest();
            grabRequest.setDeviceUUID(getDeviceUniqueCode());
            grabRequest.setDeviceName(getDeviceName());
            grabRequest.setDeviceModel(DeviceModel.Qnx.QNX_INSTRUMENT);

            // 设置分辨率
            if (this.rectSize != null) {
                grabRequest.setWidth(this.rectSize.getWidth());
                grabRequest.setHeight(this.rectSize.getHeight());
            } else {
                // 使用默认分辨率
                grabRequest.setWidth(1920);
                grabRequest.setHeight(1080);
            }

            // 注册流
            log.info("QnxInstrument '{}': 开始注册视频流...", getDeviceName());
            String streamUrl = streamService.pushStream(grabRequest);
            log.info("QnxInstrument '{}': 视频流注册成功，URL: {}", getDeviceName(), streamUrl);

            // 等待流初始化完成
            try {
                log.info("QnxInstrument '{}': 等待流初始化完成...", getDeviceName());
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 尝试获取第一帧以验证流是否正常
            try {
                Frame frame = streamService.grab(getDeviceUniqueCode());
                if (frame != null) {
                    log.info("QnxInstrument '{}': 成功获取第一帧，尺寸: {}x{}",
                            getDeviceName(), frame.imageWidth, frame.imageHeight);

                    // 如果rectSize未设置，从第一帧获取
                    if (this.rectSize == null) {
                        this.rectSize = new RectSize(frame.imageWidth, frame.imageHeight);
                        log.info("QnxInstrument '{}': 从第一帧设置rectSize: {}x{}",
                                getDeviceName(), this.rectSize.getWidth(), this.rectSize.getHeight());
                    }
                } else {
                    log.warn("QnxInstrument '{}': 视频流注册成功但获取第一帧为null", getDeviceName());
                }
            } catch (Exception e) {
                log.error("QnxInstrument '{}': 获取第一帧失败: {}", getDeviceName(), e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 注册视频流失败: {}", getDeviceName(), e.getMessage(), e);
        }
    }

    @Override
    public VisionResult fetchCloudVision(VisionResult visionResult) {
        return null;
    }
}
