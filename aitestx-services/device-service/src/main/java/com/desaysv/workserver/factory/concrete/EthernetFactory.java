package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.canoe.VectorEthernetFDX;
import com.desaysv.workserver.devices.bus.tosun.TosunEthernet;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractEthernetFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class EthernetFactory implements AbstractEthernetFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Bus.VECTOR_ETHERNET:
                return createVectorEthernetDevice(deviceRegisterForm);
            case DeviceModel.Bus.TOSUN_ETHERNET:
                return createToSunEthernetDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createVectorEthernetDevice(DeviceRegisterForm deviceRegisterForm) {
        return new VectorEthernetFDX(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createToSunEthernetDevice(DeviceRegisterForm deviceRegisterForm) {
        return new TosunEthernet(deviceRegisterForm.getDeviceOperationParameter());
    }

}
