package com.desaysv.workserver.controller;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件系统调试控制器
 * 用于诊断文件路径和图片加载问题
 */
@Slf4j
@RestController
@RequestMapping("/api/debug")
public class FileSystemDebugController {

    private static final String BASE_PATH = "D:\\FlyTest\\data\\server\\system\\images\\";

    @Data
    public static class FileInfo {
        private String name;
        private boolean isDirectory;
        private long size;
        private String lastModified;
        private String fullPath;
        private boolean canRead;

        public FileInfo(File file) {
            this.name = file.getName();
            this.isDirectory = file.isDirectory();
            this.size = file.isFile() ? file.length() : 0;
            this.lastModified = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(file.lastModified()));
            this.fullPath = file.getAbsolutePath();
            this.canRead = file.canRead();
        }
    }

    /**
     * 获取文件系统信息，用于调试图片路径问题
     *
     * @param path 相对路径
     * @return 文件系统信息
     */
    @GetMapping("/files")
    public ResponseEntity<List<FileInfo>> getFileSystemInfo(@RequestParam(required = false) String path) {
        try {
            String targetPath;
            if (path == null || path.isEmpty()) {
                targetPath = BASE_PATH;
            } else {
                String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
                targetPath = BASE_PATH + decodedPath;
            }

            log.info("查看目录: {}", targetPath);

            File directory = new File(targetPath);
            if (!directory.exists()) {
                log.error("目录不存在: {}", targetPath);
                return ResponseEntity.notFound().build();
            }

            if (!directory.isDirectory()) {
                log.error("路径不是目录: {}", targetPath);
                return ResponseEntity.badRequest().build();
            }

            File[] files = directory.listFiles();
            if (files == null) {
                log.error("无法列出目录内容: {}", targetPath);
                return ResponseEntity.ok(new ArrayList<>());
            }

            List<FileInfo> fileInfos = new ArrayList<>();
            for (File file : files) {
                fileInfos.add(new FileInfo(file));
            }

            return ResponseEntity.ok(fileInfos);
        } catch (Exception e) {
            log.error("获取文件系统信息失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查指定文件是否存在
     *
     * @param path 文件相对路径
     * @return 文件信息
     */
    @GetMapping("/check-file")
    public ResponseEntity<Map<String, Object>> checkFile(@RequestParam String path) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 打印原始路径以便调试
            log.info("原始路径: {}", path);

            String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
            log.info("解码后路径: {}", decodedPath);

            // 尝试多种路径组合方式
            List<String> pathsToTry = new ArrayList<>();

            // 1. 直接使用传入的路径
            pathsToTry.add(path);

            // 2. 解码后的路径
            pathsToTry.add(decodedPath);

            // 3. 加上BASE_PATH的完整路径
            pathsToTry.add(BASE_PATH + path);

            // 4. 加上BASE_PATH的解码完整路径
            pathsToTry.add(BASE_PATH + decodedPath);

            // 检查所有可能的路径
            List<Map<String, Object>> pathResults = new ArrayList<>();
            boolean anyExists = false;

            for (String pathToCheck : pathsToTry) {
                Map<String, Object> pathResult = new HashMap<>();
                pathResult.put("path", pathToCheck);

                File file = new File(pathToCheck);
                boolean exists = file.exists();
                pathResult.put("exists", exists);
                pathResult.put("isFile", file.isFile());
                pathResult.put("isDirectory", file.isDirectory());
                pathResult.put("canRead", file.canRead());
                pathResult.put("size", file.length());

                if (exists) {
                    anyExists = true;
                    // 如果是文件，尝试自动修复
                    if (file.isFile()) {
                        result.put("success", true);
                        result.put("file", new FileInfo(file));

                        // 尝试获取父目录列表
                        File parent = file.getParentFile();
                        if (parent != null && parent.exists() && parent.isDirectory()) {
                            File[] siblings = parent.listFiles();
                            if (siblings != null) {
                                List<FileInfo> siblingInfos = Arrays.stream(siblings)
                                        .map(FileInfo::new)
                                        .collect(Collectors.toList());
                                result.put("directoryContents", siblingInfos);
                            }
                        }
                    }
                }

                pathResults.add(pathResult);
            }

            result.put("pathsChecked", pathResults);
            result.put("anyPathExists", anyExists);

            // 如果都不存在，返回404
            if (!anyExists) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(result);
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查文件失败", e);
            result.put("error", e.getMessage());
            result.put("stackTrace", Arrays.stream(e.getStackTrace())
                    .map(StackTraceElement::toString)
                    .collect(Collectors.joining("\n")));
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    /**
     * 检查文件夹中是否包含标准命名的图片文件
     *
     * @param path 文件夹路径
     * @return 标准文件的检查结果
     */
    @GetMapping("/check-standard-files")
    public ResponseEntity<Map<String, Object>> checkStandardFiles(@RequestParam String path) {
        Map<String, Object> result = new HashMap<>();

        try {
            // URL解码路径
            String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
            log.info("检查标准文件: {}", decodedPath);

            File folder = new File(BASE_PATH, decodedPath);
            if (!folder.exists() || !folder.isDirectory()) {
                result.put("error", "指定路径不是有效的文件夹");
                return ResponseEntity.badRequest().body(result);
            }

            // 递归扫描所有子文件夹
            List<Map<String, Object>> folderResults = new ArrayList<>();
            scanFolders(folder, folderResults);

            result.put("basePath", BASE_PATH);
            result.put("requestedPath", path);
            result.put("decodedPath", decodedPath);
            result.put("fullPath", folder.getAbsolutePath());
            result.put("results", folderResults);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查标准文件失败", e);
            result.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    private void scanFolders(File folder, List<Map<String, Object>> results) {
        // 检查当前文件夹
        Map<String, Object> folderResult = new HashMap<>();
        folderResult.put("path", folder.getAbsolutePath().replace(BASE_PATH, ""));
        folderResult.put("name", folder.getName());

        // 检查是否包含标准文件
        File templateFile = new File(folder, "template.jpg");
        File originFile = new File(folder, "origin.jpg");

        folderResult.put("hasTemplateJpg", templateFile.exists() && templateFile.isFile());
        folderResult.put("hasOriginJpg", originFile.exists() && originFile.isFile());

        // 列出文件夹中的所有文件
        File[] files = folder.listFiles();
        if (files != null) {
            List<String> fileNames = Arrays.stream(files)
                    .filter(File::isFile)
                    .map(File::getName)
                    .collect(Collectors.toList());
            folderResult.put("files", fileNames);

            // 检查是否可能有其他命名的图片文件
            List<String> possibleImageFiles = fileNames.stream()
                    .filter(name -> name.toLowerCase().endsWith(".jpg") ||
                            name.toLowerCase().endsWith(".jpeg") ||
                            name.toLowerCase().endsWith(".png"))
                    .filter(name -> !name.equals("template.jpg") && !name.equals("origin.jpg"))
                    .collect(Collectors.toList());

            folderResult.put("otherImageFiles", possibleImageFiles);
        }

        results.add(folderResult);

        // 递归检查子文件夹
        File[] subFolders = folder.listFiles(File::isDirectory);
        if (subFolders != null) {
            for (File subFolder : subFolders) {
                scanFolders(subFolder, results);
            }
        }
    }
}
