package com.desaysv.workserver.devices.dc_collector;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.dc_collector.interfaces.IDeviceDcCollector;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class ZH4424DcCollector extends Device implements IDeviceDcCollector {
    public ZH4424DcCollector(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_DC_COLLECTOR;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.DcCollector.DC_COLLECTOR_24_DEVICE;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public float fetchCurrent(int deviceChannel) throws DeviceReadException {
        return 0;
    }

    @Override
    public float fetchVoltage(int deviceChannel) throws DeviceReadException {
        return 0;
    }
}
