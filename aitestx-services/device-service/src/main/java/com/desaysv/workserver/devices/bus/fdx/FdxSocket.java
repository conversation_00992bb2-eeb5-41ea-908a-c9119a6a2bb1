
package com.desaysv.workserver.devices.bus.fdx;

import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;
import static com.desaysv.workserver.devices.bus.fdx.FdxCommandConstant.*;
import static com.desaysv.workserver.devices.bus.fdx.FdxGroupConstant.*;
import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.*;

@Slf4j
public class FdxSocket extends DatagramSocket {
    private static FdxSocket INSTANCE;
    private final static String serverIP = "127.0.0.1";
    private final static int serverPort = 2809;
    private final static int DEFAULT_CODE = 1;
    private final static int ADD_COMMAND_SIZE_FIX_LENGTH = 8;
    private short incSequenceNumber = 0;
    private int counter = Integer.MAX_VALUE;

    @Getter
    @Setter
    private String udsLogName;
    @Getter
    @Setter
    private String canLogName;

    public FdxSocket() throws SocketException {
        super();
    }

    public static FdxSocket getInstance() {
        if (INSTANCE == null) {
            try {
                INSTANCE = new FdxSocket();
                INSTANCE.setSoTimeout(10000);
            } catch (SocketException e) {
                log.error("~~timeout~~");
                log.error(e.getMessage(), e);
            }
        }
        return INSTANCE;
    }

    public static void setInstanceNULL() {
        INSTANCE = null;
    }

    public byte[] buildHeader() {
        byte[] sequenceNumber = ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(incSequenceNumber).array();
        if (incSequenceNumber < 32767) {
            incSequenceNumber++;
        } else {
            incSequenceNumber = 1;
        }
        return FdxUtils.mergeHexByteArrays(new byte[][]{fdxSignature, fdxMajorVersion, fdxMinorVersion, numberOfCommands,
                sequenceNumber, fdxProtocolFlags, reserved});
    }

    public byte[] reqData(byte[] groupID) throws BusError {
        try {
            return receiveData(groupID);
        } catch (BusError e) {
            throw new BusError(e);
        }
    }

    private byte[] receiveData(byte[] groupID) throws BusError {
        byte[] reqDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{buildHeader(), FdxCommandConstant.dataRequestCommandSize,
                FdxCommandConstant.dataRequestCommandCode, groupID});
        log.info("发送请求数据至CANoe:{}", Hex.encodeHexString(reqDataBytes));
        sendTo(reqDataBytes);
        DatagramPacket responsePacket = new DatagramPacket(new byte[4096], 4096);
        try {
            receive(responsePacket);
        } catch (IOException e) {
            throw new BusError(e);
        }
        log.info("接收CANoe响应数据:{}", Hex.encodeHexString(Arrays.copyOfRange(responsePacket.getData(), 0, responsePacket.getLength())));
        return FdxUtils.mergeHexByteArrays(new byte[][]{Arrays.copyOfRange(responsePacket.getData(), 0, responsePacket.getLength())});
    }


    public void sendData(byte[] commandSize, byte[] groupId, byte[] dataSize, byte[] dataBytes) throws BusError {
        byte[] dataExchange = FdxUtils.mergeHexByteArrays(new byte[][]{buildHeader(), commandSize, FdxCommandConstant.dataExchangeCommandCode,
                groupId, dataSize, dataBytes});
        sendTo(dataExchange);
    }

    private static DataGroup getDataGroupByKey(String key) {
        Map<String, DataGroup> dataGroups = CANoeFdxDescription.getInstance().getDataGroups();
        DataGroup dataGroup = dataGroups.get(key);
        return dataGroup;
    }

    public boolean sendGetCanSignalInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup getCanSignalDataGroup = getDataGroupByKey(GET_CAN_SIGNAL_ID);
        Map<String, FdxItem> items = getCanSignalDataGroup.getItems();
        byte[] signalDataSize = FdxUtils.intToByteArray(getCanSignalDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(getCanSignalDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] getCanSignalGroupId = FdxUtils.intToByteArray(getCanSignalDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] msgNameBytes = stringBytesPaddedSize(messageName, items.get(CAN_MSG_NAME).getSize());
        byte[] signalNameBytes = stringBytesPaddedSize(signalName, items.get(GET_CAN_SIG_NAME).getSize());
        byte[] signalValueBytes = doubleToHexBytes(signalValue);
        byte[] setSignalDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, nodeNameBytes, msgNameBytes, signalNameBytes, signalValueBytes});
        boolean pass = sendAndBackwardReadData(signalCommandSize, getCanSignalGroupId, signalDataSize, setSignalDataBytes);
        log.info("sendGetCanSignalInfo：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean sendGetCanSignalCounter() throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup getCanSignalCounterDataGroup = getDataGroupByKey(GET_CAN_SIGNAL_COUNTER_ID);
        byte[] getCanSignalCounterGroupId = FdxUtils.intToByteArray(getCanSignalCounterDataGroup.getGroupId());
        byte[] signalDataSize = FdxUtils.intToByteArray(getCanSignalCounterDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(getCanSignalCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        boolean pass = setCounter(signalCommandSize, getCanSignalCounterGroupId, signalDataSize);
        log.info("sendGetCanSignalCounter：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean sendCheckTurnLampInfo(String turnLampType, int workTime, int checkPeriod) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup checkTurnLampDataGroup = getDataGroupByKey(CHECK_TURN_LAMP_ID);
        Map<String, FdxItem> items = checkTurnLampDataGroup.getItems();
        byte[] checkTurnLampDataSize = FdxUtils.intToByteArray(checkTurnLampDataGroup.getGroupSize());
        byte[] checkTurnLampCommandSize = FdxUtils.intToByteArray(checkTurnLampDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] checkTurnLampGroupId = FdxUtils.intToByteArray(checkTurnLampDataGroup.getGroupId());
        int lightTypeCode = getLightTypeCode(turnLampType);
        byte[] lightTypeBytes = int32ToHexBytes(lightTypeCode, items.get(LIGHT_TYPE).getSize());
        byte[] workTimeBytes = int32ToHexBytes(workTime, items.get(WORK_TIME).getSize());
        byte[] checkPeriodBytes = int32ToHexBytes(checkPeriod, items.get(CYCLE_NUMBER).getSize());
        byte[] checkTurnLampDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{lightTypeBytes, workTimeBytes, checkPeriodBytes});
        boolean pass = sendAndBackwardReadData(checkTurnLampCommandSize, checkTurnLampGroupId, checkTurnLampDataSize, checkTurnLampDataBytes);
        log.info("sendCheckTurnLampInfo：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public  static int getLightTypeCode(String turnLampType) {
        if (turnLampType == null) {
            throw new IllegalArgumentException("turnLampType不能为null");
        }
        switch (turnLampType.toUpperCase()) {
            case "LEFTTURN":
                return 0;
            case "RIGHTTURN":
                return 1;
            case "DOUBLEFLASH":
                return 2;
            default:
                throw new IllegalArgumentException("无效的转向灯状态: " + turnLampType);
        }
    }


    public boolean sendStartCheckTurnLamp() throws BusError {
        DataGroup startCheckTurnLightDataGroup = getDataGroupByKey(START_CHECK_TURN_LIGHT_ID);
        byte[] startCheckTurnLightGroupId = FdxUtils.intToByteArray(startCheckTurnLightDataGroup.getGroupId());
        byte[] startCheckTurnLightDataSize = FdxUtils.intToByteArray(startCheckTurnLightDataGroup.getGroupSize());
        byte[] startCheckTurnLightCommandSize = FdxUtils.intToByteArray(startCheckTurnLightDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(startCheckTurnLightCommandSize, startCheckTurnLightGroupId, startCheckTurnLightDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(DEFAULT_CODE, hexBytesToInt32(startCheckTurnLightDataSize))}));
    }


    public boolean sendCheckFourDoor(String lockStatusCommand) throws BusError {
        DataGroup checkFourDoorDataGroup = getDataGroupByKey(CHECK_FOUR_DOOR_ID);
        byte[] checkFourDoorGroupId = FdxUtils.intToByteArray(checkFourDoorDataGroup.getGroupId());
        byte[] checkFourDoorDataSize = FdxUtils.intToByteArray(checkFourDoorDataGroup.getGroupSize());
        byte[] checkFourDoorCommandSize = FdxUtils.intToByteArray(checkFourDoorDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        int lockStatusCode = getLockStatusCode(lockStatusCommand);
        return sendAndBackwardReadData(checkFourDoorCommandSize, checkFourDoorGroupId, checkFourDoorDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(lockStatusCode, hexBytesToInt32(checkFourDoorDataSize))}));
    }

    private int getLockStatusCode(String lockStatusCommand) {
        if (lockStatusCommand == null) {
            throw new IllegalArgumentException("lockStatusCommand不能为null");
        }
        switch (lockStatusCommand.toUpperCase()) {
            case "UNLOCK":
                return 0;
            case "LOCK":
                return 1;
            default:
                throw new IllegalArgumentException("无效的锁命令: " + lockStatusCommand);
        }
    }

    public boolean sendStartCheckFourDoor() throws BusError {
        DataGroup startCheckFourDoorLockDataGroup = getDataGroupByKey(START_CHECK_FOUR_DOOR_LOCK_ID);
        byte[] startCheckFourDoorLockGroupId = FdxUtils.intToByteArray(startCheckFourDoorLockDataGroup.getGroupId());
        byte[] startCheckFourDoorLockDataSize = FdxUtils.intToByteArray(startCheckFourDoorLockDataGroup.getGroupSize());
        byte[] startCheckFourDoorLockCommandSize = FdxUtils.intToByteArray(startCheckFourDoorLockDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(startCheckFourDoorLockCommandSize, startCheckFourDoorLockGroupId, startCheckFourDoorLockDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(DEFAULT_CODE, hexBytesToInt32(startCheckFourDoorLockDataSize))}));
    }

    public double getCanSignalValue() throws BusError {
        DataGroup getCanSignalDataGroup = getDataGroupByKey(GET_CAN_SIGNAL_ID);
        byte[] getCanSignalGroupId = FdxUtils.intToByteArray(getCanSignalDataGroup.getGroupId());
        return FdxDataProcessor.parseSignalValue(reqData(getCanSignalGroupId));
    }


    public boolean sendSetCanSignalInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        DataGroup setCanSignalDataGroup = getDataGroupByKey(SET_CAN_SIGNAL_ID);
        Map<String, FdxItem> items = setCanSignalDataGroup.getItems();
        byte[] signalDataSize = FdxUtils.intToByteArray(setCanSignalDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(setCanSignalDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanSignalGroupId = FdxUtils.intToByteArray(setCanSignalDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] msgNameBytes = stringBytesPaddedSize(messageName, items.get(CAN_MSG_NAME).getSize());
        byte[] signalNameBytes = stringBytesPaddedSize(signalName, items.get(SET_CAN_SIG_NAME).getSize());
        byte[] signalValueBytes = doubleToHexBytes(signalValue);
        byte[] setSignalDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, nodeNameBytes, msgNameBytes, signalNameBytes, signalValueBytes});
        return sendAndBackwardReadData(signalCommandSize, setCanSignalGroupId, signalDataSize, setSignalDataBytes);
    }

    public boolean sendSetCanSignalCounter() throws BusError {
        DataGroup setCanSignalCounterDataGroup = getDataGroupByKey(SET_CAN_SIGNAL_COUNTER_ID);
        byte[] setCanSignalCounterGroupId = FdxUtils.intToByteArray(setCanSignalCounterDataGroup.getGroupId());
        byte[] setCanSignalCounterDataSize = FdxUtils.intToByteArray(setCanSignalCounterDataGroup.getGroupSize());
        byte[] setCanSignalCounterCommandSize = FdxUtils.intToByteArray(setCanSignalCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanSignalCounterCommandSize, setCanSignalCounterGroupId, setCanSignalCounterDataSize);
    }

    public boolean sendSetCanMessageCSRollingInfo(Integer deviceChannel, String ecuNodeName, int messageID, int checksumStatus, int rollingCounterStatus) throws BusError {
        DataGroup setCanMessageCSRollingDataGroup = getDataGroupByKey(SET_CAN_CHECKSUM_ROLLING_ID);
        Map<String, FdxItem> items = setCanMessageCSRollingDataGroup.getItems();
        byte[] canChecksumRollingDataSize = FdxUtils.intToByteArray(setCanMessageCSRollingDataGroup.getGroupSize());
        byte[] canChecksumRollingCommandSize = FdxUtils.intToByteArray(setCanMessageCSRollingDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanChecksumRollingGroupId = FdxUtils.intToByteArray(setCanMessageCSRollingDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageIDBytes = int32ToHexBytes(messageID, items.get(CHECKSUM_ROLLING_MSG_ID).getSize());
        byte[] checksumStatusBytes = int32ToHexBytes(checksumStatus);
        byte[] rollingCounterStatusBytes = int32ToHexBytes(rollingCounterStatus);
        byte[] byteArrays = mergeHexByteArrays(new byte[][]{nodeChannelBytes, nodeNameBytes, messageIDBytes, checksumStatusBytes, rollingCounterStatusBytes});
        return sendAndBackwardReadData(canChecksumRollingCommandSize, setCanChecksumRollingGroupId, canChecksumRollingDataSize, byteArrays);
    }

    public boolean sendSetCanMessageCSRollingCounter() throws BusError {
        DataGroup setCanMessageCSRollingCounterDataGroup = getDataGroupByKey(SET_CAN_CHECKSUM_ROLLING_COUNTER_ID);
        byte[] setCanChecksumRollingCounterGroupId = FdxUtils.intToByteArray(setCanMessageCSRollingCounterDataGroup.getGroupId());
        byte[] setCanChecksumRollingCounterDataSize = FdxUtils.intToByteArray(setCanMessageCSRollingCounterDataGroup.getGroupSize());
        byte[] setCanChecksumRollingCounterCommandSize = FdxUtils.intToByteArray(setCanMessageCSRollingCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanChecksumRollingCounterCommandSize, setCanChecksumRollingCounterGroupId, setCanChecksumRollingCounterDataSize);
    }


    public boolean sendSetCanPTSInfo(String ecuNodeName, String messageId, String byteInstruction) throws BusError {
        DataGroup setCanPTSDataGroup = getDataGroupByKey(SET_CAN_PTS_ID);
        Map<String, FdxItem> items = setCanPTSDataGroup.getItems();
        byte[] canPTSDataSize = FdxUtils.intToByteArray(setCanPTSDataGroup.getGroupSize());
        byte[] canPTSCommandSize = FdxUtils.intToByteArray(setCanPTSDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanPTSGroupId = FdxUtils.intToByteArray(setCanPTSDataGroup.getGroupId());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] usedArrayElementsNumberBytes = int32ToHexBytes(byteInstruction.length() / 2);   //四个字节的有效数组元素个数
        byte[] byteInstructionBytes = Arrays.copyOf(ByteUtils.hexStringToByteArray(byteInstruction), hexBytesToInt32(intToByteArray(items.get(SET_CAN_PTS_DATE_TX).getSize())) - 4);  //除了4个字节的有效数组元素外，剩下的都是数据
        byte[] canPTSTxBytes = mergeHexByteArrays(new byte[][]{usedArrayElementsNumberBytes, byteInstructionBytes});
        byte[] setCanPTSDataBytes = mergeHexByteArrays(new byte[][]{nodeNameBytes, canPTSTxBytes});
        return sendAndBackwardReadData(canPTSCommandSize, setCanPTSGroupId, canPTSDataSize, setCanPTSDataBytes);
    }

    public boolean sendSetCanPTSCounter() throws BusError {
        DataGroup setCanPTSCounterDataGroup = getDataGroupByKey(SET_CAN_PTS_COUNTER_ID);
        byte[] setCanPTSCounterGroupId = FdxUtils.intToByteArray(setCanPTSCounterDataGroup.getGroupId());
        byte[] setCanPTSCounterDataSize = FdxUtils.intToByteArray(setCanPTSCounterDataGroup.getGroupSize());
        byte[] setCanPTSCounterCommandSize = FdxUtils.intToByteArray(setCanPTSCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanPTSCounterCommandSize, setCanPTSCounterGroupId, setCanPTSCounterDataSize);
    }

    public boolean sendSetCanSingleMsgStatusInfo(Integer deviceChannel, String ecuNodeName, String messageName, int messageStatus) throws BusError {
        DataGroup setCanSingleMsgControlDataGroup = getDataGroupByKey(SET_CAN_SINGLE_MSG_CONTROL_ID);
        Map<String, FdxItem> items = setCanSingleMsgControlDataGroup.getItems();
        byte[] singleMsgControlDataSize = FdxUtils.intToByteArray(setCanSingleMsgControlDataGroup.getGroupSize());
        byte[] singleMsgControlCommandSize = FdxUtils.intToByteArray(setCanSingleMsgControlDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanSingleMsgControlGroupId = FdxUtils.intToByteArray(setCanSingleMsgControlDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageNameBytes = stringBytesPaddedSize(messageName, items.get(CAN_MSG_NAME).getSize());
        byte[] messageStatusBytes = int32ToHexBytes(messageStatus);
        byte[] setCanSingleMsgControlDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, ecuNodeNameBytes, messageNameBytes, messageStatusBytes});
        return sendAndBackwardReadData(singleMsgControlCommandSize, setCanSingleMsgControlGroupId, singleMsgControlDataSize, setCanSingleMsgControlDataBytes);
    }

    public boolean sendSetCanSingleMsgStatusCounter() throws BusError {
        DataGroup setCanSingleMsgStatusCounterDataGroup = getDataGroupByKey(SET_CAN_SINGLE_MSG_CONTROL_COUNTER_ID);
        byte[] setCanSingleMsgControlCounterGroupId = FdxUtils.intToByteArray(setCanSingleMsgStatusCounterDataGroup.getGroupId());
        byte[] setCanSingleMsgControlCounterDataSize = FdxUtils.intToByteArray(setCanSingleMsgStatusCounterDataGroup.getGroupSize());
        byte[] setCanSingleMsgControlCounterCommandSize = FdxUtils.intToByteArray(setCanSingleMsgStatusCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanSingleMsgControlCounterCommandSize, setCanSingleMsgControlCounterGroupId, setCanSingleMsgControlCounterDataSize);
    }

    public boolean sendSetCanEcuAllMsgStatusInfo(Integer deviceChannel, String ecuNodeName, int messageStatus) throws BusError {
        DataGroup setCanEcuAllMsgControlDataGroup = getDataGroupByKey(SET_CAN_ALL_MSG_CONTROL_ID);
        Map<String, FdxItem> items = setCanEcuAllMsgControlDataGroup.getItems();
        byte[] setCanEcuAllMsgDataSize = FdxUtils.intToByteArray(setCanEcuAllMsgControlDataGroup.getGroupSize());
        byte[] setCanEcuAllMsgCommandSize = FdxUtils.intToByteArray(setCanEcuAllMsgControlDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanEcuAllMsgControlGroupId = FdxUtils.intToByteArray(setCanEcuAllMsgControlDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageStatusBytes = int32ToHexBytes(messageStatus);
        byte[] setCanEcuAllMsgControlDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, ecuNodeNameBytes, messageStatusBytes});
        return sendAndBackwardReadData(setCanEcuAllMsgCommandSize, setCanEcuAllMsgControlGroupId, setCanEcuAllMsgDataSize, setCanEcuAllMsgControlDataBytes);
    }

    public boolean sendSetCanEcuAllMsgStatusCounter() throws BusError {
        DataGroup setCanEcuAllMsgStatusCounterDataGroup = getDataGroupByKey(SET_CAN_ALL_MSG_CONTROL_COUNTER_ID);
        byte[] setCanAllMsgControlCounterGroupId = FdxUtils.intToByteArray(setCanEcuAllMsgStatusCounterDataGroup.getGroupId());
        byte[] setCanAllMsgControlCounterDataSize = FdxUtils.intToByteArray(setCanEcuAllMsgStatusCounterDataGroup.getGroupSize());
        byte[] setCanAllMsgControlCounterCommandSize = FdxUtils.intToByteArray(setCanEcuAllMsgStatusCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanAllMsgControlCounterCommandSize, setCanAllMsgControlCounterGroupId, setCanAllMsgControlCounterDataSize);
    }

    public boolean sendSetCanMsgDLCInfo(String ecuNodeName, String messageId, double messageDLC) throws BusError {
        DataGroup setCanMsgDLCDataGroup = getDataGroupByKey(SET_CAN_MSG_DLC_ID);
        Map<String, FdxItem> items = setCanMsgDLCDataGroup.getItems();
        byte[] setCanMsgDLCDataSize = FdxUtils.intToByteArray(setCanMsgDLCDataGroup.getGroupSize());
        byte[] setCanMsgDLCCommandSize = FdxUtils.intToByteArray(setCanMsgDLCDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanMsgDLCGroupId = FdxUtils.intToByteArray(setCanMsgDLCDataGroup.getGroupId());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageIdBytes = stringBytesPaddedSize(messageId, items.get(CAN_MSG_NAME).getSize());
        byte[] messageDLCBytes = doubleToHexBytes(messageDLC);
        byte[] setCanMsgDLCDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{ecuNodeNameBytes, messageIdBytes, messageDLCBytes});
        return sendAndBackwardReadData(setCanMsgDLCCommandSize, setCanMsgDLCGroupId, setCanMsgDLCDataSize, setCanMsgDLCDataBytes);

    }

    public boolean sendSetCanMsgDLCCounter() throws BusError {
        DataGroup setCanMsgDLCCounterDataGroup = getDataGroupByKey(SET_CAN_MSG_DLC_COUNTER_ID);
        byte[] setCanMsgDLCCounterGroupId = FdxUtils.intToByteArray(setCanMsgDLCCounterDataGroup.getGroupId());
        byte[] setCanMsgDLCCounterDataSize = FdxUtils.intToByteArray(setCanMsgDLCCounterDataGroup.getGroupSize());
        byte[] setCanMsgDLCCounterCommandSize = FdxUtils.intToByteArray(setCanMsgDLCCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanMsgDLCCounterCommandSize, setCanMsgDLCCounterGroupId, setCanMsgDLCCounterDataSize);
    }

    public boolean sendSetCanMsgCycleTimeInfo(String ecuNodeName, String messageId, double messageCycleTime) throws BusError {
        DataGroup setCanMsgCycleTimeDataGroup = getDataGroupByKey(SET_CAN_MSG_CYCLE_TIME_ID);
        Map<String, FdxItem> items = setCanMsgCycleTimeDataGroup.getItems();
        byte[] setCanMsgCycleTimeDataSize = FdxUtils.intToByteArray(setCanMsgCycleTimeDataGroup.getGroupSize());
        byte[] setCanMsgCycleTimeCommandSize = FdxUtils.intToByteArray(setCanMsgCycleTimeDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanMsgCycleTimeGroupId = FdxUtils.intToByteArray(setCanMsgCycleTimeDataGroup.getGroupId());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageIdBytes = stringBytesPaddedSize(messageId, items.get(CAN_MSG_NAME).getSize());
        byte[] messageCycleTimeBytes = doubleToHexBytes(messageCycleTime);
        byte[] setCanMsgCycleTimeDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{ecuNodeNameBytes, messageIdBytes, messageCycleTimeBytes});
        return sendAndBackwardReadData(setCanMsgCycleTimeCommandSize, setCanMsgCycleTimeGroupId, setCanMsgCycleTimeDataSize, setCanMsgCycleTimeDataBytes);
    }

    public boolean sendSetCanCycleTimeCounter() throws BusError {
        DataGroup setCanCycleTimeCounterDataGroup = getDataGroupByKey(SET_CAN_MSG_CYCLE_TIME_COUNTER_ID);
        byte[] setCanMsgCycleTimeCounterGroupId = FdxUtils.intToByteArray(setCanCycleTimeCounterDataGroup.getGroupId());
        byte[] setCanMsgCycleTimeCounterDataSize = FdxUtils.intToByteArray(setCanCycleTimeCounterDataGroup.getGroupSize());
        byte[] setCanMsgCycleTimeCounterCommandSize = FdxUtils.intToByteArray(setCanCycleTimeCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanMsgCycleTimeCounterCommandSize, setCanMsgCycleTimeCounterGroupId, setCanMsgCycleTimeCounterDataSize);
    }

    public byte[] SendGetCanPTSInfo() throws BusError {
        DataGroup getCanPTSDataGroup = getDataGroupByKey(GET_CAN_PTS_ID);
        byte[] getCanPTSGroupId = FdxUtils.intToByteArray(getCanPTSDataGroup.getGroupId());
        return reqData(getCanPTSGroupId);
    }

    public byte[] SendGetXcpData() throws BusError {
        DataGroup getCanXcpDataGroup = getDataGroupByKey(GET_CAN_XCP_ID);
        byte[] getCanXCPGroupId = FdxUtils.intToByteArray(getCanXcpDataGroup.getGroupId());
        return reqData(getCanXCPGroupId);
    }

    public boolean sendGetCanXCPInfo(String ecuNodeName, String getXcpName, double getXcpValue) throws BusError {
        DataGroup getCanXCPDataGroup = getDataGroupByKey(GET_CAN_XCP_ID);
        Map<String, FdxItem> items = getCanXCPDataGroup.getItems();
        byte[] getCanXCPDataSize = FdxUtils.intToByteArray(getCanXCPDataGroup.getGroupSize());
        byte[] getCanXCPCommandSize = FdxUtils.intToByteArray(getCanXCPDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] getCanXCPGroupId = FdxUtils.intToByteArray(getCanXCPDataGroup.getGroupId());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_XCP_NODES).getSize());
        byte[] getXcpNameBytes = stringBytesPaddedSize(getXcpName, items.get(GET_CAN_XCP_NAME).getSize());
        byte[] getXcpValueBytes = doubleToHexBytes(getXcpValue);
        byte[] getCanXCPDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{ecuNodeNameBytes, getXcpNameBytes, getXcpValueBytes});
        return sendAndBackwardReadData(getCanXCPCommandSize, getCanXCPGroupId, getCanXCPDataSize, getCanXCPDataBytes);
    }

    public boolean sendGetCanXCPCounter() throws BusError {
        DataGroup getCanXCPCounterDataGroup = getDataGroupByKey(GET_CAN_XCP_COUNTER);
        byte[] getCanXCPCounterGroupId = FdxUtils.intToByteArray(getCanXCPCounterDataGroup.getGroupId());
        byte[] getCanXCPCounterDataSize = FdxUtils.intToByteArray(getCanXCPCounterDataGroup.getGroupSize());
        byte[] getCanXCPCounterCommandSize = FdxUtils.intToByteArray(getCanXCPCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(getCanXCPCounterCommandSize, getCanXCPCounterGroupId, getCanXCPCounterDataSize);
    }

    public boolean sendSetCanXCPInfo(String ecuNodeName, String messageId, double messageValue) throws BusError {
        DataGroup setCanXCPDataGroup = getDataGroupByKey(SET_CAN_XCP_ID);
        Map<String, FdxItem> items = setCanXCPDataGroup.getItems();
        byte[] setCanXCPDataSize = FdxUtils.intToByteArray(setCanXCPDataGroup.getGroupSize());
        byte[] setCanXCPCommandSize = FdxUtils.intToByteArray(setCanXCPDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanXCPGroupId = FdxUtils.intToByteArray(setCanXCPDataGroup.getGroupId());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageIdBytes = stringBytesPaddedSize(messageId, items.get(SET_CAN_XCP_NAME).getSize());
        byte[] messageValueBytes = doubleToHexBytes(messageValue);
        byte[] setCanXCPDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{ecuNodeNameBytes, messageIdBytes, messageValueBytes});
        return sendAndBackwardReadData(setCanXCPCommandSize, setCanXCPGroupId, setCanXCPDataSize, setCanXCPDataBytes);
    }

    public boolean sendSetCanXCPCounter() throws BusError {
        DataGroup setCanXCPCounterDataGroup = getDataGroupByKey(SET_CAN_XCP_COUNTER);
        byte[] setCanXCPCounterGroupId = FdxUtils.intToByteArray(setCanXCPCounterDataGroup.getGroupId());
        byte[] setCanXCPCounterDataSize = FdxUtils.intToByteArray(setCanXCPCounterDataGroup.getGroupSize());
        byte[] setCanXCPCounterCommandSize = FdxUtils.intToByteArray(setCanXCPCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setCanXCPCounterCommandSize, setCanXCPCounterGroupId, setCanXCPCounterDataSize);
    }

    public boolean sendSetLinSignalInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        DataGroup setLinSignalDataGroup = getDataGroupByKey(SET_LIN_SIGNAL_ID);
        Map<String, FdxItem> items = setLinSignalDataGroup.getItems();
        byte[] signalDataSize = FdxUtils.intToByteArray(setLinSignalDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(setLinSignalDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setCanSignalGroupId = FdxUtils.intToByteArray(setLinSignalDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] msgNameBytes = stringBytesPaddedSize(messageName, items.get(LIN_MSG_NAME).getSize());
        byte[] signalNameBytes = stringBytesPaddedSize(signalName, items.get(SET_LIN_SIG_NAME).getSize());
        byte[] signalValueBytes = doubleToHexBytes(signalValue);
        byte[] setSignalDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, nodeNameBytes, msgNameBytes, signalNameBytes, signalValueBytes});
        return sendAndBackwardReadData(signalCommandSize, setCanSignalGroupId, signalDataSize, setSignalDataBytes);
    }

    public boolean sendSetLinSignalCounter() throws BusError {
        DataGroup setLinSignalCounterDataGroup = getDataGroupByKey(SET_LIN_SIGNAL_COUNTER_ID);
        byte[] setLinSignalCounterGroupId = FdxUtils.intToByteArray(setLinSignalCounterDataGroup.getGroupId());
        byte[] setLinSignalCounterDataSize = FdxUtils.intToByteArray(setLinSignalCounterDataGroup.getGroupSize());
        byte[] setLinSignalCounterCommandSize = FdxUtils.intToByteArray(setLinSignalCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setLinSignalCounterCommandSize, setLinSignalCounterGroupId, setLinSignalCounterDataSize);
    }

    public boolean sendGetLinSignalInfo(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        DataGroup getLinSignalDataGroup = getDataGroupByKey(GET_LIN_SIGNAL_ID);
        Map<String, FdxItem> items = getLinSignalDataGroup.getItems();
        byte[] signalDataSize = FdxUtils.intToByteArray(getLinSignalDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(getLinSignalDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] getLinSignalGroupId = FdxUtils.intToByteArray(getLinSignalDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] nodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] msgNameBytes = stringBytesPaddedSize(messageName, items.get(LIN_MSG_NAME).getSize());
        byte[] signalNameBytes = stringBytesPaddedSize(signalName, items.get(GET_LIN_SIG_NAME).getSize());
        byte[] signalValueBytes = doubleToHexBytes(signalValue);
        byte[] setSignalDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, nodeNameBytes, msgNameBytes, signalNameBytes, signalValueBytes});
        return sendAndBackwardReadData(signalCommandSize, getLinSignalGroupId, signalDataSize, setSignalDataBytes);
    }

    public boolean sendGetLinSignalCounter() throws BusError {
        DataGroup getLinSignalCounterDataGroup = getDataGroupByKey(GET_LIN_SIGNAL_COUNTER_ID);
        byte[] getLinSignalCounterGroupId = FdxUtils.intToByteArray(getLinSignalCounterDataGroup.getGroupId());
        byte[] signalDataSize = FdxUtils.intToByteArray(getLinSignalCounterDataGroup.getGroupSize());
        byte[] signalCommandSize = FdxUtils.intToByteArray(getLinSignalCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(signalCommandSize, getLinSignalCounterGroupId, signalDataSize);
    }

    public byte[] reqLinSignalBytes() throws BusError {
        DataGroup getLinSignalDataGroup = getDataGroupByKey(GET_LIN_SIGNAL_ID);
        byte[] getLinSignalGroupId = FdxUtils.intToByteArray(getLinSignalDataGroup.getGroupId());
        return reqData(getLinSignalGroupId);
    }

    public boolean sendSetLinSingleMsgStatusInfo(Integer deviceChannel, String ecuNodeName, int messageID, int messageStatus) throws BusError {
        DataGroup setLinSingleMsgControlDataGroup = getDataGroupByKey(SET_LIN_SINGLE_MSG_CONTROL_ID);
        Map<String, FdxItem> items = setLinSingleMsgControlDataGroup.getItems();
        byte[] singleMsgControlDataSize = FdxUtils.intToByteArray(setLinSingleMsgControlDataGroup.getGroupSize());
        byte[] singleMsgControlCommandSize = FdxUtils.intToByteArray(setLinSingleMsgControlDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setLinSingleMsgControlGroupId = FdxUtils.intToByteArray(setLinSingleMsgControlDataGroup.getGroupId());
        byte[] nodeChannelBytes = int32ToHexBytes(deviceChannel, items.get(SET_NODE_CHANNEL).getSize());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageIDBytes = int32ToHexBytes(messageID, items.get(SET_LIN_FrameID).getSize());
        byte[] messageStatusBytes = int32ToHexBytes(messageStatus);
        byte[] setCanSingleMsgControlDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{nodeChannelBytes, ecuNodeNameBytes, messageIDBytes, messageStatusBytes});
        return sendAndBackwardReadData(singleMsgControlCommandSize, setLinSingleMsgControlGroupId, singleMsgControlDataSize, setCanSingleMsgControlDataBytes);
    }

    public boolean sendSetLinSingleMsgStatusCounter() throws BusError {
        DataGroup sendSetLinSingleMsgStatusCounterDataGroup = getDataGroupByKey(SET_LIN_SINGLE_MSG_CONTROL_COUNTER_ID);
        byte[] setLinSingleMsgControlCounterGroupId = FdxUtils.intToByteArray(sendSetLinSingleMsgStatusCounterDataGroup.getGroupId());
        byte[] setLinSingleMsgControlCounterDataSize = FdxUtils.intToByteArray(sendSetLinSingleMsgStatusCounterDataGroup.getGroupSize());
        byte[] setLinSingleMsgControlCounterCommandSize = FdxUtils.intToByteArray(sendSetLinSingleMsgStatusCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setLinSingleMsgControlCounterCommandSize, setLinSingleMsgControlCounterGroupId, setLinSingleMsgControlCounterDataSize);
    }

    public boolean sendSetLinEcuAllMsgStatusInfo(String ecuNodeName, int messageStatus) throws BusError {
        DataGroup setLinEcuAllMsgControlDataGroup = getDataGroupByKey(SET_LIN_ALL_MSG_CONTROL_ID);
        Map<String, FdxItem> items = setLinEcuAllMsgControlDataGroup.getItems();
        byte[] setLinEcuAllMsgDataSize = FdxUtils.intToByteArray(setLinEcuAllMsgControlDataGroup.getGroupSize());
        byte[] setLinEcuAllMsgCommandSize = FdxUtils.intToByteArray(setLinEcuAllMsgControlDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setLinEcuAllMsgControlGroupId = FdxUtils.intToByteArray(setLinEcuAllMsgControlDataGroup.getGroupId());
        byte[] ecuNodeNameBytes = stringBytesPaddedSize(ecuNodeName, items.get(SET_NAME_NODES).getSize());
        byte[] messageStatusBytes = int32ToHexBytes(messageStatus);
        byte[] setLinEcuAllMsgControlDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{ecuNodeNameBytes, messageStatusBytes});
        return sendAndBackwardReadData(setLinEcuAllMsgCommandSize, setLinEcuAllMsgControlGroupId, setLinEcuAllMsgDataSize, setLinEcuAllMsgControlDataBytes);
    }

    public boolean sendSetLinEcuAllMsgStatusCounter() throws BusError {
        DataGroup setLinEcuAllMsgStatusCounterDataGroup = getDataGroupByKey(SET_LIN_ALL_MSG_CONTROL_COUNTER_ID);
        byte[] setLinAllMsgControlCounterGroupId = FdxUtils.intToByteArray(setLinEcuAllMsgStatusCounterDataGroup.getGroupId());
        byte[] setLinAllMsgControlCounterDataSize = FdxUtils.intToByteArray(setLinEcuAllMsgStatusCounterDataGroup.getGroupSize());
        byte[] setLinAllMsgControlCounterCommandSize = FdxUtils.intToByteArray(setLinEcuAllMsgStatusCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setLinAllMsgControlCounterCommandSize, setLinAllMsgControlCounterGroupId, setLinAllMsgControlCounterDataSize);
    }

    public boolean sendAndBackwardReadData(byte[] commandSize, byte[] groupId, byte[] dataSize, byte[] dataBytes) throws BusError {
        long sTime = System.currentTimeMillis();
        byte[] dataExchange = FdxUtils.mergeHexByteArrays(new byte[][]{buildHeader(), commandSize, FdxCommandConstant.dataExchangeCommandCode,
                groupId, dataSize, dataBytes});
//        log.info("sendDataAndJudgeBytes---dataBytes.length: {}", dataBytes.length);
        sendTo(dataExchange);
        byte[] reqDataBytes = reqData(groupId);
        byte[] reqBytes = Arrays.copyOfRange(reqDataBytes, reqDataBytes.length - dataBytes.length, reqDataBytes.length);
        log.info("发送data:{}, 回读data：{}", Hex.encodeHexString(dataBytes), Hex.encodeHexString(reqDataBytes));
        boolean res = Arrays.equals(dataBytes, reqBytes);
//        log.info("sendDataAndJudgeBytes, 返回结果:{}, 执行时长:{}毫秒", res ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return res;
    }


    public void sendTo(byte[] bytes) throws BusError {
        try {
            send(new DatagramPacket(
                    bytes,
                    bytes.length,
                    InetAddress.getByName(serverIP),
                    serverPort)); //响应
        } catch (IOException e) {
            throw new BusError(e);
        }
    }

    public int getCounter() {
        if (counter > Integer.MIN_VALUE) {
            counter--;
        } else {
            counter = Integer.MAX_VALUE;
        }
        return counter;
    }

    public boolean setReturnValue(int returnValue) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup returnValueDataGroup = getDataGroupByKey(RETURN_VALUE_ID);
        int groupSize = returnValueDataGroup.getGroupSize();
        byte[] returnValueGroupId = FdxUtils.intToByteArray(returnValueDataGroup.getGroupId());
        byte[] returnValueDataSize = FdxUtils.intToByteArray(groupSize);
        byte[] returnValueCommandSize = FdxUtils.intToByteArray(groupSize + 8);
        sendData(returnValueCommandSize, returnValueGroupId, returnValueDataSize, int32ToHexBytes(returnValue, groupSize));
        boolean pass = returnValue == getReturnValueFromCANoe();
        log.info("setReturnValue值：{}, 返回结果:{}, 执行时长:{}毫秒", returnValue, pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean setCounter(byte[] commandSize, byte[] groupId, byte[] dataSize) throws BusError {
        int counter = getCounter();
        byte[] counterBytes = int32ToHexBytes(counter, hexBytesToInt32(dataSize));
        sendData(commandSize, groupId, dataSize, counterBytes);
        return counter == getCounterFromCANoe(groupId, dataSize);
    }

    public boolean sendSetPtsSwVersion() throws BusError {
        DataGroup setPtsSwVersionDataGroup = getDataGroupByKey(PTS_SW_VERSION);
        byte[] setPtsSwVersionDataSize = FdxUtils.intToByteArray(setPtsSwVersionDataGroup.getGroupSize());
        byte[] setPtsSwVersionCommandSize = FdxUtils.intToByteArray(setPtsSwVersionDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setPtsSwVersionGroupId = FdxUtils.intToByteArray(setPtsSwVersionDataGroup.getGroupId());
        return sendAndBackwardReadData(setPtsSwVersionCommandSize, setPtsSwVersionGroupId, setPtsSwVersionDataSize,
                stringBytesPaddedSize(PTS_SW_VERSION_INIT_VALUE, setPtsSwVersionDataGroup.getGroupSize()));
    }

    public boolean sendSetUpgrade() throws BusError {
        DataGroup setUpgradeDataGroup = getDataGroupByKey(SET_UPGRADE);
        Map<String, FdxItem> items = setUpgradeDataGroup.getItems();
        byte[] setUpgradeDataSize = FdxUtils.intToByteArray(setUpgradeDataGroup.getGroupSize());
        byte[] setUpgradeCommandSize = FdxUtils.intToByteArray(setUpgradeDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setUpgradeGroupId = FdxUtils.intToByteArray(setUpgradeDataGroup.getGroupId());
        return sendAndBackwardReadData(setUpgradeCommandSize, setUpgradeGroupId, setUpgradeDataSize,
                FdxDataProcessor.createNotificationUpgradeDataBytes());
    }

    public boolean sendSetUpgradeCounter() throws BusError {
        DataGroup setUpgradeCounterDataGroup = getDataGroupByKey(SET_UPGRADE_COUNTER);
        byte[] setUpgradeCounterGroupId = FdxUtils.intToByteArray(setUpgradeCounterDataGroup.getGroupId());
        byte[] setUpgradeCounterDataSize = FdxUtils.intToByteArray(setUpgradeCounterDataGroup.getGroupSize());
        byte[] setUpgradeCounterCommandSize = FdxUtils.intToByteArray(setUpgradeCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setUpgradeCounterCommandSize, setUpgradeCounterGroupId, setUpgradeCounterDataSize);
    }

    public int getCounterFromCANoe(byte[] groupId, byte[] dataSize) throws BusError {
        byte[] data = reqData(groupId);
        int startIndex = data.length - hexBytesToInt32(dataSize);
        return hexBytesToInt32(Arrays.copyOfRange(data, startIndex, startIndex + 4));
    }

    public int getReturnValueFromCANoe() throws BusError {
        Map<String, DataGroup> dataGroups = CANoeFdxDescription.getInstance().getDataGroups();
        DataGroup returnValueDataGroup = dataGroups.get(RETURN_VALUE_ID);
        Map<String, FdxItem> items = returnValueDataGroup.getItems();
        byte[] returnValueGroupId = FdxUtils.intToByteArray(returnValueDataGroup.getGroupId());
        int returnValueItemSize = items.get(RETURN_VALUE).getSize();
        byte[] data = reqData(returnValueGroupId);
        return hexBytesToInt32(Arrays.copyOfRange(data, data.length - returnValueItemSize, data.length));
    }

    public byte[] getUDSRecUdsData() throws BusError {
        DataGroup getUDSRecUdsDataGroup = getDataGroupByKey(GET_UDS_REC_UDS_DATA);
        byte[] getUDSRecUdsDataGroupId = FdxUtils.intToByteArray(getUDSRecUdsDataGroup.getGroupId());
        byte[] getDoCANRecUdsDataSize = FdxUtils.intToByteArray(getUDSRecUdsDataGroup.getGroupSize());
        return getRecUdsDataFromCANoe(getUDSRecUdsDataGroupId, getDoCANRecUdsDataSize);
    }

    public String getUDSRecUdsDataDescribe() throws BusError {
        DataGroup getUDSRecUdsDataDescribeGroup = getDataGroupByKey(GET_UDS_REC_UDS_DATA_DESCRIBE);
        byte[] getUDSRecUdsDataDescribeGroupId = FdxUtils.intToByteArray(getUDSRecUdsDataDescribeGroup.getGroupId());
        byte[] getDoCANRecUdsDataDescribeDataSize = FdxUtils.intToByteArray(getUDSRecUdsDataDescribeGroup.getGroupSize());
        String udsRecUdsDataDescribe = hexStringToUTF8(getRecUdsDataDescribeFromCANoe(getUDSRecUdsDataDescribeGroupId, getDoCANRecUdsDataDescribeDataSize));
        log.info("udsRecUdsDataDescribe: {}", udsRecUdsDataDescribe);
        return udsRecUdsDataDescribe;
    }

    public String getUDSRecUdsDataDescribe22() throws BusError {
        DataGroup getUDSRecUdsDataDescribe22Group = getDataGroupByKey(GET_UDS_REC_UDS_DATA_DESCRIBE22);
        byte[] getUDSRecUdsDataDescribe22GroupId = FdxUtils.intToByteArray(getUDSRecUdsDataDescribe22Group.getGroupId());
        byte[] getDoCANRecUdsDataDescribe22DataSize = FdxUtils.intToByteArray(getUDSRecUdsDataDescribe22Group.getGroupSize());
        String udsRecUdsDataDescribe22 = hexStringToUTF8(getRecUdsDataDescribeFromCANoe(getUDSRecUdsDataDescribe22GroupId, getDoCANRecUdsDataDescribe22DataSize));
        log.info("udsRecUdsDataDescribe22: {}", udsRecUdsDataDescribe22);
        return udsRecUdsDataDescribe22;
    }

    public byte[] getRecUdsDataFromCANoe(byte[] groupId, byte[] dataSize) throws BusError {
        byte[] reqDataBytes = reqData(groupId);
        int recUdsDataLength = hexBytesToInt32(dataSize);
        if (recUdsDataLength < 0 || recUdsDataLength > reqDataBytes.length) {
            throw new IllegalArgumentException("Invalid recUdsDataLength: " + recUdsDataLength);
        }
        int startIndex = reqDataBytes.length - recUdsDataLength;
        if (startIndex < 0) {
            throw new IllegalArgumentException("Start index is out of bounds: " + startIndex);
        }
        // 从字节数组中提取有效数据长度
        int effectiveDataLength;
        try {
            effectiveDataLength = hexBytesToInt32(Arrays.copyOfRange(reqDataBytes, startIndex, startIndex + 4));
        } catch (ArrayIndexOutOfBoundsException e) {
            // 在这种情况下，我们可能无法获得有效数据长度，因此抛出异常
            throw new RuntimeException("Failed to extract effective data length.", e);
        }
        // 验证有效数据长度是否合理
        if (effectiveDataLength < 0 || (startIndex + 4 + effectiveDataLength) > reqDataBytes.length) {
            throw new IllegalArgumentException("Invalid effectiveDataLength: " + effectiveDataLength);
        }
        // 尝试提取有效数据
        try {
            return Arrays.copyOfRange(reqDataBytes, startIndex + 4, startIndex + 4 + effectiveDataLength);
        } catch (ArrayIndexOutOfBoundsException e) {
            // 如果由于某种原因无法提取有效数据，抛出异常
            throw new RuntimeException("Failed to extract effective data.", e);
        }
    }

    private byte[] getRecUdsDataDescribeFromCANoe(byte[] groupId, byte[] dataSize) throws BusError {
        byte[] reqDataBytes = reqData(groupId);
        int recUdsDataDescribeLength = hexBytesToInt32(dataSize);
        if (recUdsDataDescribeLength < 0 || recUdsDataDescribeLength > reqDataBytes.length) {
            throw new IllegalArgumentException("Invalid recUdsDataDescribeLength: " + recUdsDataDescribeLength);
        }
        int startIndex = reqDataBytes.length - recUdsDataDescribeLength;
        if (startIndex < 0) {
            throw new IllegalArgumentException("Start index is out of bounds: " + startIndex);
        }
        try {
            return Arrays.copyOfRange(reqDataBytes, startIndex, startIndex + recUdsDataDescribeLength);
        } catch (ArrayIndexOutOfBoundsException e) {
            // 如果由于某种原因无法提取有效数据，抛出异常
            throw new RuntimeException("Failed to extract effective data.", e);
        }
    }

    public String getPtsSwVersionFromCANoe() throws BusError {
        DataGroup ptsSwVersionDataGroup = getDataGroupByKey(PTS_SW_VERSION);
        byte[] ptsSwVersionDataSize = FdxUtils.intToByteArray(ptsSwVersionDataGroup.getGroupSize());
        byte[] ptsSwVersionGroupId = FdxUtils.intToByteArray(ptsSwVersionDataGroup.getGroupId());
        byte[] data = reqData(ptsSwVersionGroupId);
        int xmlDataSize = hexBytesToInt32(ptsSwVersionDataSize);
        int startIndex = data.length - xmlDataSize;
        return hexBytesToString(Arrays.copyOfRange(data, startIndex, startIndex + xmlDataSize));
    }

    public boolean setAddress(int addressID) throws BusError {
        DataGroup setAddressDataGroup = getDataGroupByKey(SET_ADDRESS);
        byte[] setAddressGroupId = FdxUtils.intToByteArray(setAddressDataGroup.getGroupId());
        byte[] setDoCANAddressDataSize = FdxUtils.intToByteArray(setAddressDataGroup.getGroupSize());
        byte[] setAddressCommandSize = FdxUtils.intToByteArray(setAddressDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setAddressCommandSize, setAddressGroupId, setDoCANAddressDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(addressID, hexBytesToInt32(setDoCANAddressDataSize))}));
    }

    public boolean sendUdsData(String sendUdsDataString) throws BusError {
        DataGroup sendUdsDataGroup = getDataGroupByKey(SEND_UDS_DATA);
        byte[] sendUDSDataGroupId = FdxUtils.intToByteArray(sendUdsDataGroup.getGroupId());
        byte[] sendUDSDataCommandSize = FdxUtils.intToByteArray(sendUdsDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] sendUDSDataDataSize = FdxUtils.intToByteArray(sendUdsDataGroup.getGroupSize());
        return sendAndBackwardReadData(sendUDSDataCommandSize, sendUDSDataGroupId, sendUDSDataDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{stringBytesToCANoeDataType(sendUdsDataString)}));
    }

    //UDS的log录制
    public boolean setUDSLogName() throws BusError {
        if (udsLogName == null)
            return false;
        DataGroup setLogNameDataGroup = getDataGroupByKey(SET_UDS_LOG_NAME);
        byte[] setUDSLogNameGroupId = FdxUtils.intToByteArray(setLogNameDataGroup.getGroupId());
        byte[] setUDSLogNameDataSize = FdxUtils.intToByteArray(setLogNameDataGroup.getGroupSize());
        byte[] setUDSLogNameCommandSize = FdxUtils.intToByteArray(setLogNameDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setUDSLogNameCommandSize, setUDSLogNameGroupId, setUDSLogNameDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{stringBytesPaddedSize(udsLogName, hexBytesToInt32(setUDSLogNameDataSize))}));
    }

//    public boolean setUSDLogStatus(int statusCode) throws BusError {
//        DataGroup setUDSLogStatusDataGroup = getDataGroupByKey(SET_UDS_LOGGING_STATUS);
//        byte[] setUDSLogStatusGroupId = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupId());
//        byte[] setUDSLogStatusDataSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize());
//        byte[] setUDSLogStatusCommandSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
//        return setLoggingStatus(setUDSLogStatusCommandSize, setUDSLogStatusGroupId, setUDSLogStatusDataSize,
//                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(statusCode, hexBytesToInt32(setUDSLogStatusDataSize))}));
//    }

    public boolean setUSDStartLogging() throws BusError {
        DataGroup setUDSLogStatusDataGroup = getDataGroupByKey(SET_UDS_START_LOGGING);
        byte[] setUDSLogStatusGroupId = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupId());
        byte[] setUDSLogStatusDataSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize());
        byte[] setUDSLogStatusCommandSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setLoggingStatus(setUDSLogStatusCommandSize, setUDSLogStatusGroupId, setUDSLogStatusDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(DEFAULT_CODE, hexBytesToInt32(setUDSLogStatusDataSize))}));
    }

    public boolean setUDSStopLogging() throws BusError {
        DataGroup setUDSLogStatusDataGroup = getDataGroupByKey(SET_UDS_STOP_LOGGING);
        byte[] setUDSLogStatusGroupId = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupId());
        byte[] setUDSLogStatusDataSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize());
        byte[] setUDSLogStatusCommandSize = FdxUtils.intToByteArray(setUDSLogStatusDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setLoggingStatus(setUDSLogStatusCommandSize, setUDSLogStatusGroupId, setUDSLogStatusDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(DEFAULT_CODE, hexBytesToInt32(setUDSLogStatusDataSize))}));
    }

    //普通CAN脚本的log录制
    public boolean setCANLogName() throws BusError {
        DataGroup setCANLogNameDataGroup = getDataGroupByKey(SET_CAN_LOG_NAME);
        byte[] setCANLogNameGroupId = FdxUtils.intToByteArray(setCANLogNameDataGroup.getGroupId());
        byte[] setCANLogNameDataSize = FdxUtils.intToByteArray(setCANLogNameDataGroup.getGroupSize());
        byte[] setCANLogNameCommandSize = FdxUtils.intToByteArray(setCANLogNameDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setCANLogNameCommandSize, setCANLogNameGroupId, setCANLogNameDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{stringBytesPaddedSize(canLogName, hexBytesToInt32(setCANLogNameDataSize))}));
    }

    public boolean setCanLogStatus(int statusCode) throws BusError {
        DataGroup setCanLogStatusDataGroup = getDataGroupByKey(SET_CAN_LOGGING_STATUS);
        byte[] setCanLogStatusGroupId = FdxUtils.intToByteArray(setCanLogStatusDataGroup.getGroupId());
        byte[] setCanLogStatusDataSize = FdxUtils.intToByteArray(setCanLogStatusDataGroup.getGroupSize());
        byte[] setCanLogStatusCommandSize = FdxUtils.intToByteArray(setCanLogStatusDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setLoggingStatus(setCanLogStatusCommandSize, setCanLogStatusGroupId, setCanLogStatusDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(statusCode, hexBytesToInt32(setCanLogStatusDataSize))}));
    }


    public boolean setCANStartLogging() throws BusError {
        return setCanLogStatus(1);
    }

    public boolean setCANStopLogging() throws BusError {
        return setCanLogStatus(0);
    }


    public int getFinishFlagFromCANoe(byte[] groupId, byte[] dataSize) throws BusError {
        byte[] data = reqData(groupId);
        int startIndex = data.length - hexBytesToInt32(dataSize);
        return hexBytesToInt32(Arrays.copyOfRange(data, startIndex, startIndex + 4));
    }

    public static <T> T getSupplierWithTimeout(Supplier<T> function, long timeout, TimeUnit unit, Supplier<Boolean> isResultConsistent) throws BusError {
        Supplier<T> geSupplier = () -> {
            try {
                return function.get();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };

        return FdxTimeoutUtils.executeWithTimeout(geSupplier, timeout, unit, isResultConsistent);
    }

    /**
     * 读取CANoe返回returnValue的结果
     */
    public boolean getReturnValue() throws BusError {
        return getReturnValue(GET_RETURN_VALUE_TIMEOUT);
    }

    /**
     * 读取CANoe返回returnValue的结果
     */
    public boolean getReturnValue(int timeout) throws BusError {
        long startMills = System.currentTimeMillis();
        int returnVal;
        while (true) {
            returnVal = getReturnValueFromCANoe();
            if (returnVal != NOT_RESPONSE) {
                log.info("读取returnVal响应事件完毕，returnVal：{}, 共耗时:{}ms", returnVal, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > timeout) {
                log.info("读取returnVal响应事件超时:{}ms, returnVal：{}", System.currentTimeMillis() - startMills, returnVal);
                break;
            }
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        }
        return returnVal == RETURN_VALUE_PASS;
    }

    /**
     * 读取CANoe返回ptsSwVersion的结果
     */
    public String getPtsSwVersion() throws BusError {
        long startMills = System.currentTimeMillis();
        String ptsSwVersion;
        int count = 0;
//        String initPtsSwVersion = getPtsSwVersionFromCANoe();
        while (true) {
            count++;
            ptsSwVersion = getPtsSwVersionFromCANoe();
//            if (initValue != null && !initPtsSwVersion.equals(initValue) && ptsSwVersion.equals(initValue)) {
//                log.info("读取系统变量:ptsSwVersion的初始值:{}，成功！", ptsSwVersion);
//                initPtsSwVersion = ptsSwVersion;
//            }
            if (!ptsSwVersion.equals("NULL")) {
                log.info("读取系统变量:ptsSwVersion的最新值:{}，成功！！！读取SystemVariant次数:{}, 共耗时:{}ms", ptsSwVersion, count, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > GET_PTS_SW_VERSION_TIMEOUT) {
                log.info("读取系统变量:ptsSwVersion的最新值:{}, 存在读取时间超时！！！读取SystemVariant次数:{}, 共耗时:{}ms", ptsSwVersion, count, System.currentTimeMillis() - startMills);
                break;
            }
        }
        return ptsSwVersion;
    }

    public boolean setStartUdsTestFlag(int flag) throws BusError {
        DataGroup setStartUdsTestDataGroup = getDataGroupByKey(SET_START_UDS_TEST);
        byte[] setStartUdsTestGroupId = FdxUtils.intToByteArray(setStartUdsTestDataGroup.getGroupId());
        byte[] setStartUdsTestDataSize = FdxUtils.intToByteArray(setStartUdsTestDataGroup.getGroupSize());
        byte[] setStartUdsTestCommandSize = FdxUtils.intToByteArray(setStartUdsTestDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setStartUdsTestCommandSize, setStartUdsTestGroupId, setStartUdsTestDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(flag, hexBytesToInt32(setStartUdsTestDataSize))})));
    }

    public boolean setResetChargeFlag(int flag) throws BusError {
        DataGroup setResetChargeDataGroup = getDataGroupByKey(SET_RESET_CHARGE);
        byte[] setResetChargeGroupId = FdxUtils.intToByteArray(setResetChargeDataGroup.getGroupId());
        byte[] resetChargeDataSize = FdxUtils.intToByteArray(setResetChargeDataGroup.getGroupSize());
        byte[] resetChargeCommandSize = FdxUtils.intToByteArray(setResetChargeDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(resetChargeCommandSize, setResetChargeGroupId, resetChargeDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(flag, hexBytesToInt32(resetChargeDataSize))})));
    }

    public boolean sendSetXcpSwitchInfo(int commandId) throws BusError {
        DataGroup setXcpSwitchDataGroup = getDataGroupByKey(SET_XCP_FUN_SWITCH);
        byte[] setXcpFunSwitchGroupId = FdxUtils.intToByteArray(setXcpSwitchDataGroup.getGroupId());
        byte[] xcpFunSwitchDataSize = FdxUtils.intToByteArray(setXcpSwitchDataGroup.getGroupSize());
        byte[] xcpFunSwitchCommandSize = FdxUtils.intToByteArray(setXcpSwitchDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(xcpFunSwitchCommandSize, setXcpFunSwitchGroupId, xcpFunSwitchDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(commandId, hexBytesToInt32(xcpFunSwitchDataSize))})));
    }

    public boolean sendSetKeyPositionInfo(int commandId) throws BusError {
        DataGroup setKeyPositionDataGroup = getDataGroupByKey(SET_KEY_POSITION);
        byte[] setKeyPositionGroupId = FdxUtils.intToByteArray(setKeyPositionDataGroup.getGroupId());
        byte[] keyPositionDataSize = FdxUtils.intToByteArray(setKeyPositionDataGroup.getGroupSize());
        byte[] keyPositionCommandSize = FdxUtils.intToByteArray(setKeyPositionDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(keyPositionCommandSize, setKeyPositionGroupId, keyPositionDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(commandId, hexBytesToInt32(keyPositionDataSize))})));
    }

    public boolean sendSetKeyButtonInfo(int commandId) throws BusError {
        int keyButtonValue = 1;
        switch (commandId) {
            case 0:
                DataGroup setKeyLockDataGroup = getDataGroupByKey(SET_KEY_LOCK);
                byte[] setKeyLockGroupId = FdxUtils.intToByteArray(setKeyLockDataGroup.getGroupId());
                byte[] keyLockDataSize = FdxUtils.intToByteArray(setKeyLockDataGroup.getGroupSize());
                byte[] keyLockCommandSize = FdxUtils.intToByteArray(setKeyLockDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
                return sendAndBackwardReadData(keyLockCommandSize, setKeyLockGroupId, keyLockDataSize,
                        Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(keyButtonValue, hexBytesToInt32(keyLockDataSize))})));
            case 1:
                DataGroup setKeyUnlockDataGroup = getDataGroupByKey(SET_KEY_UNLOCK);
                byte[] setKeyUnlockGroupId = FdxUtils.intToByteArray(setKeyUnlockDataGroup.getGroupId());
                byte[] keyUnlockDataSize = FdxUtils.intToByteArray(setKeyUnlockDataGroup.getGroupSize());
                byte[] keyUnlockCommandSize = FdxUtils.intToByteArray(setKeyUnlockDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
                return sendAndBackwardReadData(keyUnlockCommandSize, setKeyUnlockGroupId, keyUnlockDataSize,
                        Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(keyButtonValue, hexBytesToInt32(keyUnlockDataSize))})));
            case 2:
                DataGroup setKeyFindCarDataGroup = getDataGroupByKey(SET_KEY_FIND_CAR);
                byte[] setKeyFindCarGroupId = FdxUtils.intToByteArray(setKeyFindCarDataGroup.getGroupId());
                byte[] keyFindCarDataSize = FdxUtils.intToByteArray(setKeyFindCarDataGroup.getGroupSize());
                byte[] keyFindCarCommandSize = FdxUtils.intToByteArray(setKeyFindCarDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
                return sendAndBackwardReadData(keyFindCarCommandSize, setKeyFindCarGroupId, keyFindCarDataSize,
                        Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(keyButtonValue, hexBytesToInt32(keyFindCarDataSize))})));
            case 3:
                DataGroup setKeyTallgateDataGroup = getDataGroupByKey(SET_KEY_TALLGATE);
                byte[] setKeyTallgateGroupId = FdxUtils.intToByteArray(setKeyTallgateDataGroup.getGroupId());
                byte[] keyTallgateDataSize = FdxUtils.intToByteArray(setKeyTallgateDataGroup.getGroupSize());
                byte[] keyTallgateCommandSize = FdxUtils.intToByteArray(setKeyTallgateDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
                return sendAndBackwardReadData(keyTallgateCommandSize, setKeyTallgateGroupId, keyTallgateDataSize,
                        Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(keyButtonValue, hexBytesToInt32(keyTallgateDataSize))})));
            case 4:
                DataGroup setKeyLowVoltageDataGroup = getDataGroupByKey(SET_KEY_LOW_VOLTAGE);
                byte[] setKeyLowVoltageGroupId = FdxUtils.intToByteArray(setKeyLowVoltageDataGroup.getGroupId());
                byte[] keyLowVoltageDataSize = FdxUtils.intToByteArray(setKeyLowVoltageDataGroup.getGroupSize());
                byte[] keyLowVoltageCommandSize = FdxUtils.intToByteArray(setKeyLowVoltageDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
                return sendAndBackwardReadData(keyLowVoltageCommandSize, setKeyLowVoltageGroupId, keyLowVoltageDataSize,
                        Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(keyButtonValue, hexBytesToInt32(keyLowVoltageDataSize))})));
        }
        return false;
    }

    public boolean sendRDefogStsInfo(int commandId) throws BusError {
        DataGroup setRDefogStsDataGroup = getDataGroupByKey(SET_RDEFOGSTS);
        byte[] setRDefogStsGroupId = FdxUtils.intToByteArray(setRDefogStsDataGroup.getGroupId());
        byte[] setRDefogStsDataSize = FdxUtils.intToByteArray(setRDefogStsDataGroup.getGroupSize());
        byte[] setRDefogStsCommandSize = FdxUtils.intToByteArray(setRDefogStsDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setRDefogStsCommandSize, setRDefogStsGroupId, setRDefogStsDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(commandId, hexBytesToInt32(setRDefogStsDataSize))})));
    }

    public boolean sendSetCanXCPVarInfo(String varName, int xcpValue) throws BusError {
        DataGroup setCanXCPVarDataGroup = getDataGroupByKey(SET_XCP_VAR);
        Map<String, FdxItem> items = setCanXCPVarDataGroup.getItems();
        byte[] setCanXCPVarGroupId = FdxUtils.intToByteArray(setCanXCPVarDataGroup.getGroupId());
        byte[] setCanXCPVarDataSize = FdxUtils.intToByteArray(setCanXCPVarDataGroup.getGroupSize());
        byte[] setCanXCPVarCommandSize = FdxUtils.intToByteArray(setCanXCPVarDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] varNameBytes = stringBytesPaddedSize(varName, items.get(SET_XCP_VAR_NAME).getSize());
        byte[] xcpValueBytes = int32ToHexBytes(xcpValue);
        byte[] setCanXCPVarDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{varNameBytes, xcpValueBytes});
        return sendAndBackwardReadData(setCanXCPVarCommandSize, setCanXCPVarGroupId, setCanXCPVarDataSize, setCanXCPVarDataBytes);
    }


    public boolean sendSetCanXCPBtn() throws BusError {
        int btnValue = 1;
        DataGroup setXcpBtnDataGroup = getDataGroupByKey(SET_XCP_BTN);
        byte[] setXcpBtnGroupId = FdxUtils.intToByteArray(setXcpBtnDataGroup.getGroupId());
        byte[] setXcpBtnDataSize = FdxUtils.intToByteArray(setXcpBtnDataGroup.getGroupSize());
        byte[] setXcpBtnCommandSize = FdxUtils.intToByteArray(setXcpBtnDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        sendData(setXcpBtnCommandSize, setXcpBtnGroupId, setXcpBtnDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(btnValue, hexBytesToInt32(setXcpBtnDataSize))}));
        return true;
    }

    public boolean sendGetXcpVarInfo(String varName, int varValue) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup checkXcpVarDataGroup = getDataGroupByKey(CHECK_XCP_VAR);
        Map<String, FdxItem> items = checkXcpVarDataGroup.getItems();
        byte[] checkXcpVarGroupId = FdxUtils.intToByteArray(checkXcpVarDataGroup.getGroupId());
        byte[] checkXcpVarDataSize = FdxUtils.intToByteArray(checkXcpVarDataGroup.getGroupSize());
        byte[] checkXcpVarCommandSize = FdxUtils.intToByteArray(checkXcpVarDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] varNameBytes = stringBytesPaddedSize(varName, items.get(SET_XCP_VAR_NAME).getSize());
        byte[] varValueBytes = int32ToHexBytes(varValue);
        byte[] checkXcpVarDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{varNameBytes, varValueBytes});
        boolean pass = sendAndBackwardReadData(checkXcpVarCommandSize, checkXcpVarGroupId, checkXcpVarDataSize, checkXcpVarDataBytes);
        log.info("sendGetXcpVarInfo：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public int getCanXcpVarValue() throws BusError {
        DataGroup getCanXcpVarDataGroup = getDataGroupByKey(CHECK_XCP_VAR);
        byte[] getCanXcpVarGroupId = FdxUtils.intToByteArray(getCanXcpVarDataGroup.getGroupId());
        return FdxDataProcessor.parseIntValueFromBytes(reqData(getCanXcpVarGroupId));
    }


    public boolean sendGetCanXCPBtn() throws BusError {
        int btnValue = 1;
        DataGroup getXcpBtnDataGroup = getDataGroupByKey(GET_XCP_BTN);
        byte[] getXcpBtnGroupId = FdxUtils.intToByteArray(getXcpBtnDataGroup.getGroupId());
        byte[] getXcpBtnDataSize = FdxUtils.intToByteArray(getXcpBtnDataGroup.getGroupSize());
        byte[] getXcpBtnCommandSize = FdxUtils.intToByteArray(getXcpBtnDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        sendData(getXcpBtnCommandSize, getXcpBtnGroupId, getXcpBtnDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(btnValue, hexBytesToInt32(getXcpBtnDataSize))}));
        return true;
    }

    public boolean sendLastCheckMsgIDInfo(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup lastCheckCanMsgDataGroup = getDataGroupByKey(SET_CHECK_MSG);
        Map<String, FdxItem> items = lastCheckCanMsgDataGroup.getItems();
        byte[] lastCheckCanMsgGroupId = FdxUtils.intToByteArray(lastCheckCanMsgDataGroup.getGroupId());
        byte[] lastCheckCanMsgDataSize = FdxUtils.intToByteArray(lastCheckCanMsgDataGroup.getGroupSize());
        byte[] lastCheckCanMsgCommandSize = FdxUtils.intToByteArray(lastCheckCanMsgDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] channelBytes = int32ToHexBytes(deviceChannel, items.get(MSG_CHANNEL).getSize());
        byte[] messageIDBytes = int32ToHexBytes(StrUtils.hexStrToInt(messageId), items.get(MSG_ID).getSize());
        byte[] msgCheckStatusBytes = int32ToHexBytes(exist ? 1 : 0, items.get(MSG_CHECK_STATUS).getSize());  //1:存在，0：不存在
        byte[] checkTimeBytes = int32ToHexBytes(milliSecond, items.get(CHECK_TIME).getSize());
        byte[] checkCanMsgIDDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{channelBytes, messageIDBytes, msgCheckStatusBytes, checkTimeBytes});
        boolean pass = sendAndBackwardReadData(lastCheckCanMsgCommandSize, lastCheckCanMsgGroupId, lastCheckCanMsgDataSize, checkCanMsgIDDataBytes);
        log.info("sendLastCheckMsgIDInfo：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean sendEventMsgInfo(Integer deviceChannel, String msgID, int msgTime, int msgCounter, String msgData) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup sendEventMsgDataGroup = getDataGroupByKey(SEND_EVENT_MSG_ID);
        Map<String, FdxItem> items = sendEventMsgDataGroup.getItems();
        byte[] sendEventMsgGroupId = FdxUtils.intToByteArray(sendEventMsgDataGroup.getGroupId());
        byte[] sendEventMsgDataSize = FdxUtils.intToByteArray(sendEventMsgDataGroup.getGroupSize());
        byte[] sendEventMsgCommandSize = FdxUtils.intToByteArray(sendEventMsgDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] channelBytes = int32ToHexBytes(deviceChannel, items.get(MSG_CHANNEL).getSize());
        byte[] messageIDBytes = int32ToHexBytes(StrUtils.hexStrToInt(msgID), items.get(MSG_ID).getSize());
        byte[] msgTimeBytes = int32ToHexBytes(msgTime, items.get(MSG_TIME).getSize());  //1:存在，0：不存在
        byte[] msgCounterBytes = int32ToHexBytes(msgCounter, items.get(MSG_COUNTER).getSize());
        byte[] usedArrayElementsNumberBytes = int32ToHexBytes(msgData.length() / 2);   //四个字节的有效数组元素个数
        byte[] byteInstructionBytes = Arrays.copyOf(ByteUtils.hexStringToByteArray(msgData), items.get(MSG_DATA).getSize() - 4);  //除了4个字节的有效数组元素外，剩下的都是数据
        byte[] sendEventMsgDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{channelBytes, messageIDBytes, msgTimeBytes, msgCounterBytes, usedArrayElementsNumberBytes, byteInstructionBytes});
        boolean pass = sendAndBackwardReadData(sendEventMsgCommandSize, sendEventMsgGroupId, sendEventMsgDataSize, sendEventMsgDataBytes);
        log.info("sendLastCheckMsgIDInfo：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        long sTime = System.currentTimeMillis();
        DataGroup checkVoltageDataGroup = getDataGroupByKey(CHECK_VOLTAGE_ID);
        Map<String, FdxItem> items = checkVoltageDataGroup.getItems();
        byte[] checkVoltageGroupId = FdxUtils.intToByteArray(checkVoltageDataGroup.getGroupId());
        byte[] checkVoltageDataSize = FdxUtils.intToByteArray(checkVoltageDataGroup.getGroupSize());
        byte[] checkVoltageCommandSize = FdxUtils.intToByteArray(checkVoltageDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] pinNumberBytes = int32ToHexBytes(pinNumber, items.get(PIN_NUMBER).getSize());
        byte[] pinAliveTimeBytes = int32ToHexBytes(pinAliveTime, items.get(PIN_ALIVE_TIME).getSize());
        byte[] pinNoAliveTimeBytes = int32ToHexBytes(pinNoAliveTime, items.get(PIN_NO_ALIVE_TIME).getSize());
        byte[] workCycleNumberBytes = int32ToHexBytes(workCycleNumber, items.get(WORK_CYCLE_NUMBER).getSize());
        byte[] allDataBytes = FdxUtils.mergeHexByteArrays(new byte[][]{pinNumberBytes, pinAliveTimeBytes, pinNoAliveTimeBytes, workCycleNumberBytes});
        boolean pass = sendAndBackwardReadData(checkVoltageCommandSize, checkVoltageGroupId, checkVoltageDataSize, allDataBytes);
        log.info("checkVoltage：返回结果:{}, 执行时长:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return pass;
    }

    public boolean sendStartCheckPinVoltage() throws BusError {
        DataGroup sendStartCheckPinVoltageDataGroup = getDataGroupByKey(START_CHECK_PIN_ID);
        byte[] sendStartCheckPinVoltageGroupId = FdxUtils.intToByteArray(sendStartCheckPinVoltageDataGroup.getGroupId());
        byte[] sendStartCheckPinVoltageDataSize = FdxUtils.intToByteArray(sendStartCheckPinVoltageDataGroup.getGroupSize());
        byte[] sendStartCheckPinVoltageCommandSize = FdxUtils.intToByteArray(sendStartCheckPinVoltageDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(sendStartCheckPinVoltageCommandSize, sendStartCheckPinVoltageGroupId, sendStartCheckPinVoltageDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(DEFAULT_CODE, hexBytesToInt32(sendStartCheckPinVoltageDataSize))}));
    }


    public boolean sendSetStartCheckMsg() throws BusError {
        int startValue = 1;
        DataGroup startCheckMsgDataGroup = getDataGroupByKey(SET_START_CHECK_MSG);
        byte[] startCheckMsgGroupId = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupId());
        byte[] startCheckMsgDataSize = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupSize());
        byte[] startCheckMsgCommandSize = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        sendData(startCheckMsgCommandSize, startCheckMsgGroupId, startCheckMsgDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(startValue, hexBytesToInt32(startCheckMsgDataSize))}));
        return true;
    }

    public boolean sendSetStartCheckLinMsg() throws BusError {
        int startValue = 1;
        DataGroup startCheckMsgDataGroup = getDataGroupByKey(SET_START_CHECK_LIN_MSG);
        byte[] startCheckMsgGroupId = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupId());
        byte[] startCheckMsgDataSize = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupSize());
        byte[] startCheckMsgCommandSize = FdxUtils.intToByteArray(startCheckMsgDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        sendData(startCheckMsgCommandSize, startCheckMsgGroupId, startCheckMsgDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{int32ToHexBytes(startValue, hexBytesToInt32(startCheckMsgDataSize))}));
        return true;
    }

    public boolean getUDSFinishFlag() throws BusError {
        DataGroup getUDSFinishDataGroup = getDataGroupByKey(GET_UDS_FINISH);
        byte[] getUDSFinishGroupId = FdxUtils.intToByteArray(getUDSFinishDataGroup.getGroupId());
        byte[] getUDSFinishDataSize = FdxUtils.intToByteArray(getUDSFinishDataGroup.getGroupSize());
        return getFinishFlag(getUDSFinishGroupId, getUDSFinishDataSize, GET_TEST_RETURN_TIMEOUT);
    }


    public boolean setTemperature(int mode, double value) throws BusError {
        DataGroup setTemperatureDataGroup = getDataGroupByKey(SET_TEMPERATURE);
        byte[] setTemperatureGroupId = FdxUtils.intToByteArray(setTemperatureDataGroup.getGroupId());
        byte[] setTemperatureDataSize = FdxUtils.intToByteArray(setTemperatureDataGroup.getGroupSize());
        byte[] setTemperatureCommandSize = FdxUtils.intToByteArray(setTemperatureDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(setTemperatureCommandSize, setTemperatureGroupId, setTemperatureDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{doubleToHexBytes(value), int32ToHexBytes(mode, 8)})));
    }

    public boolean setTemperatureCounter() throws BusError {
        DataGroup setTemperatureCounterDataGroup = getDataGroupByKey(SET_TEMPERATURE_COUNTER);
        byte[] setTemperatureCounterGroupId = FdxUtils.intToByteArray(setTemperatureCounterDataGroup.getGroupId());
        byte[] setTemperatureCounterDataSize = FdxUtils.intToByteArray(setTemperatureCounterDataGroup.getGroupSize());
        byte[] setTemperatureCounterCommandSize = FdxUtils.intToByteArray(setTemperatureCounterDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return setCounter(setTemperatureCounterCommandSize, setTemperatureCounterGroupId, setTemperatureCounterDataSize);
    }

    public boolean setFindKeyAndTime(boolean findKey, int findKeyTime) throws BusError {
        String groupKey = findKey ? SET_XCP_FIND_THE_KEY : SET_XCP_FIND_NO_KEY;
        String itemKey = findKey ? FIND_THE_KEY : FIND_NO_KEY;
        DataGroup setFindTheKeyDataGroup = getDataGroupByKey(groupKey);
        Map<String, FdxItem> items = setFindTheKeyDataGroup.getItems();
        byte[] setFindTheKeyGroupId = FdxUtils.intToByteArray(setFindTheKeyDataGroup.getGroupId());
        byte[] findTheKeyDataSize = FdxUtils.intToByteArray(setFindTheKeyDataGroup.getGroupSize());
        byte[] findTheKeyCommandSize = FdxUtils.intToByteArray(setFindTheKeyDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] findTimeBytes = int32ToHexBytes(findKeyTime, items.get(FIND_KEY_TIME).getSize());
        byte[] findKeyBytes = int32ToHexBytes(1, items.get(itemKey).getSize());
        return sendAndBackwardReadData(findTheKeyCommandSize, setFindTheKeyGroupId, findTheKeyDataSize,
                FdxUtils.mergeHexByteArrays(new byte[][]{findTimeBytes, findKeyBytes}));
    }


    public boolean setPowerState(String powerState) throws BusError {
        DataGroup setPowerStateDataGroup = getDataGroupByKey(SET_POWER_STATUS);
        byte[] setPowerStateGroupId = FdxUtils.intToByteArray(setPowerStateDataGroup.getGroupId());
        byte[] powerStateDataSize = FdxUtils.intToByteArray(setPowerStateDataGroup.getGroupSize());
        byte[] powerStateCommandSize = FdxUtils.intToByteArray(setPowerStateDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        int powerStateCode = getPowerStateCode(powerState);
        return sendAndBackwardReadData(powerStateCommandSize, setPowerStateGroupId, powerStateDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(powerStateCode, hexBytesToInt32(powerStateDataSize))})));
    }

    /**
     * 将忽略大小写的电源状态字符串映射为对应状态码
     *
     * @param powerState 输入状态字符串（不区分大小写）
     * @return 对应的状态码：OFF->0, ACC->1, ON->2, START->3
     * @throws IllegalArgumentException 当输入无效参数时抛出异常
     */
    public static int getPowerStateCode(String powerState) {
        if (powerState == null) {
            throw new IllegalArgumentException("输入参数不能为null");
        }
        String normalized = powerState.toUpperCase();
        switch (normalized) {
            case "OFF":
                return 0;
            case "ACC":
                return 1;
            case "ON":
                return 2;
            case "START":
                return 4;
            default:
                throw new IllegalArgumentException("无效的电源状态: " + powerState);
        }
    }

    public boolean setMirrorFoldSTS(int commandCode) throws BusError {
        DataGroup setMirrorFoldSTSDataGroup = getDataGroupByKey(SET_MIRRORFOLD_STS);
        byte[] mirrorFoldSTSGroupId = FdxUtils.intToByteArray(setMirrorFoldSTSDataGroup.getGroupId());
        byte[] mirrorFoldSTSDataSize = FdxUtils.intToByteArray(setMirrorFoldSTSDataGroup.getGroupSize());
        byte[] mirrorFoldSTSCommandSize = FdxUtils.intToByteArray(setMirrorFoldSTSDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        return sendAndBackwardReadData(mirrorFoldSTSCommandSize, mirrorFoldSTSGroupId, mirrorFoldSTSDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(commandCode, hexBytesToInt32(mirrorFoldSTSDataSize))})));
    }

    public boolean setLampSwitch(String command) throws BusError {
        DataGroup setLampSwitchDataGroup = getDataGroupByKey(SET_LAMP_SWITCH);
        byte[] lampSwitchGroupId = FdxUtils.intToByteArray(setLampSwitchDataGroup.getGroupId());
        byte[] lampSwitchDataSize = FdxUtils.intToByteArray(setLampSwitchDataGroup.getGroupSize());
        byte[] lampSwitchCommandSize = FdxUtils.intToByteArray(setLampSwitchDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] commandBytes = stringBytesPaddedSize(command.toUpperCase(), hexBytesToInt32(lampSwitchDataSize));
        return sendAndBackwardReadData(lampSwitchCommandSize, lampSwitchGroupId, lampSwitchDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{commandBytes})));
    }

    public static int getMirrorFoldCode(String command) {
        if (command == null) {
            throw new IllegalArgumentException("输入参数不能为null");
        }
        String normalized = command.toUpperCase();
        switch (normalized) {
            case "UNFLOD":
                return 0;
            case "FLOD":
                return 1;
            default:
                throw new IllegalArgumentException("无效的后视镜状态状态: " + command);
        }
    }

    public boolean setKeepSessionStatus(String status) throws BusError {
        DataGroup setKeepSessionDataGroup = getDataGroupByKey(SET_UDS_KEEP_SESSION_ID);
        byte[] setKeepSessionGroupId = FdxUtils.intToByteArray(setKeepSessionDataGroup.getGroupId());
        byte[] setKeepSessionDataSize = FdxUtils.intToByteArray(setKeepSessionDataGroup.getGroupSize());
        byte[] setKeepSessionCommandSize = FdxUtils.intToByteArray(setKeepSessionDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        int statusCode = "start".equals(status.toLowerCase()) ? 1 : 0;
        return sendAndBackwardReadData(setKeepSessionCommandSize, setKeepSessionGroupId, setKeepSessionDataSize,
                Objects.requireNonNull(mergeHexByteArrays(new byte[][]{int32ToHexBytes(statusCode, hexBytesToInt32(setKeepSessionDataSize))})));
    }


    public boolean setBoardCardInit(String initType) throws BusError {
        String initCodeString = getInitCodeString(initType);
        DataGroup setInitDataGroup = getDataGroupByKey(SET_INIT);
        byte[] setInitGroupId = FdxUtils.intToByteArray(setInitDataGroup.getGroupId());
        byte[] setInitDataSize = FdxUtils.intToByteArray(setInitDataGroup.getGroupSize());
        byte[] setInitCommandSize = FdxUtils.intToByteArray(setInitDataGroup.getGroupSize() + ADD_COMMAND_SIZE_FIX_LENGTH);
        byte[] setInitBytes = stringBytesPaddedSize(initCodeString, setInitDataGroup.getGroupSize());
        return sendAndBackwardReadData(setInitCommandSize, setInitGroupId, setInitDataSize, setInitBytes);
    }

    public static String getInitCodeString(String initType) {
        if (initType == null) {
            throw new IllegalArgumentException("输入参数不能为null");
        }
        String normalized = initType.toUpperCase();
        if (normalized.matches("[1-7]")) {
            return normalized;
        }
        switch (normalized) {
            case "EXCEPTKL30":
                return "FE";
            case "ALL":
                return "FF";
            default:
                throw new IllegalArgumentException("无效的初始化状态: " + initType);
        }
    }


    public byte[] getUDSKey() throws BusError {
        DataGroup getUDSKeyDataGroup = getDataGroupByKey(GET_UDS_KEY);
        byte[] getUdsKeyGroupId = FdxUtils.intToByteArray(getUDSKeyDataGroup.getGroupId());
        byte[] getUdsKeyDataSize = FdxUtils.intToByteArray(getUDSKeyDataGroup.getGroupSize());
        byte[] reqDataBytes = reqData(getUdsKeyGroupId);
        int udsKeyDataLength = hexBytesToInt32(getUdsKeyDataSize);
        if (udsKeyDataLength < 0 || udsKeyDataLength > reqDataBytes.length) {
            throw new IllegalArgumentException("Invalid UdsKeyDataLength: " + udsKeyDataLength);
        }
        int startIndex = reqDataBytes.length - udsKeyDataLength;
        if (startIndex < 0) {
            throw new IllegalArgumentException("Start index is out of bounds: " + startIndex);
        }
        try {
            int effectiveLength = hexBytesToInt32(Arrays.copyOfRange(reqDataBytes, startIndex, startIndex + 4)); //有效字节长度是4个字节
            int dataStartIndex = startIndex + 4;  //udskey数据起始位置
            return Arrays.copyOfRange(reqDataBytes, dataStartIndex, dataStartIndex + effectiveLength);  //startIndex + 4是因为要去掉请求回来udskey的有效长度位数4个字节，四个4节后的才是真正udskey的数据
        } catch (ArrayIndexOutOfBoundsException e) {
            // 如果由于某种原因无法提取有效数据，抛出异常
            throw new RuntimeException("Failed to extract effective data.", e);
        }
    }



    public String getUDSLogPathFromCANoe() throws BusError {
        DataGroup getUDSLogPathDataGroup = getDataGroupByKey(GET_UDS_KEY);
        byte[] getUDSLogPathGroupId = FdxUtils.intToByteArray(getUDSLogPathDataGroup.getGroupId());
        byte[] getUDSLogPathDataSize = FdxUtils.intToByteArray(getUDSLogPathDataGroup.getGroupSize());
        byte[] data = reqData(setTestCaseGroupId);
        int startIndex = data.length - 24;
        return hexBytesToString(Arrays.copyOfRange(data, startIndex, startIndex + 24));
    }


    /**
     * 读取CANoe返回TestResult的结果
     */
    public boolean getFinishFlag(byte[] groupId, byte[] dataSize, int timeOut) throws BusError {
        log.info("正在读取诊断Finish Flag响应事件中....响应事件最大的超时时间：5分钟");
        long startMills = System.currentTimeMillis();
        int finishFlag;
        while (true) {
            finishFlag = getFinishFlagFromCANoe(groupId, dataSize);
            if (finishFlag == FINISH_FLAG) {
                log.info("读取Finish Flag响应事件完毕，finishFlag：{}, 共耗时:{}ms", finishFlag, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > timeOut) {
                log.info("读取Finish Flag响应事件超时:{}ms, finishFlag：{}", System.currentTimeMillis() - startMills, finishFlag);
                break;
            }
        }
        return finishFlag == FINISH_FLAG;
    }




    /**
     * -----------------------------------------以下函数暂时不需要-----------------------------------------------------
     */

    public boolean executeSetFlyTestControl(int fyTestControl) throws BusError {
        long sTime = System.currentTimeMillis();
        sendData(flyTestControlCommandSize, flyTestControlGroupId, flyTestControlDataSize, int32ToHexBytes(fyTestControl, 8));
        boolean res = fyTestControl == getFlyTestControlFromCANoe();
        log.info("setFlyTestControl：{}, 返回结果:{}, 执行时长:{}毫秒", fyTestControl, res ? "成功" : "失败", System.currentTimeMillis() - sTime);
        return res;
    }

    public boolean setTestCase(String caseIdString) throws BusError {
        return sendAndBackwardReadData(canTestCaseCommandSize, setTestCaseGroupId, canTestCaseDataSize, FdxDataProcessor.createSetTestCaseDataBytes(caseIdString));
    }

    public boolean executeTestCase(String caseIdString) throws BusError {
        return executeTestCase(caseIdString, GET_TEST_RETURN_TIMEOUT);
    }

    public boolean executeTestCase(String caseIdString, int timeOut) throws BusError {
        long sTime = System.currentTimeMillis();
        if (!setFlyTestControl(FLYTEST_CONTROL_FLAG)) {
            ActionSequencesLoggerUtil.info("设置CaseId:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒", caseIdString,
                    "失败", SET_FLYTEST_CONTROL_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!setTestCase(caseIdString)) {
            ActionSequencesLoggerUtil.info("设置CaseId:{},  返回结果:{}, 原因:{}, 执行时长:{}毫秒", caseIdString,
                    "失败", SET_TEST_CASE_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!testCaseEqualsZero(timeOut)) {
            log.info("testCaseEqualsZero响应事件超时:{}ms", System.currentTimeMillis() - sTime);
            return false;
        }
        return true;
    }


    public boolean setFlyTestControl(int fyTestControl) {
        try {
            if (getFlyTestControlFromCANoe() != fyTestControl) {
                return executeSetFlyTestControl(fyTestControl);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    public int getFlyTestControlFromCANoe() throws BusError {
        byte[] data = reqData(flyTestControlGroupId);
        int startIndex = data.length - 8;
        return hexBytesToInt32(Arrays.copyOfRange(data, startIndex, startIndex + 4));
    }

    public String getTestCaseFromCANoe() throws BusError {
        byte[] data = reqData(setTestCaseGroupId);
        int startIndex = data.length - 24;
        return hexBytesToString(Arrays.copyOfRange(data, startIndex, startIndex + 24));
    }

    public int getTestResultFromCANoe() throws BusError {
        byte[] data = reqData(testResultGroupId);
        int startIndex = data.length - hexBytesToInt32(testCaseDataSize);
        return hexBytesToInt32(Arrays.copyOfRange(data, startIndex, startIndex + 4));
    }




    /**
     * 读取CANoe返回TestResult的结果是否等于0，等于0说明执行case完毕
     */
    public boolean testCaseEqualsZero(int timeOut) throws BusError {
        long startMills = System.currentTimeMillis();
        String testCase;
        while (true) {
            testCase = getTestCaseFromCANoe();
            if (TEST_CASE_END_FLAG.equals(testCase)) {
                log.info("读取testCase响应事件完毕，testCase：{}, 共耗时:{}ms", testCase, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > timeOut) {
                log.info("读取testCase响应事件超时:{}ms, testCase：{}", System.currentTimeMillis() - startMills, testCase);
                break;
            }
        }
        log.info("读取testCase:{}", testCase);
        return TEST_CASE_END_FLAG.equals(testCase);
    }

    /**
     * 读取CANoe返回TestResult的结果
     */
    public boolean getTestResult() throws BusError {
        long startMills = System.currentTimeMillis();
        int testResult;
        while (true) {
            testResult = getTestResultFromCANoe();
            if (testResult != TEST_RESULT_INVALID) {
                log.info("读取testResult响应事件完毕，testResult：{}, 共耗时:{}ms", testResult, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > GET_TEST_RESULT_TIMEOUT) {
                log.info("读取testResult响应事件超时:{}ms, testResult：{}", System.currentTimeMillis() - startMills, testResult);
                break;
            }
        }
        return testResult == TEST_RESULT_PASS;
    }


    /**
     * 读取CANoe返回log path的结果
     */
    public String getUDSLogPath() throws BusError {
        long startMills = System.currentTimeMillis();
        String logPath;
        while (true) {
            logPath = getUDSLogPathFromCANoe();
            if (!logPath.equals(UDS_LOG_FINISH)) {
                log.info("读取UDSLogPath响应事件完毕，UDSLogPath：{}, 共耗时:{}ms", logPath, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > GET_RETURN_VALUE_TIMEOUT) {
                log.info("读取UDSLogPath响应事件超时:{}ms, UDSLogPath：{}", System.currentTimeMillis() - startMills, logPath);
                break;
            }
        }
        return logPath;
    }


    /**
     * 1: kMeasurementState_NotRunning
     * 2: kMeasurementState_PreStart
     * 3: kMeasurementState_Running
     * 4: kMeasurementState_Stop
     */
    public boolean isCANoeRunning() throws BusError {
        byte[] dataExchange = FdxUtils.mergeHexByteArrays(new byte[][]{buildHeader(), canoeStatusCommandSize, canoeStatusCommandCode});
        sendTo(dataExchange);
        DatagramPacket responsePacket = new DatagramPacket(new byte[4096], 4096);
        try {
            receive(responsePacket);
        } catch (IOException e) {
            log.error("fdx读取超时, 检测CANoe是否开启fdx通信或重启CANoe\n", e);
            return false;
        }
        byte[] bytes = mergeHexByteArrays(new byte[][]{Arrays.copyOfRange(responsePacket.getData(), 0, responsePacket.getLength())});
        assert bytes != null;
        int statusCode = byteArrayToInt8(new byte[]{bytes[bytes.length - 12]});
        log.info("读取CANoe运行状态：{}", statusCode == 3 ? "running" : "not running");
        return statusCode == 3;
    }

    public boolean setLoggingStatus(byte[] commandSize, byte[] groupId, byte[] dataSize, byte[] dataBytes) throws BusError {
        byte[] dataExchange = FdxUtils.mergeHexByteArrays(new byte[][]{buildHeader(), commandSize, FdxCommandConstant.dataExchangeCommandCode,
                groupId, dataSize, dataBytes});
        sendTo(dataExchange);
        return true;
    }


    public static void main(String[] args) throws BusError {
//        FdxSocket fdxSocket = FdxSocket.getInstance();
//        fdxSocket.getReturnValue();
//        VectorCanFDX vectorCanFDX = new VectorCanFDX(new DeviceOperationParameter());
//        vectorCanFDX.compareCanSignal(1, "Msg1", "Signal1", "2");
        int str = StrUtils.hexStrToInt("0xA");
        byte[] messageIDBytes = int32ToHexBytes(str, 4);
        System.out.println(str + ", " + Hex.encodeHexString(messageIDBytes));
        byte[] setInitBytes = stringBytesPaddedSize("FE", 4);
        System.out.println(Hex.encodeHexString(setInitBytes));
        String initCodeString = getInitCodeString("EXCEPTKL30");
        System.out.println(initCodeString);
        int doubleFlash = getLightTypeCode("DoubleFlash");
        System.out.println(doubleFlash);

    }


}
