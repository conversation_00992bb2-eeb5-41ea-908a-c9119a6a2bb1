package com.desaysv.workserver.devices.electronic_load;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.electronic_load.base.ElectronicLoadDevice;
import com.desaysv.workserver.devices.electronic_load.base.N68000ElectronicSCPICommands;
import com.desaysv.workserver.devices.electronic_load.base.interfaces.IElectronicLoad;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

@Slf4j
public class N68000ElectronicLoad extends ElectronicLoadDevice implements IElectronicLoad {
    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @JSONField(serialize = false)
    private JVisaInstrument instrument;

    public N68000ElectronicLoad(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    public N68000ElectronicLoad() {
        this(new DeviceOperationParameter());
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.ElectronicLoad.N68000;
    }

    public String sendAndReceiveString(String command) {
        try {
            byte[] rawData =  query((command + "\n").getBytes(), false);
            return new String(rawData).trim();
        } catch (DeviceSendException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public boolean setInputState(String statusCommand) {
        //打开负载输入
        try {
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_STATE, statusCommand));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 电子负载工作于恒电流功能时，无论输入电压如何改变，电子负载始终消耗一个恒定的电流。
     */

    @Override
    public boolean setHighLevelCC(float current) {
        try {
            //切换至恒电流测试功能
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_FUNCTION, "CC"));
            //设置恒电流量程为大量程
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CC_RANGE, 0));
            //设置带载电流为XXA
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CC_HIGH_LEVEL, current));
            //设置上升斜率为 XX * 200A/ms
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CC_HIGH_SLEWRATE_RAISE, current * 200));
            //设置下降斜率为 XX * 100A/m
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CC_HIGH_SLEWRATE_FALL, current * 100));
            setInputState("ON");
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean setLowLevelCC(float current) {
        try {
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CC_LOW_LEVEL, current));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 电子负载工作于恒电压功能时，负载输入端将消耗足够的电流以使输入电压维持于设定电压值。
     */
    @Override
    public boolean setHighLevelCV(float voltage) {
        try {
            //切换至恒电压测试功能
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_FUNCTION, "CV"));
            //设置恒电压量程为大量程
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_RANGE, 0));
            //设置恒电压响应速度为慢速
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_RESPONSE, 2));
            //设置为恒电压放电模式
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_PATTERN, 0));
            //设置带载电压为XX V
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_HIGH_LEVEL, voltage));
            //设置上升斜率为 XX * 10v/ms
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_HIGH_SLEWRATE_RAISE, voltage * 10));
            //设置下降斜率为 XX * 100v/m
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_HIGH_SLEWRATE_FALL, 100));
            //打开负载输入
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_STATE, "ON"));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean setLoweLevelCV(float voltage) {
        try {
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CV_LOW_LEVEL, voltage));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 电子负载工作于恒电阻功能时，负载输入端等效为恒定电阻。此时，负载会随着输入电
     * 压的改变而线性改变输入电流
     */
    @Override
    public boolean setLoweLevelCR(float resistance) {
        try {
            //切换至恒电阻测试功能
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_FUNCTION, "CR"));
            //设置恒电阻量程为小量程
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CR_RANGE, 1));
            //设置带载阻值为 xx 欧姆
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CR_LOW_LEVEL, resistance));
            //设置上升斜率为 XX * 300 Ω/ms
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CR_HIGH_SLEWRATE_RAISE, resistance * 300));
            //设置下降斜率为 XX * 300 Ω/ms
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CR_HIGH_SLEWRATE_FALL, resistance * 300));
            //打开负载输入
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_STATE, "ON"));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 电子负载工作于恒功率功能时，负载输入端将消耗恒定的功率。
     */
    @Override
    public boolean setLoweLevelCP(float power) {
        try {
            //切换至恒功率测试功能
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_FUNCTION, "CP"));
            //设置带载功率为 XX W
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CP_LEVEL, power));
            //设置上升斜率为 XX W/ms
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CP_RAISE, power * 7));
            //设置下降斜率为 XX W/m
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_CP_FALL, power * 5));
            //打开负载输入
            sendCommand(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.SET_INPUT_STATE, "ON"));
            return true;
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public float fetchLoadCurrent() throws DeviceReadException {
        return getValue(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.MEASURE_CURRENT));
    }

    @Override
    public float fetchLoadVoltage() throws DeviceReadException {
        return getValue(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.MEASURE_VOLTAGE));
    }

    @Override
    public float fetchLoadResistance() throws DeviceReadException {
        return getValue(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.MEASURE_RESISTANCE));
    }

    @Override
    public float fetchLoadPower() throws DeviceReadException {
        return getValue(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.MEASURE_POWER));
    }

    @Override
    public float fetchLoadTemperature() throws DeviceReadException {
        return getValue(N68000ElectronicSCPICommands.formatCommand(N68000ElectronicSCPICommands.MEASURE_TEMPERATURE));
    }

    private void sendCommand(String command) throws JVisaException {
        try {
            send(command + "\n");
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
        }
    }

    public float getValue(String command) throws DeviceReadException {
        String response = sendAndReceiveString(command);
        return Float.parseFloat(response.replaceAll("[^0-9.E+-]", ""));
    }
}
