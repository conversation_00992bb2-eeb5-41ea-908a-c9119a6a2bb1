package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.port.PortDevice;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/15 21:00
 * @description : 适用于IT6722/IT6722A/IT6723/IT6723B/IT6723C/IT6723G/IT6723H/IT6724/IT6724B/IT6724C/IT6724G/IT6724H/IT6726B/IT6726C/IT6726G/IT6726H/IT6726V
 * @modified By :
 * @since : 2023/5/15
 **/
public class IT67Protocol extends PowerSCPIProtocol{
    public IT67Protocol(PortDevice device) {
        super(device);
    }
}
