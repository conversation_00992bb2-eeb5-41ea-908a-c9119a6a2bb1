package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
public class PreciousPoint3D {

    private BigDecimal x;
    private BigDecimal y;
    private BigDecimal z;
    private BigDecimal r;


    public PreciousPoint3D() {
        x = BigDecimal.valueOf(0);
        y = BigDecimal.valueOf(0);
        z = BigDecimal.valueOf(0);
        r = BigDecimal.valueOf(0);
    }

    public PreciousPoint2D toPreciousPoint2D() {
        return new PreciousPoint2D(x, y);
    }

    public MoveEntity toMoveEntity() {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(x.doubleValue());
        moveEntity.setY(y.doubleValue());
        moveEntity.setZ(z.doubleValue());
        moveEntity.setR(r.doubleValue());
        return moveEntity;
    }

    @Override
    public String toString() {
        return String.format("(%f,%f,%f,%f)", x, y, z, r);
    }

}