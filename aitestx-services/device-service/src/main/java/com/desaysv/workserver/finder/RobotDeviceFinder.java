package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.robot.RobotDevice;
import com.desaysv.workserver.devices.robot.base.IPRobotDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.PingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@Lazy
public class RobotDeviceFinder {

    public List<Device> findAllIpRobots(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        RobotDevice device;
        int deviceIndex;
        try {
            for (String ipAddr : RobotDevice.IP_ADDRESS) {
                if (PingUtils.ping(ipAddr)) {
                    device = new IPRobotDevice();
                    device.setDeviceName(ipAddr);
                    device.setDeviceUniqueCode(device.getDeviceName());
                    deviceIndex = Device.getDeviceModelIndex(deviceModel);
                    device.setAliasName(deviceModel + "#" + deviceIndex);
//                    device.setDeviceAliasName(deviceModel + "#" + deviceIndex);
                    device.setDeviceOperationParameter(new DeviceOperationParameter());
                    devices.add(device);
                }
            }
        } catch (Exception e) {
            log.debug(e.getMessage(), e);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
        }
        return devices;
    }
}
