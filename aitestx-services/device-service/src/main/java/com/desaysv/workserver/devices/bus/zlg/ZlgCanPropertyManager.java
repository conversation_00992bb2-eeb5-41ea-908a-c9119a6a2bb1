package com.desaysv.workserver.devices.bus.zlg;

/**
 * 周立功CANFD系列属性管理器
 * 适用的设备：USBCANFD-100U、USBCANFD-200U、USBCANFD-MINI。
 *
 * @reference <a href="https://manual.zlg.cn/web/#/152/6359">...</a>
 */
public class ZlgCanPropertyManager {
    private final ZlgCanLib.IProperty property;

    public ZlgCanPropertyManager(ZlgCanLib.IProperty property) {
        this.property = property;
    }

    /**
     * 根据属性名设置通道属性值
     *
     * @param propertyName 属性名
     * @param channel      通道号
     * @param value        属性值
     * @return 是否设置成功
     */
    private boolean setChannelValueByPropertyName(String propertyName, int channel, Object value) {
        int result = property.SetValue.setValueFunc(String.format("%s/%s", channel, propertyName), String.valueOf(value));
        return result == ZlgCanConstants.STATUS_OK;
    }

    /**
     * CANFD 控制器标准类型，ISO 或非 ISO，通常使用 ISO 标准
     * 注意点：需要在ZCAN_InitCAN之前设置
     *
     * @param channel 通道号
     * @param value   CANFD控制器标准类型, 0-CANFD ISO， 1-CANFD Non-ISO
     * @return 是否设置成功
     */
    public boolean setCanfdStandard(int channel, int value) {
        return setChannelValueByPropertyName("canfd_standard", channel, value);
    }

    public boolean setCanfdStandard(int channel) {
        return setCanfdStandard(channel, 0);
    }

    /**
     * CANFD 控制器仲裁域波特率，支持常用标准波特率，若列表中无所需值可使用自定义波特率。CANFD 控制器可接收 CAN 报文，仲裁域波特率对应 CAN 波特率。
     * 注意点：需要在ZCAN_InitCAN之前设置
     *
     * @param channel 通道号
     * @param value   仲裁域波特率, 单位 bps，如值为 500000 则表示波特率为500k, 【1000000，800000，500000，250000，125000，100000，50000】
     * @return 是否设置成功
     */
    public boolean setCanfdAbitBaudRate(int channel, int value) {
        return setChannelValueByPropertyName("canfd_abit_baud_rate", channel, value);
    }

    /**
     * CANFD 控制器数据域波特率，支持支持常用标准波特率，若列表中无所需值可使用自定义波特率。
     * 注意点：需要在ZCAN_InitCAN之前设置
     *
     * @param channel 通道号
     * @param value   数据域波特率,单位 bps，如值为 500000 则表示波特率为500k,【5000000，4000000，2000000，1000000，800000，500000，250000，125000，100000】
     * @return 是否设置成功
     */
    public boolean setCanfdDbitBaudRate(int channel, int value) {
        return setChannelValueByPropertyName("canfd_dbit_baud_rate", channel, value);
    }

    /**
     * 设置 CANFD 控制器为任意有效波特率，设置值可通过 ZCANPRO 的波特率计算器进行计算。如果设置了自定义波特率则无需再设置仲裁域以及数据域波特率。
     * 注意点：需要在ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @param value   自定义波特率(通过ZCANPRO的波特率计算器)
     * @return 是否设置成功
     */
    public boolean setBaudRateCustom(int channel, int value) {
        return setChannelValueByPropertyName("baud_rate_custom", channel, value);
    }

    /**
     * USBCANFD 每通道内置 120Ω终端电阻，可通过属性设置选择使能或不使能。
     * 注意点：需要在ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @param value   0-不使能，1-使能
     * @return 是否设置成功
     */
    public boolean setInternalResistance(int channel, int value) {
        return setChannelValueByPropertyName("initenal_resistance", channel, value);
    }

    /**
     * 设备发送CAN(FD)报文时的超时时间，单位毫秒，默认为15000ms。
     * 注：通道进入BUSOFF也会导致发送立即返回，不会等设置的超时时间。
     * 注意点：在打开设备以后任意位置设置都会生效
     *
     * @param channel 通道号
     * @param value   自定义超时时间，“1000”,单位毫秒，最大值为4000
     * @return 是否设置成功
     */
    public boolean setTxTimeout(int channel, int value) {
        return setChannelValueByPropertyName("tx_timeout", channel, value);
    }

    /**
     * 定时发送CAN帧
     * USBCANFD 支持每通道最大 100 条定时发送列表，只需将待发送数据及周期设置到设备并使能，设备将自动进行发送。相比于 PC 端的发送，
     * 定时发送精度高，周期准。在设备进行定时发送任务时，PC 端仍可调用数据发送接口进行数据发送。
     * 列表添加完成并设置apply_auto_send 开启定时发送后，设备会按列表索引顺序依次启动发送，
     * 如需延时启动，可通过 auto_send_param 属性进行设置。
     * 注意点：需要在ZCAN_StartCAN之后设置
     *
     * @param channel 通道号
     * @param value   ZCAN_AUTO_TRANSMIT_OBJ指针, 需将指针强制转换为char*
     * @return 是否设置成功
     */
    public boolean setAutoSend(int channel, ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ value) {
        return setChannelValueByPropertyName("auto_send", channel, value);
    }

    /**
     * 定时发送CANFD帧
     * 注意点：需要在ZCAN_StartCAN之后设置
     *
     * @param channel 通道号
     * @param value   ZCANFD_AUTO_TRANSMIT_OBJ指针，需将指针强制转换为char*
     * @return 是否设置成功
     */
    public boolean setAutoSendCanFd(int channel, ZlgCanLib.ZCANFD_AUTO_TRANSMIT_OBJ value) {
        return setChannelValueByPropertyName("auto_send_canfd", channel, value);
    }

    /**
     * 定时发送附加参数（用于定时发送列表间的延时启动）
     * 注意点：需要在ZCAN_StartCAN之后设置
     *
     * @param channel 通道号
     * @param value   ZCAN_AUTO_TRANSMIT_OBJ_PARAM指针,需将指针强制转换为char*
     * @return 是否设置成功
     */
    public boolean setAutoSendParam(int channel, ZlgCanLib.ZCAN_AUTO_TRANSMIT_OBJ_PARAM value) {
        return setChannelValueByPropertyName("auto_send_param", channel, value);
    }

    /**
     * 清空定时发送
     * 注意点：需要在ZCAN_StartCAN之后设置
     *
     * @param channel 通道号
     * @return 是否清除成功
     */
    public boolean clearAutoSend(int channel) {
        return setChannelValueByPropertyName("clear_auto_send", channel, 0);
    }

    /**
     * 通过队列发送，用户可以提前准备好多帧报文，以及报文之间的间隔，将准备好的报文批量发送给设备，设备按照预定义的帧间隔进行精准发送，
     * 通过此方式可提高发送帧之间的帧间隔精度。区别与定时发送，队列发送每帧只发送一次，需由用户不断准备报文并批量发送到设备。
     * <p>
     * 首先用户需设置设备发送模式为队列发送，当设备处于队列发送模式时，定时发送功能将被禁用。
     * 队列模式下的数据发送使用 ZCAN_Transmit/ZCAN_TransmitFD 接口，返回值表示有多少帧已经加入到设备的发送队列中。
     * 队列模式下可以通过接口获取设备端可用的队列空间。
     * 队列模式下帧间隔单位 ms，长度 2 字节需要分别填入 can/canfd 帧中的res0(帧间隔低 8 位)和res1(帧间隔高 8 位)字段中，
     * \n设备支持最大帧间隔时间为 65535ms，同时需要设置延时发送标志位(TX_DELAY_SEND_FLAG)为 1 标识使用队列发送。
     * 队列模式下，CAN 帧的 TX_DELAY_SEND_FLAG 标志位在 frame.__pad 字段的Bit7 位，CANFD 帧的 TX_DELAY_SEND_FLAG
     * \n标志位在 frame.flags 字段的 Bit7位。标志位为 1 表示使用队列顺序发送数据。标志为 0 表示直接发送到总线。
     * 队列模式下，单次 ZCAN_Transmit/ZCAN_TransmitFD 函数调用时，发送多帧数据会按照第1帧的TX_DELAY_SEND_FLAG位
     * \n决定此次调用采用直接发送到总线或者使用队列模式进行发送。
     * 设备发送当前帧的同时会启动计时器按照当前帧设定的时间进行计时，计时时间结束会从队列取下一帧进行发送并重新开始计时
     *
     * @param channel 通道号
     * @param value   0-正常模式，1-队列模式，设备上电默认0
     * @return 是否设置成功
     */
    public boolean setSendMode(int channel, int value) {
        return setChannelValueByPropertyName("set_send_mode", channel, value);
    }

    /**
     * 获取发送队列可用缓存数量（仅队列模式）
     *
     * @param channel 通道号
     * @return 是否获取成功
     */
    public boolean get_device_available_tx_count(int channel) {
        return setChannelValueByPropertyName("get_device_available_tx_count/1", channel, null);
    }

    /**
     * 清空发送缓存（仅队列模式，缓存中未发送的帧将被清空，停止时使用）
     *
     * @param channel 通道号
     * @return 是否设置成功
     */
    public boolean clear_delay_send_queue(int channel) {
        return setChannelValueByPropertyName("clear_delay_send_queue", channel, 0);
    }

    /**
     * 自定义序列号通常用于多卡同时使用时对卡以及通道进行区分，比如 3 个USBCAFD-100U 可分别设置序列号为 U-1，U-2，U-3，
     * 打开时获取序列号并加以判断。所设置的序列号可掉电保存。
     *
     * @param channel      通道号
     * @param serialNumber 自定义字符串，最多128字符
     * @return 是否设置成功
     */
    public boolean setCn(int channel, String serialNumber) {
        return setChannelValueByPropertyName("set_cn", channel, serialNumber);
    }

    /**
     * 获取自定义序列号
     *
     * @param channel 通道号
     * @return 是否获取成功
     */
    public boolean getCn(int channel) {
        return setChannelValueByPropertyName("get_cn/1", channel, null);
    }

    /**
     * USBCANFD 系列每通道最多可设置 64 组滤波，为白名单模式，即如果不设置滤波 CAN通道将接收所有报文，如果设置了滤波，
     * CAN 通道将只接收滤波范围内的报文。添加一条滤波的标准顺序是：设置滤波模式，设置起始帧，设置结束帧。
     * 如果要添加多条就重复上述步骤，添加完滤波并不会立即生效，需设置 filter_ack 使能所设的滤波表。
     * 注意点：需要再ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @param value   0-标准帧，1-扩展帧
     * @return 是否设置成功
     */
    public boolean setFilterMode(int channel, int value) {
        return setChannelValueByPropertyName("filter_mode", channel, value);
    }

    /**
     * 设置滤波起始帧ID
     * 注意点：需要再ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @param value   自定义,“0x00000000”,16进制字符
     * @return 是否设置成功
     */
    public boolean setFilterStart(int channel, String value) {
        return setChannelValueByPropertyName("filter_start", channel, value);
    }

    /**
     * 设置滤波结束帧ID
     * 注意点：需要再ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @param value   自定义,“0x00000000”,16进制字符
     * @return 是否设置成功
     */
    public boolean setFilterEnd(int channel, String value) {
        return setChannelValueByPropertyName("filter_end", channel, value);
    }

    /**
     * 滤波生效（全部滤波ID段同时生效）
     * 注意点：需要再ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @return 是否设置成功
     */
    public boolean setFilterAck(int channel) {
        return setChannelValueByPropertyName("filter_ack", channel, 0);
    }

    /**
     * 清除滤波
     * 注意点：需要再ZCAN_InitCAN之后设置
     *
     * @param channel 通道号
     * @return 是否清除成功
     */
    public boolean clearFilter(int channel) {
        return setChannelValueByPropertyName("filter_clear", channel, 0);
    }
}
