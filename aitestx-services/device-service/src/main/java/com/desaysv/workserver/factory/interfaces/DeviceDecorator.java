package com.desaysv.workserver.factory.interfaces;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 15:20
 * @description :
 * @modified By :
 * @since : 2022-5-6
 */
public interface DeviceDecorator {

//    Device getDevice();
//
//    //Getter
//    default String getDeviceModel() {
//        return getDevice().getDeviceModel();
//    }
//
//    default String getDeviceType() {
//        return getDevice().getDeviceType();
//    }
//
//    //Getter&Setter
//    default Integer getId() {
//        return getDevice().getId();
//    }
//
//    default void setId(Integer id) {
//        getDevice().setId(id);
//    }
//
//    default String getDeviceName() {
//        return getDevice().getDeviceName();
//    }
//
//    default void setDeviceName(String deviceName) {
//        getDevice().setDeviceName(deviceName);
//    }
//
//    default String getDeviceAliasName() {
//        return getDevice().getDeviceAliasName();
//    }
//
//    default void setDeviceSimpleName(String deviceAliasName) {
//        getDevice().setDeviceAliasName(deviceAliasName);
//    }
//
//    default String getDeviceUniqueCode() {
//        return getDevice().getDeviceUniqueCode();
//    }
//
//    default void setDeviceUniqueCode(String deviceUniqueCode) {
//        getDevice().setDeviceUniqueCode(deviceUniqueCode);
//    }
//
//    default Integer getDevicePort() {
//        return getDevice().getDevicePort();
//    }
//
//    default void setDevicePort(Integer devicePort) {
//        getDevice().setDevicePort(devicePort);
//    }
//
//    default OperationParameter getDeviceOperationParameter() {
//        return getDevice().getDeviceOperationParameter();
//    }
//
//    default void setDeviceOperationParameter(OperationParameter deviceOperationParameter) {
//        getDevice().setDeviceOperationParameter(deviceOperationParameter);
//    }
//
//    String toString();
}
