package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.robot.DobotMG400;
import com.desaysv.workserver.devices.robot.DobotMagician;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.interfaces.AbstractRobotFactory;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 11:30
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Component
@Lazy
public class RobotFactory implements AbstractRobotFactory {

//    @Bean
//    @Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE, proxyMode = ScopedProxyMode.TARGET_CLASS)
//    private DobotDeviceDecorator getDobotDeviceDecorator(DobotDevice dobotDevice) {
//        return new DobotDeviceDecorator(dobotDevice);
//    }

//    @PostConstruct
//    public void test() {
//        System.out.println("test decorator1:" + System.identityHashCode(getDobotDeviceDecorator(new DobotMG400(new OperationParameter()))));
//        System.out.println("test decorator2:" + System.identityHashCode(getDobotDeviceDecorator(new DobotMG400(new OperationParameter()))));
//    }

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Robot.DOBOT_MG400)) {
            return createDobotMG400(deviceRegisterForm);
        } else if (deviceModel.equals(DeviceModel.Robot.DOBOT_MAGICIAN)) {
            return createDobotMagician(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createDobotMG400(DeviceRegisterForm deviceRegisterForm) {
        return new DobotMG400(deviceRegisterForm.getDeviceOperationParameter());
//        return new DobotDeviceDecorator(new DobotMG400(deviceRegisterForm.getDeviceOperationParameter()));
    }

    @Override
    public Device createDobotMagician(DeviceRegisterForm deviceRegisterForm) {
        return new DobotMagician(deviceRegisterForm.getDeviceOperationParameter());
    }
}
