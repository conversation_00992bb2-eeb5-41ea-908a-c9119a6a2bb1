package com.desaysv.workserver.stream;

public abstract class PortStreamProducer extends StreamProducer {

    public PortStreamProducer() {
    }

    public PortStreamProducer(String url) {
        super(url);
    }

    public PortStreamProducer(String url, int w, int h) {
        super(url, w, h);
    }

    public PortStreamProducer(int w, int h) {
        super(w, h);
    }

    protected abstract void startStream(String deviceModel, int devicePort) throws Exception;

    public abstract String pushStream(String deviceModel, int devicePort) throws Exception;

    public void tryLoad() {

    }
}
