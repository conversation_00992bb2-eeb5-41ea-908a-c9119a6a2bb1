package com.desaysv.workserver.devices.bus.base;

import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.base.lin.LinMessage;

import java.util.List;

/**
 * 支持修改一个周期消息报文
 */
public interface ModifiableCyclicTask {


    /**
     * 更新周期发送消息报文内容（不修改时间）
     *
     * @param message 新修改的报文，备注：can id不能被修改
     */
    default void modifyData(CanMessage message) {
        if (message.isLoop() && message.getLoopDatas() != null && !message.getLoopDatas().isEmpty()) {
            modifyLoopData(message.getLoopDatas());
        } else {
            modifyData(message.getData());
        }
    }

    void modifyData(byte[] newData);

    default void modifyLoopData(List<byte[]> newLoopDatas){

    }

    void modifyPeriod(float period);

    default void modifyData(FlexrayMessage message){

    }
    default void modifyData(LinMessage message){

    }

    void modifyDlc(int dlc);

}
