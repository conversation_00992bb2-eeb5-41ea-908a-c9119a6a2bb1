package com.desaysv.workserver.config.can;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.config.DeviceConfig;
import com.desaysv.workserver.config.lin.NetLinConfigParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class CanConfig extends DeviceConfig {

    private PtsConfig ptsConfig = new PtsConfig();

    private UdsModel udsModel = new UdsModel();

    //TODO：序列和反序列化Integer Key到String Key
    private Map<Integer, String> e2eTypes = new HashMap<>();

    // 全局的e2e状态设置
    private Map<Integer, Boolean> e2eGlobalStatus = new HashMap<>();

    @JSONField(serialize = false)
    private Map<Integer, CanMessageEventListener> e2eEvents = new HashMap<>();

    private Map<String, CanConfigParameter> configParameters = new HashMap<>();

    private Map<String, NetCanConfigParameter> configNetParameters = new HashMap<>();

    private Map<String, NetLinConfigParameter> configLinParameters = new HashMap<>();

    private Map<String, DbcConfig> dbcConfigs = new HashMap<>(); //key-》通道channel

    private String sendMode = "软件发送";

}
