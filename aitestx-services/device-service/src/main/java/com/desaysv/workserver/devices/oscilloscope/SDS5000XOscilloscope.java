package com.desaysv.workserver.devices.oscilloscope;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.oscilloscope.base.SigLentOscilloscopeDevice;
import com.desaysv.workserver.devices.oscilloscope.interfaces.IOscilloscope;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.desaysv.workserver.utils.DataUtils.scientificToFloat;

@Slf4j
public class SDS5000XOscilloscope extends SigLentOscilloscopeDevice implements IOscilloscope {
    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @JSONField(serialize = false)
    private JVisaInstrument instrument;
    private static final long SLEEP_1000_MILLIS = 1000L;
    private static final long SLEEP_500_MILLIS = 500L;
    private static final long SLEEP_200_MILLIS = 200L;
    //缓冲区大小
    private static final int BUFFER_SIZE = 32768;
    private static final String STOP_STATUS = "Stop";
    private static final double HIGH_FREQUENCY_THRESHOLD = 1000.0;
    private static final Object TIMEBASE_HIGH_FREQUENCY = 2.00E-05;    // us/div
    private static final Object TIMEBASE_LOW_FREQUENCY = 2.00E-03;     // ms /div
    private static final Object CHANNEL_SCALE_FACTOR = 5.00E+00;       // 5V / div

    public SDS5000XOscilloscope() {
        this(new DeviceOperationParameter());
    }

    public SDS5000XOscilloscope(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Oscilloscope.SDS_5000X;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        boolean open = super.open();
        if (!open) {
            return false;
        }
        if (visaResourceManager == null) {
            try {
                visaResourceManager = new JVisaResourceManager();
            } catch (JVisaException e) {
                log.warn(e.getMessage(), e);
            }
        }
        try {
            instrument = visaResourceManager.openInstrument(getDeviceName());
            instrument.setTimeout(10000); // 增加超时到10秒
            initializeSDS();
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
        }
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        boolean result = true;
        Exception closeException = null;

        if (instrument != null) {
            try {
                instrument.write(SDSSCPICommands.CLEAR_SCREEN);
                instrument.close();
            } catch (JVisaException e) {
                log.error("关闭仪器失败: {}", e.getMessage());
                result = false;
                closeException = e;
            } finally {
                instrument = null;
            }
        }

        if (visaResourceManager != null) {
            try {
                visaResourceManager.close();
            } catch (JVisaException e) {
                log.error("关闭资源管理器失败: {}", e.getMessage());
                result = false;
                closeException = e;
            } finally {
                visaResourceManager = null;
            }
        }

        try {
            result &= super.close();
        } catch (Exception e) {
            log.error("父类关闭操作异常: {}", e.getMessage());
            result = false;
            closeException = e;
        }

        if (closeException != null) {
            throw new DeviceCloseException(closeException);
        }
        return result;
    }


    private void safeSleep(long millis) throws InterruptedException {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new InterruptedException("设备休眠被中断");
        }
    }

    public void initializeSDS() throws JVisaException {
        try {
            String idn = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.IDN));
            log.info("示波器IDN：{}", idn);
            sendCommand(SDSSCPICommands.RESET_INSTRUMENTS);
            safeSleep(SLEEP_1000_MILLIS);
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.SET_MEASURE_STATUS, "ON"));
            safeSleep(SLEEP_1000_MILLIS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new JVisaException("初始化过程被中断", e);
        }
    }

    @Override
    public Map<String, Float> getMeasureAll(Integer deviceChannel) {
        Map<String, Float> map = new HashMap<>(2);
        boolean deviceReady = true;
        try {
            try {
                sendCommand(SDSSCPICommands.STOP);
            } catch (JVisaException e) {
                throw new RuntimeException(e);
            }
            safeSleep(SLEEP_500_MILLIS);
            map.put("freq", getFrequency(deviceChannel));
            safeSleep(SLEEP_200_MILLIS);
            map.put("duty", getDuty(deviceChannel));
            try {
                safeSleep(SLEEP_200_MILLIS);
                sendCommand(SDSSCPICommands.RUN);
            } catch (JVisaException e) {
                throw new RuntimeException(e);
            }
        } catch (InterruptedException e) {
            log.warn("测量等待被中断，设备通道：{}", deviceChannel);
            Thread.currentThread().interrupt();
            deviceReady = false;
        }
        return deviceReady ? map : Collections.emptyMap();
    }

    public String sendAndReceiveString(String command) throws JVisaException {
        try {
            instrument.write(command);
            safeSleep(SLEEP_1000_MILLIS);
            instrument.clear();
            safeSleep(SLEEP_1000_MILLIS);
            return instrument.readString(BUFFER_SIZE);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new JVisaException("读取操作被中断", e);
        }
    }

    @Override
    public float getFrequency(Integer deviceChannel) {
        try {
            String freq = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.MEASURE_SIMPLE_VALUE, "FREQ"));
            log.info("SDS5000X getFrequency: {}", freq);
            return scientificToFloat(freq);
        } catch (JVisaException e) {
            log.error("SDS5000X getFrequency error", e);
        }
        return -1;
    }

    @Override
    public float getAmplitude(Integer deviceChannel) {
        try {
            String amplitude = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.MEASURE_SIMPLE_VALUE, "AMPL"));
            return scientificToFloat(amplitude);
        } catch (JVisaException e) {
            log.error("SDS5000X getAmplitude error", e);
        }
        return -1;
    }

    @Override
    public float getDuty(Integer deviceChannel) {
        try {
            String duty = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.MEASURE_SIMPLE_VALUE, "DUTY"));
            log.info("SDS5000X getDuty: {}", duty);
            return scientificToFloat(duty);
        } catch (JVisaException e) {
            log.error("SDS5000X getDuty error", e);
        }
        return -1;
    }

    @Override
    public boolean setInitValue(float frequencyBaseValue) {
        try {
            //Khz以上设置的时基和垂直分辨率不一样，具体数值是实际测试上得出的
            Object timeBase = frequencyBaseValue > HIGH_FREQUENCY_THRESHOLD ? TIMEBASE_HIGH_FREQUENCY : TIMEBASE_LOW_FREQUENCY;
            String triggerStatus = sendAndReceiveString(SDSSCPICommands.TRIGGER_STATUS);
            if (STOP_STATUS.equalsIgnoreCase(triggerStatus)) {
                safeSleep(SLEEP_200_MILLIS);
                sendCommand(SDSSCPICommands.RUN);
            }
            safeSleep(SLEEP_200_MILLIS);
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.TIMEBASE_SCALE, timeBase));
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.CHAN1_SCALE, CHANNEL_SCALE_FACTOR));
            safeSleep(SLEEP_200_MILLIS);
            return true;
        } catch (JVisaException | InterruptedException e) {
            log.error("设置值失败", e);
            return false;
        }
    }

    @Override
    public boolean setOscStatus(String deviceStatus) {
        String status = deviceStatus.equalsIgnoreCase("STOP") ? "STOP" : "RUN";
        try {
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.SET_STATUS, status));
        } catch (JVisaException e) {
            throw new RuntimeException(e);
        }
        return false;
    }

    @Override
    public float getRaiseTime(Integer deviceChannel) {
        return 0;
    }

    @Override
    public float getFullTime(Integer deviceChannel) {
        return 0;
    }

    @Override
    public float getTopValue(Integer deviceChannel) {
        return 0;
    }

    @Override
    public float getBaseValue(Integer deviceChannel) {
        return 0;
    }


    private void sendCommand(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        instrument.write(command);
    }

    public static void main(String[] args) throws RuntimeException, JVisaException, DeviceCloseException {
        SDS5000XOscilloscope sds5000XOscilloscope = new SDS5000XOscilloscope(new DeviceOperationParameter());
//        String s = "USB0::0xF4EC::0xEE38::SDS5XCBX3R0127::INSTR";  //SDS5000X
        String s = "USB0::0xF4EC::0x1013::SDS6PCDD7R0318::INSTR";   //SDS6000X
        sds5000XOscilloscope.setDeviceName(s);
        try {
            sds5000XOscilloscope.open();
            String IDN = sds5000XOscilloscope.sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.IDN));
            System.out.println("IDN--" + IDN);
            //sds5000XOscilloscope.initializeSDS();
//            sds5000XOscilloscope.sendCommand("FCNT STATE,ON");
//            // 2. 复位设备
//            sds5000XOscilloscope.sendCommand("*RST");
//            Thread.sleep(1000); // 等待复位完成
            //4. 打开测量
//            sds5000XOscilloscope.sendCommand(":MEASure ON");
            Thread.sleep(2000); // 等待自动配置
            // 3. 设置自动配置
//            String freq2 = sds5000XOscilloscope.sendAndReceiveString("FCNT?\n");
//            System.out.println("Frequency: " + freq2);
//            sds5000XOscilloscope.sendCommand(":AUTOSet EXECute");
//            sds5000XOscilloscope.sendCommand(":MEASure:MODE ADVanced\n");
//            Thread.sleep(1000); // 等待自动配置
//
//            sds5000XOscilloscope.sendCommand(":MEASure:ADVanced:CLEar\n");
//            Thread.sleep(1000); // 等待自动配置
//            sds5000XOscilloscope.sendCommand(":MEASure:ADVanced:P1:SOURce1 C1\n");
//            Thread.sleep(1000); // 等待自动配置
//
//            sds5000XOscilloscope.sendCommand(":MEASure:ADVanced:P1:TYPE DUTY\n");
//            Thread.sleep(1000); // 等待自动配置
//
//            String type = sds5000XOscilloscope.sendAndReceiveString(":MEAS:ADV:P1:TYPE?\n");
//            Thread.sleep(1000); // 等待自动配置
//            System.out.println("Type:"  + type);

//            sds5000XOscilloscope.sendCommand(":MEASure:ADVanced:P2:SOURce1 C2\n");

//            for (int i = 0; i < 10; i++) {
//                Thread.sleep(2000); // 等待自动配置
//                String freq = sds5000XOscilloscope.sendAndReceiveString(":MEAS:SIMPle:VALue? FREQ\n");
//                Thread.sleep(2000);
//                String duty = sds5000XOscilloscope.sendAndReceiveString(":MEAS:SIMPle:VALue? DUTY\n");
////            Thread.sleep(3000);
//                System.out.println(String.format("Frequency%s: ", i) + freq + " Hz");
//                System.out.println(String.format("Duty Cycle%s: ", i) + duty + " %");
//            }

//            String s1 = sds5000XOscilloscope.sendAndReceiveString("COUN:STAT?");
//            System.out.println(s1);

        } catch (DeviceOpenException | DeviceOpenRepeatException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            sds5000XOscilloscope.close();
        }
    }
}
