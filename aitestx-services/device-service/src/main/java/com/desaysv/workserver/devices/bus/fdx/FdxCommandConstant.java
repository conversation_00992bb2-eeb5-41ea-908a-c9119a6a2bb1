package com.desaysv.workserver.devices.bus.fdx;


import org.apache.commons.codec.binary.Hex;

public class FdxCommandConstant {
    //header
    public final static byte[] fdxSignature = new byte[]{(byte) 0x43, (byte) 0x41, (byte) 0x4E, (byte) 0x6F, (byte) 0x65, (byte) 0x46, (byte) 0x44, (byte) 0x58};
    public final static byte[] fdxMajorVersion = new byte[]{(byte) 0x02};
    public final static byte[] fdxMinorVersion = new byte[]{(byte) 0x01};
    public final static byte[] numberOfCommands = new byte[]{(byte) 0x01, (byte) 0x00};
    public final static byte[] fdxProtocolFlags = new byte[]{(byte) 0x00};
    public final static byte[] reserved = new byte[]{(byte) 0x00};
    //default CommandSize and CommandCode
    public final static byte[] startCommandSize = new byte[]{(byte) 0x04, (byte) 0x00};
    public final static byte[] startCommandCode = new byte[]{(byte) 0x01, (byte) 0x00};
    public final static byte[] stopCommandSize = new byte[]{(byte) 0x04, (byte) 0x00};
    public final static byte[] stopCommandCode = new byte[]{(byte) 0x02, (byte) 0x00};
    public final static byte[] dataRequestCommandSize = new byte[]{(byte) 0x06, (byte) 0x00};
    public final static byte[] dataRequestCommandCode = new byte[]{(byte) 0x06, (byte) 0x00};
    public final static byte[] dataExchangeCommandCode = new byte[]{(byte) 0x05, (byte) 0x00};
    //status Request command
    public final static byte[] canoeStatusCommandSize = new byte[]{(byte) 0x04, (byte) 0x00};
    public final static byte[] canoeStatusCommandCode = new byte[]{(byte) 0x0A, (byte) 0x00};

    //需要与CANoe fdx.xml的GroupID关联
    public final static byte[] reqGroupId = new byte[]{(byte) 0x01, (byte) 0x00};
    public final static byte[] testReqGroupId = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] dataExchangeGroupId = new byte[]{(byte) 0x02, (byte) 0x00};
    //groupId = 2
    public final static byte[] writeCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] writeDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] writeGroupId = new byte[]{(byte) 0x02, (byte) 0x00};
    //returnValue
    public final static byte[] returnValueGroupId = new byte[]{(byte) 0x01, (byte) 0x00};
    public final static byte[] returnValueCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};    //8 + datasize(8) = 16==>换算成16进制 0x10 （占两个字节且小端排序）//
    public final static byte[] returnValueDataSize = new byte[]{(byte) 0x08, (byte) 0x00};     //FDX xml中GroupId = 1的size占8字节 ==>换算成16进制 0x08 （占两个字节且小端排序）//
    //counter
    public final static byte[] counterCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};    //8 + datasize(8) = 16==>换算成16进制 0x10 （占两个字节且小端排序）//
    public final static byte[] counterDataSize = new byte[]{(byte) 0x08, (byte) 0x00};     //FDX xml中所有counterGroupId的size占8字节 ==>换算成16进制 0x08 （占两个字节且小端排序）//
    //SetSignal and getSignal
    public final static byte[] setCanSignalGroupId = new byte[]{(byte) 0x02, (byte) 0x00};
    public final static byte[] setCanSignalCounterGroupId = new byte[]{(byte) 0x03, (byte) 0x00};
    public final static byte[] getCanSignalGroupId = new byte[]{(byte) 0x04, (byte) 0x00};
    public final static byte[] getCanSignalCounterGroupId = new byte[]{(byte) 0x05, (byte) 0x00};
    public final static byte[] setLinSignalGroupId = new byte[]{(byte) 0x17, (byte) 0x00};
    public final static byte[] setLinSignalCounterGroupId = new byte[]{(byte) 0x18, (byte) 0x00};
    public final static byte[] getLinSignalGroupId = new byte[]{(byte) 0x19, (byte) 0x00};
    public final static byte[] getLinSignalCounterGroupId = new byte[]{(byte) 0x1A, (byte) 0x00};

    public final static byte[] signalCommandSize = new byte[]{(byte) 0x60, (byte) 0x00};    //8 + datasize(88) = 96 ==>换算成16进制 0x60 （占两个字节且小端排序）//
    public final static byte[] signalDataSize = new byte[]{(byte) 0x58, (byte) 0x00};     //FDX xml中GroupId = 2/4/23/25的size占88字节 ==>换算成16进制 0x58 （占两个字节且小端排序）//

    //SingleMsgControl and AllMsgControl
    public final static byte[] setCanSingleMsgControlGroupId = new byte[]{(byte) 0x11, (byte) 0x00};
    public final static byte[] setCanSingleMsgControlCounterGroupId = new byte[]{(byte) 0x12, (byte) 0x00};
    public final static byte[] setCanAllMsgControlGroupId = new byte[]{(byte) 0x13, (byte) 0x00};
    public final static byte[] setCanAllMsgControlCounterGroupId = new byte[]{(byte) 0x14, (byte) 0x00};
    public final static byte[] setLinSingleMsgControlGroupId = new byte[]{(byte) 0x1B, (byte) 0x00};
    public final static byte[] setLinSingleMsgControlCounterGroupId = new byte[]{(byte) 0x1C, (byte) 0x00};
    public final static byte[] setLinAllMsgControlGroupId = new byte[]{(byte) 0x1D, (byte) 0x00};
    public final static byte[] setLinAllMsgControlCounterGroupId = new byte[]{(byte) 0x1E, (byte) 0x00};

    public final static byte[] singleMsgControlCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
    public final static byte[] singleMsgControlDataSize = new byte[]{(byte) 0x38, (byte) 0x00};
    public final static byte[] allMsgControlCommandSize = new byte[]{(byte) 0x20, (byte) 0x00};
    public final static byte[] allMsgControlDataSize = new byte[]{(byte) 0x18, (byte) 0x00};
    //MsgCycleTime
    public final static byte[] setCanMsgCycleTimeGroupId = new byte[]{(byte) 0x06, (byte) 0x00};
    public final static byte[] setCanMsgCycleTimeCounterGroupId = new byte[]{(byte) 0x07, (byte) 0x00};
    public final static byte[] canMsgCycleTimeCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
    public final static byte[] canMsgCycleTimeDataSize = new byte[]{(byte) 0x38, (byte) 0x00};
    //MsgDLC
//    public final static byte[] setCanMsgDLCGroupId = new byte[]{(byte) 0x08, (byte) 0x00};
//    public final static byte[] setCanMsgDLCCounterGroupId = new byte[]{(byte) 0x09, (byte) 0x00};
//    public final static byte[] canMsgDLCCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
//    public final static byte[] canMsgDLCDataSize = new byte[]{(byte) 0x38, (byte) 0x00};
    ///////////////////////////////////////////////////
    public final static byte[] setCanMsgDLCGroupId = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] setCanMsgDLCCounterGroupId = new byte[]{(byte) 0x09, (byte) 0x00};
    public final static byte[] canMsgDLCCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
    public final static byte[] canMsgDLCDataSize = new byte[]{(byte) 0x38, (byte) 0x00};

    ///////////////////////////////////////////////////

    //ChecksumRolling
    public final static byte[] setCanChecksumRollingGroupId = new byte[]{(byte) 0x15, (byte) 0x00};
    public final static byte[] setCanChecksumRollingCounterGroupId = new byte[]{(byte) 0x16, (byte) 0x00};
    public final static byte[] canChecksumRollingCommandSize = new byte[]{(byte) 0x28, (byte) 0x00};
    public final static byte[] canChecksumRollingDataSize = new byte[]{(byte) 0x20, (byte) 0x00};

    //PTS
    public final static byte[] setCanPTSGroupId = new byte[]{(byte) 0x0A, (byte) 0x00};
    public final static byte[] setCanPTSCounterGroupId = new byte[]{(byte) 0x0B, (byte) 0x00};
    public final static byte[] getCanPTSGroupId = new byte[]{(byte) 0x0C, (byte) 0x00};
    public final static byte[] canPTSCommandSize = new byte[]{(byte) 0x24, (byte) 0x00};
    public final static byte[] canPTSDataSize = new byte[]{(byte) 0x1C, (byte) 0x00};

    //XCP
    public final static byte[] setCanXCPGroupId = new byte[]{(byte) 0x0D, (byte) 0x00};
    public final static byte[] setCanXCPCounterGroupId = new byte[]{(byte) 0x0E, (byte) 0x00};
    public final static byte[] getCanXCPGroupId = new byte[]{(byte) 0x0F, (byte) 0x00};
    public final static byte[] getCanXCPCounterGroupId = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] canXCPCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
    public final static byte[] canXCPDataSize = new byte[]{(byte) 0x38, (byte) 0x00};
    public final static byte[] getCanXCPCommandSize = new byte[]{(byte) 0x40, (byte) 0x00};
    public final static byte[] getCanXCPDataSize = new byte[]{(byte) 0x38, (byte) 0x00};

    public final static byte[] ptsSwVersionGroupId = new byte[]{(byte) 0x1F, (byte) 0x00};
    public final static byte[] setNotificationUpgradeGroupId = new byte[]{(byte) 0x20, (byte) 0x00};
    public final static byte[] setUpgradeCounterGroupId = new byte[]{(byte) 0x21, (byte) 0x00};
    public final static byte[] ptsSwVersionCommandSize = new byte[]{(byte) 0x3A, (byte) 0x00};
    public final static byte[] ptsSwVersionDataSize = new byte[]{(byte) 0x32, (byte) 0x00};
    public final static byte[] notificationUpgradeCommandSize = new byte[]{(byte) 0x92, (byte) 0x02};
    public final static byte[] notificationUpgradeDataSize = new byte[]{(byte) 0x8A, (byte) 0x02};

    //FBL/NM/TP/DataLink/OTA/DoCAN
    public final static byte[] flyTestControlGroupId = new byte[]{(byte) 0x01, (byte) 0x00};
    public final static byte[] setTestCaseGroupId = new byte[]{(byte) 0x02, (byte) 0x00};
    public final static byte[] testResultGroupId = new byte[]{(byte) 0x03, (byte) 0x00};

    //uds
    public final static byte[] setStartUDSTestGroupId = new byte[]{(byte) 0x23, (byte) 0x00};
    public final static byte[] setUDSAddressGroupId = new byte[]{(byte) 0x24, (byte) 0x00};
    public final static byte[] getUDSFinishFlagGroupId = new byte[]{(byte) 0x25, (byte) 0x00};
    public final static byte[] setUDSSendUDSDataGroupId = new byte[]{(byte) 0x26, (byte) 0x00};
    public final static byte[] getUDSRecUdsDataGroupId = new byte[]{(byte) 0x27, (byte) 0x00};
    public final static byte[] getUDSRecUdsDataDescribeGroupId = new byte[]{(byte) 0x28, (byte) 0x00};
    public final static byte[] getUDSRecUdsDataDescribe22GroupId = new byte[]{(byte) 0x29, (byte) 0x00};
    public final static byte[] setUDSLogNameGroupId = new byte[]{(byte) 0x2A, (byte) 0x00};
    public final static byte[] setUDSStartLoggingGroupId = new byte[]{(byte) 0x2B, (byte) 0x00};
    public final static byte[] setUDSStopLoggingGroupId = new byte[]{(byte) 0x2C, (byte) 0x00};
    public final static byte[] setUDSSecurityKeyGroupId = new byte[]{(byte) 0x2D, (byte) 0x00};

    //CAN LOG
    public final static byte[] setCANLogNameGroupId = new byte[]{(byte) 0x20, (byte) 0x00};
    public final static byte[] setCANStartLoggingGroupId = new byte[]{(byte) 0x21, (byte) 0x00};
//    public final static byte[] setCANStopLoggingGroupId = new byte[]{(byte) 0x22, (byte) 0x00};

    //ETH: XML文件待补充
    public final static byte[] setDoIPAddressGroupId = new byte[]{(byte) 0x0D, (byte) 0x00};
    public final static byte[] setDoIPSendUDSDataGroupId = new byte[]{(byte) 0x0E, (byte) 0x00};
    public final static byte[] getDoIPFinishFlagGroupId = new byte[]{(byte) 0x0F, (byte) 0x00};
    public final static byte[] getDoIPRecUdsDataGroupId = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] getDoIPRecUdsDataDescribeGroupId = new byte[]{(byte) 0x11, (byte) 0x00};
    public final static byte[] getDoIPRecUdsDataDescribe22GroupId = new byte[]{(byte) 0x12, (byte) 0x00};
    public final static byte[] setDoIPLogNameGroupId = new byte[]{(byte) 0x0A, (byte) 0x00};
    public final static byte[] setDoIPStartLoggingGroupId = new byte[]{(byte) 0x0B, (byte) 0x00};
    public final static byte[] setDoIPStopLoggingGroupId = new byte[]{(byte) 0x0C, (byte) 0x00};

    public final static byte[] flyTestControlCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] flyTestControlDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] testCaseDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] canTestCaseCommandSize = new byte[]{(byte) 0x20, (byte) 0x00};
    public final static byte[] canTestCaseDataSize = new byte[]{(byte) 0x18, (byte) 0x00};
    public final static byte[] setAddressCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] setDoCANAddressDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] setDoCANSendUDSDataCommandSize = new byte[]{(byte) 0xC0, (byte) 0x0B};
    public final static byte[] setDoCANSendUDSDataDataSize = new byte[]{(byte) 0xB8, (byte) 0x0B};
    public final static byte[] setDoCANFinishFlagDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] getDoCANRecUdsDataSize = new byte[]{(byte) 0xB8,(byte) 0x0B};
    public final static byte[] getDoCANRecUdsDataDescribeDataSize = new byte[]{(byte) 0xB8,(byte) 0x0B};
    public final static byte[] getDoCANRecUdsDataDescribe22DataSize = new byte[]{(byte) 0xB8,(byte) 0x0B};
    public final static byte[] setDoCANLogNameCommandSize = new byte[]{(byte) 0xD0, (byte) 0x00};
    public final static byte[] setDoCANLogNameDataSize = new byte[]{(byte) 0xC8, (byte) 0x00};
    public final static byte[] setLogStatusCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] setLogStatusDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] setStartUdsTestCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] setStartUdsTestDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    public final static byte[] setDoCANSecurityKeyCommandSize = new byte[]{(byte) 0xC0, (byte) 0x0B};
    public final static byte[] setDoCANSecurityKeyDataSize = new byte[]{(byte) 0xB8, (byte) 0x0B};

    //ETH
    public final static byte[] getDoIPRecUdsDataSize = new byte[]{(byte) 0xE8,(byte) 0x03};
    public final static byte[] getDoIPRecUdsDataDescribeDataSize = new byte[]{(byte) 0xE8,(byte) 0x03};
    public final static byte[] getDoIPRecUdsDataDescribe22DataSize = new byte[]{(byte) 0xE8,(byte) 0x03};
    public final static byte[] setDoIPSendUDSDataCommandSize = new byte[]{(byte) 0x20, (byte) 0x00};
    public final static byte[] setDoIPSendUDSDataDataSize = new byte[]{(byte) 0x18, (byte) 0x00};

    //Reset Charge
    public final static byte[] setResetChargeGroupId = new byte[]{(byte) 0x32, (byte) 0x00};
    public final static byte[] resetChargeCommandSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] resetChargeDataSize = new byte[]{(byte) 0x08, (byte) 0x00};
    //UDS 27 key
    public final static byte[] getUdsKeyGroupId = new byte[]{(byte) 0x2D, (byte) 0x00};
    public final static byte[] getUdsKeyDataSize = new byte[]{(byte) 0x64, (byte) 0x00};

    //setTemperature
    public final static byte[] setTemperatureGroupId = new byte[]{(byte) 0x33, (byte) 0x00};
    public final static byte[] setTemperatureCommandSize = new byte[]{(byte) 0x18, (byte) 0x00};
    public final static byte[] setTemperatureDataSize = new byte[]{(byte) 0x10, (byte) 0x00};
    public final static byte[] setTemperatureCounterGroupId = new byte[]{(byte) 0x34, (byte) 0x00};

}


