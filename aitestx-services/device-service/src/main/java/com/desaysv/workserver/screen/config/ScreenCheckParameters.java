package com.desaysv.workserver.screen.config;

import lombok.Data;

@Data
public class ScreenCheckParameters {

    private int upperLimitPointReportRate;
    private int lowerLimitPointReportRate;

    private boolean checkPointReportRate;

    private double linearPercentDeviationThreshold; //线性度误差阈值

    private int pointPixelDeviationThreshold; //单点偏移阈值

//    private int lineAngleDeviationThreshold; //直线上的点角度偏移阈值

}
