package com.desaysv.workserver.devices.serial;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.devices.serial.interfaces.ISerial;
import com.desaysv.workserver.devices.serial.text_match.SerialJudgeParameter;
import com.desaysv.workserver.devices.serial.text_match.WaitFilterParameter;
import com.desaysv.workserver.exceptions.device.*;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

/**
 * 串口设备
 */
@Getter
public abstract class SerialDevice extends PortDevice<SerialConfig> implements ISerial {
    public static final String MUST_EXIST_MONITOR_START = "-----------------mustExistMonitorStart-----------------";
    public static final String MUST_EXIST_MONITOR_END = "-----------------mustExistMonitorEnd-----------------";


    public static final String FORBID_EXIST_MONITOR_START = "-----------------forbidExistMonitorStart-----------------";
    public static final String FORBID_EXIST_MONITOR_END = "-----------------forbidExistMonitorEnd-----------------";
    public static final String WAIT_FILTER = "-----------------waitFilter-----------------";
    public static final String JUDGE_TEXT = "-----------------judgeText-----------------";


    private final String deviceType = DeviceType.DEVICE_SERIAL;

    public SerialDevice() {
        this(new DeviceOperationParameter());
    }

    public SerialDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


//    @Override
//    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
//        return super.open();
//    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        return super.close();
    }

    public abstract boolean mustExistMonitorStart(String text);

    public abstract OperationResult mustExistMonitorEnd();

    public abstract boolean forbidExistMonitorStart(String text);

    public abstract OperationResult forbidExistMonitorEnd();

    public abstract boolean waitFilter(WaitFilterParameter waitFilterParameter) throws OperationFailNotification;

    public abstract boolean judgeText(SerialJudgeParameter serialJudgeParameter) throws OperationFailNotification;


    @Override
    public Class<SerialConfig> getDeviceConfigClass() {
        return SerialConfig.class;
    }

    @Override
    public void updateConfig(SerialConfig deviceConfig) {
        super.updateConfig(deviceConfig);
    }

}
