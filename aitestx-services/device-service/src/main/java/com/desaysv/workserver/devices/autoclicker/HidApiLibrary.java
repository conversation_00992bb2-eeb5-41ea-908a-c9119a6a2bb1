package com.desaysv.workserver.devices.autoclicker;

import com.sun.jna.*;

public interface HidApiLibrary extends Library {
    HidApiLibrary INSTANCE = Native.load("hidapi.dll", HidApiLibrary.class);

    void hid_init();

    void hid_exit();

    Pointer hid_open(short var1, short var2, WString var3);

    void hid_close(Pointer var1);

    Pointer hid_error(Pointer var1);

    int hid_read(Pointer var1, Structure.ByReference var2, int var3);

    int hid_read_timeout(Pointer var1, Structure.ByReference var2, int var3, int var4);

    int hid_write(Pointer var1, Structure.ByReference var2, int var3);

    int hid_get_feature_report(Pointer var1, Structure.ByReference var2, int var3);

    int hid_send_feature_report(Pointer var1, Structure.ByReference var2, int var3);

    int hid_get_indexed_string(Pointer var1, int var2, Structure.ByReference var3, int var4);

    int hid_get_manufacturer_string(Pointer var1, Structure.ByReference var2, int var3);

    int hid_get_product_string(Pointer var1, Structure.ByReference var2, int var3);

    int hid_get_serial_number_string(Pointer var1, Structure.ByReference var2, int var3);

    int hid_set_nonblocking(Pointer var1, int var2);

    HidDeviceInfoStructure hid_enumerate(short var1, short var2);

    void hid_free_enumeration(Pointer var1);

    Pointer hid_open_path(String var1);
}