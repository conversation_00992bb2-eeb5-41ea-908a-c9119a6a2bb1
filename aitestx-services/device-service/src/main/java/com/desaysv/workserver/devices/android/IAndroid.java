package com.desaysv.workserver.devices.android;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.CaptureSupport;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.model.roi.RoiRectInfo;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public interface IAndroid extends CaptureSupport {

    String screenshot() throws OperationFailNotification;

    static List<PointInt> splitIntoPointIntPairs(List<String> coordinates) {
        List<PointInt> pairs = new ArrayList<>();
        // 检查输入列表是否为空或null
        if (coordinates == null || coordinates.isEmpty()) {
            return pairs; // 返回空列表
        }

        // 遍历列表，每次跳过两个元素
        for (int i = 0; i + 1 < coordinates.size(); i += 2) {
            // 获取当前元素作为 x 坐标
            int x = Integer.parseInt(coordinates.get(i));
            // 获取下一个元素作为 y 坐标
            int y = Integer.parseInt(coordinates.get(i + 1));

            // 创建一个新的 PointInt 对象
            PointInt point = new PointInt(x, y);

            // 将 PointInt 对象添加到结果列表中
            pairs.add(point);
        }

        return pairs;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdbRegexRule).ADB_CLICK"})
    default boolean click(List<String> coordinates) throws OperationFailNotification {
        List<PointInt> pointInts = splitIntoPointIntPairs(coordinates);
        boolean ok = true;
        for (PointInt pointInt : pointInts) {
            ok &= click(pointInt);
        }
        return ok;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdbRegexRule).ADB_SWIPE"})
    default boolean swipe(int startX, int startY, int endX, int endY) throws OperationFailNotification {
        return swipe(new PointInt(startX, startY), new PointInt(endX, endY));
    }


    boolean click(PointInt pointInt) throws OperationFailNotification;


    default boolean swipe(JSONArray jsonArray) throws OperationFailNotification {
        JSONObject startPointInt = jsonArray.getJSONObject(0);
        JSONObject endPointInt = jsonArray.getJSONObject(1);

        PointInt startPoint = startPointInt.to(PointInt.class);
        PointInt endPoint = endPointInt.to(PointInt.class);
        return swipe(startPoint, endPoint);
    }

    boolean swipe(PointInt startPoint, PointInt endPoint) throws OperationFailNotification;

    boolean randomClick(RoiRectInfo roiRectInfo) throws OperationFailNotification;

    boolean randomSwipe(RoiRectInfo roiRectInfo) throws OperationFailNotification;

    boolean executeADBCommand(String command) throws OperationFailNotification;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdbRegexRule).SEND"})
    default boolean sendAnything(String text, String parenthesisContext, String checkedContext) throws OperationFailNotification {
        return executeADBCommand(parenthesisContext);
    }

    boolean multiFingerSwipe(RoiRectInfo roiRectInfo, int fingerCount, String direction) throws OperationFailNotification, IOException;

}
