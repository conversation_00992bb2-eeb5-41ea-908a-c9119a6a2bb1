package com.desaysv.workserver.stream.rtsp;

import com.desaysv.workserver.devices.qnx.QnxInstrument;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.stream.QnxStreamProducer;
import com.desaysv.workserver.stream.grabber.QnxStreamGrabber;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.FrameGrabber;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@EqualsAndHashCode(callSuper = true)
@Slf4j
@Data
public class RtspQnxStreamProducer extends QnxStreamProducer {
    private final String deviceName;
    private QnxStreamGrabber qnxStreamGrabber;
    private String rtmpUrl;
    //是否禁用动态输出
    private boolean blockDynamicOutput = false;
    private Thread streamThread;
    private ExecutorService grabExecutor;
    private Runnable streamRunnable;
    private boolean alive = true;

    public RtspQnxStreamProducer(int w, int h, String deviceName) {
        super(w, h);
        this.deviceName = deviceName;
        this.qnxStreamGrabber = new QnxStreamGrabber();
    }

    @Override
    protected void startStream(String deviceModel, String deviceUUID) throws Exception {
        DeviceRegisterManager deviceManager = SpringContextHolder.getBean(DeviceRegisterManager.class);
        QnxInstrument qnxInstrument = (QnxInstrument) deviceManager.getDevice(deviceName);

        if (qnxInstrument == null) {
            throw new IllegalStateException("无法找到QNX设备: " + deviceName);
        }

        this.rtmpUrl = "rtmp://127.0.0.1/live?live=1";
        log.info("QNX设备 '{}' 的RTMP URL: {}", deviceName, rtmpUrl);

        // 设置URL到StreamProducer
        setUrl(this.rtmpUrl);

        qnxStreamGrabber.setRtmpUrl(this.rtmpUrl);

        try {
            qnxStreamGrabber.start();
            Frame frame = qnxStreamGrabber.grab();
            if (frame != null) {
                log.info("QNX设备 '{}' 成功获取第一帧，尺寸: {}x{}",
                        deviceName, frame.imageWidth, frame.imageHeight);
                setWidth(frame.imageWidth);
                setHeight(frame.imageHeight);
            } else {
                log.warn("QNX设备 '{}' 获取第一帧为null", deviceName);
            }

            // 准备流处理线程
            prepareStreamProcessing();

            // 启动流处理
            startStreamProcessing();
        } catch (Exception e) {
            log.error("启动QNX视频流失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String pushStream(String deviceModel, String deviceUUID) throws Exception {
        startStream(deviceModel, deviceUUID);
        return rtmpUrl;
    }

    @Override
    public Frame grabFrame() throws FrameGrabberException {
        try {
            return qnxStreamGrabber.grab();
        } catch (Exception e) {
            log.error("获取QNX视频帧失败", e);
            throw new FrameGrabberException(e);
        }
    }

    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {
        // 实现流分发逻辑
        // 可以将帧发送到RTMP服务器，或者保存到本地，或者进行其他处理

        // 如果需要推流到RTMP服务器，可以参考RtspPortStreamProducer的实现
        if (imageBuffer.getFrame() != null) {
            log.debug("QNX设备 '{}' 分发视频帧，尺寸: {}x{}",
                    deviceName,
                    imageBuffer.getFrame().imageWidth,
                    imageBuffer.getFrame().imageHeight);
        }
    }

    @Override
    public RectSize getDefaultSize() {
        int width = qnxStreamGrabber.getImageWidth();
        int height = qnxStreamGrabber.getImageHeight();
        if (width > 0 && height > 0) {
            return new RectSize(width, height);
        }
        return null;
    }

    /**
     * 控制视频动态或静止
     *
     * @param isOutput: 是否输出动态帧
     */
    @Override
    public void outputDynamicFrame(boolean isOutput) {
        if (isOutput) {
            if (blockDynamicOutput) {
                log.info("QNX设备 '{}' 开启动态画面输出", deviceName);
                blockDynamicOutput = false;
                synchronized (this) {
                    notifyAll();  // 唤醒等待的线程
                }
            }
        } else {
            // 禁用动态输出
            log.info("QNX设备 '{}' 开启静态画面输出", deviceName);
            blockDynamicOutput = true;
        }
    }

    @Override
    public void setExposureAuto(CameraSettings cameraSettings) {
        // QNX设备可能不支持此功能
    }

    @Override
    public void setReverseX(boolean status) {
        // QNX设备可能不支持此功能
    }

    @Override
    public void setReverseY(boolean status) {
        // QNX设备可能不支持此功能
    }

    @Override
    public void grabberSwitch(boolean status) {
        try {
            if (status) {
                qnxStreamGrabber.start();
            } else {
                qnxStreamGrabber.stop();
            }
        } catch (Exception e) {
            log.error("切换QNX视频流状态失败", e);
        }
    }

    @Override
    public CameraSettings getCameraSettings() {
        return new CameraSettings();
    }

    @Override
    public double getGrabberFrameRate() {
        return 30.0; // 默认帧率，可根据实际情况调整
    }

    @Override
    public void closeStream() {
        try {
            log.info("QNX设备 '{}' 关闭视频流", deviceName);
            alive = false;

            // 唤醒可能在等待的线程
            synchronized (this) {
                notifyAll();
            }

            // 关闭执行器
            if (grabExecutor != null) {
                grabExecutor.shutdownNow();
                try {
                    if (!grabExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.warn("QNX设备 '{}' 流处理线程池未能在5秒内关闭", deviceName);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 释放流抓取器
            if (qnxStreamGrabber != null) {
                qnxStreamGrabber.release();
            }
        } catch (Exception e) {
            log.error("QNX设备 '{}' 关闭视频流失败: {}", deviceName, e.getMessage(), e);
        }
    }

    /**
     * 准备流处理线程
     */
    private void prepareStreamProcessing() {
        this.streamRunnable = () -> {
            while (alive) {
                try {
                    if (blockDynamicOutput) {
                        synchronized (this) {
                            try {
                                // 当blockDynamicOutput为true时，线程等待
                                wait();
                            } catch (InterruptedException e) {
                                if (!alive) {
                                    log.warn("QNX设备 '{}' 流处理线程被中断: {}", deviceName, e.getMessage());
                                }
                            }
                        }
                    }

                    if (alive) {
                        // 获取图像缓冲区
                        ImageBuffer buffer = grabImageBuffer();

                        // 分发视频流
                        if (buffer.getFrame() != null) {
                            distributeStream(buffer);
                        }

                        // 短暂休眠，避免CPU占用过高
                        TimeUnit.MILLISECONDS.sleep(10);
                    }
                } catch (Exception e) {
                    if (alive) {
                        handleStreamProcessingException(e);
                    }
                }
            }
        };
    }

    /**
     * 启动流处理线程
     */
    private void startStreamProcessing() {
        log.info("QNX设备 '{}' 启动流处理线程", deviceName);
        grabExecutor = Executors.newSingleThreadExecutor(r -> {
            Thread t = new Thread(r, "QnxStreamProcessor-" + deviceName);
            t.setDaemon(true);
            return t;
        });
        grabExecutor.execute(streamRunnable);
    }

    /**
     * 处理流处理过程中的异常
     */
    private void handleStreamProcessingException(Exception e) {
        log.error("QNX设备 '{}' 流处理异常: {}", deviceName, e.getMessage(), e);
        try {
            // 尝试恢复流
            recoverStream();
        } catch (Exception ex) {
            log.error("QNX设备 '{}' 恢复流失败: {}", deviceName, ex.getMessage(), ex);
        }
    }

    /**
     * 恢复流处理
     */
    private void recoverStream() throws Exception {
        log.info("QNX设备 '{}' 恢复视频流采集", deviceName);
        if (qnxStreamGrabber != null) {
            qnxStreamGrabber.restart();
        }
    }

    /**
     * 捕捉图像buffer（地址相同）
     *
     * @return 图像buffer
     */
    public ImageBuffer grabImageBuffer() throws FrameGrabber.Exception {
        return qnxStreamGrabber.grabByteBuffer();
    }
}
