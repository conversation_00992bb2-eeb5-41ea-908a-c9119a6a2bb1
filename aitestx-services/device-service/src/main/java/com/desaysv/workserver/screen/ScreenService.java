package com.desaysv.workserver.screen;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.filemanager.project.ScreenFileManager;
import com.desaysv.workserver.screen.config.ScreenConfig;
import com.desaysv.workserver.screen.entity.TouchParsePackage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Getter
@Service
@Slf4j
@Lazy
public class ScreenService {

    private ScreenConfig screenConfig;

    public ScreenConfig loadConfig(String projectName) {
        ScreenFileManager fileManager = ProjectFileManager.of(projectName, ScreenFileManager.class);
        String jsonText = fileManager.readScreenConfig();
        screenConfig = JSON.parseObject(jsonText, ScreenConfig.class);
        if (screenConfig == null) {
            screenConfig = new ScreenConfig();
        }
//        log.info("导入屏幕配置:{}", screenConfig);
        return screenConfig;
    }

    public void updateConfig(ScreenConfig screenConfig) {
        log.info("更新屏幕配置:{}", screenConfig);
        ScreenFileManager fileManager = ProjectFileManager.of(screenConfig.getProject(), ScreenFileManager.class);
        fileManager.writeScreenConfig(JSON.toJSONString(screenConfig));
        this.screenConfig = screenConfig;
    }

    public OperationResult parseTouchPoint(TouchParsePackage touchParsePackage) {
        log.info("解析报点数据:{}", touchParsePackage);

        OperationResult operationResult = new OperationResult();
        operationResult.setOk(true);
        operationResult.setData(TouchPointUtils.parseStringToPoints(touchParsePackage.getText(), touchParsePackage.getTouchPointMatrixConfig()));
        log.info(String.valueOf(operationResult));
        return operationResult;
    }

}
