package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.entity.Point2D;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PreciousPoint2D {

    private BigDecimal x;
    private BigDecimal y;

    public PreciousPoint2D() {

    }

    public PreciousPoint2D(double x, double y) {
        this(BigDecimal.valueOf(x), BigDecimal.valueOf(y));
    }

    public PreciousPoint2D(BigDecimal x, BigDecimal y) {
        this.x = x;
        this.y = y;
    }

    public Point2D toPoint2D() {
        return new Point2D(x.doubleValue(), y.doubleValue());
    }

    public PointInt toPointInt() {
        return new PointInt(x.intValue(), y.intValue());
    }

    public MoveEntity toMoveEntity(double z, double r) {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(x.doubleValue());
        moveEntity.setY(y.doubleValue());
        moveEntity.setZ(z);
        moveEntity.setR(r);
        return moveEntity;
    }

    public PreciousPoint3D toPreciousPoint3D(double z, double r) {
        PreciousPoint3D preciousPoint3D = new PreciousPoint3D();
        preciousPoint3D.setX(x);
        preciousPoint3D.setY(y);
        preciousPoint3D.setZ(BigDecimal.valueOf(z));
        preciousPoint3D.setR(BigDecimal.valueOf(r));
        return preciousPoint3D;
    }


}
