package com.desaysv.workserver.devices.bus.base;

import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.base.lin.LinMessage;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 指定周期的消息发送任务
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public abstract class CyclicSendTask extends CyclicTask {

    private CanMessage canMessage; //周期发送的消息

//    private final int canId;

    private int arbitrationId;

    private float period; //发送报文的周期（秒）

    private Float duration; //发送持续时间（秒）

    private FlexrayMessage flexrayMessage;//Flexray消息

    private int slotId;//Flexray消息

    private LinMessage linMessage;//Lin消息

    private String linId;//Lin消息

    public CyclicSendTask(CanMessage message, float period) {
        this.canMessage = message;
//        this.canId = message.getArbitrationId();
        this.arbitrationId = message.getArbitrationId();
        this.period = period;
    }

    public CyclicSendTask(CanMessage message, float period, Float duration) {
        this(message, period);
        this.duration = duration;
    }

    public CyclicSendTask(FlexrayMessage message, float period) {
        this.flexrayMessage = message;
//        this.canId = message.getSlotId();
        this.arbitrationId = message.getSlotId();
        this.slotId = message.getSlotId();
        this.period = period;
    }

    public CyclicSendTask(FlexrayMessage message, float period, Float duration) {
        this(message, period);
        this.duration = duration;
    }

    public CyclicSendTask(LinMessage message, float period) {
        this.linMessage = message;
        this.linId = message.getIdHex();
        this.period = period;
    }

    public CyclicSendTask(LinMessage message, float period, Float duration) {
        this(message, period);
        this.duration = duration;
    }

    @Override
    public String toString() {
        return String.format("CyclicSendTask(channel=%d, taskId=0x%x, duration=%s, message=[%s])",
                getTaskId().getChannel(), getTaskId().getArbitrationId(), duration == null ? "无限周期" : duration + "s", canMessage);
    }


}
