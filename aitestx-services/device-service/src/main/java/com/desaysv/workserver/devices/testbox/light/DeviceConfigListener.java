package com.desaysv.workserver.devices.testbox.light;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.LinkedHashMap;
import java.util.Map;

public class DeviceConfigListener extends AnalysisEventListener<TestBoxDeviceConfig> {
//    private final Map<String, DeviceConfig> configMap = new ConcurrentSkipListMap<>();  //无序
private final Map<String, TestBoxDeviceConfig> configMap = new LinkedHashMap<>();  //有序的
    private final KeyGenerator keyGenerator;
    
    public DeviceConfigListener(KeyGenerator keyGenerator) {
        this.keyGenerator = keyGenerator;
    }

    @Override
    public void invoke(TestBoxDeviceConfig data, AnalysisContext context) {
        String compositeKey = keyGenerator.generate(data);
        configMap.put(compositeKey, data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 可选的后处理逻辑
    }

    public Map<String, TestBoxDeviceConfig> getResult() {
        return configMap;
    }
}