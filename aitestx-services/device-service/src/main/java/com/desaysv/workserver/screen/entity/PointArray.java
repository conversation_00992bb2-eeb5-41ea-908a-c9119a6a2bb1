package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.utils.MathUtils;
import com.desaysv.workserver.utils.PearsonRatioUtils;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class PointArray {
    private double[] x;

    private double[] y;

    public static void main(String[] args) {
        PointInt a = new PointInt(144, 170);
        PointInt b = new PointInt(144, 171);
        PointInt c = new PointInt(145, 172);
        PointInt d = new PointInt(146, 172);
        PointInt e = new PointInt(148, 172);
        PointInt f = new PointInt(151, 172);
        PointInt g = new PointInt(155, 172);
        PointInt h = new PointInt(160, 172);
        PointInt i = new PointInt(166, 172);
        PointInt j = new PointInt(173, 172);
        PointInt k = new PointInt(510, 169);

        PointArray pointArray = PointUtils.splitArray(a, b, c, d, e, f, g, h, i, j, k);
        System.out.println("getPearsonRelate:" + MathUtils.getPearsonRelate(pointArray.getX(), pointArray.getY()));
        System.out.println("getPearsonByDim:" + PearsonRatioUtils.getPearsonByDim(pointArray.getX(), pointArray.getY()));
        System.out.println("------------------");
        double[] d1 = {0, 5, 10};
        double[] d2 = {0, 5, 10};
        System.out.println("getPearsonRelate:" + MathUtils.getPearsonRelate(d1, d2));
        System.out.println("getPearsonByDim:" + PearsonRatioUtils.getPearsonByDim(d1, d2));
        System.out.println("------------------");
        d1 = new double[]{0, 20000, 4000};
        d2 = new double[]{4, 2, 0};
        System.out.println("getPearsonRelate:" + MathUtils.getPearsonRelate(d1, d2));
        System.out.println("getPearsonByDim:" + PearsonRatioUtils.getPearsonByDim(d1, d2));

    }
}