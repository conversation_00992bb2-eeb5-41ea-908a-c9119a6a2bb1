package com.desaysv.workserver.devices.power.protocol;

import java.util.Arrays;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 10:00
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
public abstract class PowerBytesProtocol implements PowerProtocol {

    final int SYNC_HEAD = 0; // 同步头
    final int POWER_ADDR = 1; // 电源地址
    final int CMD_BYTE = 2; // 命令字
    final int MSG_START_BYTE = 3; // 4—25 字节为相关信息内容
    final int CHECK_SUM = 25; // 校验码
    final int DATA_LENGTH = 26; // 命令总长度

    final byte[] cmd = new byte[DATA_LENGTH];

    final static float DEFAULT_VALUE = -1;

    final static int HEADER = 0xAA;

    final static int ADDR = 0x00;

    public static float getDefaultValue() {
        return DEFAULT_VALUE;
    }

    byte[] getBytes() {
        Arrays.fill(cmd, (byte) 0x00);
        cmd[SYNC_HEAD] = (byte) HEADER;
        cmd[POWER_ADDR] = (byte) ADDR;
        return cmd;
    }

    byte getCheckSum(byte[] command) {
        int checksum = 0;
        for (int i = 0; i < command.length - 1; i++) {
            checksum += command[i];
        }
        return (byte) checksum;
    }


    void checksum() {
        // 第 26 字节为校验码，是前 25 个字节累加和
        cmd[CHECK_SUM] = getCheckSum(cmd);
    }

    public int getDataLength() {
        return DATA_LENGTH;
    }


}
