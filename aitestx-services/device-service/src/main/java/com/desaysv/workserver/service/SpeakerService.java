package com.desaysv.workserver.service;

import com.desaysv.workserver.devices.speaker.SpeakerDevice;
import com.desaysv.workserver.devices.speaker.SpeakerMessage;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.finder.SoundDeviceFinder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

/**
 * 扬声器服务类
 */
@Service
@Slf4j
public class SpeakerService {

    @Autowired
    private SoundDeviceFinder soundDeviceFinder;

    private SpeakerDevice speakerDevice;

    /**
     * 初始化扬声器设备
     *
     * @return 是否初始化成功
     */
    public boolean initSpeakerDevice() {
        try {
            if (speakerDevice != null) {
                log.info("扬声器设备已初始化");
                return true;
            }

            List<Device> outputDevices = soundDeviceFinder.findAllSoundOutputDevices();
            if (outputDevices.isEmpty()) {
                log.error("未找到可用的扬声器设备");
                return false;
            }

            // 选择第一个找到的扬声器设备
            speakerDevice = new SpeakerDevice();
            speakerDevice.setDeviceName(outputDevices.get(0).getDeviceName());
            speakerDevice.setAliasName("主扬声器");
            speakerDevice.setDeviceUniqueCode(outputDevices.get(0).getDeviceUniqueCode());

            // 尝试打开设备
            return speakerDevice.open();
        } catch (Exception e) {
            log.error("初始化扬声器设备失败", e);
            return false;
        }
    }

    /**
     * 播放文本内容
     *
     * @param text 要播放的文本
     * @return 是否播放成功
     */
    public boolean playText(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.warn("播放文本为空，忽略请求");
            return false;
        }

        try {
            if (speakerDevice == null && !initSpeakerDevice()) {
                log.error("扬声器设备未初始化");
                return false;
            }

            SpeakerMessage message = SpeakerMessage.withText(text);
            speakerDevice.speak(message);
            return true;
        } catch (Exception e) {
            log.error("播放文本失败: {}", text, e);
            return false;
        }
    }

    /**
     * 播放音频文件
     *
     * @param audioFile 音频文件
     * @return 是否播放成功
     */
    public boolean playAudioFile(File audioFile) {
        if (audioFile == null || !audioFile.exists()) {
            log.warn("音频文件不存在，忽略请求");
            return false;
        }

        try {
            if (speakerDevice == null && !initSpeakerDevice()) {
                log.error("扬声器设备未初始化");
                return false;
            }

            SpeakerMessage message = SpeakerMessage.withAudioFile(audioFile.getAbsolutePath());
            speakerDevice.speak(message);
            return true;
        } catch (Exception e) {
            log.error("播放音频文件失败: {}", audioFile.getAbsolutePath(), e);
            return false;
        }
    }

    /**
     * 关闭扬声器设备
     */
    public void closeSpeakerDevice() {
        if (speakerDevice != null) {
            try {
                speakerDevice.close();
                log.info("扬声器设备已关闭");
            } catch (Exception e) {
                log.error("关闭扬声器设备失败", e);
            } finally {
                speakerDevice = null;
            }
        }
    }
}
