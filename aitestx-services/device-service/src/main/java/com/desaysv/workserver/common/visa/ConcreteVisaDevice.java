package com.desaysv.workserver.common.visa;

import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;

/**
 * 模拟的Visa设备类
 */
public class ConcreteVisaDevice extends VisaDevice {

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public String getDeviceType() {
        return null;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return true;
    }
}
