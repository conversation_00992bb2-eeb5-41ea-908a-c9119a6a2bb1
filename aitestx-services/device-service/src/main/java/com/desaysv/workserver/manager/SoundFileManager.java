package com.desaysv.workserver.manager;

import com.desaysv.workserver.filemanager.project.ProjectFileManager;

import java.io.File;

public class SoundFileManager extends ProjectFileManager {
    private File soundFileFolder;
    private File failSoundFileFolder;

    public SoundFileManager(String projectName) {
        super(projectName);
    }
    @Override
    protected void initSubPaths(String dynamicFolderName) {
        soundFileFolder = createFolder(fileDbPath, "sounds");
        failSoundFileFolder = createFolder(soundFileFolder, "failSounds");
    }
    public String getFailSoundFileFolderPath() {
        return failSoundFileFolder.getAbsolutePath();
    }
    public String getSoundFileFolderPath() {
        return soundFileFolder.getAbsolutePath();
    }
}
