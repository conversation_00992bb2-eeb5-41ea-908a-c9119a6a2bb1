package com.desaysv.workserver.devices.serial;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy;
import ch.qos.logback.core.rolling.TimeBasedRollingPolicy;
import ch.qos.logback.core.util.FileSize;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.MonitorType;
import com.desaysv.workserver.WebSocketServer;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.CheckPointInfo;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.serial.manager.SerialPortEventManager;
import com.desaysv.workserver.devices.serial.tcpserver.TCPSetting;
import com.desaysv.workserver.devices.serial.text_match.*;
import com.desaysv.workserver.devices.tcpserver.TCPServerControl;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.manager.SerialLogFileManager;
import com.desaysv.workserver.monitor.data.DataDistributor;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.monitor.data.MonitorAction;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.screen.ScreenService;
import com.desaysv.workserver.screen.TouchPointMonitor;
import com.desaysv.workserver.screen.distributor.EventBasedTouchPointDistributor;
import com.desaysv.workserver.screen.result.report.TouchReport;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.StrUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 11:48
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
@Slf4j
public class SerialPortDevice extends SerialDevice implements TestProcessListener {
    private final static int TIMEOUT_MILLISECONDS = 2000;
    private final static int LOGIC_SERVER_PORT = 10429;
    private final Map<String, SerialMonitorContext> monitorContexts = new HashMap<>();

    private final SerialRecvQueueManager serialRecvQueueManager;

    private EventBasedTouchPointDistributor eventBasedTouchPointDistributor;

    private DeviceDataDispatcher<TouchReport> touchPointReportDispatcher;

    @JSONField(serialize = false)
    private DeviceDataDispatcher<SerialReceiveMessage> byteDataSender;

    @JSONField(serialize = false)
    private DeviceDataDispatcher<List<PointInt>> touchPointDeviceDataDispatcher;

    @JSONField(serialize = false)
    private DataDistributor<byte[]> receiveDataDistributor;

    @Getter
    private final String deviceModel = DeviceModel.Serial.PORT_SERIAL;

    @JSONField(serialize = false)
    private UsbSerialPortEventListener serialPortEventListener;

    @JSONField(serialize = false)
    private LogMatchMonitor logMatchMonitor;

    @JSONField(serialize = false)
    private TouchPointMonitor touchPointMonitor;
    private SaleaeLogicClient saleaeLogicClient;
    private TCPServerControl tcpServer;
    private Logger serialLogger;
    private String scriptName;
    private static final int EQUAL = 0;
    private static final int GREATER = 1;
    private static final int LESS = 2;
    private static final int GREATER_EQUAL = 3;
    private static final int LESS_EQUAL = 4;

    private transient SerialPortEventManager serialPortEventManager;


    public SerialPortDevice() {
        this(new DeviceOperationParameter());
    }

    public SerialPortDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        serialRecvQueueManager = new SerialRecvQueueManager();
        try {
            this.serialPortEventManager = SerialPortEventManager.getInstance();
        } catch (Exception e) {
            log.error("Failed to obtain SerialPortEventManager. Serial port open/close events might not be published for device: {}", getDeviceName(), e);
            this.serialPortEventManager = null;
        }
    }

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(getDeviceName(), this);
            config();
            if (this.serialPortEventManager != null) {
                this.serialPortEventManager.notifyPortOpened(this);
            }
        }
    }

    private void config() {
        SerialConfig serialConfig = getDeviceConfig();
        if (serialConfig != null) {
            ExecutorService executorService = Executors.newSingleThreadExecutor();
            for (SerialMessage serialMessage : serialConfig.getTextMapSendWhenOpen().values()) {
                if (serialMessage.isValid()) {
                    executorService.execute(() -> {
                        try {
                            log.info("初始化串口后自动发送:{}", serialMessage.getText());
                            send(serialMessage.getText(), serialMessage.isHex());
                        } catch (DeviceSendException e) {
                            log.warn(e.getMessage(), e);
                        }
                    });

                }
            }
            executorService.shutdown();
        }
//        if (serialConfig.isEnableTouchDataTransfer()) {
//            ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
//            MonitorAction monitorAction = MonitorAction.of(getDeviceAliasName(), MonitorType.TOUCH_POINT_DATA);
//            startKeepAliveTouchPointMonitor(screenService, monitorAction);
//        }
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        if (logMatchMonitor != null) {
            logMatchMonitor.stopAll();
            logMatchMonitor = null;
        }
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        boolean isOpened = super.open();
        if (isOpened) {
            initializeSerialLogger();
            serialPortEventListener = new UsbSerialPortEventListener(getSerialPort());
            serialPortEventListener.setDataListener(byteArray -> outputLog(byteArray, false, getAliasName()));
            if (!isSimulated()) {
                try {
                    SerialUtils.addListener(getSerialPort(), serialPortEventListener);
                } catch (SerialExceptions.TooManyListeners | SerialExceptions.UnsupportedCommOperationException e) {
                    throw new DeviceOpenException("Failed to add serial port listener: " + e.getMessage(), e);
                }
            }
        }
        return isOpened;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        boolean closedSuccessfully = false;
        try {
            if (byteDataSender != null) {
                byteDataSender.stopAll();
                byteDataSender = null;
            }
            if (touchPointDeviceDataDispatcher != null) {
                touchPointDeviceDataDispatcher.stopAll();
                touchPointDeviceDataDispatcher = null;
            }
            if (receiveDataDistributor != null) {
                receiveDataDistributor.stop();
                receiveDataDistributor = null;
            }
            if (serialPortEventListener != null) {
                serialPortEventListener.clear();
                serialPortEventListener = null;
            }
            TestProcessManager.removeTestProcessListener(getDeviceName());

            closedSuccessfully = super.close();

        } finally {
            if (closedSuccessfully && this.serialPortEventManager != null) {
                this.serialPortEventManager.notifyPortClosed(this);
            }
        }
        return closedSuccessfully;
    }

    @Override
    public boolean send(byte[] message, boolean terminator) throws DeviceSendException {
        outputLog(message, true, getAliasName());
        return super.send(message, terminator);
    }

    @Override
    public void sendCompleted(byte[] message) {
        if (isSimulated() && serialPortEventListener != null) {
            serialPortEventListener.selfSendAndReceive(message);
        }
    }

    /**
     * 初始化接收数据分发器
     */
    private void initReceiveDataDistributor() {
        if (receiveDataDistributor == null) {
            receiveDataDistributor = new DataDistributor<>();
            serialPortEventListener.addByteDistributor(receiveDataDistributor);
            receiveDataDistributor.start();
        }
    }

    @Override
    public boolean sendAndMatch(MessageText messageText) throws OperationFailNotification {
        initReceiveDataDistributor();
        boolean matched = false;
        StringBuilder receiveString = new StringBuilder();
        Consumer<byte[]> consumer = receiveDataDistributor.addConsumer(bytes -> {
            if (messageText.isHex()) {
                receiveString.append(StrUtils.getHexStringWithBlank(bytes)).append(" ");
            } else {
                receiveString.append(new String(bytes));
            }
            log.info("发送后接收到返回值:{}", receiveString);
        });
        for (int loop = 0; loop <= messageText.getTimeoutRetry(); loop++) {
            receiveString.setLength(0);
            OperationResult sendResult = send(messageText);
            if (sendResult.isFailed()) {
                //发送失败，继续重试
                continue;
            }
            long startMills = System.currentTimeMillis();
            try {
                while ((System.currentTimeMillis() - startMills) / 1000.0 <= messageText.getReceiveTimeout()) {
                    matched = receiveString.toString().trim().contains(messageText.getMatchText().trim());
                    try {
                        TimeUnit.MILLISECONDS.sleep(500);
                    } catch (InterruptedException e) {
                        break;
                    }
                    if (matched) {
                        break;
                    }
                }
            } finally {
                receiveDataDistributor.removeConsumer(consumer);
            }
            log.info("捕获到字符串:{}", receiveString);
            if (matched) {
                //匹配成功，退出循环
                break;
            }
        }
        if (!matched) {
            throw new OperationFailNotification(String.format("匹配字符串%s失败，实际字符串是:%s", messageText.getMatchText(), receiveString));
        }
        return true;
    }

    @Override
    public boolean sendByInterpretation(String name, String checkedContext) {
        String command = getCommandByInterpretation(name);
        if (command == null || command.isEmpty()) {
            log.error("未找到对应名字命令");
            return false;
        }
        try {
            return sendByActionSequence(command, StrUtils.isHexadecimal(command), checkedContext);
        } catch (DeviceSendException e) {
            log.error("发送命令失败", e);
        }
        return false;
    }

    public String getCommandByInterpretation(String name) {
        try {
            String jsonFilePath = getDeviceConfig().getCommandsConfigPath();
            String content = new String(Files.readAllBytes(Paths.get(jsonFilePath)));
            JSONArray jsonArray = JSON.parseArray(content);

            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                String interpretation = jsonObject.getString("commandInterpretation");

                if (name.equals(interpretation)) {
                    return jsonObject.getString("command");
                }
            }
        } catch (IOException e) {
            log.error("读取文件时出错: " + e.getMessage());
        } catch (Exception e) {
            log.error("解析 JSON 时出错: " + e.getMessage());
        }
        return null;
    }

    /**
     * 发送并匹配返回数据
     *
     * @param messageText 待发送的消息
     * @return 返回匹配到的字符串, 若未匹配到则返回接收到的数据原始值
     * @throws OperationFailNotification
     */
    public String sendAndMatchReturnData(MessageText messageText) throws OperationFailNotification {
        initReceiveDataDistributor();
        final String matchTarget = messageText.getMatchText().trim();
        final StringBuilder receiveBuffer = new StringBuilder();
        final AtomicBoolean matched = new AtomicBoolean(false);
        final AtomicReference<String> matchedPart = new AtomicReference<>(null);

        // 使用弱引用避免内存泄漏
        final AtomicReference<Consumer<byte[]>> consumerRef = new AtomicReference<>();

        try {
            // 注册数据消费者（线程安全）
            consumerRef.set(receiveDataDistributor.addConsumer(bytes -> {
                String newData;
                if (messageText.isHex()) {
                    newData = StrUtils.getHexStringWithBlank(bytes) + " ";
                } else {
                    newData = new String(bytes, StandardCharsets.UTF_8);
                }

                // 同步块保证线程安全
                synchronized (receiveBuffer) {
                    receiveBuffer.append(newData);

                    // 仅在有新数据时尝试匹配
                    if (!matchTarget.isEmpty()) {
                        String currentData = receiveBuffer.toString().trim();
                        int index = currentData.indexOf(matchTarget);
                        if (index != -1) {
                            matched.set(true);
                            // 提取匹配部分（包含完整匹配字符串）
                            int endIndex = index + matchTarget.length();
                            matchedPart.set(currentData.substring(index, endIndex));
                        }
                    }
                }
            }));

            for (int attempt = 0; attempt <= messageText.getTimeoutRetry(); attempt++) {
                // 重置状态
                synchronized (receiveBuffer) {
                    receiveBuffer.setLength(0);
                }
                matched.set(false);
                matchedPart.set(null);

                // 发送命令
                OperationResult sendResult = send(messageText);
                if (sendResult.isFailed()) {
                    log.warn("尝试{}/{}：发送失败", attempt, messageText.getTimeoutRetry());
                    continue;
                }

                // 等待结果
                long deadline = System.currentTimeMillis() + (long) (messageText.getReceiveTimeout() * 1000);
                while (System.currentTimeMillis() < deadline) {
                    // 检查匹配状态
                    if (matched.get()) {
                        log.info("匹配成功，返回数据:{}", matchedPart.get());
                        return matchedPart.get();
                    }

                    // 动态休眠时间（50-200ms）
                    long remaining = deadline - System.currentTimeMillis();
                    try {
                        TimeUnit.MILLISECONDS.sleep(Math.max(50, Math.min(remaining, 200)));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("匹配被中断");
                        break;
                    }
                }

                // 检查最后一次（可能发生在sleep后）
                synchronized (receiveBuffer) {
                    String finalData = receiveBuffer.toString().trim();
                    if (matchTarget.isEmpty()) {
                        log.info("没有数据需要匹配，返回所有接收的数据");
                        return finalData;
                    }

                    if (finalData.contains(matchTarget)) {
                        int index = finalData.indexOf(matchTarget);
                        return finalData.substring(index, index + matchTarget.length());
                    }
                }

                log.debug("尝试{}超时, 重试中...", attempt);
            }

            // 最终结果处理
            synchronized (receiveBuffer) {
                String finalData = receiveBuffer.toString().trim();
                if (matchTarget.isEmpty()) {
                    return finalData;
                }

                if (finalData.isEmpty()) {
                    throw new OperationFailNotification("重试"
                            + messageText.getTimeoutRetry() + "后，未接收到数据。");
                }

                throw new OperationFailNotification("未匹配字到目标符串. 接收到的所有数据为: ["
                        + finalData + "], 目标符串: [" + matchTarget + "]");
            }

        } finally {
            // 清理消费者
            if (consumerRef.get() != null) {
                receiveDataDistributor.removeConsumer(consumerRef.get());
            }
        }
    }


    /**
     * 发送命令并返回结果
     *
     * @param messageText
     * @return boolean 返回是否成功
     * @throws OperationFailNotification
     */
    public boolean sendReturnResult(MessageText messageText) throws OperationFailNotification {
        for (int attempt = 0; attempt <= messageText.getTimeoutRetry(); attempt++) {
            // 发送命令
            OperationResult sendResult = send(messageText);
            if (!sendResult.isFailed()) {
                log.info("命令发送成功（尝试 {}/{}）", attempt, messageText.getTimeoutRetry());
                return true;
            }
            log.warn("尝试 {}/{}：发送失败", attempt, messageText.getTimeoutRetry());
        }
        throw new OperationFailNotification("重试" + messageText.getTimeoutRetry() + "次后，发送命令失败");
    }


    // LOG监控
    @JSONField(serialize = false)
    private LogMatchDistributor getLogMonitor(String monitorType, String monitorText) {
        if (logMatchMonitor == null) {
            logMatchMonitor = new LogMatchMonitor(serialPortEventListener);
        }
        return logMatchMonitor.getLogMatchDistributor(monitorType, monitorText);
    }


    @Override
    public boolean mustExistMonitorStart(String text) {
        text = text.trim();
        log.info("日志必须出现关键字检测开始:{}", text);
        outputLog(MUST_EXIST_MONITOR_START.getBytes(), true, getAliasName());
        LogMatchDistributor logMatchDistributor = getLogMonitor(LogMatchMonitor.MUST_EXIST, text);
        logMatchDistributor.startMatch(text);
        monitorContexts.put(LogMatchMonitor.MUST_EXIST, new SerialMonitorContext(text, logMatchDistributor));
        return true;
    }

    @Override
    public OperationResult mustExistMonitorEnd() {
        log.info("日志必须出现关键字检测结束");
        outputLog(MUST_EXIST_MONITOR_END.getBytes(), true, getAliasName());
        SerialMonitorContext context = monitorContexts.get(LogMatchMonitor.MUST_EXIST);
        if (context == null) {
            return new OperationResult().fail("未找到MUST_EXIST数据");
        }
        LogMatchDistributor logMatchDistributor = context.getLogMatchDistributor();
        logMatchDistributor.pauseDataConsumer();
        List<String> matchingTexts = logMatchDistributor.getMatchingText();
        boolean matched = !matchingTexts.isEmpty();
        OperationResult operationResult = new OperationResult();
        if (matched) {
            log.info("测试通过，匹配到文本:{}", context.getText());
            operationResult.ok(String.format("测试通过，匹配到文本:%s", context.getText()));
        } else {
            log.warn("测试失败，未匹配到文本:{}", context.getText());
            operationResult.fail(String.format("测试失败，未匹配到文本:%s", context.getText()));
        }
        monitorContexts.remove(LogMatchMonitor.MUST_EXIST);
        return operationResult;
    }

    @Override
    public boolean forbidExistMonitorStart(String text) {
        text = text.trim();
        log.info("日志禁止出现关键字检测开始:{}", text);
        outputLog(FORBID_EXIST_MONITOR_START.getBytes(), true, getAliasName());
        LogMatchDistributor logMatchDistributor = getLogMonitor(LogMatchMonitor.FORBID_EXIST, text);
        logMatchDistributor.startMatch(text);
        monitorContexts.put(LogMatchMonitor.FORBID_EXIST, new SerialMonitorContext(text, logMatchDistributor));
        return true;
    }

    @Override
    public OperationResult forbidExistMonitorEnd() {
        log.info("日志禁止出现关键字检测结束");
        outputLog(FORBID_EXIST_MONITOR_END.getBytes(), true, getAliasName());
        SerialMonitorContext context = monitorContexts.get(LogMatchMonitor.FORBID_EXIST);
        if (context == null) {
            return new OperationResult().fail("未找到FORBID_EXIST的数据");
        }
        OperationResult operationResult = new OperationResult();
        LogMatchDistributor logMatchDistributor = context.getLogMatchDistributor();
        logMatchDistributor.pauseDataConsumer();
        List<String> matchingTexts = logMatchDistributor.getMatchingText();
        boolean matched = !matchingTexts.isEmpty();

        if (matched) {
            log.warn("测试失败，匹配到文本:{}", context.getText());
            operationResult.fail(String.format("测试失败，匹配到文本:%s", context.getText()));
        } else {
            log.info("测试通过，未匹配到文本:{}", context.getText());
            operationResult.ok(String.format("测试通过，未匹配到文本:%s", context.getText()));
        }
        monitorContexts.remove(LogMatchMonitor.FORBID_EXIST);
        return operationResult;
    }

    @Override
    public boolean waitFilter(WaitFilterParameter waitFilterParameter) throws OperationFailNotification {
        String text = waitFilterParameter.getText().trim();
        int timeout = waitFilterParameter.getTimeout() * 1000;
        log.info("日志等待过滤关键字:{}，超时:{}s", text, waitFilterParameter.getTimeout());
        outputLog(WAIT_FILTER.getBytes(), true, getAliasName());
        initReceiveDataDistributor();
        StringBuilder receiveString = new StringBuilder();
        boolean matched = false;

        Consumer<byte[]> consumer = receiveDataDistributor.addConsumer(bytes -> {
            String receivedText = new String(bytes);
            receiveString.append(receivedText);
        });
        try {
            long startTime = System.currentTimeMillis();
            while ((System.currentTimeMillis() - startTime) <= timeout) {
                if (receiveString.toString().contains(text)) {
                    //包含text
                    matched = true;
                    break;
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        } finally {
            receiveDataDistributor.removeConsumer(consumer);
        }
        if (!matched) {
            throw new OperationFailNotification(
                    String.format("等待过滤超时:%ds，未找到文本: %s", waitFilterParameter.getTimeout(), text)
            );
        }
        return matched;
    }

    @Override
    public boolean judgeText(SerialJudgeParameter serialJudgeParameter) throws OperationFailNotification {
        double target = serialJudgeParameter.getContent();
        int timeout = serialJudgeParameter.getTimeout() * 1000;
        int operator = serialJudgeParameter.getOperator();
        log.info("判断大小: {}, 超时: {}s", target, serialJudgeParameter.getTimeout());
        outputLog(WAIT_FILTER.getBytes(), true, getAliasName());
        initReceiveDataDistributor();
        StringBuilder receiveString = new StringBuilder();
        Consumer<byte[]> consumer = receiveDataDistributor.addConsumer(bytes ->
                receiveString.append(new String(bytes).trim())
        );

        try {
            long startTime = System.currentTimeMillis();
            while (System.currentTimeMillis() - startTime <= timeout) {
                String receivedText = receiveString.toString().trim();
                // 检查是否为有效数字或十六进制字符串
                if (isValidNumber(receivedText)) {
                    Number receivedNumber = convertToNumber(receivedText);
                    // 根据操作符进行比较
                    if (compareNumbers(receivedNumber.doubleValue(), target, operator)) {
                        return true;
                    } else {
                        log.warn("判断失败，接收到的数字 {} 不满足条件与目标数字 {}", receivedNumber, target);
                    }
                }
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        } finally {
            receiveDataDistributor.removeConsumer(consumer);
        }
        // 如果超时仍未匹配成功，抛出异常
        throw new OperationFailNotification(
                String.format("超时 %ds，未找到符合条件的数字", serialJudgeParameter.getTimeout())
        );
    }

    /**
     * 判断字符串是否是有效的数字（包括整数、小数和十六进制）
     *
     * @param text 输入字符串
     * @return 如果是有效数字返回 true，否则返回 false
     */
    private boolean isValidNumber(String text) {
        return isNumber(text) || isHex(text);
    }

    /**
     * 将字符串转换为数字（支持整数、小数和十六进制）
     *
     * @param text 输入字符串
     * @return 转换后的数字
     * @throws NumberFormatException 如果字符串无法转换为数字
     */
    private Number convertToNumber(String text) {
        if (isNumber(text)) {
            return Double.parseDouble(text);
        } else if (isHex(text)) {
            return Integer.parseInt(text, 16);
        } else {
            throw new NumberFormatException("无法将字符串转换为数字: " + text);
        }
    }

    /**
     * 根据操作符比较两个数字
     *
     * @param receivedNumber 接收到的数字
     * @param target         目标数字
     * @param operator       操作符（EQUAL, GREATER, LESS, GREATER_EQUAL, LESS_EQUAL）
     * @return 如果满足条件返回 true，否则返回 false
     */
    private boolean compareNumbers(double receivedNumber, double target, int operator) {
        switch (operator) {
            case EQUAL:
                return receivedNumber == target;
            case GREATER:
                return receivedNumber > target;
            case LESS:
                return receivedNumber < target;
            case GREATER_EQUAL:
                return receivedNumber >= target;
            case LESS_EQUAL:
                return receivedNumber <= target;
            default:
                throw new IllegalArgumentException("未知的操作符: " + operator);
        }
    }


    // webSocket传输log

    /**
     * 判断字符串是否是数字（包括整数和小数）
     *
     * @param text 输入字符串
     * @return 如果是数字返回 true，否则返回 false
     */
    private boolean isNumber(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        String numberRegex = "^-?\\d+(\\.\\d+)?$";
        return text.matches(numberRegex);
    }

    /**
     * 判断字符串是否是十六进制格式
     *
     * @param text 输入字符串
     * @return 如果是十六进制返回 true，否则返回 false
     */
    private boolean isHex(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        String hexRegex = "^[0-9a-fA-F]+$";
        return text.matches(hexRegex);
    }

    /**
     * 基于事件触发的报点监控
     *
     * @param screenService 显示屏服务
     * @return EventBasedTouchPointDistributor
     */
    @JSONField(serialize = false)
    public EventBasedTouchPointDistributor getEventBasedTouchPointDistributor(ScreenService screenService) {
        if (touchPointMonitor == null) {
            touchPointMonitor = new TouchPointMonitor(serialPortEventListener);
        }
        return touchPointMonitor.getEventBasedTouchPointDistributor(screenService);
    }

    //TODO： 以下监控事务后续移动到单独类管理

    public void outputLog(byte[] byteArray, boolean isSend, String deviceAliasName) {
        // TODO: 改为通用Distributor
        String hexString = StrUtils.getHexStringWithBlank(byteArray);
        String logMessage = String.format("串口 %s -> 字节表示: %s", isSend ? "发送" : "接收", hexString);
        if (serialLogger != null) {
            if (getDeviceConfig().isSendOrReceiveWithHex()) {
                serialLogger.debug(logMessage);
            } else {
                String ascii = StrUtils.hexToAscii(hexString);
                serialLogger.debug(ascii);
            }
        }
        if (byteDataSender != null) {
            byteDataSender.product(MonitorType.LOG_DATA, new SerialReceiveMessage(System.currentTimeMillis(), isSend, byteArray, deviceAliasName));
        }
    }

    /**
     * 监控串口LOG
     */
    public void monitorByteLog(MonitorAction monitorAction) {
        //初始化分发程序
        if (byteDataSender == null) {
            byteDataSender = new DeviceDataDispatcher<>();
            pushLogToWebsocket(monitorAction);
        }
    }

    /**
     * 推送实时串口LOG到websocket
     */
    private void pushLogToWebsocket(MonitorAction monitorAction) {
        DataDistributor<SerialReceiveMessage> dataDistributor = new DataDistributor<>(data -> {
            List<WebSocketServer> webSocketServers = WebSocketServer.getWebsocketServers().get(MonitorType.LOG_DATA);
            if (webSocketServers != null) {
                for (WebSocketServer webSocketServer : webSocketServers) {
                    //FIXME: socketTimeout
                    webSocketServer.sendMessageByObject(data);
                }
            }
        });
        byteDataSender.start(monitorAction, dataDistributor);
    }


    /**
     * 实时报点监控
     *
     * @param screenService 显示屏服务
     */
    public void startKeepAliveTouchPointMonitor(ScreenService screenService, MonitorAction monitorAction) {
        if (touchPointDeviceDataDispatcher == null) {
            if (touchPointMonitor == null) {
                //初始化分发程序
                touchPointMonitor = new TouchPointMonitor(serialPortEventListener);
            }
            touchPointDeviceDataDispatcher = new DeviceDataDispatcher<>();
            touchPointMonitor.getKeepAliveTouchPointDistributor(screenService, touchPointDeviceDataDispatcher);
            pushTouchPointsToWebsocket(monitorAction);
        }
    }

    /**
     * 推送实时报点到websocket
     */
    private void pushTouchPointsToWebsocket(MonitorAction monitorAction) {
        DataDistributor<List<PointInt>> dataDistributor = new DataDistributor<>(data -> {
            List<WebSocketServer> webSocketServers = WebSocketServer.getWebsocketServers().get(MonitorType.TOUCH_POINT_DATA);
            if (webSocketServers != null) {
                for (WebSocketServer webSocketServer : webSocketServers) {
                    webSocketServer.sendMessageByObject(data);
                }
            }
        });
        touchPointDeviceDataDispatcher.start(monitorAction, dataDistributor);
    }

    public OperationResult pointCheckStart(CheckPointInfo checkPointInfo) {
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        Device device = deviceFinderManager.findDeviceByAliasName(checkPointInfo.getScreenConfig().getSerialAliasName());
        if (device != null) {
            ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);
            eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
            if (checkPointInfo.isEnableTrace()) {
                traceCheckStart();
            }
            operationResult.ok("开始报点检查成功");
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }

    public OperationResult pointCheckStop(CheckPointInfo checkPointInfo) {
        OperationResult operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(checkPointInfo);
        productTouchReportToDispatcher(operationResult);
        if (checkPointInfo.isEnableTrace()) {
            traceCheck(checkPointInfo, operationResult);
        }
        return operationResult;
    }

    private void traceCheck(CheckPointInfo checkPointInfo, OperationResult operationResult) {
        int traceResult = judgeTrace(checkPointInfo.getTouchType(), traceCheckStop());
        if (traceResult == 0) {
            operationResult.ok();
        } else {
            operationResult.fail();
            String message = traceResult == 1
                    ? "trace中出现多个[" + checkPointInfo.getTouchType() + "]事件"
                    : "trace中未出现[" + checkPointInfo.getTouchType() + "]事件";
            operationResult.setMessage(message);
        }
    }

    private void productTouchReportToDispatcher(OperationResult operationResult) {
        if (touchPointReportDispatcher == null) {
            return;
        }
        TouchReport touchReport = (TouchReport) operationResult.getData();
        if (touchReport == null) {
            touchReport = new TouchReport();
            touchReport.setOk(false);
            touchReport.setTouchType(TouchReport.TouchType.UNDEFINED);
        }
        touchPointReportDispatcher.product(MonitorType.TOUCH_REPORT, touchReport);
    }

    public void traceCheckStart() {
        if (tcpServer.isServerRunning()) {
            tcpServer.startCollectingData();
        }
    }

    public List<String> traceCheckStop() {
        if (tcpServer.isServerRunning()) {
            tcpServer.stopCollectingData();
        }
        return tcpServer.getCollectedData();
    }

    public int judgeTrace(String touchType, List<String> touchTypeList) {
        String eventStr = touchType.toLowerCase();
        int count = 0;
        for (String item : touchTypeList) {
            if (item.toLowerCase().contains(eventStr)) {
                count++;
            }
        }
        return Integer.compare(count, 1);
    }


    public boolean buildTcpServer(TCPSetting tcpSetting) throws OperationFailNotification {
        String address = tcpSetting.getServerIp();
        int port = tcpSetting.getServerPort();
        tcpServer = TCPServerControl.newInstance(address, port);
        tcpServer.startServer();
        if (tcpServer.isServerRunning()) {
            tcpServer.receiveData();
            return true;
        } else {
            throw new OperationFailNotification("TCP服务启动失败");
        }
    }


    public boolean stopTcpServer(TCPSetting tcpSetting) {
        tcpServer.stopServer();
        return true;
    }


    public boolean logicStartCollection() throws OperationFailNotification {
        try {
            saleaeLogicClient = new SaleaeLogicClient("localhost", LOGIC_SERVER_PORT);
            String response = saleaeLogicClient.sendCommand("START");
            if (response.equals("ACK")) {
                return true;
            } else {
                saleaeLogicClient.close();
                throw new OperationFailNotification("开始失败,请设备检查连接");
            }
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
    }


    public boolean logicStopCollection(String filePath) throws OperationFailNotification {
        try {
            String outputFilePath = "path/to/output/" + StrUtils.getTimeStamp() + ".sal";
            String response = saleaeLogicClient.sendCommand("STOP");
            if (response.equals("ACK")) {
                response = saleaeLogicClient.sendCommand("EXPORT_DATA2, " + outputFilePath + ", logicdata, binary, 1000000000, 0");
                if (response.equals("ACK")) {
                    saleaeLogicClient.close();
                    return true;
                }
            } else {
                saleaeLogicClient.close();
                throw new OperationFailNotification("停止失败请设备检查连接");
            }
            return false;
        } catch (IOException e) {
            throw new OperationFailNotification(e);
        }
    }

    private void initializeSerialLogger() {
        SerialConfig serialConfig = getDeviceConfig();
        if (serialConfig == null || !serialConfig.isSaveLogFlag()) {
            return; // 如果日志开关关闭，直接返回，不初始化日志记录器
        }

        // 清理旧的 Appender，防止重复添加
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        context.start();
        serialLogger = context.getLogger("SERIAL_LOGGER");
        if (serialLogger.getAppender("ROLLING_FILE_APPENDER") != null) {
            return;
        }

        // 准备日志环境
        File logFolder = null;
        if (serialConfig.isUseCustomLogPath() && !serialConfig.getCustomLogPath().isEmpty()) {
            logFolder = new File(serialConfig.getCustomLogPath()); // 使用自定义日志路径
        } else {
            logFolder = SerialLogFileManager.of(SerialLogFileManager.class).getFolder(); // 默认路径
        }

        // 设置日志格式
        PatternLayoutEncoder encoder = new PatternLayoutEncoder();
        encoder.setContext(context);
        encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n");
        encoder.start();

        RollingFileAppender<ILoggingEvent> rollingFileAppender = new RollingFileAppender<>();
        rollingFileAppender.setName("ROLLING_FILE_APPENDER");
        rollingFileAppender.setContext(context);

        // 根据 logSaveType 设置日志保存策略
        if (serialConfig.getLogSaveType() == 0) {
            //按时间保存日志文件
            TimeBasedRollingPolicy<ILoggingEvent> rollingPolicy = new TimeBasedRollingPolicy<>();
            rollingPolicy.setContext(context);
            rollingPolicy.setParent(rollingFileAppender);
            String timePattern = serialConfig.getLogSaveTimePeriod().equals("hour") ? "yyyy-MM-dd-HH" : "yyyy-MM-dd";
            rollingPolicy.setFileNamePattern(logFolder.getAbsolutePath() + File.separator + "serial.%d{" + timePattern + "}.txt");
            rollingPolicy.setMaxHistory(100);
            rollingPolicy.start();
            rollingFileAppender.setRollingPolicy(rollingPolicy);
        } else if (serialConfig.getLogSaveType() == 1) {
            // 按日期和文件大小保存日志文件
            SizeAndTimeBasedRollingPolicy<ILoggingEvent> sizeAndTimeBasedRollingPolicy = new SizeAndTimeBasedRollingPolicy<>();
            sizeAndTimeBasedRollingPolicy.setContext(context);
            sizeAndTimeBasedRollingPolicy.setParent(rollingFileAppender);
            sizeAndTimeBasedRollingPolicy.setFileNamePattern(logFolder.getAbsolutePath() + File.separator + "serial.%d{yyyy-MM-dd}.%i.txt");
            sizeAndTimeBasedRollingPolicy.setMaxFileSize(FileSize.valueOf("10MB"));
            sizeAndTimeBasedRollingPolicy.setMaxHistory(100);
            sizeAndTimeBasedRollingPolicy.start();
            rollingFileAppender.setRollingPolicy(sizeAndTimeBasedRollingPolicy);
        } else if (serialConfig.getLogSaveType() == 2) {
            // 按脚本名称和文件大小保存日志文件
            SizeAndTimeBasedRollingPolicy<ILoggingEvent> sizeAndTimeBasedRollingPolicy = new SizeAndTimeBasedRollingPolicy<>();
            sizeAndTimeBasedRollingPolicy.setContext(context);
            sizeAndTimeBasedRollingPolicy.setParent(rollingFileAppender);
            sizeAndTimeBasedRollingPolicy.setFileNamePattern(logFolder.getAbsolutePath() + File.separator + scriptName + "serial.%d{yyyy-MM-dd}.%i.txt");
            sizeAndTimeBasedRollingPolicy.setMaxFileSize(FileSize.valueOf("10MB"));
            sizeAndTimeBasedRollingPolicy.setMaxHistory(100);
            sizeAndTimeBasedRollingPolicy.start();
            rollingFileAppender.setRollingPolicy(sizeAndTimeBasedRollingPolicy);
        }
        rollingFileAppender.setEncoder(encoder);
        rollingFileAppender.start();
        serialLogger.addAppender(rollingFileAppender);
    }

    @Override
    public void testcaseStart(ExecutionContext executionContext) {
        if (getDeviceConfig().isSaveLogFlag() && executionContext.getOperationContext() != null) {
            scriptName = executionContext.getOperationContext().getCaseName();
            initializeSerialLogger();
        }
    }

    @Override
    public void testCaseComplete(ExecutionContext executionContext, boolean isFailed) {
        if (serialLogger != null) {
            ((LoggerContext) LoggerFactory.getILoggerFactory()).stop();
            serialLogger = null;
        }
    }

    @Override
    public void updateConfig(SerialConfig deviceConfig) {
        super.updateConfig(deviceConfig);
        SerialConfig serialConfig = getDeviceConfig();
        if (serialConfig == null) {
            return;
        }

        if (serialConfig.isSaveLogFlag()) {
            initializeSerialLogger(); // 开启日志
        } else {
            if (serialLogger != null) {
                ((LoggerContext) LoggerFactory.getILoggerFactory()).stop(); // 停止日志
                serialLogger = null;
            }
        }
    }

    private String extractReceiveMessage(String input) {
        //优化规则是因为之前的太固化了，例如1.serial#1-Receive-5B F5 EB F5 BF FF DF FF BB EF EB等语法，matcher.find()失败，
        // 导致接收串口数据判定失败的问题
//        String regex = "Serial-Receive-(.*)";
        String regex = "^(?:\\d+[a-zA-Z]?\\d*\\.)?(?i)Serial(?:#\\d+)?-Receive-(.*?)(?:-|$)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1).trim(); // 返回第一个捕获组并去除前后空格
        }
        return "";
    }

    @Override
    public boolean sendByActionSequence(String message, boolean isHex, String checkedContext) throws DeviceSendException {
        CompletableFuture.runAsync(() -> {
            if (StrUtils.containsIgnoreCase(checkedContext, BaseRegexRule.SERIAL_RX_CONSTANT)) {
                //先清空缓存队列
                serialRecvQueueManager.clear();
                String compareMessage = extractReceiveMessage(checkedContext);
                //需要检查串口接收
                log.info("实时读取待匹配接收值:{}，超时时间:{}ms",
                        compareMessage, TIMEOUT_MILLISECONDS);
                String feedback = readPortFeedback(isHex, compareMessage, TIMEOUT_MILLISECONDS);
                serialRecvQueueManager.put(compareMessage, feedback);
            }
        });
        return super.send(message, isHex);
    }

    @Override
    public String readPortFeedback(boolean isHex, String compareMessage, int timeoutMilliseconds) {
        String feedback;
        if (getDeviceConfig().getReceiveTimeout() != 0) {
            timeoutMilliseconds = (int) (getDeviceConfig().getReceiveTimeout() * 1000);
        } else {
            timeoutMilliseconds = TIMEOUT_MILLISECONDS;
        }
        try {
            if (serialRecvQueueManager.contains(compareMessage)) {
                //包含缓存队列中
                feedback = serialRecvQueueManager.get(compareMessage);
                log.info("串口已经缓存待匹配待判断值:{}，实际反馈是：{}", compareMessage, feedback);
            } else {
                initReceiveDataDistributor();
                StringBuilder receiveString = new StringBuilder();
                Consumer<byte[]> consumer = receiveDataDistributor.addConsumer(bytes -> {
                    if (isHex) {
                        receiveString.append(StrUtils.getHexStringWithBlank(bytes)).append(" ");
                    } else {
                        receiveString.append(new String(bytes).trim());
                    }
                });
                long startMills = System.currentTimeMillis();
                try {
                    while (System.currentTimeMillis() - startMills <= timeoutMilliseconds) {
                        if (!receiveString.toString().trim().isEmpty()) {
                            break;
                        }
                        try {
                            TimeUnit.MILLISECONDS.sleep(500);
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                } finally {
                    receiveDataDistributor.removeConsumer(consumer);
                }
                feedback = receiveString.toString().trim();
                log.info("接收到串口返回值:{}", feedback);
            }
        } finally {
            //清空缓冲队列
            serialRecvQueueManager.clear();
        }
        return feedback;
    }


    public static void main(String[] args) {
        String findText = "/storage/emulated";
        String line = "/data/media             5954664 3200428   1689276  66% /storage/emulated";
        Matcher result = Pattern.compile(findText).matcher(line);
        System.out.println(result.find());
    }

}
