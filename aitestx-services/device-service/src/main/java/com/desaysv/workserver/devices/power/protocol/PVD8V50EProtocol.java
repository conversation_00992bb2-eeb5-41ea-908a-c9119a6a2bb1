package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.devices.power.base.PowerDevice;


public class PVD8V50EProtocol extends PowerSCPIProtocol {


    public PVD8V50EProtocol(PowerDevice device) {
        super(device);
    }

    @Override
    public boolean selectChannel(Integer channel) {
        if (channel != null) {
            return sendCommand(ScpiCommandConstants.selectChannel(channel, true));
        }
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand((isOutput ? ScpiCommandConstants.OUTPUT_ON : ScpiCommandConstants.OUTPUT_OFF) + ScpiCommandConstants.END);
    }

    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        voltage = Float.parseFloat(String.format("%.2f", voltage));
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_VOLTAGE_DC + " " + voltage) + ScpiCommandConstants.END);
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_CURRENT_POS + " " + current) + ScpiCommandConstants.END);
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_VOLTAGE_DC_VALUE + ScpiCommandConstants.END);
    }

    @Override
    public boolean readCurrent(Integer channel) {
//        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_CURRENT_POS + ScpiCommandConstants.END);
        //此处电流读取是读取样机的电流，也就是这台电源的电源有效值
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_CURRENT_EFFECTIVE_VALUE + ScpiCommandConstants.END);
    }

}
