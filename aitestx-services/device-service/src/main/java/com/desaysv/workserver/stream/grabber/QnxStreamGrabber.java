package com.desaysv.workserver.stream.grabber;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;

@Slf4j
public class QnxStreamGrabber extends StreamGrabber {
    
    private FFmpegFrameGrabber frameGrabber;
    @Setter
    private String rtmpUrl;
    
    public QnxStreamGrabber() {
    }

    @Override
    public ImageBuffer grabByteBuffer() throws FrameGrabberException {
        try {
            Frame frame = grab();
            if (frame == null) {
                return new ImageBuffer();
            }
            
            ImageBuffer imageBuffer = new ImageBuffer();
            imageBuffer.setFrame(frame);
            return imageBuffer;
        } catch (Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    @Override
    public void start() throws FrameGrabberException {
        try {
            if (frameGrabber != null) {
                frameGrabber.start();
            }
        } catch (FFmpegFrameGrabber.Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    @Override
    public void stop() {
        try {
            if (frameGrabber != null) {
                frameGrabber.stop();
            }
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("停止QNX视频流失败", e);
        }
    }

    @Override
    public void trigger() {
        // QNX设备不需要触发
    }

    @Override
    public Frame grab() throws FrameGrabberException {
        try {
            if (frameGrabber == null) {
                initGrabber();
            }

            Frame frame = frameGrabber.grab();
            if (frame == null) {
                log.warn("QnxStreamGrabber: 获取帧为null，尝试重新初始化...");
                // 尝试重新初始化
                release();
                initGrabber();
                frame = frameGrabber.grab();
                if (frame == null) {
                    log.error("QnxStreamGrabber: 重新初始化后仍获取帧为null");
                }
            }
            return frame;
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("QnxStreamGrabber: 获取帧失败: {}", e.getMessage(), e);
            throw new FrameGrabberException(e);
        }
    }
    
    private void initGrabber() throws FrameGrabberException {
        try {
            if (rtmpUrl == null || rtmpUrl.isEmpty()) {
                rtmpUrl = "rtmp://127.0.0.1/live?live=1";
                log.info("QnxStreamGrabber: 使用固定RTMP URL: {}", rtmpUrl);
            }
            
            log.info("QnxStreamGrabber: 初始化FFmpegFrameGrabber，URL: {}", rtmpUrl);
            frameGrabber = new FFmpegFrameGrabber(rtmpUrl);
            frameGrabber.setOption("stimeout", "5000000"); // 5秒连接超时(微秒)
            frameGrabber.setOption("rtmp_live", "live");   // 指定为直播流

            log.info("QnxStreamGrabber: 启动FFmpegFrameGrabber...");
            frameGrabber.start();
            log.info("QnxStreamGrabber: FFmpegFrameGrabber启动成功，报告尺寸: {}x{}",
                    frameGrabber.getImageWidth(), frameGrabber.getImageHeight());
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("QnxStreamGrabber: 初始化失败: {}", e.getMessage(), e);
            throw new FrameGrabberException("初始化QNX视频流失败: " + e.getMessage());
        }
    }

    @Override
    public void release() {
        try {
            if (frameGrabber != null) {
                frameGrabber.release();
                frameGrabber = null;
            }
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("释放QNX视频流资源失败", e);
        }
    }
    
    @Override
    public int getImageWidth() {
        return frameGrabber != null ? frameGrabber.getImageWidth() : 0;
    }
    
    @Override
    public int getImageHeight() {
        return frameGrabber != null ? frameGrabber.getImageHeight() : 0;
    }
    
    @Override
    public CameraSettings getCameraSettings(CameraSettings cameraSettings) {
        return cameraSettings;
    }
}