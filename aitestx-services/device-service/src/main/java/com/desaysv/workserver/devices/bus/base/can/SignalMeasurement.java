package com.desaysv.workserver.devices.bus.base.can;

import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class SignalMeasurement {

    private String signalName;

    /**
     * CAN信号值支持+-偏差写法（如30+-2，表示28~32）,也支持范围写法，如20~30
     */
    private String signalValue;

    public void setSignalName(String signalName) {
        this.signalName = signalName != null ? signalName.trim() : null;
    }

    public void setSignalValue(String signalValue) {
        this.signalValue = signalValue != null ? signalValue.trim() : null;
    }
}