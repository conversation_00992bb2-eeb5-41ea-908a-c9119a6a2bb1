package com.desaysv.workserver.devices.device_udp;

import com.desaysv.workserver.devices.bus.fdx.FdxUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.IOException;
import java.net.*;
import java.time.Instant;
import java.util.Arrays;

@Setter
@Slf4j
public class CommandUdpClient extends DatagramSocket {
    private static CommandUdpClient INSTANCE;
    private String serverIP;
    private int serverPort;

    public CommandUdpClient() throws SocketException {
        super();
    }

    public static CommandUdpClient getInstance() {
        if (INSTANCE == null) {
            try {
                INSTANCE = new CommandUdpClient();
                INSTANCE.setSoTimeout(10000);
            } catch (SocketException e) {
                log.error("~~UDP超时~~");
                log.error(e.getMessage(), e);
            }
        }
        return INSTANCE;
    }

    public static void setInstanceNULL() {
        INSTANCE = null;
    }

    public byte[] sendAndReceiveData(byte[] dataBytes) throws UdpError {
        long sTime = System.currentTimeMillis();
        log.info("发送请求数据至udp目标:{}", Hex.encodeHexString(dataBytes));
        sendTo(dataBytes);
        DatagramPacket responsePacket = new DatagramPacket(new byte[4096], 4096);
        try {
            receive(responsePacket);
        } catch (IOException e) {
            throw new UdpError(e);
        }
        log.info("接收udp目标响应数据:{}", Hex.encodeHexString(Arrays.copyOfRange(responsePacket.getData(), 0, responsePacket.getLength())));
//        log.info("执行时长:{}毫秒", System.currentTimeMillis() - sTime);
        return FdxUtils.mergeHexByteArrays(new byte[][]{Arrays.copyOfRange(responsePacket.getData(), 0, responsePacket.getLength())});
    }

    public void sendTo(byte[] bytes) throws UdpError {
        try {
            send(new DatagramPacket(
                    bytes,
                    bytes.length,
                    InetAddress.getByName(serverIP),
                    serverPort)); //响应
        } catch (IOException e) {
            throw new UdpError(e);
        }
    }


    /**
     * 判断目标主机是否可达（基于 UDP Ping）
     * @param host         目标主机地址
     * @param port         目标端口
     * @param timeoutMs    单次请求超时时间（毫秒）
     * @param maxAttempts  最大尝试次数
     * @param intervalMs   每次尝试间隔（毫秒）
     * @return             true 表示至少一次 Ping 成功，false 表示全部失败
     */
    public static boolean isNetworkReachable(String host, int port,
                                             int timeoutMs, int maxAttempts,
                                             int intervalMs) {
        try (DatagramSocket socket = new DatagramSocket()) {
            InetAddress address = InetAddress.getByName(host);
            socket.setSoTimeout(timeoutMs);
            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                try {
                    // 发送请求
                    byte[] sendData = Instant.now().toString().getBytes();
                    DatagramPacket packet = new DatagramPacket(sendData, sendData.length, address, port);
                    socket.send(packet);

                    // 接收响应（仅验证是否有响应）
                    byte[] recvData = new byte[1024];
                    DatagramPacket recvPacket = new DatagramPacket(recvData, recvData.length);
                    socket.receive(recvPacket);
                    // 收到任何响应即视为成功
                    return true;
                } catch (SocketTimeoutException e) {
                    // 超时后继续下一次尝试
                    if (attempt < maxAttempts) {
                        Thread.sleep(intervalMs);
                    }
                } catch (IOException e) {
                    // 非超时错误直接终止
                    break;
                }
            }
        } catch (UnknownHostException e) {
            // 无效主机名
            return false;
        } catch (IOException | InterruptedException e) {
            // 网络或线程错误
            return false;
        }
        return false;
    }


}
