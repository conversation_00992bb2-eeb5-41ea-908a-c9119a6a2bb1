package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.command.AdbCommandConsole;
import com.desaysv.workserver.utils.command.AdbMessages;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: QinHao
 * @description:
 * @date: 2025/4/10 16:16
 */
@Slf4j
public class AdbHudDevice extends UsbAndroid {
    private AdbCommandConsole console;
    private List<String> commandList;

    public AdbHudDevice() {
        super();
        try {
            console = new AdbCommandConsole();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public AdbHudDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            console = new AdbCommandConsole();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        boolean result = super.open();
        if (result) {
            commandList = getCommandList();
            if (!commandList.isEmpty()) {
                for (String command : commandList) {
                    try {
                        console.executeCommand(command);
                        Thread.sleep(500);
                    } catch (Exception e) {
                        log.error("执行命令失败: " + command, e);
                        return false;
                    }
                }
            }
        }
        return result;
    }


    private List<String> getCommandList() {
        String command = getDeviceOperationParameter().getCommands();

        if (command == null) {
            return new ArrayList<>();
        }
        if (command.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return new ArrayList<>(Arrays.asList(command.split("\n")));
        } catch (Exception e) {
            log.error("拆分命令出错:" + e.getMessage());
            return new ArrayList<>();
        }
    }


    @Override
    public synchronized boolean close() {
        try {
            console.shutdown();
        } catch (IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return super.close();

    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Android.ADB_HUD;
    }

    @Override
    public boolean executeADBCommand(String command) throws OperationFailNotification {
        if (commandList != null && !commandList.isEmpty()) {
            try {
                return console.executeCommandWithRetry(command, 2000, "不是内部或外部命令", 2, commandList);
            } catch (IOException | InterruptedException e) {
                log.error(e.getMessage(), e);
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public boolean executeADBCommand(AdbMessages adbMessages) {
        try {
            return console.executeCommandWithValidation(adbMessages.getAdbMessage(), adbMessages.getDelay(), adbMessages.getExpectedAdbResult());
        } catch (IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean adbMustExist(String expectedAdbResult, int timeoutMillis) {
        try {
            return console.checkExpected(expectedAdbResult, timeoutMillis);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean adbForbidExist(String forbiddenAdbResult, int timeoutMillis) {
        try {
            return console.checkForbidden(forbiddenAdbResult, timeoutMillis);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

}
