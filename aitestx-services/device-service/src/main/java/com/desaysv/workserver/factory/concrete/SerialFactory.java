package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.I2C.I2CDevice;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.interfaces.AbstractSerialFactory;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 11:21
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
@Component
@Lazy
public class SerialFactory implements AbstractSerialFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Serial.PORT_SERIAL)) {
            return createSerialPortDevice(deviceRegisterForm);
        } else if (deviceModel.equals(DeviceModel.UsbI2C.USB_I2C)) {
            return createUsbI2CDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createSerialPortDevice(DeviceRegisterForm deviceRegisterForm) {
        return new SerialPortDevice(deviceRegisterForm.getDeviceOperationParameter());
//        return new SerialDeviceDecorator(new SerialPortDevice(deviceRegisterForm.getDeviceOperationParameter()));
    }

    @Override
    public Device createUsbI2CDevice(DeviceRegisterForm deviceRegisterForm) {
        return new I2CDevice(deviceRegisterForm.getDeviceOperationParameter());
    }
}
