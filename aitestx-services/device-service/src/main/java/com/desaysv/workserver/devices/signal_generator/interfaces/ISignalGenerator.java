package com.desaysv.workserver.devices.signal_generator.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ISignalGenerator {
    Logger log = LogManager.getLogger(ISignalGenerator.class.getSimpleName());
    boolean setSignalInput(Integer deviceChannel, int waveFormCode, int amplitudeValue, float frequencyValue, float dutyValue);
    boolean setSignalInput(Integer deviceChannel, int amplitudeValue, float frequencyValue, float dutyValue);

    boolean setWaveForm(Integer deviceChannel, int waveFormCode);

    boolean setFrequency(Integer deviceChannel, float frequencyValue);

    boolean setAmplitude(Integer deviceChannel, int amplitudeValue);

    boolean setDutyCycle(Integer deviceChannel, float dutyCycleValue);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SignalGeneratorRegexRule).SET_SIGNAL_INPUT"})
    default ActualExpectedResult setSignalInputInfo(Integer deviceChannel, int amplitudeValue, float frequencyValue, float dutyValue) {
//    default ActualExpectedResult setSignalInputInfo(Integer deviceChannel, int waveFormCode, int amplitudeValue, float frequencyValue, float dutyCycleValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setSignalInput(deviceChannel, amplitudeValue, frequencyValue, dutyValue);
        actualExpectedResult.put("setSignalInputInfo", pass, "");
        log.info("设置信号输入事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SignalGeneratorRegexRule).SET_WAVEFORM"})
    default ActualExpectedResult setWaveFormInfo(Integer deviceChannel, int waveFormCode) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setWaveForm(deviceChannel, waveFormCode);
        actualExpectedResult.put("setWaveFormInfo", pass, waveFormCode);
        log.info("设置波形事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SignalGeneratorRegexRule).SET_FREQUENCY"})
    default ActualExpectedResult setFrequencyInfo(Integer deviceChannel, float frequency) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setFrequency(deviceChannel, frequency);
        actualExpectedResult.put("setFrequencyInfo", pass, frequency);
        log.info("设置频率事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SignalGeneratorRegexRule).SET_AMPLITUDE"})
    default ActualExpectedResult setAmplitudeInfo(Integer deviceChannel, int amplitude) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setAmplitude(deviceChannel, amplitude);
        actualExpectedResult.put("setAmplitudeInfo", pass, amplitude);
        log.info("设置幅值事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SignalGeneratorRegexRule).SET_DUTY_CYCLE"})
    default ActualExpectedResult setDutyCycleInfo(Integer deviceChannel, float dutyCycle) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setDutyCycle(deviceChannel, dutyCycle);
        actualExpectedResult.put("setDutyCycleInfo", pass, dutyCycle);
        log.info("设置占空比事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }
}
