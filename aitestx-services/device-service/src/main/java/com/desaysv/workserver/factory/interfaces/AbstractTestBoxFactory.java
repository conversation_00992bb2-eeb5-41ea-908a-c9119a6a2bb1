package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractTestBoxFactory extends AbstractDeviceCreator {
    Device createTestBoxDevice(DeviceRegisterForm deviceRegisterForm);

    Device createLightTestBoxDevice(DeviceRegisterForm deviceRegisterForm);

    Device createrRZCUTestBoxDevice(DeviceRegisterForm deviceRegisterForm);
}
