package com.desaysv.workserver.virtual.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.bus.base.BusError;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

import static com.desaysv.workserver.devices.testbox.TestBoardBoxUtils.isWithinDeviation;

public interface IAdsTool {
    Logger log = LogManager.getLogger(IAdsTool.class.getSimpleName());

    Map<String, Float> fetchPWMValues() throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).XS_GET_PWM"})
    default ActualExpectedResult getXSPWMInfo(String pwmValueString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        Map<String, Float> pwmValueResMap;
        String pwmValueRes = "NA";
        try {
            pwmValueResMap = fetchPWMValues();
            pwmValueRes = pwmValueResMap.get("CH2").toString();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = pwmValueString.equals(pwmValueRes);
        log.info("PWM期望值:{},实际获取PWM值:{}%,检测结果:{},共耗时:{}毫秒", pwmValueString, pwmValueRes, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("comparePWM", pass, String.format("%s%%", pwmValueRes));
        return actualExpectedResult;
    }
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).GET_PWM"})
    default ActualExpectedResult getPWMInfo(String pwmValueString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        Map<String, Float> pwmValueResMap;
        String pwmValueRes = "NA";
        try {
            pwmValueResMap = fetchPWMValues();
            pwmValueRes = pwmValueResMap.get("CH2").toString();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = pwmValueString.equals(pwmValueRes);
        log.info("获取PWM期望值:{},实际获取PWM信号值:{}%,检测结果:{},共耗时:{}毫秒", pwmValueString, pwmValueRes, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("comparePWM", pass, String.format("%s%%", pwmValueRes));
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).COMPARE_PWM"})
    default ActualExpectedResult comparePWMInfo(float pwmBaseValue, float pwmDeviation) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        Map<String, Float> pwmValueResMap;
        String pwmValueRes = "NA";
        try {
            pwmValueResMap = fetchPWMValues();
            pwmValueRes = pwmValueResMap.get("CH2").toString();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        float getPWMValue = Float.parseFloat(pwmValueRes);
        boolean pass = isWithinDeviation(getPWMValue, pwmBaseValue, pwmDeviation);
        log.info("获取PWM期望值:{}%,偏差:{}%,实际获取PWM信号值:{}%,检测结果:{},共耗时:{}毫秒", pwmBaseValue, pwmDeviation, pwmValueRes, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("comparePWM", pass, String.format("%s%%", pwmValueRes));
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).COMPARE_TWO_PWM"})
    default ActualExpectedResult compareTwoPWMInfo(float firstPwmBaseValue, float firstPwmDeviation, float secondPwmBaseValue, float secondPwmDeviation) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String downPwmValueRes = "NA";
        String upPwmValueRes= "NA";
        boolean pass = false;
        Map<String, Float> pwmValueResMap;
        String pwmValueRes = "NA";
        try {
            pwmValueResMap = fetchPWMValues();
            if (pwmValueResMap != null && !pwmValueResMap.isEmpty()) {
                float upPWMValue = pwmValueResMap.get("CH1");
                float downPWMValue = pwmValueResMap.get("CH2");
                pass =  isWithinDeviation(downPWMValue, firstPwmBaseValue, firstPwmDeviation) && isWithinDeviation(upPWMValue, secondPwmBaseValue, secondPwmDeviation);
                downPwmValueRes = String.format("%s%%", downPWMValue);
                upPwmValueRes = String.format("%s%%", upPWMValue);
            }
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }

        log.info("获取Down 58xd_pwm期望值:{}%,偏差:{}%, UP 58xd_pwm期望值:{}%,偏差:{}%, 实际获取Down 58xd_pwm值:{}%, UP 58xd_pwm值:{}%, 检测结果:{},共耗时:{}毫秒",
                firstPwmBaseValue, firstPwmDeviation, secondPwmBaseValue, secondPwmDeviation, downPwmValueRes, upPwmValueRes, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("comparePWM", pass, String.format("%s-%s", downPwmValueRes, upPwmValueRes));
        return actualExpectedResult;
    }


    boolean setAdsToolLogName(String logName) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).SET_ADS_TOOL_LOG_NAME"})
    default ActualExpectedResult setAdsToolLogNameInfo(String logName) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setAdsToolLogName(logName);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setAdsToolLogName", pass, logName);
        log.info("执行setAdsToolLogName logName:{},检测结果:{},共耗时:{}毫秒", logName, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setAdsToolLog(Integer deviceChannel, int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.AdsToolRegexRule).SET_ADS_TOOL_LOG"})
    default ActualExpectedResult setAdsToolLogInfo(Integer deviceChannel, int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setAdsToolLog(deviceChannel, commandId);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行ADS Tool Log:{},检测结果:{},共耗时:{}毫秒", commandId == 0 ? "开始录制" : "结束录制", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setAdsToolLogInfo", pass, commandId);
        return actualExpectedResult;
    }


}
