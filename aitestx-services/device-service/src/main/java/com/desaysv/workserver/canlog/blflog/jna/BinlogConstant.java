package com.desaysv.workserver.canlog.blflog.jna;

public class BinlogConstant {

    public static final long GENERIC_READ = 0x80000000L;//只读
    public static final int GENERIC_WRITE = 0x40000000;//写权限
    public static final int FILE_APPEND_DATA = 0x0004;

    public static final int BL_OBJ_SIGNATURE = 0x4A424F4C;
    public static final int BL_OBJ_TYPE_CAN_MESSAGE = 1;
    public static final int BL_OBJ_TYPE_CAN_MESSAGE2 = 86;
    public static final int BL_OBJ_TYPE_CAN_FD_MESSAGE_64 = 101;

    public static final int BL_OBJ_TYPE_reserved_5 = 115;

    public static final int BL_FLUSH_STREAM = 0x00000001;

    public static final int BL_OBJ_FLAG_TIME_TEN_MICS = 0x00000001; /* 10 micro second timestamp */
    public static final int BL_OBJ_FLAG_TIME_ONE_NANS = 0x00000002; /* 1 nano second timestamp */

}
