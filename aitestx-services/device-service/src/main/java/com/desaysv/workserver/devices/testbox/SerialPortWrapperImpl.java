package com.desaysv.workserver.devices.testbox;

import com.serotonin.modbus4j.serial.SerialPortWrapper;
import jssc.SerialPort;
import jssc.SerialPortException;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.io.OutputStream;

@Slf4j
public class SerialPortWrapperImpl implements SerialPortWrapper {
    private final SerialPort port;
    private final int baudRate;
    private final int dataBits;
    private final int stopBits;
    private final int parity;
    private final int flowControlIn;
    private final int flowControlOut;

    public SerialPortWrapperImpl(String commPortId, int baudRate, int dataBits,
                                 int stopBits, int parity, int flowControlIn, int flowControlOut) {

        this.baudRate = baudRate;
        this.dataBits = dataBits;
        this.stopBits = stopBits;
        this.parity = parity;
        this.flowControlIn = flowControlIn;
        this.flowControlOut = flowControlOut;
        port = new SerialPort(commPortId);
    }

    @Override
    public void close() throws Exception {
        port.closePort();
        log.debug("端口{}关闭", port.getPortName());
    }

    @Override
    public synchronized void open() throws SerialPortException {
        try {
            port.openPort();
            port.setParams(this.getBaudRate(), this.getDataBits(), this.getStopBits(), this.getParity());
            port.setFlowControlMode(flowControlIn | flowControlOut);
            log.debug("端口{}打开成功", port.getPortName());
        } catch (SerialPortException e) {
            log.error("端口{}打开失败:{} ", port.getPortName(), e.getMessage());
            throw e;
        }
    }

    @Override
    public InputStream getInputStream() {
        return new SerialInputStream(port);
    }

    @Override
    public OutputStream getOutputStream() {
        return new SerialOutputStream(port);
    }

    @Override
    public int getBaudRate() {
        return baudRate;
    }


    @Override
    public int getDataBits() {
        return dataBits;
    }

    @Override
    public int getStopBits() {
        return stopBits;
    }

    @Override
    public int getParity() {
        return parity;
    }

    @Override
    public int getFlowControlIn() {
        return flowControlIn;
    }

    @Override
    public int getFlowControlOut() {
        return flowControlOut;
    }
}