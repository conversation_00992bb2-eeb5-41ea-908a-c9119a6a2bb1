package com.desaysv.workserver.devices.bus.zlg;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.config.lin.LinConfig;
import com.desaysv.workserver.config.lin.LinConfigParameter;
import com.desaysv.workserver.config.lin.NetLinConfigParameter;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.FilterLinMessage;
import com.desaysv.workserver.devices.bus.base.lin.LinMessage;
import com.desaysv.workserver.devices.bus.base.lin.SequenceableLinBus;
import com.desaysv.workserver.exceptions.can.ZlgCanExecuteException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.DataUtils;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public class ZlgLin extends SequenceableLinBus {
    private Pointer deviceHandle; // 设备句柄
    private final ZlgApi zlgApi;
    private final Map<Integer, Pointer> channelHandleMap = new HashMap<>();
    private static final Lock lock = new ReentrantLock();//静态锁 类级别锁 保证即便多个zlgcan实例也只有一个能同时open
    private static final Condition condition = lock.newCondition();
    private boolean isOpen;


    public ZlgLin(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        zlgApi = new ZlgApi();
    }

    @Override
    public String getDeviceModel() {
       return DeviceModel.Bus.ZLG_USBCANFD_200U;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        LinConfig linConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
        if (linConfig != null) {
            linConfig.getConfigParameters().clear();
            updateConfig(linConfig);
        }
        return openDevice();
    }

    /**
     * 自动打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean autoOpen() throws DeviceOpenException {
        //改成从CanConfig获取
        LinConfig linConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
        if (linConfig == null) {
            throw new DeviceOpenException("周立功配置文件不存在，请删除设备重新连接");
        }
        Map<String, LinConfigParameter> configParameters = linConfig.getConfigParameters();
        Map<String, NetLinConfigParameter> configNetParameters = linConfig.getConfigNetParameters();
        if (CollectionUtils.isEmpty(configParameters) && CollectionUtils.isEmpty(configNetParameters)) {
            throw new DeviceOpenException("周立功配置文件不含通道，请删除设备重新连接");
        }
        boolean isOpenOk = openDevice();

        if (isOpenOk) {
            if (!CollectionUtils.isEmpty(configParameters)) {
                Object ch0OpenParams = configParameters.get("1");
                Object ch1OpenParams = configParameters.get("2");
                if (ch0OpenParams != null) {
                    //打开通道1
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch0OpenParams), LinConfigParameter.class), linConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道2
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch1OpenParams), LinConfigParameter.class), linConfig);
                }
            }
            if (!CollectionUtils.isEmpty(configNetParameters)) {
                Object ch0OpenParams = configNetParameters.get("1");
                Object ch1OpenParams = configNetParameters.get("2");
                Object ch2OpenParams = configNetParameters.get("3");
                Object ch3OpenParams = configNetParameters.get("4");
                if(!isOpen){
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_mode", ((NetLinConfigParameter)ch0OpenParams).getWorkMode());
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/ip", ((NetLinConfigParameter)ch0OpenParams).getIp());
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_port", ((NetLinConfigParameter)ch0OpenParams).getWorkPort());
                    isOpen = true;
                }
                if (ch0OpenParams != null) {
                    //打开通道1
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch0OpenParams), NetLinConfigParameter.class), linConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道2
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch1OpenParams), NetLinConfigParameter.class), linConfig);
                }
                if (ch2OpenParams != null) {
                    //打开通道3
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch2OpenParams), NetLinConfigParameter.class), linConfig);
                }
                if (ch3OpenParams != null) {
                    //打开通道4
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch3OpenParams), NetLinConfigParameter.class), linConfig);
                }
            }
        }
        return isOpenOk;
    }

    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    private boolean openDevice() throws DeviceOpenException {
        lock.lock();
        try {
            if (deviceHandle != null) {
                log.info("周立功设备已连接:{}", getDeviceName());
                return true;
            }

            if (!isSimulated()) {
                Integer deviceType = (Integer) ZlgCanConstants.getConstantsMap().get(getDeviceModel());
                if (deviceType == null) {
                    throw new DeviceOpenException(String.format("周立功CAN设备型号未知:%s", getDeviceModel()));
                }

                // 尝试获取已存在的设备句柄，判断设备是否已经连接
                deviceHandle = ZlgUtil.getInstance().getDeviceHandle(deviceType + "-" + getDevicePort());
                if (deviceHandle == null) {
                    deviceHandle = ZlgApi.openDevice(deviceType, getDevicePort());
                }
                if (deviceHandle == null) {
                    throw new DeviceOpenException(String.format("周立功%s连接失败，请检查CAN盒子是否连接正常", getDeviceName()));
                }
                //打开设备
                ZlgCanLib.ZCAN_DEVICE_INFO deviceInfo = ZlgApi.getDeviceInfo(deviceHandle);
                if (deviceInfo != null) {
                    log.info("周立功{}设备信息:{}", getDeviceName(), ZlgDeviceInfo.format(deviceInfo));
                }
            }
            condition.signalAll();
            return true;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 关闭设备
     *
     * @return
     * @throws DeviceCloseException
     */
    @Override
    public boolean close() throws DeviceCloseException {
        lock.lock();
        try {
            log.info("关闭周立功{}", getDeviceName());
            boolean isOk = super.close();
            if (!isSimulated()) {
                //关闭所有还没关闭的通道
                for (Map.Entry<Integer, Pointer> entry : channelHandleMap.entrySet()) {
                    try {
                        ZlgApi.stopLinChannel(entry.getValue(), entry.getKey());
                    } catch (ZlgCanExecuteException e) {
                        throw new DeviceCloseException(e);
                    }
                }
                channelHandleMap.clear();
                // 删除已存在的设备句柄
                Integer deviceType = (Integer) ZlgCanConstants.getConstantsMap().get(getDeviceModel());
                ZlgUtil.getInstance().removeDeviceHandle(deviceType + "-" + getDevicePort());
                if (deviceHandle != null) {
                    isOk &= ZlgApi.closeDevice(deviceHandle);
                    deviceHandle = null;
                }
            }
            isOpen = false;
            return isOk;
        } finally {
            lock.unlock();
        }
    }

    protected Pointer getChannelHandle(int channel) {
        return channelHandleMap.get(channel);
    }

    /**
     * 打开通道
     *
     * @param linConfigParameter
     * @param linConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(LinConfigParameter linConfigParameter, LinConfig linConfig) throws DeviceOpenException {
        lock.lock();
        try {
            if (!isSimulated()) {
                try {
                    int channel = linConfigParameter.getChannel();
                    if (channel == -1) {
                        //启动通道1
                        startChannel(linConfig.getConfigParameters().get("1"));
                        //启动通道2
                        startChannel(linConfig.getConfigParameters().get("2"));
                    } else {
                        startChannel(linConfigParameter);
                    }
                } catch (ZlgCanExecuteException e) {
                    throw new DeviceOpenException(e);
                }
            }
        } finally {
            lock.unlock();
        }
        return true;
    }

    /**
     * 打开通道
     *
     * @param netLinConfigParameter
     * @param linConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(NetLinConfigParameter netLinConfigParameter, LinConfig linConfig) throws DeviceOpenException {
        lock.lock();
        try {
            if (!isSimulated()) {
                try {
                    int channel = netLinConfigParameter.getChannel();
                    if (channel == -1) {
                        //启动通道1
                        startChannel(linConfig.getConfigNetParameters().get("1"));
                        //启动通道2
                        startChannel(linConfig.getConfigNetParameters().get("2"));
                        //启动通道3
                        startChannel(linConfig.getConfigNetParameters().get("3"));
                        //启动通道4
                        startChannel(linConfig.getConfigNetParameters().get("4"));
                    } else {
                        startChannel(netLinConfigParameter);
                    }
                } catch (ZlgCanExecuteException e) {
                    throw new DeviceOpenException(e);
                }
            }
        } finally {
            lock.unlock();
        }
        return true;
    }

    /**
     * 启动通道
     *
     * @param linConfigParameter 通道参数
     * @return
     * @throws ZlgCanExecuteException
     */
    private boolean startChannel(LinConfigParameter linConfigParameter) throws ZlgCanExecuteException {
        if (getChannelHandle(linConfigParameter.getChannel()) != null) {
            log.info("周立功{}通道{}已打开", getDeviceName(), linConfigParameter.getChannel());
            return true;
        }
        log.info("打开周立功{}通道{}:\n{}",
                getDeviceName(),
                linConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(linConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        Pointer channelHandle = ZlgApi.startLin(deviceHandle, linConfigParameter);

        channelHandleMap.put(linConfigParameter.getChannel(), channelHandle);
        return true;
    }

    /**
     * 启动通道
     *
     * @param netLinConfigParameter 通道参数
     * @return
     * @throws ZlgCanExecuteException
     */
    private boolean startChannel(NetLinConfigParameter netLinConfigParameter) throws ZlgCanExecuteException {
        if (getChannelHandle(netLinConfigParameter.getChannel()) != null) {
            log.info("周立功{}通道{}已打开", getDeviceName(), netLinConfigParameter.getChannel());
            return true;
        }
        log.info("打开周立功{}通道{}:\n{}",
                getDeviceName(),
                netLinConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(netLinConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        Pointer channelHandle = ZlgApi.startLin(deviceHandle, netLinConfigParameter);
        channelHandleMap.put(netLinConfigParameter.getChannel(), channelHandle);
        return true;
    }

    @Override
    public boolean openLinChannel(LinConfigParameter linConfigParameter) throws DeviceOpenException {
        //写入配置文件
        LinConfig linConfig = writeConfig(linConfigParameter);
        if (deviceHandle == null) {
            //未打开设备尝试打开设备
            openDevice();
        }
        return openChannelInternal(linConfigParameter, linConfig);
    }

    @Override
    public boolean openLinChannel(NetLinConfigParameter netLinConfigParameter) throws DeviceOpenException {
        //写入配置文件
        LinConfig linConfig = writeConfig(netLinConfigParameter);
        if (deviceHandle == null) {
            //未打开设备尝试打开设备
            openDevice();
        }
        if(!isOpen){
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_mode", netLinConfigParameter.getWorkMode());
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/ip", netLinConfigParameter.getIp());
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_port", netLinConfigParameter.getWorkPort());
            isOpen = true;
        }
        return openChannelInternal(netLinConfigParameter, linConfig);
    }

    @Override
    public int getMaxChannelCount() {
        return 2;
    }

    /**
     * 关闭通道
     *
     * @param channel
     * @return
     * @throws DeviceCloseException
     */
    @Override
    public boolean closeLinChannel(int channel) throws DeviceCloseException {
        lock.lock();
        try {
            if (isSimulated()) {
                return true;
            }
            boolean isOk;
            try {
                if (channel == -1) {
                    isOk = ZlgApi.stopLinChannel(channelHandleMap.get(1), 1)
                            & ZlgApi.stopLinChannel(channelHandleMap.get(2), 2);
                    channelHandleMap.remove(1);
                    channelHandleMap.remove(2);
                    return isOk;
                }
                isOk = ZlgApi.stopLinChannel(channelHandleMap.get(channel), channel);
                channelHandleMap.remove(channel);
                return isOk;
            } catch (ZlgCanExecuteException e) {
                throw new DeviceCloseException(e);
            }
        } finally {
            lock.unlock();
        }

    }


    @Override
    public boolean setLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) throws BusError {
        return false;
    }

    @Override
    public boolean setLinSingleMsgControl(Integer deviceChannel, String ecuNodeName, String messageID, int messageStatus) throws BusError {
        return false;
    }

    @Override
    public boolean setLinAllMsgStatus(String ecuNodeName, int status) throws BusError {
        return false;
    }

    @Override
    public boolean setLinChannelMsgStatus(Integer deviceChannel, int status) throws BusError {
        return false;
    }

    @Override
    public double fetchLinSignal(Integer deviceChannel,String ecuNodeName, String messageName, String signalName) throws BusError {
        return 0;
    }

    @Override
    public boolean sendLinMessage(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        //设置 LIN0（主）ID 响应data
        /*byte[] LinData = ByteUtils.hexStringToByteArray(byteInstruction);
        ZlgCanLib.ZCAN_LIN_PUBLISH_CFG masterPublishCfg = new ZlgCanLib.ZCAN_LIN_PUBLISH_CFG();
        masterPublishCfg.ID = (byte) DataUtils.parseHexString(messageId);
        masterPublishCfg.dataLen = 8;
        masterPublishCfg.chkSumMode = 1;
        masterPublishCfg.data = LinData;
        int res = ZlgCanLib.Instance.ZCAN_SetLINPublish(channelHandleMap.get(0), masterPublishCfg, 1);  //发布id和data
        log.info("设置 LIN0（主）ID 响应data ：{}, {}",LinData,  res == 1 ? "成功" : "失败");*/
        ZlgCanLib.ZCAN_LIN_MSG lin_msg = new ZlgCanLib.ZCAN_LIN_MSG(1);
        //打印lin_msg的长度
        log.info("lin_msg字节长度:{}", lin_msg.size());
        lin_msg.chnl = deviceChannel.byteValue();
        lin_msg.dataType = 0; // 0-正常LIN报文，1-错误LIN报文（接收时有效）
        lin_msg.data = new ZlgCanLib.ZCAN_LIN_MSG.Data(1);
        lin_msg.data.zcanlinData = new ZlgCanLib.ZCAN_LIN_MSG.ZCANLINData(1);
        lin_msg.data.zcanlinData.PID = 0x01;
        long result = ZlgCanLib.Instance.ZCAN_TransmitLIN(channelHandleMap.get(deviceChannel), lin_msg, 1);   //仅头部
        log.info("TransmitLIN {}", result == 1 ? "成功" : "失败");
        return  result == 1;
    }

    @Override
    public boolean setLinWakeUp(Integer deviceChannel, int wakeUpCommand) throws BusError {
        return false;
    }

    /*
     * 发送LIN消息到指定的设备通道 做从站响应数据
     * */
    public boolean sendLinMessage(Integer deviceChannel, String messageId, byte[] LinData) {
        //设置 LIN0（主）ID 响应data
        ZlgCanLib.ZCAN_LIN_PUBLISH_CFG masterPublishCfg = new ZlgCanLib.ZCAN_LIN_PUBLISH_CFG();
        masterPublishCfg.ID = (byte) DataUtils.parseHexString(messageId);
        masterPublishCfg.dataLen = (byte) LinData.length;
        masterPublishCfg.data = LinData;
        int result = ZlgCanLib.Instance.ZCAN_SetLINPublish(channelHandleMap.get(deviceChannel), masterPublishCfg, 1);  //发布id和data
        if (!(result == 1)) {
            log.info("设置 LIN 响应data ：{}, {}, {}, 长度：{} ", LinData, "失败", messageId, masterPublishCfg.dataLen);
        }
        return result == 1;
    }

    /*
     *  取消掉此ID的发布
     * */
    @Override
    public void stopLinSubscibeMessage(Integer deviceChannel, Integer messageId) {
        ZlgCanLib.ZCAN_LIN_SUBSCIBE_CFG zcanLinSubscibeCfg = new ZlgCanLib.ZCAN_LIN_SUBSCIBE_CFG();
        zcanLinSubscibeCfg.ID = messageId.byteValue();
        zcanLinSubscibeCfg.dataLen = (byte) 0xFF;
        zcanLinSubscibeCfg.chkSumMode = 3;
        int result = ZlgCanLib.Instance.ZCAN_SetLINSubscribe(channelHandleMap.get(deviceChannel), zcanLinSubscibeCfg, 1);  // 取消掉此ID的发布
        if (!(result == 1)) {
            log.info("设置LIN 取消掉此ID的发布 {}, {}", "失败", Integer.toHexString(messageId));
        }
    }

    public boolean send(Integer deviceChannel, LinMessage linMessage) throws BusError {
        return sendLinMessage(deviceChannel,linMessage.getIdHex(),ByteUtils.byteArrayToHexString(linMessage.getData()));
    }
    @Override
    public void send(LinMessage message, Float timeout) throws BusError {
        boolean isSendOk = sendLinMessage(message.getChannel(), message.getIdHex(), message.getData());

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }
    }

    @Override
    public FilterLinMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public String readLinDataByIdHex(Integer deviceChannel, String messageId) {
        byte msgId = (byte) DataUtils.parseHexString(messageId);
        return zlgApi.readLinMessage(getChannelHandle(1), msgId, 2000);
    }

    @Override
    public String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError {
        return null;
    }


    /*private boolean initLinConfig() {
        // 初始化并打开 LIN 通道，LIN0 设置为主，LIN1 设置为从，波特率设置为 9600
        ZlgCanLib.ZCAN_LIN_INIT_CONFIG masterInitConfig = new ZlgCanLib.ZCAN_LIN_INIT_CONFIG((byte) 1, (byte) 1, 9600);
        ZlgCanLib.ZCAN_LIN_INIT_CONFIG slaveInitConfig = new ZlgCanLib.ZCAN_LIN_INIT_CONFIG((byte) 0, (byte) 1, 9600);
        channelHandleMap.put(0, zlgCanApi.startLinChannel(deviceHandle,0, masterInitConfig));
        channelHandleMap.put(1, zlgCanApi.startLinChannel(deviceHandle,1, slaveInitConfig));
        //设置通道合并接收属性 - 分通道接收，全局属性只需要设置一次
        zlgCanApi.setReceiveMerge(channelHandleMap.get(0),true);  //在通道LIN1接收报文
        //设置 LIN1（从）ID 响应,主节点发仅头部，从节点响应data
        ZlgCanLib.ZCAN_LIN_PUBLISH_CFG slavePublishCfg = new ZlgCanLib.ZCAN_LIN_PUBLISH_CFG();
        slavePublishCfg.ID = 1;
        slavePublishCfg.dataLen = 8;
        slavePublishCfg.chkSumMode = 1;
        slavePublishCfg.data = new byte[]{0x2, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8};
        int res = ZlgCanLib.Instance.ZCAN_SetLINPublish(channelHandleMap.get(1), slavePublishCfg, 1);
        log.info("设置 LIN1（从）ID 响应 :{}", res == 1 ? "成功" : "失败");
        return res == 1;
    }*/

    public static void main(String[] args) {
        ZlgLin zlgLin = new ZlgLin(new DeviceOperationParameter());
        //zlgLin.open();
        //发送
        //zlgLin.setLinFrame(0, "0x01", "0100000000000000");
        //String result = zlgLin.fetchLinFrameWithID("01");
        //System.out.println(!result.isEmpty() ? result : "读取失败");
//        zlgLin.sendMessage();
    }

}


