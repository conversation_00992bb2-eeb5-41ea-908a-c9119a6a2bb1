package com.desaysv.workserver.hud;

import java.util.List;

public class HudDetectionResponse {
    private List<String> effectImages;
    private List<String> realImages;
    private List<String> matchResults;

    public HudDetectionResponse(List<String> effectImages, List<String> realImages, List<String> matchResults) {
        this.effectImages = effectImages;
        this.realImages = realImages;
        this.matchResults = matchResults;
    }


    // Getter and Setter
    public List<String> getEffectImageList() {
        return effectImages;
    }

    public void setEffectImageList(List<String> effectImageList) {
        this.effectImages = effectImageList;
    }

    public List<String> getRealImageList() {
        return realImages;
    }

    public void setRealImageList(List<String> realImageList) {
        this.realImages = realImageList;
    }

    public List<String> getMatchResults() {
        return matchResults;
    }

    public void setMatchResults(List<String> matchResults) {
        this.matchResults = matchResults;
    }

    // 重写toString方法
    @Override
    public String toString() {
        return "HudDetectionResponse{" +
                "effectImages=" + effectImages +
                ", realImages=" + realImages +
                ", matchResults=" + matchResults +
                '}';
    }
}
