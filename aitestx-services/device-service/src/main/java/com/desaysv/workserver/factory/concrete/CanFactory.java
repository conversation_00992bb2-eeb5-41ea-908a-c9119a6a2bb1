package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.canoe.VectorCanFDX;
import com.desaysv.workserver.devices.bus.nican.NICan8502;
import com.desaysv.workserver.devices.bus.tosun.can.TC1013;
import com.desaysv.workserver.devices.bus.tosun.can.TC1016;
import com.desaysv.workserver.devices.bus.tosun.can.TC1034;
import com.desaysv.workserver.devices.bus.tosun.can.TC1026;
import com.desaysv.workserver.devices.bus.zlg.ZlgCanFd200U;
import com.desaysv.workserver.devices.bus.zlg.ZlgCanFdDtu400UEwgr;
import com.desaysv.workserver.devices.bus.zlg.ZlgCanFdMiniI;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractCanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class CanFactory implements AbstractCanFactory {
    @Autowired
    private TC1013 tc1013;
    @Autowired
    private TC1016 tc1016;
    @Autowired
    private TC1026 tc1026;
    @Autowired
    private TC1034 tc1034;

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Bus.ZLG_USBCANFD_200U:
                return createZlgCanFd200UDevice(deviceRegisterForm);
            case DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR:
                return createZlgCanFdDtu400UEwgrDevice(deviceRegisterForm);
            case DeviceModel.Bus.ZLG_USBCAN_I_MINI:
                return createZlgCanFdMiniIDevice(deviceRegisterForm);
            case DeviceModel.Bus.VECTOR_CAN:
                return this.createVectorCANoeDevice(deviceRegisterForm);
            case DeviceModel.Bus.NI_CAN_8502:
                return createNICan8502Device(deviceRegisterForm);
            case DeviceModel.Bus.TC1013:
                return createTC1013Device(deviceRegisterForm);
            case DeviceModel.Bus.TC1016:
                return createTC1016Device(deviceRegisterForm);
            case DeviceModel.Bus.TC1026:
                return createTC1026Device(deviceRegisterForm);
            case DeviceModel.Bus.TC1034:
                return createTC1034Device(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createZlgCanFdMiniIDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZlgCanFdMiniI(deviceRegisterForm.getDeviceOperationParameter());
    }


    @Override
    public Device createZlgCanFd200UDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZlgCanFd200U(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createZlgCanFdDtu400UEwgrDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZlgCanFdDtu400UEwgr(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createVectorCANoeDevice(DeviceRegisterForm deviceRegisterForm) {
//        return new VectorCan(deviceRegisterForm.getDeviceOperationParameter());
        return new VectorCanFDX(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createTC1013Device(DeviceRegisterForm deviceRegisterForm) {
        tc1013.setDeviceOperationParameter(deviceRegisterForm.getDeviceOperationParameter());
        return tc1013;
    }

    @Override
    public Device createTC1016Device(DeviceRegisterForm deviceRegisterForm) {
        tc1016.setDeviceOperationParameter(deviceRegisterForm.getDeviceOperationParameter());
        return tc1016;
    }

    @Override
    public Device createTC1026Device(DeviceRegisterForm deviceRegisterForm) {
        tc1026.setDeviceOperationParameter(deviceRegisterForm.getDeviceOperationParameter());
        return tc1026;
    }

    @Override
    public Device createTC1034Device(DeviceRegisterForm deviceRegisterForm) {
        tc1034.setDeviceOperationParameter(deviceRegisterForm.getDeviceOperationParameter());
        return tc1034;
    }

    @Override
    public Device createNICan8502Device(DeviceRegisterForm deviceRegisterForm) {
        return new NICan8502(deviceRegisterForm.getDeviceOperationParameter());
    }

}
