package com.desaysv.workserver.screen.result.report;

import com.desaysv.workserver.screen.result.mist.BackTracePoint;
import com.desaysv.workserver.screen.result.mist.TestPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 屏幕点击测试报告
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScreenPointSummary extends TouchSummary {

    private TestPoint testPoint; //实际测试点

    private BackTracePoint reportPoint; //显示屏报点

    private Double distanceDeviation; //报点和实际测试距离偏差

}
