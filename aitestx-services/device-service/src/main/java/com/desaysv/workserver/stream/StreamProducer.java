package com.desaysv.workserver.stream;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.utils.ImageUtils;
import lombok.Data;
import org.bytedeco.javacv.Frame;

@Data
public abstract class StreamProducer {
    private int width;
    private int height;
    private double frameRate;
    private String url;

    public StreamProducer() {
    }

    public StreamProducer(String url) {
        this.url = url;
    }

    public StreamProducer(String url, int w, int h) {
        this.url = url;
        if (w > 0 && h > 0) {
            this.width = w;
            this.height = h;
        }
    }

    public StreamProducer(int w, int h) {
        if (w > 0 && h > 0) {
            this.width = w;
            this.height = h;
        }
    }

    public RectSize getSize() {
        return new RectSize(width, height);
    }

    public Frame cropFrame(Frame frame, AbsoluteRoiRect rect) {
        if (rect == null) {
            return frame;
        }
        return ImageUtils.crop(frame, rect);
    }

    public Frame cropFrame(Frame frame, ScaledRoiRect roi) {
        return cropFrame(frame, roi.toRect(getSize()));
    }

    public abstract Frame grabFrame() throws FrameGrabberException;

    public Frame grabFrame(AbsoluteRoiRect rect) throws FrameGrabberException {
        return cropFrame(grabFrame(), rect);
    }

    public Frame grabFrame(ScaledRoiRect roi) throws FrameGrabberException {
        return grabFrame(roi.toRect(getSize()));
    }

    protected abstract void distributeStream(ImageBuffer imageBuffer);

    public abstract RectSize getDefaultSize();

    public abstract void outputDynamicFrame(boolean isOutput);

    public abstract void setExposureAuto(CameraSettings cameraSettings);

    public abstract void setReverseX(boolean status);

    public abstract void setReverseY(boolean status);

    public abstract void grabberSwitch(boolean status);
    public abstract CameraSettings getCameraSettings();

    public void closeStream() {

    }
    public abstract double getGrabberFrameRate();


    public static void main(String[] args) {
        RectSize size = new RectSize(10, 30);
        System.out.println(size);
    }


}