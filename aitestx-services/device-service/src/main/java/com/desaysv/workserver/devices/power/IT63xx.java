package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.IT63Protocol;
import com.desaysv.workserver.devices.power.protocol.PowerSCPIProtocol;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * IT63系列电源（IT6322B）
 */
public class IT63xx extends DefaultPower {
    public IT63xx() {
        this(new DeviceOperationParameter());
    }

    public IT63xx(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        //设置通信协议 如果协议为空则使用默认USB协议
        String commProtocol = (String) deviceOperationParameter.get("protocol");
        commProtocol = commProtocol == null ? CommProtocol.USB : commProtocol;
        setCommProtocol(commProtocol);
        PowerSCPIProtocol powerProtocol = new IT63Protocol(this);
        setPowerProtocol(powerProtocol);
    }

    @Override
    public int getNumberChannels() {
        return 3;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT63xx;
    }

}
