package com.desaysv.workserver.devices.power.base;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.power.base.interfaces.IPowerDevice;
import com.desaysv.workserver.devices.power.protocol.PowerProtocol;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.monitor.MeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.ScheduledExecutorService;

/**
 * 电源设备
 */
@Slf4j
public abstract class PowerDevice extends PortDevice implements MeasurementComparator, IPowerDevice {
    @Getter
    private final String deviceType = DeviceType.DEVICE_POWER;

    @JSONField(serialize = false)
    @Getter
    @Setter
    private PowerProtocol powerProtocol;

    @Autowired
    @JSONField(serialize = false)
    private ScheduledExecutorService readScheduledExecutorService;

    public PowerDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    private void shutdownSchedule() {
        if (readScheduledExecutorService != null) {
            readScheduledExecutorService.shutdown();
            readScheduledExecutorService = null;
        }
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        shutdownSchedule();
        return super.close();
    }


    public abstract String getDeviceModel();

}
