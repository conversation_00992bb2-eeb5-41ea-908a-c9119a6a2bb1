package com.desaysv.workserver.operation.command;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.bytedeco.opencv.global.opencv_calib3d;
import org.bytedeco.opencv.global.opencv_core;
import org.bytedeco.opencv.global.opencv_imgcodecs;
import org.bytedeco.opencv.global.opencv_imgproc;
import org.bytedeco.opencv.opencv_core.*;
import org.opencv.core.CvType;

import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.IOException;

@Slf4j
public class RectangleDisplacement {
    private double pixelToRealRatio;

    public double calibrate(String calibrationImagePath, double realSquareSideLength) {
        // 参数验证
        if (calibrationImagePath == null || calibrationImagePath.isEmpty()) {
            throw new IllegalArgumentException("标定图像路径不能为空");
        }
        if (realSquareSideLength <= 0) {
            throw new IllegalArgumentException("实际方块边长必须大于0");
        }

        try (// 所有需要关闭的资源统一管理
             Mat image = opencv_imgcodecs.imread(calibrationImagePath);
             Mat gray = new Mat();
             Mat edges = new Mat();
             Mat hierarchy = new Mat();
             MatVector contours = new MatVector()
        ) {
            // 图像预处理
            opencv_imgproc.cvtColor(image, gray, opencv_imgproc.COLOR_BGR2GRAY);
            opencv_imgproc.Canny(gray, edges, 50, 150);

            // 查找轮廓
            opencv_imgproc.findContours(edges, contours, hierarchy, opencv_imgproc.RETR_TREE, opencv_imgproc.CHAIN_APPROX_SIMPLE);

            // 遍历所有轮廓寻找方形
            for (int i = 0; i < contours.size(); i++) {
                Mat contour = contours.get(i);
                RotatedRect rect = opencv_imgproc.minAreaRect(new Mat(contour));
                if (isSquare(rect)) {
                    double pixelSideLength = Math.min(rect.size().width(), rect.size().height());
                    pixelToRealRatio = realSquareSideLength / pixelSideLength;
                    break;
                }
            }
            return pixelToRealRatio;
        }
    }

    private boolean isSquare(RotatedRect rect) {
        double aspectRatio = Math.min(rect.size().width(), rect.size().height()) / Math.max(rect.size().width(), rect.size().height());
        return aspectRatio > 0.8 && aspectRatio < 1.2; // Allowing some tolerance for skewed squares
    }

    public double calculateDisplacement(String imagePath1, String imagePath2, String outputImagePath, double pixelToRealRatio) {
        Mat image1 = opencv_imgcodecs.imread(imagePath1);
        Mat image2 = opencv_imgcodecs.imread(imagePath2);

        Point centroid1 = findPolygonCentroid(image1);
        Point centroid2 = findPolygonCentroid(image2);

        double pixelDisplacement = Math.sqrt(Math.pow(centroid2.x() - centroid1.x(), 2) + Math.pow(centroid2.y() - centroid1.y(), 2));
        double realDisplacement = pixelDisplacement * pixelToRealRatio;

        Mat combinedImage = combineImages(image1, image2);
        drawDisplacement(combinedImage, centroid1, centroid2, realDisplacement);

        opencv_imgcodecs.imwrite(outputImagePath, combinedImage);

        return realDisplacement;
    }

    private Point findPolygonCentroid(Mat image) {
        Mat gray = new Mat();
        opencv_imgproc.cvtColor(image, gray, opencv_imgproc.COLOR_BGR2GRAY);
        Mat edges = new Mat();
        opencv_imgproc.Canny(gray, edges, 50, 150);

        MatVector contours = new MatVector();
        Mat hierarchy = new Mat();
        opencv_imgproc.findContours(edges, contours, hierarchy, opencv_imgproc.RETR_TREE, opencv_imgproc.CHAIN_APPROX_SIMPLE);

        for (int i = 0; i < contours.size(); i++) {
            Mat contour = contours.get(i);
            Moments moments = opencv_imgproc.moments(contour);
            if (moments.m00() != 0) {
                int cx = (int) (moments.m10() / moments.m00());
                int cy = (int) (moments.m01() / moments.m00());
                return new Point(cx, cy);
            }
        }
        return new Point(0, 0);
    }

    private void drawDisplacement(Mat image, Point centroid1, Point centroid2, double displacement) {
        opencv_imgproc.circle(image, centroid1, 5, new Scalar(0, 255, 0, 0), -1, 8, 0);
        opencv_imgproc.circle(image, centroid2, 5, new Scalar(0, 0, 255, 0), -1, 8, 0);
        opencv_imgproc.line(image, centroid1, centroid2, new Scalar(255, 0, 0, 0), 2, 8, 0);
        opencv_imgproc.putText(image, String.format("%.2f cm", displacement), new Point((centroid1.x() + centroid2.x()) / 2 + 10, (centroid1.y() + centroid2.y()) / 2 - 10),
                opencv_imgproc.FONT_HERSHEY_SIMPLEX, 0.5, new Scalar(255, 0, 0, 0), 2, opencv_imgproc.LINE_AA, false);
    }

    private Mat combineImages(Mat image1, Mat image2) {
        Mat combinedImage = new Mat();
        opencv_core.addWeighted(image1, 0.5, image2, 0.5, 0.0, combinedImage);
        return combinedImage;
    }

    public double calibrateFromFrame(Frame frame, double realSquareSideLength) {
        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage bufferedImage = converter.convert(frame);

        Mat image = bufferedImageToMat(bufferedImage);
        Mat undistortedImage = new Mat();
        undistortImage(image, undistortedImage);

        Mat gray = new Mat();
        opencv_imgproc.cvtColor(undistortedImage, gray, opencv_imgproc.COLOR_BGR2GRAY);
        Mat binary = new Mat();
        opencv_imgproc.threshold(gray, binary, 50, 255, opencv_imgproc.THRESH_BINARY);

        MatVector contours = new MatVector();
        Mat hierarchy = new Mat();
        opencv_imgproc.findContours(binary, contours, hierarchy, opencv_imgproc.RETR_TREE, opencv_imgproc.CHAIN_APPROX_SIMPLE);
        log.info("找到的等值线数量: {}", contours.size());
        double pixelToRealRatio = 0;

        for (int i = 0; i < contours.size(); i++) {
            Mat contour = contours.get(i);
            Mat approx = new Mat();
            opencv_imgproc.approxPolyDP(new Mat(contour), approx, opencv_imgproc.arcLength(new Mat(contour), true) * 0.02, true);

            if (approx.total() == 4) {
                RotatedRect rect = opencv_imgproc.minAreaRect(new Mat(contour));
                if (isSquare(rect)) {
                    double pixelSideLength = Math.min(rect.size().width(), rect.size().height());
                    pixelToRealRatio = realSquareSideLength / pixelSideLength;
                    break;
                }
            }
        }

        if (pixelToRealRatio == 0) {
            log.error("在框架中没有找到矩形。");
        }

        return pixelToRealRatio;
    }

    private void undistortImage(Mat src, Mat dst) {
        Mat cameraMatrix = Mat.eye(3, 3, CvType.CV_64F).asMat();
        Mat distCoeffs = Mat.zeros(5, 1, CvType.CV_64F).asMat();
        opencv_calib3d.undistort(src, dst, cameraMatrix, distCoeffs);
    }

    private Mat bufferedImageToMat(BufferedImage bufferedImage) {
        int type = bufferedImage.getType() == 0 ? BufferedImage.TYPE_3BYTE_BGR : bufferedImage.getType();
        Mat mat = new Mat(bufferedImage.getHeight(), bufferedImage.getWidth(), CvType.CV_8UC3);
        byte[] data = ((DataBufferByte) bufferedImage.getRaster().getDataBuffer()).getData();
        mat.data().put(data);
        return mat;
    }


    private BufferedImage matToBufferedImage(Mat mat) {
        int type = mat.channels() == 1 ? BufferedImage.TYPE_BYTE_GRAY : BufferedImage.TYPE_3BYTE_BGR;
        BufferedImage image = new BufferedImage(mat.cols(), mat.rows(), type);
        byte[] data = new byte[mat.rows() * mat.cols() * (int) mat.elemSize()];
        mat.data().get(data);
        image.getRaster().setDataElements(0, 0, mat.cols(), mat.rows(), data);
        return image;
    }


    public Frame calculateDisplacement(String imagePath1, String imagePath2, double pixelToRealRatio) {
        // 读取图像路径中的图像并将其转换为Mat对象
        Mat image1 = opencv_imgcodecs.imread(imagePath1);
        Mat image2 = opencv_imgcodecs.imread(imagePath2);

        if (image1.empty() || image2.empty()) {
            log.error("无法加载图像，请检查图像路径: " + (image1.empty() ? imagePath1 : imagePath2));
            return null;
        }

        Point centroid1 = findPolygonCentroid(image1);
        Point centroid2 = findPolygonCentroid(image2);

        double pixelDisplacement = Math.sqrt(Math.pow(centroid2.x() - centroid1.x(), 2) + Math.pow(centroid2.y() - centroid1.y(), 2));
        double realDisplacement = pixelDisplacement * pixelToRealRatio;

        opencv_imgproc.circle(image1, centroid1, 5, new Scalar(0, 255, 0, 0), -1, 8, 0);
        opencv_imgproc.circle(image2, centroid2, 5, new Scalar(0, 255, 0, 0), -1, 8, 0);

        Mat combinedImage = combineImages(image1, image2);
        drawDisplacement(combinedImage, centroid1, centroid2, realDisplacement);

        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage resultBufferedImage = matToBufferedImage(combinedImage);

        return converter.convert(resultBufferedImage);
    }

    public static void main(String[] args) {
        RectangleDisplacement rd = new RectangleDisplacement();
        String calibrationImagePath = "D:\\uidq9934\\My Pictures\\test.jpg";
        double realSquareSideLength = 5;
        double ratio = rd.calibrate(calibrationImagePath, realSquareSideLength);
        System.out.println("像素真实比: " + ratio);

        String imagePath1 = "D:\\FlyTest\\data\\server\\projects\\ABT 12.9\\database\\fileDB\\pictures\\camera\\Camera#1\\roiPictures\\1111\\1111_ROI_20241011141141.png";
        String imagePath2 = "D:\\FlyTest\\data\\server\\projects\\ABT 12.9\\database\\fileDB\\pictures\\camera\\Camera#1\\templatePictures\\1111.png";

        Mat image1 = opencv_imgcodecs.imread(imagePath1.replace("\\", "/"));
        Mat image2 = opencv_imgcodecs.imread(imagePath2.replace("\\", "/"));

        if (image1.empty() || image2.empty()) {
            System.err.println("无法加载图像，请检查图像路径和文件格式: " + (image1.empty() ? imagePath1 : imagePath2));
            return;
        }

        Frame frame = rd.calculateDisplacement(imagePath1, imagePath2, ratio);
    }
}