package com.desaysv.workserver.devices.bus.base;

import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class FilterCanMessage extends CanMessage {

    private boolean alreadyFiltered;//是否已经过滤

    public FilterCanMessage(float timestamp, int arbitrationId, boolean isExtendedId, boolean isRemoteFrame, boolean isErrorFrame, int channel, Integer dlc, byte[] data, boolean isCanFd, boolean alreadyFiltered) {
        super(timestamp, arbitrationId, isExtendedId, isRemoteFrame, isErrorFrame, channel, dlc, data, isCanFd);
        this.alreadyFiltered = alreadyFiltered;
    }

}
