package com.desaysv.workserver.devices.bus.canoe.com;

import com.jacob.activeX.ActiveXComponent;

import java.util.HashMap;
import java.util.Map;

public class VectorCanFactory {
    private final Map<Integer, ActiveXComponent> vectorDeviceMap = new HashMap<>();

    public ActiveXComponent getVectorCanInstance(Integer deviceIndex) {
        if (!vectorDeviceMap.containsKey(deviceIndex)) {
            vectorDeviceMap.put(deviceIndex, VectorShareComponent.getInstance());
        }
        return vectorDeviceMap.get(deviceIndex);
    }

}
