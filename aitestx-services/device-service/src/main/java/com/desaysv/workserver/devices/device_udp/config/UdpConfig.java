package com.desaysv.workserver.devices.device_udp.config;

import com.desaysv.workserver.config.ProjectConfig;
import com.desaysv.workserver.devices.device_udp.VariableInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class UdpConfig extends ProjectConfig {

    private String path;

    private Map<String, VariableInfo> variables;

}