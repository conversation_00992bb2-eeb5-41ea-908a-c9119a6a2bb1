package com.desaysv.workserver.devices.robot.config;

import com.desaysv.workserver.model.roi.ScaledPoint;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import lombok.Data;

@Data
public class ScaledMonitorAreaConfig {

    //起点
    private Double startX;
    //起点y
    private Double startY;
    //终点x
    private Double endX;
    //终点y
    private Double endY;

    /**
     * 检查坐标点是否有效(非空且非0)
     */
    public boolean isValid() {
        // 检查是否有null值
        if (startX == null || startY == null || endX == null || endY == null) {
            return false;
        }

        // 检查是否为0值
        return startX != 0.0 && startY != 0.0 && endX != 0.0 && endY != 0.0;
    }

    public ScaledRoiRect toScaledRoiRect() {
        ScaledRoiRect roiRect = new ScaledRoiRect();
        roiRect.setPointStart(new ScaledPoint(startX, startY));
        roiRect.setPointEnd(new ScaledPoint(endX, endY));
        return roiRect;
    }

}
