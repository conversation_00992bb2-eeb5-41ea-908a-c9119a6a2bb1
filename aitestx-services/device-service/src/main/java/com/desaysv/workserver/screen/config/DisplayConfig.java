package com.desaysv.workserver.screen.config;

import com.desaysv.workserver.screen.entity.PreciousPoint3D;
import com.desaysv.workserver.model.roi.RectSize;
import lombok.Data;

@Data
public class DisplayConfig {

    private PreciousPoint3D leftTop;

    private PreciousPoint3D leftBottom;

    private PreciousPoint3D rightTop;

    private PreciousPoint3D rightBottom;

    private PreciousPoint3D center;

    private RectSize screen;

    private ScreenCheckParameters screenCheckParameters;

    public DisplayConfig() {
        leftTop = new PreciousPoint3D();
        leftBottom = new PreciousPoint3D();
        rightTop = new PreciousPoint3D();
        rightBottom = new PreciousPoint3D();
        center = new PreciousPoint3D();
        screen = new RectSize();
        screenCheckParameters = new ScreenCheckParameters();
    }

}