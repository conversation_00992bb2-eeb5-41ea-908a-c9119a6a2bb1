package com.desaysv.workserver.devices.robot.type.mg400.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-28 11:55
 * @description :
 * @modified By :
 * @since : 2022-7-28
 */
@Data
public class SwipeRobotCoordinates {
    //TODO：改成sessionID记录robotName、projectName、deviceModelName，方便客户端不传该参数
    private String deviceName; //改为Device类型

    private List<String> coordinateNameList;

    private String projectName;

    private boolean noLift;
}
