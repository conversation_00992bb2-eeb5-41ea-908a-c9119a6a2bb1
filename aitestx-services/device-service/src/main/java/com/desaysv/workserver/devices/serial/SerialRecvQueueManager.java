package com.desaysv.workserver.devices.serial;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SerialRecvQueueManager {
    private final Map<String, String> dataQueue = new ConcurrentHashMap<>();

    public void put(String key,String data) {
        dataQueue.put(key, data);
    }

    public String get(String key) {
        return dataQueue.get(key);
    }

    public boolean contains(String key) {
        return dataQueue.containsKey(key);
    }

    public void clear() {
        dataQueue.clear();
    }
}