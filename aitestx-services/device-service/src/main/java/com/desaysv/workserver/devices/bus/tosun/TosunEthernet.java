package com.desaysv.workserver.devices.bus.tosun;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class TosunEthernet extends Device {
    public TosunEthernet() {
        this(new DeviceOperationParameter());
    }

    public TosunEthernet(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceModel.Bus.TOSUN_ETHERNET;
    }

    @Override
    public String getDeviceModel() {
        return DeviceType.DEVICE_ETHERNET;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

}
