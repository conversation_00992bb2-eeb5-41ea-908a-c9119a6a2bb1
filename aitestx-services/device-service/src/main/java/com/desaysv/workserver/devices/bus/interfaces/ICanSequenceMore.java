package com.desaysv.workserver.devices.bus.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.utils.ByteUtils;

import java.util.List;

public interface ICanSequenceMore extends ICanSequence {

    /**
     * 发送CAN报文
     *
     * @param deviceChannel CAN通道
     * @param messageId     CAN报文ID
     * @param byteData      CAN报文数据
     * @param period        CAN报文周期
     * @param cycle         CAN报文发送次数
     * @param isCanFd       是否为CANFD
     * @return
     */
    boolean sendCanData(Integer deviceChannel, String messageId, byte[] byteData, Integer period, Integer cycle, boolean isCanFd) throws BusError;

    /**
     * 停止发送CAN报文
     *
     * @param deviceChannel CAN通道
     * @param messageId     CAN报文ID/名称
     * @return
     */
    boolean stopSendPeriodicCanData(Integer deviceChannel, String messageId);

    /**
     * 停止所有CAN报文
     *
     * @param deviceChannel CAN通道
     * @return
     */
    boolean stopAllCanMessage(Integer deviceChannel);

    /**
     * 设置CAN通道报文状态
     *
     * @param deviceChannel CAN通道
     * @param messageStatus 通道报文状态
     * @return
     */
    boolean setCanChannelMessageStatus(Integer deviceChannel, int messageStatus) throws BusError;

    /**
     * 设置CAN通道报文状态（支持排除多个ECU)
     *
     * @param deviceChannel CAN通道
     * @param excludeEcus   排除的ecu列表
     * @param messageStatus 通道报文状态
     * @return
     */
    boolean setCanChannelMessageStatus(Integer deviceChannel, List<String> excludeEcus, int messageStatus) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).SEND_CAN_MESSAGE_PERIODIC"})
    default ActualExpectedResult sendCanMessageBySequencePeriodic(Integer deviceChannel, String messageId, String byteData, Integer period) {
        return sendCanMessageBySequence(deviceChannel, messageId, byteData, period, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).SEND_CAN_MESSAGE"})
    default ActualExpectedResult sendCanMessageBySequence(Integer deviceChannel, String messageId, String byteData, Integer period, Integer cycle) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = sendCanData(deviceChannel, optimizeHexString(messageId), ByteUtils.hexStringToByteArray(optimizeByteString(byteData)), period, cycle, false);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("sendCanMessageBySequence", pass, byteData);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).STOP_SEND_CAN_MESSAGE"})
    default ActualExpectedResult stopSendCanMessage(Integer deviceChannel, String messageId) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = stopSendPeriodicCanData(deviceChannel, messageId);
        actualExpectedResult.put("stopSendCanMessage", pass, messageId);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).STOP_ALL_SEND_CAN_MESSAGE"})
    default ActualExpectedResult stopSendAllCanMessage(Integer deviceChannel) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = stopAllCanMessage(deviceChannel);
        actualExpectedResult.put("stopSendAllCanMessage", pass);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_ALL_CHANNEL_CAN_MSG"})
    default ActualExpectedResult setChannelCanMessageStatus(Integer deviceChannel, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanChannelMessageStatus(deviceChannel, messageStatus);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("stopSendAllCanMessage", pass);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_ALL_CHANNEL_CAN_MSG_EXCLUDE"})
    default ActualExpectedResult setChannelCanMessageStatus(Integer deviceChannel, int messageStatus, List<String> excludeEcus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanChannelMessageStatus(deviceChannel, excludeEcus, messageStatus);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("stopSendAllCanMessage", pass);
        return actualExpectedResult;
    }
}
