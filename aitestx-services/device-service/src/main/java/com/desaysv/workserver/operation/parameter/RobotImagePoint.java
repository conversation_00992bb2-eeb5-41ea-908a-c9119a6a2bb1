package com.desaysv.workserver.operation.parameter;

import com.desaysv.workserver.devices.robot.config.RobotConfig;
import lombok.Data;


/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:机械臂视觉点击判断设置
 * @date: 2024/1/15 19:38
 * @version: 1.0
 */
@Data
public class RobotImagePoint {
    private String coordinateName;
    private String visionAlgorithm;
    private int similarity;
    private RobotConfig robotConfig;
    private String projectName;

    public RobotImagePoint() {
        robotConfig = new RobotConfig();
    }

}
