package com.desaysv.workserver.devices.bus.fdx;

import com.desaysv.workserver.filemanager.project.FdxFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MultiXMLParser {
    public static boolean parseMultipleXML(List<String> filePaths) {
        Map<String, DataGroup> dataGroups = new HashMap<>();
        for (String filePath : filePaths) {
            dataGroups.putAll(parseSingleXML(filePath));
        }
        ObjectMapper mapper = new ObjectMapper();
        try {
            // 将数据组列表转换为JSON字符串
            String jsonString = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(dataGroups);
            // 指定输出文件路径
            FdxFileManager fdxFileManager =  ProjectFileManager.of(FdxFileManager.class);
            File outputFile = fdxFileManager.getFdxFilePath();
            CANoeFdxDescription.getInstance().setFdxFiles(filePaths);
            CANoeFdxDescription.getInstance().setDataGroups(dataGroups);
            // 将JSON字符串写入文件
            mapper.writeValue(outputFile, CANoeFdxDescription.getInstance());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    private static Map<String, DataGroup>  parseSingleXML(String filePath) {
        Map<String, DataGroup> dataGroups = new HashMap<>();
        try {
            File inputFile = new File(filePath);
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = null;
            try {
                dBuilder = dbFactory.newDocumentBuilder();
            } catch (ParserConfigurationException e) {
                log.error(e.getMessage(), e);
            }
            Document doc = dBuilder.parse(inputFile);
            // Normalize the document
            doc.getDocumentElement().normalize();
            // Extract datagroup information
            NodeList datagroups = doc.getElementsByTagName("datagroup");
            for (int i = 0; i < datagroups.getLength(); i++) {
                Element datagroup = (Element) datagroups.item(i);
                String groupID = datagroup.getAttribute("groupID");
                String size = datagroup.getAttribute("size");
                DataGroup dataGroup = new DataGroup();
                dataGroup.setFilePath(filePath);
                dataGroup.setGroupId(Integer.parseInt(groupID));
                dataGroup.setGroupSize(Integer.parseInt(size));
                // Extract identifier within datagroup
                NodeList identifiers = datagroup.getElementsByTagName("identifier");
                for (int j = 0; j < identifiers.getLength(); j++) {
                    Node identifierNode = identifiers.item(j);
                    if (identifierNode.getNodeType() == Node.ELEMENT_NODE) {
                        String identifier = identifierNode.getTextContent();
                        if (StringUtils.isNotEmpty(identifier)) {
                            dataGroup.setIdentifier(identifier);
                        }
                    }
                }
                Map<String, FdxItem> itemsMap = new HashMap<>();
                NodeList items = datagroup.getElementsByTagName("item");
                for (int k = 0; k < items.getLength(); k++) {
                    Element item = (Element) items.item(k);
                    String itemOffset = item.getAttribute("offset");
                    String itemSize = item.getAttribute("size");
                    String itemType = item.getAttribute("type");
                    FdxItem itemModel = new FdxItem();
                    itemModel.setOffset(Integer.parseInt(itemOffset));
                    itemModel.setSize(Integer.parseInt(itemSize));
                    itemModel.setType(itemType);
                    // Extract sysvar inside item
                    NodeList sysvars = item.getElementsByTagName("sysvar");
                    for (int l = 0; l < sysvars.getLength(); l++) {
                        Element sysvar = (Element) sysvars.item(l);
                        String sysvarName = sysvar.getAttribute("name");
                        String sysvarNamespace = sysvar.getAttribute("namespace");
                        String sysvarValue = sysvar.getAttribute("value");
                        itemModel.setNamespace(sysvarNamespace);
                        itemModel.setName(sysvarName);
                    }
                    itemsMap.put(itemModel.getName(), itemModel);
                }
                dataGroup.setItems(itemsMap);
                dataGroups.put(dataGroup.getIdentifier(), dataGroup);
            }
        } catch (IOException  | org.xml.sax.SAXException e) {
            System.err.println("Error parsing file: " + filePath);
            log.error(e.getMessage(), e);
        }
        return dataGroups;
    }


    public static void main(String[] args) {
        List<String> filePaths = new ArrayList<>();
        filePaths.add("D:\\uidq9000\\Downloads\\NetworkTest_FDXFile.xml");
        filePaths.add("D:\\uidq9000\\Downloads\\FDXFile2.xml"); // Add paths to all your XML files
//        parseMultipleXML(filePaths);
    }
}