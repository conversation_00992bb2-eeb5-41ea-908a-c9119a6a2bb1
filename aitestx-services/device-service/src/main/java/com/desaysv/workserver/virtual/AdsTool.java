package com.desaysv.workserver.virtual;

import com.alibaba.excel.util.StringUtils;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.virtual.interfaces.IAdsTool;
import com.desaysv.workserver.devices.tcpserver.TCPServerControl;
import com.desaysv.workserver.filemanager.project.ADSToolLogFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * AdsTool
 */
@Slf4j
public class AdsTool extends OperationTarget implements IAdsTool {
    //单例模式
    @Getter
    private static final AdsTool instance = new AdsTool();
    private String logName;
    private static final Pattern xsDataPattern = Pattern.compile("OS:(\\d+),.*?58xs_data:\\s*(\\d+)");
    private static final Pattern xdDataPattern = Pattern.compile("OS:(\\d+),.*?58xd_data:\\s*(\\d+)");
    private static final Pattern downPattern = Pattern.compile("OS:(\\d+),.*?Down 58xd_pwm:\\s*(\\d+)");
    private static final Pattern upPattern = Pattern.compile("OS:(\\d+),.*?UP 58xd_pwm:\\s*(\\d+)");
    private static final String READ_PWM_COMMAND = "read pwm";
   
    public AdsTool() {

    }


    @Override
    public Map<String, Float> fetchPWMValues() throws BusError {
        String pwmValue = "NA";
        TCPServerControl tcpServer = TCPServerControl.getInstance();
        try {
            log.info("PWM变化中,等待10s...");
            tcpServer.startCollectingData();
            Thread.sleep(10000);
//            tcpServer.clearCollectingData();
            log.info("开始发命令<read pwm>去读取PWM值");
            boolean success = tcpServer.sendData(READ_PWM_COMMAND);
            if (!success) {
                log.error("发送命令<read pwm>到ADS Tools读取PWM失败");
                tcpServer.stopCollectingData();
                return null;
            }
            Thread.sleep(3000);
            List<String> collectedData = tcpServer.getCollectedData();
            tcpServer.stopCollectingData();
            if (collectedData!= null && !collectedData.isEmpty()) {
                String pwmDataString = collectedData.get(collectedData.size() - 1);
                System.out.println("接收到的PWM数据: " + pwmDataString);
                Map<String, Float> pwmChannelValueMap = parseChannels(pwmDataString);
                return pwmChannelValueMap;
            }
            return null;
        } catch (Throwable e) {
            throw new BusError(e);
        }
    }

    public static Map<String, Float> parseChannels(String input) {
        Map<String, Float> channelMap = new LinkedHashMap<>();
        try {
            // 移除时间戳并分割参数
            String[] sections = input.split("\\]");
            String dataPart = sections.length > 1 ? sections[1].trim() : input;
            String[] params = dataPart.split(",");
            int paramIndex = 0;
            // 解析ADS Tools界面PWM 8个通道的值
            for (int ch = 1; ch <= 8; ch++) {
                String key = "CH" + ch;
                float value = 0.0f;
                if (paramIndex < params.length) {
                    // 提取有效数值部分（处理带冒号和不带冒号的情况）
                    String[] segments = params[paramIndex].split(":");
                    String valueStr = segments.length > 1 ?
                            segments[segments.length - 1] :
                            segments[0];
                    System.out.println(key+","+valueStr);
                    value = Float.parseFloat(valueStr.trim());
                    paramIndex++;
                }
                System.out.println("key==" + key+",value=="+value);
                channelMap.put(key, value);
            }
        } catch (Exception e) {
            System.out.println("解析错误: "+e.getMessage());
            return new LinkedHashMap<>(); // 返回空Map表示失败
        }
        return channelMap;
    }

    @Override
    public boolean setAdsToolLogName(String logName) throws BusError {
//        this.logName = "D:\\FlyTest\\data\\server\\projects\\Suzuki Y17 AWC\\config\\" + logName + ".txt";
        this.logName = ProjectFileManager.of(ADSToolLogFileManager.class).getFolder(logName + ".txt").getAbsolutePath();
        return true;
    }

    @Override
    public boolean setAdsToolLog(Integer deviceChannel, int commandId) throws BusError {
        TCPServerControl tcpServer = TCPServerControl.getInstance();
        if (commandId == 0) {
            if (StringUtils.isEmpty(logName)) return false;
            tcpServer.saveLog(logName);
            tcpServer.startSaveData(logName);
        } else {
            tcpServer.stopSaveData();
        }
        return true;
    }

}
