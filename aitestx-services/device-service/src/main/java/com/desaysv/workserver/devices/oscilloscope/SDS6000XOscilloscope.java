package com.desaysv.workserver.devices.oscilloscope;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.oscilloscope.base.SigLentOscilloscopeDevice;
import com.desaysv.workserver.devices.oscilloscope.interfaces.IOscilloscope;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.desaysv.workserver.utils.DataUtils.scientificToFloat;

@Slf4j
public class SDS6000XOscilloscope extends SigLentOscilloscopeDevice implements IOscilloscope {
    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @JSONField(serialize = false)
    private JVisaInstrument instrument;
    private static final long SLEEP_1000_MILLIS = 1000L;
    private static final long SLEEP_500_MILLIS = 500L;
    private static final long SLEEP_200_MILLIS = 200L;
    //缓冲区大小
    private static final int BUFFER_SIZE = 32768;
    private static final int TOTAL_CHANNEL = 3;
    private static final String STOP_STATUS = "Stop";
    private static final double HIGH_FREQUENCY_THRESHOLD = 1000.0;
    private static final Object TIMEBASE_HIGH_FREQUENCY = 2.00E-05;    // us/div
    private static final Object TIMEBASE_LOW_FREQUENCY = 2.00E-03;     // ms /div
    private static final Object CHANNEL_SCALE_FACTOR = 5.00E+00;       // 5V / div

    public SDS6000XOscilloscope() {
        this(new DeviceOperationParameter());
    }

    public SDS6000XOscilloscope(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Oscilloscope.SDS_6000X;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (visaResourceManager == null) {
            try {
                visaResourceManager = new JVisaResourceManager();
            } catch (JVisaException e) {
                log.warn(e.getMessage(), e);
            }
        }
        try {
            instrument = visaResourceManager.openInstrument(getDeviceName());
            instrument.setTimeout(10000); // 增加超时到10秒
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
        }
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        boolean result = true;
        Exception closeException = null;
        if (instrument != null) {
            try {
//                instrument.write(SDSSCPICommands.CLEAR_SCREEN);
                instrument.close();
            } catch (JVisaException e) {
                log.error("关闭仪器失败: {}", e.getMessage());
                result = false;
                closeException = e;
            } finally {
                instrument = null;
            }
        }

        if (visaResourceManager != null) {
            try {
                visaResourceManager.close();
            } catch (JVisaException e) {
                log.error("关闭资源管理器失败: {}", e.getMessage());
                result = false;
                closeException = e;
            } finally {
                visaResourceManager = null;
            }
        }

        try {
            result &= super.close();
        } catch (Exception e) {
            log.error("父类关闭操作异常: {}", e.getMessage());
            result = false;
            closeException = e;
        }

        if (closeException != null) {
            throw new DeviceCloseException(closeException);
        }
        return result;
    }


    private void safeSleep(long millis) throws InterruptedException {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new InterruptedException("设备休眠被中断");
        }
    }


    @Override
    public Map<String, Float> getMeasureAll(Integer deviceChannel) {
        Map<String, Float> map = new HashMap<>(2);
        boolean deviceReady = true;
        try {
            try {
                sendCommand(SDSSCPICommands.STOP);
            } catch (JVisaException e) {
                throw new RuntimeException(e);
            }
            safeSleep(SLEEP_500_MILLIS);
            map.put("freq", getFrequency(deviceChannel));
            safeSleep(SLEEP_200_MILLIS);
            map.put("duty", getDuty(deviceChannel));
            try {
                safeSleep(SLEEP_200_MILLIS);
                sendCommand(SDSSCPICommands.RUN);
            } catch (JVisaException e) {
                throw new RuntimeException(e);
            }
        } catch (InterruptedException e) {
            log.warn("测量等待被中断，设备通道：{}", deviceChannel);
            Thread.currentThread().interrupt();
            deviceReady = false;
        }
        return deviceReady ? map : Collections.emptyMap();
    }

    public String sendAndReceiveString(String command) throws JVisaException {
        try {
            instrument.write(command);
            safeSleep(SLEEP_1000_MILLIS);
            instrument.clear();
            safeSleep(SLEEP_1000_MILLIS);
            return instrument.readString(BUFFER_SIZE);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new JVisaException("读取操作被中断", e);
        }
    }

    @Override
    public float getFrequency(Integer deviceChannel) {
        try {
            String freq = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.MEASURE_SIMPLE_VALUE, "FREQ"));
            log.info("SDS6000X getFrequency: {}", freq);
            return scientificToFloat(freq);
        } catch (JVisaException e) {
            log.error("SDS6000X getFrequency error", e);
        }
        return -1;
    }

    @Override
    public float getAmplitude(Integer deviceChannel) {
        try {
            String amplitude = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.MEASURE_SIMPLE_VALUE, "AMPL"));
            return scientificToFloat(amplitude);
        } catch (JVisaException e) {
            log.error("SDS6000X getAmplitude error", e);
        }
        return -1;
    }

//    @Override
//    public float getDuty(Integer deviceChannel) {
//        try {
//            String duty = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
//            log.info("SDS6000X getDuty: {}", duty);
//            return scientificToFloat(duty);
//        } catch (JVisaException e) {
//            log.error("SDS6000X getDuty error", e);
//        }
//        return -1;
//    }

    @Override
    public float getDuty(Integer deviceChannel) {
        try {
            long startMills = System.currentTimeMillis();
            String duty = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
            log.info("SDS6000X 读取占空比: {},耗时：{}ms", duty, System.currentTimeMillis() - startMills);
            //如果duty返回没有包含有数字的值
            if (!duty.matches(".*[0-9].*")) {
                log.info("SDS6000X 占空比返回数据为无效值,耗时：{}ms", System.currentTimeMillis() - startMills);
                //还要判断最大值，是否是小于1V，如果小于1V，则返回0%，大于1V，则返回100%
                String max = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel + TOTAL_CHANNEL));
                log.info("SDS6000X 占空比返回数据为无效值，读取示波器最大值：{},耗时：{}ms", max, System.currentTimeMillis() - startMills);
                //转换max数据类型----暂无设备调试，待台架上示波器验证解析数据是否正确
                float maxValue = scientificToFloat(max);
                log.info("SDS6000X 解析最大值：{},耗时：{}ms", maxValue, System.currentTimeMillis() - startMills);
                if (maxValue > 1) {
                    return 100f;
                } else {
                    return 0f;
                }
            }
            float dutyValue = scientificToFloat(duty);
            //如何打印scientificToFloat(duty);的耗时
            log.info("SDS6000X getDuty: {},耗时：{}ms", dutyValue, System.currentTimeMillis() - startMills);
            return dutyValue;
        } catch (JVisaException e) {
            log.error("SDS6000X getDuty error", e);
        }
        return -1;
    }

    @Override
    public boolean setInitValue(float frequencyBaseValue) {
        try {
            //Khz以上设置的时基和垂直分辨率不一样，具体数值是实际测试上得出的
            Object timeBase = frequencyBaseValue > HIGH_FREQUENCY_THRESHOLD ? TIMEBASE_HIGH_FREQUENCY : TIMEBASE_LOW_FREQUENCY;
            String triggerStatus = sendAndReceiveString(SDSSCPICommands.TRIGGER_STATUS);
            if (STOP_STATUS.equalsIgnoreCase(triggerStatus)) {
                safeSleep(SLEEP_200_MILLIS);
                sendCommand(SDSSCPICommands.RUN);
            }
            safeSleep(SLEEP_200_MILLIS);
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.TIMEBASE_SCALE, timeBase));
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.CHAN1_SCALE, CHANNEL_SCALE_FACTOR));
            safeSleep(SLEEP_200_MILLIS);
            return true;
        } catch (JVisaException | InterruptedException e) {
            log.error("SDS6000X设置值失败", e);
            return false;
        }
    }

    @Override
    public boolean setOscStatus(String deviceStatus) {
        String status = deviceStatus.equalsIgnoreCase("STOP") ? "STOP" : "RUN";
        try {
            sendCommand(SDSSCPICommands.formatCommand(SDSSCPICommands.SET_STATUS, status));
        } catch (JVisaException e) {
            log.error("SDS6000X设置状态失败", e);
        }
        return false;
    }

    @Override
    public float getRaiseTime(Integer deviceChannel) {
        try {
            long startMills = System.currentTimeMillis();
            String raiseTimeString = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
            float raiseTime = scientificToFloat(raiseTimeString);
            log.info("SDS6000X getRaiseTime: {},耗时：{}ms", raiseTime, System.currentTimeMillis() - startMills);
            return raiseTime;
        } catch (JVisaException e) {
            log.error("SDS6000X getRaiseTime error", e);
        }
        return -1;
    }

    @Override
    public float getFullTime(Integer deviceChannel) {
        try {
            long startMills = System.currentTimeMillis();
            String fullTimeString = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
            float fullTime = scientificToFloat(fullTimeString);
            log.info("SDS6000X getFullTime: {},耗时：{}ms", fullTime, System.currentTimeMillis() - startMills);
            return fullTime;
        } catch (JVisaException e) {
            log.error("SDS6000X getFullTime error", e);
        }
        return -1;
    }

    @Override
    public float getTopValue(Integer deviceChannel) {
        try {
            long startMills = System.currentTimeMillis();
            String top = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
            float topValue = scientificToFloat(top);
            //如何打印scientificToFloat(duty);的耗时
            log.info("SDS6000X getTopValue: {},耗时：{}ms", topValue, System.currentTimeMillis() - startMills);
            return topValue;
        } catch (JVisaException e) {
            log.error("SDS6000X getTopValue error", e);
        }
        return -1;
    }

    @Override
    public float getBaseValue(Integer deviceChannel) {
        try {
            long startMills = System.currentTimeMillis();
            String base = sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.FETCH_MEASURE_VALUE, deviceChannel));
            float baseValue = scientificToFloat(base);
            //如何打印scientificToFloat(duty);的耗时
            log.info("SDS6000X getBaseValue: {},耗时：{}ms", baseValue, System.currentTimeMillis() - startMills);
            return baseValue;
        } catch (JVisaException e) {
            log.error("SDS6000X getBaseValue error", e);
        }
        return -1;
    }


    private void sendCommand(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        instrument.write(command);
    }

    public static void main(String[] args) throws RuntimeException, JVisaException, DeviceCloseException {
//        SDS6000XOscilloscope sds6000XOscilloscope = new SDS6000XOscilloscope(new DeviceOperationParameter());
////        String s = "USB0::0xF4EC::0xEE38::SDS5XCBX3R0127::INSTR";  //SDS5000X
//        String s = "USB0::0xF4EC::0x1013::SDS6PCDD7R0318::INSTR";   //SDS6000X
//        sds6000XOscilloscope.setDeviceName(s);
//        try {
//            sds6000XOscilloscope.open();
//            String IDN = sds6000XOscilloscope.sendAndReceiveString(SDSSCPICommands.formatCommand(SDSSCPICommands.IDN));
//            System.out.println("IDN--" + IDN);
//            Thread.sleep(2000); // 等待自动配置
//            String duty1 = sds6000XOscilloscope.sendAndReceiveString(":MEASure:ADVanced:P1:VALue?\n");
//            String duty2 = sds6000XOscilloscope.sendAndReceiveString(":MEASure:ADVanced:P2:VALue?\n");
//            System.out.println(String.format("Duty Cycle1: ") + scientificToFloat(duty1) + " %");
//            System.out.println(String.format("Duty Cycle2: ") + scientificToFloat(duty2) + " %");
//        } catch (DeviceOpenException | DeviceOpenRepeatException e) {
//            throw new RuntimeException(e);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        } finally {
//            sds6000XOscilloscope.close();
//        }
        String duty = "2E-01";
        String duty2 = "无效值";
        String duty3 = "****";

        boolean containsDigit1 = duty.matches(".*[0-9].*"); // 核心正则表达式
        boolean containsDigit2 = duty2.matches(".*[0-9].*"); // 核心正则表达式
        boolean containsDigit3 = duty3.matches(".*[0-9].*"); // 核心正则表达式
        System.out.println("containsDigit1: " + containsDigit1);
        System.out.println("containsDigit2: " + containsDigit2);
        System.out.println("containsDigit3: " + containsDigit3);
    }
}
