package com.desaysv.workserver.devices.usbswtich;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class UsbSwitchDevice extends PortDevice {

    public UsbSwitchDevice() {
        this(new DeviceOperationParameter());
    }

    public UsbSwitchDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_USB_SWITCH;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.UsbSwitch.USB_SWITCH;
    }

    public void open1() throws DeviceSendException {
        send("open1");
    }


    public void open2() throws DeviceSendException {
        send("open2");
    }

    public void open3() throws DeviceSendException {
        send("open3");
    }

    public void open4() throws DeviceSendException {
        send("open4");
    }

    public void open5() throws DeviceSendException {
        send("open5");
    }

    public void closeAllChannels() throws DeviceSendException {
        send("close");
    }

}
