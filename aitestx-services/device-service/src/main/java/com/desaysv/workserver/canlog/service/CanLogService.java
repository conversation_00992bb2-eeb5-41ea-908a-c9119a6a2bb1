package com.desaysv.workserver.canlog.service;

import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.queue.FixedSizeQueue;
import com.desaysv.workserver.devices.bus.nican.CanMessageVo;

import java.util.List;

public interface CanLogService {

    /**
     * 开启实时保存
     */
    void startRealTimeData();

    /**
     * 结束实时保存
     */
    void stopRealTimeData();


    /**
     * 开始保存添加到脚本
     */
    void startCaptureCanLog();

    /**
     * 停止保存添加到脚本
     */
    void stopCaptureCanLog();

    /**
     * 实时保存数据方法，需要在数据更新处调用
     *
     * @param canMessages
     */
    void realTimeSaveData(List<CanMessageVo> canMessages);

    /**
     * 保存帧日志（最多保存9999条）
     *
     * @throws BlfException
     */
    void saveLog(FixedSizeQueue<CanMessageVo> fixedQueue) throws BlfException;
}
