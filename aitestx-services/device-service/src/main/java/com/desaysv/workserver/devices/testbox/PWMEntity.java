package com.desaysv.workserver.devices.testbox;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/9 15:22
 * @description :
 * @modified By :
 * @since : 2023/6/9
 **/
@Data
public class PWMEntity {
    private float frequency; //Hz
    private float dutyCycle; //%

    public PWMEntity(float frequency, float dutyCycle) {
        this.frequency = frequency;
        this.dutyCycle = dutyCycle;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PWMEntity pwmEntity = (PWMEntity) o;
        return Float.compare(pwmEntity.frequency, frequency) == 0 && Float.compare(pwmEntity.dutyCycle, dutyCycle) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(frequency, dutyCycle);
    }
}
