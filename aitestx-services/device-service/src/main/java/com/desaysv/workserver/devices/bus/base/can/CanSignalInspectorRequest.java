package com.desaysv.workserver.devices.bus.base.can;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CanSignalInspectorRequest {

    private String messageName;

    private List<SignalMeasurement> signalMeasurements = new ArrayList<>();

    private int timeout;

    public void setMessageName(String messageName) {
        this.messageName = messageName != null ? messageName.trim() : null;
    }

}
