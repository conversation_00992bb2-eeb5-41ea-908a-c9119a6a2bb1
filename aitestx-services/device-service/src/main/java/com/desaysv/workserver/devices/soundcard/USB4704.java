package com.desaysv.workserver.devices.soundcard;

import Automation.BDaq.DeviceInformation;
import com.desaysv.Usb4704RecorderNew;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.soundcard.interfaces.ISound;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.entity.MonitorDataPackage;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.manager.SoundFileManager;
import com.desaysv.workserver.monitor.MeasureIndicator;
import com.desaysv.workserver.monitor.SoundMeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
public class USB4704 extends Device implements SoundMeasurementComparator, ISound {
    private Usb4704RecorderNew recorder;
    private volatile boolean isRecording = false;
    // 使用线程安全的集合
    private final Map<Integer, List<Double>> recordedVolumes = new ConcurrentHashMap<>();
    private final Map<Integer, List<Double>> recordedDecibels = new ConcurrentHashMap<>();
    private ScheduledExecutorService scheduler;
    private ScheduledFuture<?> volumeMonitoringTaskFuture;
    private static final int START_CHANNEL = 0; // 起始通道号（0表示第1个通道）
    private static final int SAMPLE_RATE = 44100; // 采样率
    private boolean isInitialized = false;
    private boolean isSchedulerShutdown = false;
    // 添加锁对象
    private final Object recordingLock = new Object();

    public USB4704() {
        this(new DeviceOperationParameter());
    }

    // 在构造函数中添加设备参数配置
    public USB4704(DeviceOperationParameter params) {
        super(params);
        recorder = new Usb4704RecorderNew();
        initScheduler();
    }

    private synchronized void initScheduler() {
        if (scheduler == null || isSchedulerShutdown) {
            scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread thread = new Thread(r, "USB4704-Scheduler");
                thread.setPriority(Thread.NORM_PRIORITY - 1); // 设置线程优先级降低一级
                return thread;
            });
            isSchedulerShutdown = false;
        }
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isInitialized) {
            log.info("USB4704设备已初始化");
            return true;
        }

        try {
            recorder.init(SAMPLE_RATE, START_CHANNEL); // 初始化硬件设备
            isInitialized = true;
            initScheduler(); // 确保调度器可用
            log.info("USB4704设备初始化成功");
            return true;
        } catch (Exception e) {
            throw new DeviceOpenException("USB4704设备打开失败: " + e.getMessage());
        }
    }

    @Override
    public void startRecording(Integer deviceChannel) {
        //获取当前时间
        long startTime = System.currentTimeMillis();
        if (!isInitialized && !isSimulated()) {
            try {
                open();
            } catch (DeviceOpenException | DeviceOpenRepeatException e) {
                log.error("USB4704设备初始化失败: {}", e.getMessage());
                return;
            }
        }

        // 确保调度器可用
        initScheduler();

        synchronized (recordingLock) {
            isRecording = true;
            // 使用CopyOnWriteArrayList作为内部列表
            recordedVolumes.putIfAbsent(deviceChannel, new CopyOnWriteArrayList<>());
            recordedVolumes.get(deviceChannel).clear();
            recordedDecibels.putIfAbsent(deviceChannel, new CopyOnWriteArrayList<>());
            recordedDecibels.get(deviceChannel).clear();
        }

        // 启动音量监控任务
        try {
            volumeMonitoringTaskFuture = scheduler.scheduleAtFixedRate(() -> {
                if (!isRecording) return;
                try {
                    double[] currentVolumes = recorder.getCurrentVolume();
                    List<Double> channelVolumes = recordedVolumes.get(deviceChannel);
                    for (double volume : currentVolumes) {
                        channelVolumes.add(volume);
                    }
                    double decibels = recorder.calculateDecibels(currentVolumes);
                    log.info("通道{}当前音量: {}dB", deviceChannel, decibels);
                    recordedDecibels.get(deviceChannel).add(decibels);
                    //当前音量存入recordedDecibels
//                    //等待500ms之后再记录
//                    if(System.currentTimeMillis() - startTime > 500){
//                        recordedDecibels.get(deviceChannel).add(decibels);
//                    }
                } catch (Exception e) {
                    log.error("获取音量时发生异常: {}", e.getMessage());
                }
            }, 0, 10, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("启动音量监控任务失败: {}", e.getMessage());
        }
    }

    @Override
    public float fetchVolume(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        return 0.0f;
    }

    @Override
    public String saveRecordingToFile() {
        SoundFileManager soundFileManager = ProjectFileManager.of(SoundFileManager.class);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String formattedDate = dateFormat.format(new Date());
        String fileName = formattedDate + ".wav";

        Integer channel = getDevice().getChannel();
        List<Double> volumes = recordedVolumes.get(channel);

        if (volumes == null || volumes.isEmpty()) {
            log.warn("没有可保存的录音数据");
            return "没有可保存的录音数据";
        }

        // 创建副本避免并发修改
        List<Double> volumesCopy = new ArrayList<>(volumes);
        recorder.saveAsWav(fileName, soundFileManager.getFailSoundFileFolderPath(), volumesCopy, SAMPLE_RATE);

        log.info("录音文件保存成功 文件名：{} 路径:{}", fileName, soundFileManager.getFailSoundFileFolderPath());
        return String.format("录音文件保存成功 文件名：%s 路径:%s", fileName, soundFileManager.getFailSoundFileFolderPath());
    }


    @Override
    public void stopRecording() {
        synchronized (recordingLock) {
            isRecording = false;
        }

        if (recorder != null) {
            try {
                recorder.stopRecord();
            } catch (Exception e) {
                log.error("停止录音失败: {}", e.getMessage());
            }
        }

        if (volumeMonitoringTaskFuture != null) {
            volumeMonitoringTaskFuture.cancel(false);
            volumeMonitoringTaskFuture = null;
        }
    }

    @Override
    public void beginSoundMonitor(Integer deviceChannel) {
        try {
            open();
        } catch (DeviceOpenException | DeviceOpenRepeatException e) {
            log.error(e.getMessage(), e);
        }
        log.info("开始声音监控");
        startRecording(deviceChannel);
    }

    @Override
    public boolean endSoundMonitor(Integer deviceChannel, SoundMonitor soundMonitor) {
        log.info("结束声音监控，表达式: {}, 要求数量: {}", soundMonitor.getExpression(), soundMonitor.getRequiredNums());
        // 停止录音
        stopRecording();

        int matchCount = 0; // 记录满足条件的声音数据数量

        // 使用ConcurrentHashMap不需要额外同步，直接创建副本
        Map<Integer, List<Double>> volumesCopy = new HashMap<>();
        for (Map.Entry<Integer, List<Double>> entry : recordedDecibels.entrySet()) {
            volumesCopy.put(entry.getKey(), new ArrayList<>(entry.getValue()));
        }

        // 遍历所有通道的音量记录
        for (Map.Entry<Integer, List<Double>> entry : volumesCopy.entrySet())  {
            Integer channel = entry.getKey();
            List<Double> volumes = entry.getValue();

            if (volumes.isEmpty()) {
                log.warn("声卡通道 {} 没有记录任何音量数据", channel);
                continue;
            }

            // 使用表达式判断每个声音数据是否满足条件
            String condition = soundMonitor.getExpression();

            for (Double volume : volumes) {
                boolean pass;

                if (condition.contains("&") || condition.contains("|")) {
                    // 处理逻辑同之前，但这里直接用volume代替了maxVolume
                    if (condition.contains("&") && !condition.contains("|")) {
                        String[] expressions = condition.split("&");
                        pass = true;
                        for (String expr : expressions) {
                            boolean resultOk = StrUtils.judgeExpressResult(volume + expr);
                            if (!resultOk) {
                                pass = false;
                                break;
                            }
                        }
                    } else if (condition.contains("|") && !condition.contains("&")) {
                        String[] expressions = condition.split("\\|");
                        pass = false;
                        for (String expr : expressions) {
                            boolean resultOk = StrUtils.judgeExpressResult(volume + expr);
                            if (resultOk) {
                                pass = true;
                                break;
                            }
                        }
                    } else {
                        String fullExpr = condition;
                        String[] operators = {">=", "<=", "==", "!=", ">", "<"};
                        for (String op : operators) {
                            fullExpr = fullExpr.replace(op, volume + op);
                        }
                        pass = StrUtils.judgeExpressResult(fullExpr);
                    }
                } else {
                    pass = StrUtils.judgeExpressResult(volume + condition);
                }

                if (pass) {
                    matchCount++;
//                    log.info("通道 {} 的音量 {}dB 满足条件: {}", channel, volume, condition);
                }
            }
        }

        // 判断满足条件的声音数据数量是否达到要求
        if (matchCount >= soundMonitor.getRequiredNums()) {
            log.info("声音监控成功: 满足条件的声音数据数量 {} 达到要求数量 {}", matchCount, soundMonitor.getRequiredNums());
            return true;
        } else {
            log.info("声音监控失败: 满足条件的声音数据数量 {} 未达到要求数量 {}", matchCount, soundMonitor.getRequiredNums());
            saveRecordingToFile(); // 保存失败的声音文件
            return false;
        }
    }


    @Override
    public List<Float> getRecordedVolumes(Integer deviceChannel) {
        return recordedDecibels.getOrDefault(deviceChannel, new ArrayList<>())
                .stream()
                .map(Double::floatValue)
                .collect(Collectors.toList());
    }

    @Override
    public float getVolume(Integer deviceChannel) throws DeviceReadException {
        return 0;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SOUND_CARD;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.SoundCard.USB4704_DEVICE;
    }


    @Override
    public synchronized boolean close() throws DeviceCloseException {
        try {
            stopRecording();
            
            if (isInitialized && recorder != null) {
                try {
                    recorder.close();
                } catch (Exception e) {
                    log.error("关闭录音设备失败: {}", e.getMessage());
                }
                isInitialized = false;
            }
            
            if (scheduler != null && !isSchedulerShutdown) {
                try {
                    scheduler.shutdown();
                    if (!scheduler.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                        scheduler.shutdownNow();
                    }
                    isSchedulerShutdown = true;
                } catch (Exception e) {
                    log.error("关闭调度器失败: {}", e.getMessage());
                }
            }
            
            return true;
        } catch (Exception e) {
            throw new DeviceCloseException("关闭设备失败: " + e.getMessage());
        }
    }

    public List<DeviceInformation> listSupportedDevices() {
        List<DeviceInformation> devices = new ArrayList<>();
        try {
            // 使用研华SDK创建设备信息对象
            DeviceInformation devInfo = new DeviceInformation("USB-4704,BID#0");
            if (isSimulated()) {
                devices.add(devInfo);
            } else {
                // 验证设备存在性（需要实际硬件支持）
                if (isDeviceAvailable()) {
                    devices.add(devInfo);
                    log.info("发现USB-4704设备: {}", devInfo.Description);
                }
            }
        } catch (Exception e) {
            log.error("设备检测异常", e);
        }
        return devices;
    }

    private boolean isDeviceAvailable() {
        try {
            Usb4704RecorderNew tempRecorder = new Usb4704RecorderNew();
            // 通过尝试初始化验证设备存在
            tempRecorder.init(SAMPLE_RATE, START_CHANNEL); // 需在Usb4704Recorder中添加带参数的init方法
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    public OperationResult monitorVolume(Integer deviceChannel, MonitorDataPackage monitorDataPackage) {
        return measurementMonitor(deviceChannel, MeasureIndicator.VOLUME, monitorDataPackage);
    }

    public OperationResult soundCapture(Integer deviceChannel) {
        log.info("开始获取声卡 {} 的音量", deviceChannel);
        OperationResult operationResult = new OperationResult();
        double decibels = 0;

        synchronized (recordingLock) {
            if (isRecording) {
                List<Double> decibelsList = recordedDecibels.get(deviceChannel);
                if (decibelsList != null && !decibelsList.isEmpty()) {
                    // 安全地获取最后一个元素
                    decibels = decibelsList.get(decibelsList.size() - 1);
                } else {
                    log.warn("通道 {} 没有记录的音量数据", deviceChannel);
                }
            } else {
                try {
                    double[] volume = recorder.getCurrentVolume();
                    decibels = recorder.calculateDecibels(volume);
                } catch (Exception e) {
                    log.error("音量获取过程中发生异常: {}", e.getMessage());
                    operationResult.setOk(false);
                    operationResult.setMessage(e.getMessage());
                    return operationResult;
                }
            }
        }

        operationResult.setData(decibels);
        operationResult.setOk(true);
        operationResult.setMessage("音量获取成功");
        return operationResult;
    }

    public OperationResult calibrateReferenceValue() {
        OperationResult operationResult = new OperationResult();
        try {
            double calibrateReferenceValue = recorder.calibrateReferenceValue();
            operationResult.setOk(true);
            operationResult.setMessage("校准成功");
            operationResult.setData(calibrateReferenceValue);
        } catch (Exception e) {
            log.error("校准过程中发生异常: {}", e.getMessage());
        }
        return operationResult;
    }

    public OperationResult setReferenceValue(double referenceValue) {
        OperationResult operationResult = new OperationResult();
        try {
            recorder.setReferenceValue(referenceValue);
            operationResult.setOk(true);
            operationResult.setMessage("设置成功");
        } catch (Exception e) {
            log.error("设置过程中发生异常: {}", e.getMessage());
        }
        return operationResult;
    }

    @Override
    public boolean detectBeepFrequency(Integer deviceChannel, double recordSeconds, double minFrequency, double maxFrequency) {
        SoundFileManager soundFileManager = ProjectFileManager.of(SoundFileManager.class);
        return recorder.detectBeepFrequency(soundFileManager.getFailSoundFileFolderPath(), recordSeconds, minFrequency, maxFrequency);
    }

    @Override
    public boolean detectBeeperCycle(Integer deviceChannel, double threshold, int minIntervalMillis, double recordSeconds, double targetPeriod) {
        SoundFileManager soundFileManager = ProjectFileManager.of(SoundFileManager.class);
        return recorder.detectBeeperCycle(soundFileManager.getFailSoundFileFolderPath(), threshold, minIntervalMillis, recordSeconds, targetPeriod);
    }

    public static void main(String[] args) {
        USB4704 device = new USB4704(new DeviceOperationParameter());
        List<DeviceInformation> devices = device.listSupportedDevices();

        System.out.println("检测到" + devices.size() + "个USB4704设备：");
        devices.forEach(d ->
                System.out.printf("设备号:%d 描述:%s%n",
                        d.DeviceNumber,
                        d.Description
                )
        );
    }

}
