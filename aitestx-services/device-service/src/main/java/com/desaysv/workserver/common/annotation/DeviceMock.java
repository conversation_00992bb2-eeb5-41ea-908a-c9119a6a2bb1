package com.desaysv.workserver.common.annotation;

import com.desaysv.workserver.devices.power.protocol.CommProtocol;

import java.lang.annotation.*;

@Documented
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface DeviceMock {

    int numbers() default 1;

    OperationTargetAliasMap[] alias() default {};

    String commProtocol() default CommProtocol.RS232;

    boolean enable() default true;
}
