package com.desaysv.workserver.cicd;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.electric_relay.ChannelSwitch;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.manager.DeviceManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@Data
public class AutoUpgradeService {

    @Autowired
    private RestTemplate restTemplate;

    private final DeviceManager deviceManager;

    private String getCiBuildInfoFailMessage= ""; //记录获取CiBuildInfo失败的信息

    private List<String> upgradeResultList = new ArrayList<>(); //记录升级结果

    private String filePath = "";//记录升级结果文件路径

    private String jenkinsJobUrl = "";

    private Path sourceFile;//记录源文件路径

    private String upgradeSuccess = "fail";

    private boolean copySuccess = false;//记录拷贝文件是否成功

    private final DeviceOperateUtil deviceOperateUtil;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private int retryCount = 3;//最大重试次数

    private int currentRetryCount = 3;//记录当前可允许的重试次数

    private CiBuildInfo buildInfo;//记录获取的构建信息

    private List<Device> usbSwitches;//用到的USB切换板

    private List<Device> portDevices;//用到的串口

    @Autowired
    public AutoUpgradeService(DeviceManager deviceManager) {
        this.deviceManager = deviceManager;
        deviceOperateUtil = new DeviceOperateUtil(deviceManager);
    }

    /**
     * 自动升级
     */
    public UpgradeResponse autoUpgrade() {
        UpgradeResponse upgradeResponse = new UpgradeResponse();
        boolean overallSuccess = false;
        BuildInfoResponse buildInfoResponse = null;
        // 外层全局重试循环
        for (int attempt = 0; attempt < retryCount; attempt++) {
            upgradeResponse = new UpgradeResponse(); // 每次重试创建新响应对象
            if (!copySuccess){
                // 1.获取构建信息
                buildInfoResponse = fetchBuildInfo();
                if ("403".equals(getCiBuildInfoFailMessage)) {
                    break;
                }
                if (buildInfoResponse == null) {
                    log.warn("获取构建信息失败，重试次数：{}",  attempt+1);
                    continue; // 直接进入下次重试
                }

                // 2.下载安装包
                String dsvInnerPath = buildInfo.getDsvInnerPath();
                String name = buildInfo.getName();
                sourceFile = Paths.get(dsvInnerPath, name).normalize();
                filePath  = sourceFile.toString();
                jenkinsJobUrl = buildInfo.getJenkinsJobUrl();
                upgradeResponse = downloadPackage(upgradeResponse);
                if (!(upgradeResponse.getErrorMessage() == null || upgradeResponse.getErrorMessage().isEmpty())) {
                    log.warn("下载安装包失败，重试次数：{}，错误信息：{}", attempt+1, upgradeResponse.getErrorMessage());
                    continue;
                }
            }
            // 3.升级流程（合并两个阶段）
            if (!executeUpgradeProcess(upgradeResponse)) {
                log.warn("升级过程失败，重试次数：{}，错误信息：{}", attempt+1, upgradeResponse.getErrorMessage());
                continue;
            }
            overallSuccess = true;
            break; // 全部成功则跳出循环
        }
        if (!overallSuccess) {
            getCiBuildInfoFailMessage = "403";
            if (buildInfo != null){
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "全局重试次数耗尽" + getCiBuildInfoFailMessage);
            }else {
                return autoUpgradeResultReport(upgradeResponse, "NA", "全局重试次数耗尽" + getCiBuildInfoFailMessage);
            }
        }
        return upgradeResponse;
    }

    private boolean executeUpgradeProcess(UpgradeResponse upgradeResponse) {
        upgradeResponse = controlRelaySwitche(upgradeResponse);
        if (upgradeResponse.getErrorMessage() == null || upgradeResponse.getErrorMessage().isEmpty()){
            // 3.1 执行升级流程
            upgradeResponse = upgradeProcess(upgradeResponse);
            if (upgradeResponse.getErrorMessage() == null || upgradeResponse.getErrorMessage().isEmpty()) {
                upgradeResponse = judgeVerision(upgradeResponse);
                if (upgradeResponse.getErrorMessage() == null || upgradeResponse.getErrorMessage().isEmpty()) {
                    return true;
                }else return false;
            }else return false;
        }else return false;
    }

    public static void  main(String[] args) throws Exception {
//        findUDiskPath();
//        uploadResult("{\"result\":\"success\"}");
        String dsvInnerPath = "\\\\*************\\dida2001\\Chery\\8255\\E01\\chery.D_2025_06_19_11_10_16_804\\02_在线软件";
        String name = "G9SH_CHERY_CHN_E01_FULL_1940_250619_D.zip";
        File sourceFile = new File(dsvInnerPath, name);  // 使用File处理UNC路径
        Path targetDir = Paths.get("E:\\IC4565");
        FileUtil.copyLargeFile(sourceFile.toPath(), targetDir);
    }

    /**
     * 下载升级安装包全流程
     * @return
     */
    public UpgradeResponse downloadPackage(UpgradeResponse upgradeResponse) {
        usbSwitches = deviceOperateUtil.listUsbSwitches();// 获取所有USB切换板
        if (usbSwitches.isEmpty()) {return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "未找到可用的USB切换板设备");}
        try {//先关闭usb切换板的所有通道
            try {deviceOperateUtil.closeUsbSwitches(usbSwitches);}
            catch (Exception e) {return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "关闭USB切换板所有通道失败"+e.getMessage());}
            log.info("成功关闭USB切换板的所有通道");
            Thread.sleep(5000);//等待5秒
            if (!deviceOperateUtil.openUsbSwitches(usbSwitches, 5)) {// 打开通道5 切到电脑
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "打开USB切换板通道5失败");
            }
            log.info("成功打开USB切换板通道5");
            Thread.sleep(5000);//等待5秒
            List<String> uDiskPaths = FileUtil.findUDiskPath();// 获取U盘路径（取第一个检测到的U盘）
            if (uDiskPaths.isEmpty()) {return autoUpgradeResultReport( upgradeResponse, buildInfo.getModuleId(),"未检测到可用U盘");}
            String uDiskPath = uDiskPaths.get(0);
            log.info("检测到U盘路径: {}", uDiskPath);
            String targetDirName = "";//projectId格式为：E01-IC4565，提取-后面内容,作为目标文件夹名称
            String regex = "-([^\\s-]+)$";  // 匹配最后一个 '-' 后的所有字符直到字符串结束
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(buildInfo.getProjectId());
            if (matcher.find()) {
                targetDirName = matcher.group(1);  // 取出第一个分组的内容
                log.info("提取projectId中“-”后的内容: {}", targetDirName);
            } else {return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), String.format("提取projectId中“-”后的内容失败,projectId为：%s", buildInfo.getProjectId()));}
            Path targetDir = Paths.get(uDiskPath, targetDirName);
            log.info("目标文件夹路径: {}", targetDir);
            try {
                if (!Files.exists(targetDir)) {
                    Files.createDirectories(targetDir);
                    log.info("创建文件夹成功: {}", targetDir);
                } else {
                    log.info("文件夹已存在: {}", targetDir);
                    FileUtil.deleteDirectoryContents(targetDir);
                }
            } catch (IOException e) {
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "在U盘下创建文件夹失败" + e.getMessage());
            }
            try {//拷贝文件
                FileUtil.copyLargeFile(sourceFile, targetDir);
                log.info("升级安装包文件拷贝成功");
                copySuccess = true;
            } catch (IOException e) {
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "安装包文件拷贝失败" + e.getMessage());
            }
        }catch (Exception e) {
            return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "下载安装包流程失败"+e.getMessage());
        }
        return upgradeResponse;
    }

    /**
     * 升级流程(包括切换到车机、监听串口判断是否升级完成)
     * @return
     */
    public UpgradeResponse upgradeProcess(UpgradeResponse upgradeResponse) {
        try{
            //6. 获取所有串口
            portDevices = deviceOperateUtil.listSerialPorts();
            if (portDevices.isEmpty()) {
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "未找到可用的串口设备");
            }
            Thread.sleep(5000);//等待5秒
            try {deviceOperateUtil.closeUsbSwitches(usbSwitches);}//先关闭再打开
            catch (Exception e) {return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "关闭USB切换板所有通道失败"+e.getMessage());}
            log.info("成功关闭USB切换板的所有通道");
            Thread.sleep(5000);//等待5秒
            // 7. 切换至通道4 车机
            if (!deviceOperateUtil.openUsbSwitches(usbSwitches, 4)) {
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "打开USB切换板通道4失败");
            }
            log.info("成功切换至USB切换板通道4");
            Thread.sleep(5000);//等待5秒
            try{// 启动监控,等待出现设置18分钟的超时时间
                deviceOperateUtil.monitorSerialPort(portDevices, "start service success");
            }catch (OperationFailNotification e){
                log.info("未监控到start service success");
//                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(),"未监控到start service success，升级流程失败");
            }
        }catch (Exception e){
            return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "升级流程失败"+e.getMessage());
        }
        return upgradeResponse;
    }

     /**
     * 判断版本信息
     * 发送版本信息查询命令并保存结果，需要获取的有QNX、SOC、MCU版本信息，命令分别是QNX：cat /etc/version_extra;
     * 1.dtach -a /tmp/android 切安卓 su 管理员
     * 2.1 MCU: getprop sys.vehicle.mcu.version 2.2 SOC: getprop ro.build.display.id
     * 并且进行对比版本信息，如果一致则升级成功，不一致则升级失败
     * @return
     */
    public UpgradeResponse judgeVerision(UpgradeResponse upgradeResponse) {
        try{
            upgradeResponse.setVersionInfo(buildInfo.getModuleId());
            String qnxVersionInfo = null;// QNX版本查询
            try{qnxVersionInfo = deviceOperateUtil.sendAndReceiveToSerialPort(portDevices, "cat /etc/version_extra", buildInfo.getQnxVersion());
            }catch (OperationFailNotification e){
                log.error("判断QNX版本信息失败: {}", e.getMessage());
                String pattern = "QNX_VERSION\\s*:\\s*([^\n\r]+).*?QNX_BUILT\\s*:\\s*([^\n\r]+)";
                Pattern regex = Pattern.compile(pattern, Pattern.DOTALL);
                Matcher matcher = regex.matcher(e.getMessage());
                qnxVersionInfo = matcher.find() ? matcher.group(1).trim() : e.getMessage();
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), String.format("QNX版本与预期版本不一致，预期版本：%s，实际版本：%s", buildInfo.getQnxVersion(), qnxVersionInfo));
            }log.info("获取到的QNX版本信息为: {}", qnxVersionInfo);
            upgradeResponse.setQnxVersionInfo(qnxVersionInfo);
            try {deviceOperateUtil.sendToSerialPort(portDevices, "dtach -a /tmp/android");//切安卓
            }catch (Exception e){return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "切安卓失败"+e.getMessage());}
            try {deviceOperateUtil.sendToSerialPort(portDevices, "su");//切管理员
            }catch (Exception e){return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "切管理员失败"+e.getMessage());}
            String patternString = "\\.version\\s*:\\s*([^\\]\\s]+)";//提取异常信息中的版本信息
            String mcuVersionInfo = null;// MCU版本查询
            try {mcuVersionInfo = deviceOperateUtil.sendAndReceiveToSerialPort(portDevices, "getprop sys.vehicle.mcu.version", buildInfo.getMcuVersion());
            }catch (OperationFailNotification e){
                log.error("判断MCU版本信息失败: {}", e.getMessage());
                Pattern regex = Pattern.compile(patternString, Pattern.DOTALL);
                Matcher matcher = regex.matcher(e.getMessage());
                mcuVersionInfo = matcher.find() ? matcher.group(1).trim() : e.getMessage();
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(),String.format("MCU版本与预期版本不一致，预期版本：%s，实际版本：%s", buildInfo.getMcuVersion(), mcuVersionInfo));
            }log.info("获取到的MCU版本信息为: {}", mcuVersionInfo);
            upgradeResponse.setMcuVersionInfo(mcuVersionInfo);
            String socVersionInfo = null;// SOC版本查询
            try {socVersionInfo = deviceOperateUtil.sendAndReceiveToSerialPort(portDevices, "getprop ro.build.display.id", buildInfo.getSocVersion());
            }catch (OperationFailNotification e){
                log.error("判断SOC版本信息失败: {}", e.getMessage());
                Pattern regex = Pattern.compile(patternString, Pattern.DOTALL);
                Matcher matcher = regex.matcher(e.getMessage());
                socVersionInfo = matcher.find() ? matcher.group(1).trim() : e.getMessage();
                return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(),String.format("SOC版本与预期版本不一致，预期版本：%s，实际版本：%s", buildInfo.getSocVersion(), socVersionInfo));
            }log.info("获取到的SOC版本信息为: {}", socVersionInfo);
            upgradeResponse.setSocVersionInfo(socVersionInfo);
            log.info("版本信息与预期版本信息一致，升级成功!!");
            upgradeResponse.setSuccess(true);
            upgradeResultList.add("版本信息与预期版本信息一致，升级成功!!");
            upgradeSuccess = "success";
            upgradeResultReportCloudDc(new UpgradeResultReportDto(upgradeResultList, filePath, jenkinsJobUrl, upgradeSuccess));
            try {uploadUpgradeResult("{\"result\":\"升级成功！\"}");
            }catch (Exception e1){log.error("上传升级结果失败", e1);}
        } catch (Exception e) {
            if(buildInfo.getModuleId()==null){return autoUpgradeResultReport(upgradeResponse, "NA", "未知错误: " + e.getMessage());
            }else {return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "未知错误: " + e.getMessage());}
        }finally {
            try{deviceOperateUtil.closeUsbSwitches(usbSwitches);//关闭usb切换板的所有通道
            }catch (Exception e){log.error("关闭USB切换板所有通道失败", e);}
            log.info("成功关闭USB切换板所有通道");
        }return upgradeResponse;
    }

    /**
     * 控制继电器
     * 关闭继电器通道3，等待30s后再打开继电器通道3，即一次上下电
     * @return
     */
    public UpgradeResponse controlRelaySwitche(UpgradeResponse upgradeResponse){
        List<Device> relaySwitches = deviceOperateUtil.listRelays();//查找所有继电器
        if (relaySwitches.isEmpty()) {
            return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "未找到可用的继电器设备");
        }
        try {
            deviceOperateUtil.operateRelays(relaySwitches, new ChannelSwitch(2, false, "蓄电池KL30", 0 ));
        }
        catch (Exception e){
            return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "继电器关闭失败" + e.getMessage());
        }
        log.info("关闭继电器通2成功！");
        //等待10s
        log.info("等待10s");
        try {
            Thread.sleep(10000);
        }catch (InterruptedException e){
            log.error("上电之前等待10s失败:{}", e.getMessage());
        }
        try{
            deviceOperateUtil.operateRelays(relaySwitches, new ChannelSwitch(2, true, "蓄电池KL30", 0 ));
        }
        catch (Exception e){
            return autoUpgradeResultReport(upgradeResponse, buildInfo.getModuleId(), "继电器打开失败" + e.getMessage());
        }
        log.info("打开继电器通2成功！");
        //等待40s
        log.info("等待40s");
        try {
            Thread.sleep(40000);
        }catch (InterruptedException e){
            log.error("上电之后等待40s失败:{}", e.getMessage());
        }
        return upgradeResponse;
    }


    /**
     * 从python进程获取构建信息
     */
    public BuildInfoResponse fetchBuildInfo() {
        log.info("正在获取构建信息");
        try {
            URL url = new URL("http://localhost:5000/get_artifact_url");
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                log.info("获取到构建信息：{}", response);
                String json = response.toString(); // 从 fetchArtifacts() 方法中获取的响应
                ObjectMapper objectMapper = new ObjectMapper();
                BuildInfoResponse buildInfoResponse = objectMapper.readValue(json, BuildInfoResponse.class);
                buildInfo  = buildInfoResponse.getCiBuildInfo();
                log.info("解析构建信息成功！：{}", buildInfo);
                return buildInfoResponse;
            } else {
                log.error("获取构建信息失败，状态码：{}", responseCode);
                if (responseCode == 403){
                    getCiBuildInfoFailMessage = "403";
                    upgradeResultList.add("接口访问已达3次上限,任务结束,轮询已被重置，构建信息已清空，无法获取！");
                    log.error("获取构建信息接口访问已达3次上限");
                    upgradeResultReport(new UpgradeResultReportDto("", "NA", "", "", "接口访问已达3次上限,任务结束,轮询已被重置，构建信息已清空，无法获取！！"));
                    try {
                        uploadUpgradeResult("{\"result\":\"升级失败，因为接口访问已达3次上限,任务结束,轮询已被重置，构建信息已清空，无法获取！！\"}");
                    }catch (Exception e){
                        log.error("上传升级结果失败", e);
                    }
                }
                if (responseCode == 404){
                    log.error("获取构建信息接口访问失败");
                    upgradeResultList.add("获取构建信息接口访问失败");
                    upgradeResultReport(new UpgradeResultReportDto("", "NA", "", "", "无测试任务，获取构建信息接口不可达，失败！！"));
                    try {
                        uploadUpgradeResult("{\"result\":\"升级失败，因为无测试任务，获取构建信息接口不可达，失败！！\"}");
                    }catch (Exception e){
                        log.error("上传升级结果失败", e);
                    }
                }
            }
        }catch (Exception e){
            log.error("获取构建信息失败", e);
        }
        return null;
    }

    public UpgradeResponse autoUpgradeResultReport(UpgradeResponse upgradeResponse,String moduleId,String errorMessage) {
        log.error(errorMessage);
        upgradeResultList.add(errorMessage);
        upgradeResponse.setErrorMessage(errorMessage);
        upgradeResultReport(new UpgradeResultReportDto("", moduleId, "", "", errorMessage));
        //上传升级结果到python进程
        try {
            Map<String, String> resultMap = new HashMap<>();
            resultMap.put("result", "升级失败，因为"+errorMessage+"(3次重试机制)");
            String json = objectMapper.writeValueAsString(resultMap);
            uploadUpgradeResult(json);
        }catch (Exception e){
            log.error("上传升级结果失败", e);
        }
        return upgradeResponse;
    }

    /**
     * 上传升级结果
     *
     * @param resultJson 升级结果JSON
     * @throws Exception 异常
     */
    public static void uploadUpgradeResult(String resultJson) throws Exception {
        URL url = new URL("http://localhost:5000/receive_upgrade_result");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = resultJson.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        int code = conn.getResponseCode();
        if (code == 200) {
            log.info("升级结果上传到cicd_tool成功！");
        } else {
            log.error("升级结果上传到cicd_tool失败，HTTP状态码：{}", code);
        }
    }

    /**
     * 升级结果报告邮件
     * @param dto 升级结果
     * @return boolean 成功与否
     */
    public boolean upgradeResultReportEmail(UpgradeResultReportDto dto) {
        String url = "http://127.0.0.1:12399/AITestX/actionSequence/mailUpgradeResult";

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("cicd流水线自动升级结果邮件发送请求成功");
                return true;
            } else {
                log.error("cicd流水线自动升级结果邮件发送失败，HTTP状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("cicd流水线自动升级结果通知，调用邮件接口失败", e);
            return false;
        }
    }

    /**
     * 升级结果报告机器人
     * @param dto 升级结果
     * @return boolean 成功与否
     */
    public boolean upgradeResultReportRobot(UpgradeResultReportDto dto) {
        String url = "http://127.0.0.1:12399/AITestX/actionSequence/robotUpgradeResult";

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("cicd流水线自动升级结果机器人发送请求成功");
                return true;
            } else {
                log.error("cicd流水线自动升级结果机器人发送失败，HTTP状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("cicd流水线自动升级结果通知调用机器人接口失败", e);
            return false;
        }
    }

    /**
     * 升级结果报告云文档
     * @param dto 升级结果
     * @return boolean 成功与否
     */
    public boolean upgradeResultReportCloudDc(UpgradeResultReportDto dto) {
        String url = "http://127.0.0.1:12399/AITestX/actionSequence/cloudDocUpgradeResult";

        try {
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, dto, String.class);

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                log.info("cicd流水线自动升级结果云文档发送请求成功");
                return true;
            } else {
                log.error("cicd流水线自动升级结果云文档发送失败，HTTP状态码: {}", responseEntity.getStatusCodeValue());
                return false;
            }
        } catch (Exception e) {
            log.error("cicd流水线自动升级结果通知调用云文档接口失败", e);
            return false;
        }
        finally {
            //清空升级结果列表和文件路径
            upgradeResultList.clear();
            filePath  = "";
        }
    }

    /**
     * 客户端报告升级结果到云文档
     * @return boolean 升级结果成功与否
     */
    public boolean clientReportUpgradeResultToCloudDc() {
        return upgradeResultReportCloudDc(new UpgradeResultReportDto(upgradeResultList, filePath, jenkinsJobUrl, upgradeSuccess));
    }

    /**
     *  升级结果处理
     * @param dto 升级结果
     */
    public void upgradeResultReport(UpgradeResultReportDto dto) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        dto.setEndTime(formatter.format(new Date()));
        log.info("cicd流水线自动升级结果通知邮件和机器人！");
        upgradeResultReportRobot(dto);
        if ("403".equals(getCiBuildInfoFailMessage)){
            // 使用 String.join() 方法将 List<String> 中的元素用分号拼接成一个字符串
            String errorMessage = String.join(";", upgradeResultList);
            dto.setErrorMessage(errorMessage);
            upgradeResultReportEmail(dto);
        }
    }

}
