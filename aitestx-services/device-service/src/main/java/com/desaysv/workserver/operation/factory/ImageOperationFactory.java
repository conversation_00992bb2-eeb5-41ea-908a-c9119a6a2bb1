package com.desaysv.workserver.operation.factory;

import com.desaysv.workserver.base.operation.base.OperationAbstractFactory;
import com.desaysv.workserver.base.operation.method.ImageOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.ImageOperationTarget;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.operation.parameter.ImageOperationParameter;
import com.desaysv.workserver.utils.MapToObj;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 14:53
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@Component
@Lazy
public class ImageOperationFactory implements OperationAbstractFactory {

    @Override
    public OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        OperationTarget imageOperationTarget;
        try {
            imageOperationTarget = MapToObj.mapToObj(operationTarget, ImageOperationTarget.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            imageOperationTarget = new ImageOperationTarget();
        }
        return imageOperationTarget;
    }

    @Override
    public OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        OperationMethod imageOperationMethod;
        try {
            imageOperationMethod = MapToObj.mapToObj(operationMethod, ImageOperationMethod.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            imageOperationMethod = new ImageOperationMethod();
        }
        return imageOperationMethod;
    }

    @Override
    public OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        OperationParameter imageOperationParameter;
        try {
            imageOperationParameter = MapToObj.mapToObj(operationParameter, ImageOperationParameter.class, 0);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            imageOperationParameter = new ImageOperationParameter();
            imageOperationParameter.putAll(operationParameter);
        }
        return imageOperationParameter;
    }
}
