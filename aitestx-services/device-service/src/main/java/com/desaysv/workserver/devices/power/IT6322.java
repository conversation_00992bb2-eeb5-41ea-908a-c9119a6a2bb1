package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.IT6322Protocol;
import com.desaysv.workserver.devices.power.protocol.PowerSCPIProtocol;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * IT6322电源
 */
@Slf4j
public class IT6322 extends DefaultPower {

    public IT6322() {
        this(new DeviceOperationParameter());
    }

    public IT6322(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        PowerSCPIProtocol powerProtocol = new IT6322Protocol(this);
        setPowerProtocol(powerProtocol);
    }

    @Override
    public int getNumberChannels() {
        return 3;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT6322;
    }

    public static void main(String[] args) throws DeviceReadException {
        new IT6322().setRampVoltage(0, 25, 2, "10s");
    }
}
