package com.desaysv.workserver.manager;

import com.desaysv.workserver.entity.Device;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.concurrent.LinkedBlockingDeque;

@Component
@Lazy
public class DeviceQueueManager {
    private final LinkedBlockingDeque<Device> deviceLinkedBlockingDeque;

    public DeviceQueueManager() {
        deviceLinkedBlockingDeque = new LinkedBlockingDeque<>();
    }
}

