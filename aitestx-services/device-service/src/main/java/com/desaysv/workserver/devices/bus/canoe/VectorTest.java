package com.desaysv.workserver.devices.bus.canoe;

import com.jacob.activeX.ActiveXComponent;
import com.jacob.com.Dispatch;
import com.jacob.com.Variant;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class VectorTest {

    public static void main(String[] args) {
        // 创建 CANoe Application 对象
        ActiveXComponent canApp = new ActiveXComponent("CANoe.Application");
        try {
            // 获取 CAPL 模块对象
            Dispatch system = Dispatch.call(canApp, "System").toDispatch();
            Dispatch namespaces = Dispatch.call(system, "Namespaces").toDispatch();  //获取变量组

            Dispatch ecuNode = Dispatch.call(namespaces, "Item", "XCP").toDispatch();
            Dispatch ecuNodeNamespaces = Dispatch.get(ecuNode, "Namespaces").toDispatch();  //获取变量组
            Dispatch conn = Dispatch.call(ecuNodeNamespaces, "Item", "GWM_AC").toDispatch();
            Dispatch connVariables = Dispatch.get(conn, "Variables").toDispatch();
            Dispatch sysECUAddress = Dispatch.call(connVariables, "Item", "IDX_CSC_EvapTempFbL").toDispatch();
            Variant sysECUAddressValue = Dispatch.get(sysECUAddress, "Value");
            System.out.println("sysECUAddressValue intValue:" + sysECUAddressValue);


            Dispatch namespace = Dispatch.call(namespaces, "Item", "ILcontrol").toDispatch();
            Dispatch variables = Dispatch.get(namespace, "Variables").toDispatch();
            //获取数值
//            Dispatch intVariable = Dispatch.call(variables, "Item", "ILcontrolMsgCycTime1").toDispatch();
            Dispatch intVariable = Dispatch.call(variables, "Item", "ILcontrolMsgCycTime").toDispatch();
            Variant intValue = Dispatch.get(intVariable, "Value");
            System.out.println("init intValue:" + intValue);
            Dispatch.put(intVariable, "value", 400.0);
//            Dispatch.put(intVariable, "value", 600.0);
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
            Variant intValueAfter = Dispatch.get(intVariable, "Value");
            System.out.println("after intValue:" + intValueAfter);

            //获取字符串
//            Dispatch stringVariable = Dispatch.call(variables, "Item", "ILcontrolMsgName1").toDispatch();
            Dispatch stringVariable = Dispatch.call(variables, "Item", "ILcontrolGetSigName").toDispatch();
            Variant stringValue = Dispatch.get(stringVariable, "Value");
            System.out.println("init stringValue:" + stringValue);
            Dispatch.put(stringVariable, "value", "MsgNew");
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }

            Variant stringValueAfter = Dispatch.get(stringVariable, "Value");
            System.out.println("after stringValue:" + stringValueAfter);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
