package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.common.scpi.ScpiCommandConstants;

/**
 * 适用于IT6922A/IT6932A/IT6933A/IT6942A/IT6952A/IT6953A/IT6922B/IT6932B/IT6942B/IT6952B
 */
public class IT69Protocol extends PowerSCPIProtocol {

    public IT69Protocol(PortDevice<?> device) {
        super(device);
    }

    @Override
    public boolean selectChannel(Integer channel) {
        if (channel != null) {
            return sendCommand(ScpiCommandConstants.selectChannel(channel, true));
        }
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand((isOutput ? ScpiCommandConstants.OUTPUT_ON : ScpiCommandConstants.OUTPUT_OFF) + ScpiCommandConstants.CRLF);
    }

    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        voltage = Float.parseFloat(String.format("%.2f", voltage));
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_VOLTAGE + " " + voltage) + " " + ScpiCommandConstants.CRLF);
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        return selectChannel(channel) &&
                sendCommand((ScpiCommandConstants.SET_CURRENT + " " + current) + " " + ScpiCommandConstants.CRLF);
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_VOLTAGE + ScpiCommandConstants.CRLF);
    }

    @Override
    public boolean readCurrent(Integer channel) {
        return selectChannel(channel) && sendCommand(ScpiCommandConstants.MEASURE_CURRENT + ScpiCommandConstants.CRLF);
    }
}
