package com.desaysv.workserver.devices.robot.interfaces;

import com.desaysv.workserver.devices.robot.type.mg400.entity.NamedRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RandomRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.devices.robot.type.mg400.entity.SwipeRobotCoordinates;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.utils.NetworkUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-28 11:28
 * @description :
 * @modified By :
 * @since : 2022-7-28
 */

public interface IRobotWithDB {
    RestTemplate restTemplateClient = new RestTemplate();

    default RobotReply swipeByName(List<String> coordinateNameList) {
        //默认滑动时自动抬起
        return swipeByName(coordinateNameList, false);
    }

    RobotReply swipeByName(List<String> coordinateNameList, boolean noLift);

    RobotReply swipeByName(List<String> coordinateNameList, boolean noLift, String projectName);

    RobotReply moveByName(List<String> coordinateNameList);

    RobotReply randomTouchByName(List<String> coordinateNameList);

    RobotReply centerTouchByName(List<String> coordinateNameList);


    //FIXME： 减少这种交互
    default ResponseEntity<SwipeRobotCoordinates> smartSwipe(SwipeRobotCoordinates swipeRobotCoordinates) {
        return restTemplateClient.postForEntity(String.format("http://127.0.0.1:%d/AITestX/robotCoordinates/swipe", NetworkUtils.getServerPort()),
                swipeRobotCoordinates, SwipeRobotCoordinates.class);
    }

    default ResponseEntity<RandomRobotCoordinates> smartRandomTouch(RandomRobotCoordinates randomRobotCoordinates, String type) {
        return restTemplateClient.postForEntity(String.format("http://127.0.0.1:%d/AITestX/robotCoordinates/touch" + type, NetworkUtils.getServerPort()),
                randomRobotCoordinates, RandomRobotCoordinates.class);
    }

    default ResponseEntity<RobotCoordinates> query(NamedRobotCoordinates namedRobotCoordinates) {
        return restTemplateClient.postForEntity(String.format("http://127.0.0.1:%d/AITestX/robotCoordinates/query",
                        NetworkUtils.getServerPort()),
                namedRobotCoordinates, RobotCoordinates.class);
    }

    default ResponseEntity<String> executeSafePoint(String deviceName, String projectName) {
        HttpHeaders headers = new HttpHeaders();
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        //接口参数
        map.add("deviceName", deviceName);
        map.add("projectName", projectName);
        //头部类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //构造实体对象
        HttpEntity<MultiValueMap<String, Object>> param = new HttpEntity<>(map, headers);
        return restTemplateClient.postForEntity(String.format("http://127.0.0.1:%d/AITestX/robotCoordinates/executeSafePoint",
                NetworkUtils.getServerPort()), param, String.class);
    }
}
