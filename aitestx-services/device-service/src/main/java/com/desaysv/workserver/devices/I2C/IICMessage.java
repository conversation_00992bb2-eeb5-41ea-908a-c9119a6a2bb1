package com.desaysv.workserver.devices.I2C;

import lombok.Data;

/**
 * @projectName: aitestxserver
 * @package: com.desaysv.workserver.devices.I2C
 * @className: IICmessage
 * @author: <PERSON><PERSON><PERSON>
 * @description: IIC消息
 * @date: 2024/4/10 12:15
 * @version: 1.0
 */
@Data
public class IICMessage {
    private byte[] sendBuf;
    private byte[] rcvBuf;
    private int slen;
    private int rlen;
    private int usbIndex;
    private IICConfig iicConfig;

    public IICMessage() {
        iicConfig = new IICConfig();
    }
}
