package com.desaysv.workserver.devices.daq.base;

import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.IFetchDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

/**
 * DAQ采集设备
 */
@Getter
public abstract class DaqDevice extends Device implements IFetchDevice {
    private final String deviceType = DeviceType.DEVICE_DAQ;

    public DaqDevice() {
        this(new DeviceOperationParameter());
    }

    public DaqDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return true;
    }

}
