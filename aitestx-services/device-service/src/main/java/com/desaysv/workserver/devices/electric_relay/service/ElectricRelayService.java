package com.desaysv.workserver.devices.electric_relay.service;

import com.desaysv.workserver.devices.electric_relay.ElectricRelayConfig;

import java.util.List;
import java.util.Map;

/**
 * 继电器开关服务接口
 * <p>
 * 该接口定义了基于设备延时配置来控制继电器开关的核心方法
 */
public interface ElectricRelayService {
    /**
     * 配置继电器的延时时间
     */
    void forwardRelayConfig(Map<String, Map<String, String>> deviceDelayConfigs);

    /**
     * 更新继电器的文本配置信息
     */
    void updateRelayTextConfig(ElectricRelayConfig electricRelayConfig);

    /**
     * 根据查询条件获取继电器文本配置列表
     */
    List<ElectricRelayConfig> getRelayTextConfig(Map<String, String> paramsMap);

    /**
     * 更新继电器的连接状态
     */
    void updateRelayStatusConfig(ElectricRelayConfig electricRelayConfig);

    /**
     * 更新连接开关状态
     */
    void updateConnectSwitchStatus(ElectricRelayConfig electricRelayConfig);
}