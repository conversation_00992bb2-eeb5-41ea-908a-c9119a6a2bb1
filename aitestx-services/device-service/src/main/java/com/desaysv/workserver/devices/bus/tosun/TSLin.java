package com.desaysv.workserver.devices.bus.tosun;

import com.alibaba.fastjson2.JSON;
import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaycv.tosuncan.jna.HardwareMapping;
import com.desaycv.tosuncan.model.TSLinReturnData;
import com.desaycv.tosuncan.service.TSMasterService;
import com.desaycv.tosuncan.service.impl.TSMasterServiceImpl;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.lin.LinConfig;
import com.desaysv.workserver.config.lin.LinConfigParameter;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.FilterLinMessage;
import com.desaysv.workserver.devices.bus.base.lin.LinMessage;
import com.desaysv.workserver.devices.bus.base.lin.LinRecvQueueManager;
import com.desaysv.workserver.devices.bus.base.lin.SequenceableLinBus;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.DataUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Map;

import static com.desaysv.workserver.utils.ByteUtils.bytesToHexString;

@Slf4j
public abstract class TSLin extends SequenceableLinBus {
    private final static int timeoutMilliseconds = 2000;
    private final TSMasterService tsMasterService;
    private final LinRecvQueueManager linRecvQueueManager;
    private boolean linStarted = false;

    public TSLin() {
        this(new DeviceOperationParameter());
    }

    public TSLin(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        tsMasterService = TSMasterServiceImpl.getInstance();
        linRecvQueueManager = new LinRecvQueueManager();
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        LinConfig linConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
        if (linConfig != null) {
            linConfig.getConfigParameters().clear();
            updateConfig(linConfig);
        }
        return openDevice();
    }

    /**
     * 自动打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean autoOpen() throws DeviceOpenException {
        //改成从LinConfig获取
        LinConfig linConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
        if (linConfig == null) {
            throw new DeviceOpenException("同星配置文件不存在，请删除设备重新连接");
        }
        Map<String, LinConfigParameter> configParameters = linConfig.getConfigParameters();
        if (CollectionUtils.isEmpty(configParameters)) {
            throw new DeviceOpenException("同星配置文件不含通道，请删除设备重新连接");
        }
        boolean isOpenOk = openDevice();

        if (isOpenOk) {
            for (Map.Entry<String, LinConfigParameter> entry : configParameters.entrySet()) {
                Object openParams = entry.getValue();
                if (openParams != null) {
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(openParams), LinConfigParameter.class), linConfig);
                }
            }
        }
        //自动连接
        try {
            tsMasterService.connect();
        } catch (TSCanException e) {
            throw new DeviceOpenException("调用同星connect接口失败");
        }

        return isOpenOk;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        log.info("关闭同星Lin:{}", getDeviceName());
        boolean isOk = super.close();
        if (!isSimulated()) {
            if (tsMasterService != null) {
                try {
                    tsMasterService.stop(getDeviceModel(), getDeviceIndex());
                    linStarted = false;
                } catch (TSCanException e) {
                    throw new DeviceCloseException(e);
                }
            }
        }
        return isOk;
    }

    /**
     * 打开通道
     *
     * @param linConfigParameter
     * @param linConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(LinConfigParameter linConfigParameter, LinConfig linConfig) throws DeviceOpenException {
        if (!isSimulated()) {
            try {
                int channel = linConfigParameter.getChannel();
                if (channel == -1) {
                    for (Map.Entry<String, LinConfigParameter> entry : linConfig.getConfigParameters().entrySet()) {
                        startChannel(entry.getValue());
                    }
                } else {
                    startChannel(linConfigParameter);
                }
                tsMasterService.connect();
            } catch (TSCanException e) {
                setChannelConfigured(false);
                throw new DeviceOpenException(e);
            }
        }
        setChannelConfigured(true);
        return true;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean openLinChannel(LinConfigParameter linConfigParameter) throws DeviceOpenException {
        //写入配置文件
        LinConfig linConfig = writeConfig(linConfigParameter);
        if (!linStarted) {
            //未打开设备尝试打开设备
            openDevice();
        }
        return openChannelInternal(linConfigParameter, linConfig);
    }

    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    private boolean openDevice() throws DeviceOpenException {
        if (linStarted) {
            log.info("同星设备已连接:{}", getDeviceName());
            return true;
        }
        try {
            linStarted = tsMasterService.start(getDeviceModel(), getDeviceIndex());
        } catch (TSCanException e) {
            throw new DeviceOpenException(e);
        }
        return true;
    }

    @Override
    public boolean closeLinChannel(int channel) throws DeviceCloseException {
        if (channel == -1) {
            LinConfig deviceConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), LinConfig.class);
            Map<String, LinConfigParameter> configParameters = deviceConfig.getConfigParameters();
            for (Map.Entry<String, LinConfigParameter> entry : configParameters.entrySet()) {
                String strChannel = entry.getKey();
                try {
                    return tsMasterService.stopLinChannel(getDeviceModel(), getDeviceIndex(), Integer.parseInt(strChannel));
                } catch (TSCanException e) {
                    throw new DeviceCloseException(e);
                }
            }
        } else {
            try {
                return tsMasterService.stopLinChannel(getDeviceModel(), getDeviceIndex(), channel);
            } catch (TSCanException e) {
                throw new DeviceCloseException(e);
            }
        }
        return false;
    }

    /**
     * 启动通道
     *
     * @param linConfigParameter 通道参数
     * @return
     */
    private boolean startChannel(LinConfigParameter linConfigParameter) throws TSCanException {
        log.info("打开同星{}通道{}:\n{}",
                getDeviceName(),
                linConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(linConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        int channel = linConfigParameter.getChannel();
        int linMode = linConfigParameter.getLinMode();//工作模式，从站为 0，主站为 1。
        int linBaud = linConfigParameter.getLinBaud();
        int isEnableResistance = linConfigParameter.getChkSumMode();//校验方式，1-经典校验 2-增强校验 3-自动(即经典校验跟增强校验都会进行轮询)
        log.info("\n通道:{}\n工作模式:{}\n波特率:{}\n\n是否启用电阻:{}\n",
                channel + 1, linMode == 0 ? "从站" : "主站", linBaud, isEnableResistance);
        //TODO 设置状态
        tsMasterService.setChannelEnabled(getDeviceModel(), getDeviceIndex(), HardwareMapping.TLIBApplicationChannelType.APP_LIN, channel, true);
        return true;
    }

    /**
     * 获取最大通道数
     *
     * @return 最大通道数
     */
    @Override
    public int getMaxChannelCount() {
        return getTsChannelCountByDeviceName(getDeviceModel());
    }

    public int getTsChannelCountByDeviceName(String deviceName) {
        return tsMasterService.getChannelCountByDeviceName(getDeviceType(), deviceName);
    }

    @Override
    public boolean setLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalValue) {
        return false;
    }

    @Override
    public boolean setLinSingleMsgControl(Integer deviceChannel, String ecuNodeName, String messageID, int status) {
        return false;
    }

    @Override
    public boolean setLinAllMsgStatus(String ecuNodeName, int status) {
        return false;
    }

    @Override
    public boolean setLinChannelMsgStatus(Integer deviceChannel, int status) {
        return false;
    }

    @Override
    public double fetchLinSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) {
        return 0;
    }

    @Override
    public boolean setLinPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError {
        boolean ok = false;
        if (tsMasterService != null) {
            try {
                String sendPtsMessageId = messageId == null ? getDeviceConfig().getPtsConfig().getHexSendPtsMessageId() : messageId;
                if (sendPtsMessageId == null) {
                    log.warn("PTS发送报文ID未设置");
                    return false;
                }
                String recvMessageId;
                int recvChannel = -1;
                String receivePtsMessageId = null;
                if (StrUtils.containsIgnoreCase(checkedContext, BaseRegexRule.PTX_RX_CONSTANT)) {
                    recvMessageId = StrUtils.extractHexId(checkedContext);
                    recvChannel = extractChannel(checkedContext);
                    recvChannel = recvChannel == -1 ? deviceChannel : recvChannel;
                    receivePtsMessageId = recvMessageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : recvMessageId;
                    startReadLinDataByIdHex(recvChannel, Integer.toHexString(DataUtils.parseHexString(receivePtsMessageId)));
                }
                log.info("通道{}写入PTS ID:{}，报文数据:{}", deviceChannel, sendPtsMessageId, StrUtils.addHexSpace(byteInstruction));
                ok = tsMasterService.writeLinMessage(getDeviceModel(), getDeviceIndex(), deviceChannel, (byte) DataUtils.parseHexString(sendPtsMessageId),
                        ByteUtils.hexStringToByteArray(byteInstruction));
                if (StrUtils.containsIgnoreCase(checkedContext, BaseRegexRule.PTX_RX_CONSTANT)) {
                    //先清空缓存队列
                    linRecvQueueManager.clear();
                    //需要检查PTS RX
                    log.info("通道{}发送完实时读取PTS ID:{}，设定读取超时时间:{}ms", deviceChannel, receivePtsMessageId, timeoutMilliseconds);
                    int targetCanId = DataUtils.parseHexString(receivePtsMessageId);
                    //读取PTS
                    String dataStr = endReadLinDataByIdHex(recvChannel, Integer.toHexString(DataUtils.parseHexString(receivePtsMessageId)));
                    byte[] data = ByteUtils.hexStringToByteArray(dataStr);
                    linRecvQueueManager.put(recvChannel, targetCanId, data);
                    log.info("通道{}完成读取PTS ID:{}", recvChannel, data);
                }
            } catch (Throwable e) {
                throw new BusError(e);
            }
        }
        return ok;
    }

    public static int extractChannel(String input) {
        String[] parts = input.split("-");
        if (parts.length > 1) {
            try {
                return Integer.parseInt(parts[1]); // 尝试将第二个部分转换为数字
            } catch (NumberFormatException e) {
                // 如果转换失败，说明不是数字
                return -1;
            }
        }
        return -1; // 如果没有足够的部分，返回 -1
    }

    @Override
    public String fetchLinPTS(Integer deviceChannel, String messageId) throws BusError {
        if (tsMasterService != null) {
            try {
                String receivePtsMessageId = messageId == null ? getDeviceConfig().getPtsConfig().getHexReceivePtsMessageId() : messageId;
                if (receivePtsMessageId == null) {
                    log.warn("PTS接收报文ID未设置");
                    return "";
                }
                log.info("通道{}接收及判断PTS ID:{}", deviceChannel, receivePtsMessageId);
                int targetLinId = DataUtils.parseHexString(receivePtsMessageId);
                byte[] tsLinReturnData;
                if (linRecvQueueManager.contains(deviceChannel, targetLinId)) {
                    //包含缓存队列中
                    log.info("通道{}已经缓存目标PTS接收ID:{}", deviceChannel, receivePtsMessageId);
                    tsLinReturnData = linRecvQueueManager.get(deviceChannel, targetLinId);
                } else {
                    log.info("通道{}设定读取超时:{}ms", deviceChannel, timeoutMilliseconds);
                    String dataStr = readLinDataByIdHex(deviceChannel, Integer.toHexString(DataUtils.parseHexString(receivePtsMessageId)), timeoutMilliseconds);
                    tsLinReturnData = ByteUtils.hexStringToByteArray(dataStr);
                }
                if (tsLinReturnData != null) {
                    log.info("通道{}读取到PTS报文:{}{}", deviceChannel,
                            Arrays.toString(tsLinReturnData),
                            ByteUtils.isEmpty(tsLinReturnData) ? "" : String.format("(%s)", ByteUtils.byteArrayToHexString(tsLinReturnData)));
                    return ByteUtils.byteArrayToHexString(tsLinReturnData);
                } else {
                    log.error("通道{}PTS接收报文ID:{}超时", deviceChannel, receivePtsMessageId);
                    return "NA";
                }
            } catch (Throwable e) {
                throw new BusError(e);
            } finally {
                //清空缓冲队列
                linRecvQueueManager.clear();
            }
        }
        return "";
    }

    @Override
    public void send(LinMessage message, Float timeout) throws BusError {
        if (isSimulated()) {
            return;
        }
        if (tsMasterService != null) {
            try {
                tsMasterService.writeLinMessage(getDeviceModel(), getDeviceIndex(), (byte) (message.getChannel().intValue()),
                        (byte) DataUtils.parseHexString(message.getIdHex()),
                        message.getData());
            } catch (TSCanException e) {
                throw new BusError(e);
            }
        }
    }

    @Override
    public void stopLinSubscibeMessage(Integer channel, Integer messageId) {

    }

    @Override
    public FilterLinMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public boolean sendLinMessage(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        boolean ok = false;
        if (tsMasterService != null) {
            try {
                String sendPtsMessageId = messageId;
                log.info("通道{}写入LIN Frame ID:{}，报文数据:{}", deviceChannel, sendPtsMessageId, StrUtils.addHexSpace(byteInstruction));
                ok = tsMasterService.writeLinMessage(getDeviceModel(), getDeviceIndex(), deviceChannel, (byte) DataUtils.parseHexString(sendPtsMessageId),
                        ByteUtils.hexStringToByteArray(byteInstruction));
            } catch (Throwable e) {
                throw new BusError(e);
            }
        }
        return ok;
    }



    @Override
    public boolean setLinWakeUp(Integer deviceChannel, int wakeUpCommand) throws BusError {
        if (tsMasterService != null) {
            return wakeUpCommand == 0 ? tsMasterService.startLinWakeUp(getDeviceModel(), getDeviceIndex(), deviceChannel) : tsMasterService.stopLinWakeUp(getDeviceModel(), getDeviceIndex(), deviceChannel);
        }
        return false;
    }

    public void startReadLinDataByIdHex(Integer deviceChannel, String targetLinId) {
        if (tsMasterService != null) {
            try {
                tsMasterService.startReadLinDataByIdHex(getDeviceModel(), getDeviceIndex(), deviceChannel, Integer.toHexString(DataUtils.parseHexString(targetLinId)));
            } catch (TSCanException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public String endReadLinDataByIdHex(Integer deviceChannel, String targetLinId) throws BusError {
        TSLinReturnData tsLinReturnData = null;
        if (tsMasterService != null) {
            try {
                tsLinReturnData = tsMasterService.endReadLinDataByIdHex(timeoutMilliseconds);
            } catch (Throwable e) {
                throw new BusError(e);
            }
        }
        return tsLinReturnData != null ? bytesToHexString(tsLinReturnData.getData()) : null;
    }

    @Override
    public String readLinDataByIdHex(Integer deviceChannel, String targetLinId) {
        return readLinDataByIdHex(deviceChannel, targetLinId, timeoutMilliseconds);
    }

    public String readLinDataByIdHex(Integer deviceChannel, String targetLinId, int timeoutMilliseconds) {
        TSLinReturnData tsLinReturnData = null;
        if (tsMasterService != null) {
            try {
                tsLinReturnData = tsMasterService.readLinDataByIdHex(getDeviceModel(), getDeviceIndex(), deviceChannel, Integer.toHexString(DataUtils.parseHexString(targetLinId)), timeoutMilliseconds);
            } catch (TSCanException e) {
                throw new RuntimeException(e);
            }
        }
        return tsLinReturnData != null ? bytesToHexString(tsLinReturnData.getData()) : null;
    }

}
