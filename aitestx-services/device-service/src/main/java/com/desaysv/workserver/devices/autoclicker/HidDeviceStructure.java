package com.desaysv.workserver.devices.autoclicker;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class HidDeviceStructure extends Structure implements Structure.ByReference {
    public Pointer ptr;

    public HidDeviceStructure(Pointer pointer) {
        this.ptr = pointer;
    }

    public Pointer ptr() {
        return ptr;
    }

    protected List<String> getFieldOrder() {
        return Collections.singletonList("ptr");
    }
}