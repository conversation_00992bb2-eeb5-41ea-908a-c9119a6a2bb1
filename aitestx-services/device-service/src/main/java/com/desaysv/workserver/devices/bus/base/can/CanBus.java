package com.desaysv.workserver.devices.bus.base.can;

import cantools.dbc.Message;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.canlog.blflog.service.impl.CanLogParserService;
import com.desaysv.workserver.common.constant.CanConstants;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.devices.bus.CanDevice;
import com.desaysv.workserver.devices.bus.base.*;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.e2e.GeelyE2EL946;
import com.desaysv.workserver.devices.bus.e2e.cheryE01.E2eCheryE0X;
import com.desaysv.workserver.devices.bus.e2e.gac.E2eGac;
import com.desaysv.workserver.devices.bus.e2e.xiaomiE2E.E2eXiaomi;
import com.desaysv.workserver.devices.bus.model.*;
import com.desaysv.workserver.devices.bus.utils.EncoderUtil;
import com.desaysv.workserver.devices.bus.utils.HexConvertUtil;
import com.desaysv.workserver.entity.CanReplyInfo;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.DataUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Nonnull;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

import static com.desaysv.workserver.devices.bus.utils.ModelConvertUtil.getBFieldValueForCanMessage;

/**
 * 具体CAN接口的抽象总线
 */
@Slf4j
public abstract class CanBus extends CanDevice implements ICanDevice {

//    private final String channelInfo = "unknown";

    @Getter
    @JSONField(serialize = false)
    private final List<CyclicTask> periodTasks = Collections.synchronizedList(new ArrayList<>());

    // 创建一个复用的临时列表
    @JSONField(serialize = false)
    private final List<CyclicTask> tempTasks = new ArrayList<>();

    @JSONField(serialize = false)
    private List<CanFilter> filters;

    @Setter
    @JSONField(serialize = false)
    private BusState state;

    @Setter
    private boolean channelConfigured; //是否配置通道

    @Setter
    @JSONField(serialize = false)
    private CanMessageEventListener canMessageEventListener;

    @Override
    public int getDefaultChannel() {
        return -1;
    }

    public CanBus() {
        this(new DeviceOperationParameter());
    }

    @Override
    public boolean stopAllCanMessage(Integer deviceChannel) {
        return stopAllPeriodicTasks(deviceChannel);
    }

    @Override
    public boolean pauseAllCanMessage(Integer deviceChannel) {
        return pauseAllPeriodicTasks(deviceChannel);
    }

    /**
     * 构建特定类型的CAN总线接口
     *
     * @param filters          过滤器列表
     * @param configParameters 配置参数
     */
    public CanBus(List<CanFilter> filters, DeviceOperationParameter configParameters) {
        this(configParameters);
        setFilters(filters);
        state = BusState.ACTIVE;
    }

    /**
     * 构建特定类型的CAN总线接口
     *
     * @param deviceOperationParameter 配置参数
     */
    public CanBus(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setSimulated(Boolean.parseBoolean(SpringContextHolder.getEnvironmentProperty("appConfig.mockEnabled")));
    }

    @Override
    public void closeOpenButNotConfigDevice() {
        try {
            if (isConnected() && !channelConfigured) {
                log.info("通道未配置，关闭设备");
                close();
            }
        } catch (DeviceCloseException e) {
            log.error(e.getMessage(), e);
        }
    }

    public boolean taskRunning() {
        return !periodTasks.isEmpty();
    }

    private void setFilters(List<CanFilter> filters) {
        this.filters = filters;
        applyFilters(filters);
    }

    /**
     * 检查给定的报文是否匹配到至少一个设定好的过滤器
     *
     * @param message 待检查的报文
     * @return 否匹配到至少一个设定好的过滤器
     */
    private boolean matchesFilters(CanMessage message) {
        //如果没有设置任何过滤器，所有消息都匹配
        if (filters == null) {
            return true;
        }
        for (CanFilter filter : filters) {
            //检查报文是否在过滤器中
            if (filter.getExtended() != null && filter.getExtended() != message.isExtendedId()) {
                continue;
            }
            //检查mask和id
            int canId = filter.getCanId();
            int canMask = filter.getCanMask();
            //计算
            if (((canId ^ message.getArbitrationId()) & canMask) == 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 应用过滤器作为CAN接口的硬件过滤
     *
     * @param filters 过滤器列表
     */
    public void applyFilters(List<CanFilter> filters) {

    }

    public void send(CanMessage message) throws BusError {
        Float timeout = null;
        send(message, timeout);
    }

    public abstract void send(CanMessage message, Float timeout) throws BusError;

    public CyclicTask sendPeriodic(CanMessage message, float period) {
        return sendPeriodic(message, period, null, true);
    }

    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration) {
        return sendPeriodic(message, period, duration, true);
    }

    /**
     * 开始发送给定周期的报文
     * 任务处于活跃状态直到满足以下条件任意一个
     * - 超过可选的持续时间
     * - Bus超出范围
     * - Bus被关闭
     * - stopAllPeriodicTasks方法被调用
     * - CyclicTask.stop方法被调用
     *
     * @param message   待发送的报文
     * @param period    每条报文的间隔周期（秒）
     * @param duration  报文发送的持续时间（秒），如果为空，则无限期发送
     * @param storeTask 默认真，任务将会被关联到总线实例中，可以手动禁用总线实例管理
     * @return 已启动的任务实例
     */
    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration, boolean storeTask) {
        if (duration != null && duration < 0) {
            // duration为空，代表一直发送
            duration = null;
        }
        ChannelMessageId taskId = TaskIdGenerator.generateTaskId(message.getChannel(), message.getArbitrationId());
        CyclicTask task = getCyclicTaskByTaskId(taskId);
//        log.info("获取任务:{}", task);
        if (task == null) {
            //之前不存在该任务，新建任务
            task = sendPeriodicInternal(message, period, duration);
            task.setTaskId(taskId);
            final CyclicTask finalTask = task;
            task.setCyclicTaskListener(removeTask -> {
                if (removeTask) {
                    periodTasks.remove(finalTask);
                }
            });
            if (storeTask) {
                periodTasks.add(task);
            }
        } else {
            //存在相同任务，修改报文数据
            log.info(String.format("存在相同任务%s，修改报文数据和持续时间", taskId));
            if (task instanceof ThreadBasedCyclicSendTask) {
                ThreadBasedCyclicSendTask threadBasedCyclicSendTask = (ThreadBasedCyclicSendTask) task;
                threadBasedCyclicSendTask.modifyData(message);
                threadBasedCyclicSendTask.setDuration(duration);
                threadBasedCyclicSendTask.setE2eType(message.getE2eType());
                threadBasedCyclicSendTask.enableE2e(message.isE2eEnabled());
                threadBasedCyclicSendTask.resume();
            }
        }
        return task;
    }

    public CyclicTask getCyclicTaskByTaskId(ChannelMessageId taskId) {
        synchronized (periodTasks) {
            for (CyclicTask task : periodTasks) {
                ChannelMessageId tId = task.getTaskId();
                if (tId != null && tId.equals(taskId)) {
                    return task;
                }
            }
        }
        return null;
    }


    /**
     * 默认用线程实现周期发送报文
     *
     * @param message  待发送的报文
     * @param period   每条报文的间隔周期（秒）
     * @param duration 报文发送的持续时间（秒），如果为空，则无限期发送
     * @return 已启动的任务实例
     */
    private @Nonnull CyclicSendTask sendPeriodicInternal(CanMessage message, float period, Float duration) {
        return new ThreadBasedCyclicSendTask(this, message, period, duration, canMessageEventListener);
    }

    public boolean stopAllPeriodicTasks(Integer deviceChannel) {
        return stopAllPeriodicTasks(deviceChannel, true);
    }

    public boolean pauseAllPeriodicTasks(Integer deviceChannel) {
        if (!taskRunning()) {
            return false;
        }
        if (deviceChannel == null) {
            log.info("{}暂停通道所有CAN报文", getDeviceName());
        } else {
            log.info("{}暂停通道{}所有CAN报文", getDeviceName(), deviceChannel);
        }
        for (CyclicTask task : periodTasks) {
            if (deviceChannel == null || task.getTaskId().getChannel() == deviceChannel) {
                task.pause();
            }
        }
        return true;
    }

    /**
     * 停止发送已启动的任何报文发送
     *
     * @param removeTasks 是否停止跟踪已停止的任务
     */
    public boolean stopAllPeriodicTasks(Integer deviceChannel, boolean removeTasks) {
        if (!taskRunning()) {
            return true;
        }
        if (deviceChannel == null) {
            log.info("{}停止通道所有CAN报文", getDeviceName());
        } else {
            log.info("{}停止通道{}所有CAN报文", getDeviceName(), deviceChannel);
        }
        synchronized (tempTasks) {  // 确保临时列表的线程安全
            tempTasks.clear();  // 清空临时列表
            tempTasks.addAll(periodTasks);  // 添加当前任务

            for (CyclicTask task : tempTasks) {
                if (deviceChannel == null || task.getTaskId().getChannel() == deviceChannel) {
                    task.stop(removeTasks);
                }
            }

            tempTasks.clear();  // 使用完后清空，避免内存泄漏
        }
        return true;
    }

    public void removePeriodicTask() {

    }

    public CanMessage recv(Integer channel) throws BusError {
        return recv(channel, null);
    }

    /**
     * 阻塞等待总线的消息
     *
     * @param timeout 超时时间（秒）
     */
    public CanMessage recv(Integer channel, Float timeout) throws BusError {
        long start = System.currentTimeMillis();
        Float timeLeft = timeout;
        while (true) {
            //获取CAN报文
            FilterCanMessage message = recvInternal(channel, timeLeft);
            if (message != null && (message.isAlreadyFiltered() || matchesFilters(message))) {
                log.info("接收报文:{}", message);
                return message;
            } else if (timeout != null) {
                timeLeft = timeout - (System.currentTimeMillis() - start);
                if (timeLeft <= 0) {
                    return null;
                }
            }
        }
    }

    /**
     * 读取总线的报文并且获取是否被过滤
     * 当过滤器设置后，没有匹配到，或者超时时间还没到，这个方法可能被多次调用
     * 这个方法不能直接调用
     *
     * @param timeout 等待报文的时间（秒）
     * @return FilterCanMessage 报文
     * @throws BusError 读取过程中出错，抛出CanError
     */
    public abstract FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError;

    /**
     * 丢弃可能在输出缓冲区队列化的每个报文
     */
    public void flushTxBuffer() {

    }

    /**
     * 关闭总线的清理代码
     */
    public void shutdown() {

    }

    /**
     * 检测总线接口可以适用的所有配置/通道
     */
    public void detectAvailableConfigs() {

    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        stopAllPeriodicTasks(null);
        return super.close();
    }

    @Override
    protected void stopSend(CanMessage message) {

    }

    /**
     * 判断是否为拓展帧
     * 标准帧（Standard Frame）：标识符范围是 0x000 到 0x7FF（十进制的 0 到 2047）。
     * 扩展帧（Extended Frame）：标识符范围超出 0x7FF。
     *
     * @param arbitrationId 报文id
     * @return
     */
    private static boolean isExtendedFrame(int arbitrationId) {
        //标准帧：0x0-0x7FF
        return arbitrationId > 0x7FF;
    }


    @Override
    public CyclicTask sendCanMessage(Integer deviceChannel, CanMessage message) throws BusError {
        if (!isConnected()) {
            throw new IllegalStateException("CAN总线未打开，无法发送报文");
        }
        message.setExtendedId(isExtendedFrame(message.getArbitrationId()));
        message.setChannel(deviceChannel);
        log.info("{}发送CAN报文:{}", getDeviceName(), message);
        if (message.isLoop()) {
            //循环发送
            ActionSequencesLoggerUtil.info("{}(通道{})以周期{}ms循环发送CAN报文:{}",
                    message.getHexId(),
                    message.getChannel(),
                    message.getPeriod() * 1000,
                    StrUtils.getHexStringWithBlank(message.getLoopDatas()));
            message.setDuration(null);
        } else {
            if (message.getSendTimes() == -1) {
                //一直发送
                ActionSequencesLoggerUtil.info("{}(通道{})以周期{}ms一直发送CAN报文:{}", message.getHexId(),
                        message.getChannel(),
                        message.getPeriod() * 1000,
                        StrUtils.getHexStringWithBlank(message.getData()));
                message.setDuration(null);
            } else {
                message.setDuration(message.getSendTimes() * message.getPeriod());
                ActionSequencesLoggerUtil.info("{}(通道{})报文发送持续{}s，发送帧次数:{}，每次连发帧数:{}, 报文数据:{}", message.getHexId(),
                        message.getChannel(),
                        message.getDuration(),
                        message.getSendTimes(),
                        message.getFramesPerSendNum(),
                        StrUtils.getHexStringWithBlank(message.getData()));
            }
        }
        registerMessageEventListener(getDeviceConfig(), message);
        return sendPeriodic(message, message.getPeriod(), message.getDuration());
    }

    @Override
    public OperationResult canReplay(CanReplyInfo canReplyInfo) {
        OperationResult operationResult = new OperationResult();
        try {
            CanLogParserService canLogParserService = new CanLogParserService();

            // 1. 解析日志文件增加异常捕获
            List<CanMessage> canMessages;
            try {
                canMessages = canLogParserService.parseCanLog(canReplyInfo.getFilePath());
            } catch (IOException e) {
                log.error("解析CAN日志文件失败: {}", canReplyInfo.getFilePath(), e);
                operationResult.fail("解析CAN日志文件失败");
                return operationResult;
            }

            // 2. 过滤报文时检查空结果
            List<CanMessage> filteredCanMessages = canMessages.stream()
                    .filter(message -> !canReplyInfo.getFilteredIds().contains(message.getArbitrationId()))
                    .collect(Collectors.toList());

            if (filteredCanMessages.isEmpty()) {
                log.warn("过滤后的CAN报文列表为空，文件：{}", canReplyInfo.getFilePath());
                operationResult.fail("过滤后的CAN报文列表为空");
                return operationResult;
            }

            double lastTimestamp = filteredCanMessages.get(0).getTimestamp();

            // 3. 遍历发送增加异常处理
            for (CanMessage message : filteredCanMessages) {
                try {
                    //增加一个判断，如果报文长度大于8，则设置为CAN-FD报文
                    if (message.getDlc() > 8) {
                        message.setCanFd(true);
                    }
                    log.debug("正在发送报文: {}", message);

                    double currentTimestamp = message.getTimestamp();
                    double intervalMs = (currentTimestamp - lastTimestamp) * 1000;

                    if (intervalMs < 0) {
                        log.warn("发现异常时间戳 current:{}, last:{}", currentTimestamp, lastTimestamp);
                        operationResult.fail("发现异常时间戳");
                        return operationResult;
                    }

                    long intervalNanos = (long) (intervalMs * 1_000_000);
                    LockSupport.parkNanos(intervalNanos);

                    // 4. 发送报文时捕获总线异常
                    sendCanMessage(message.getChannel(), message);
                    lastTimestamp = currentTimestamp;
                } catch (BusError e) {
                    log.error("发送报文失败: {}", message, e);
                    operationResult.fail("发送报文失败");
                    return operationResult;
                } catch (Exception e) {
                    log.error("处理报文时发生意外错误: {}", message, e);
                    operationResult.fail("处理报文时发生意外错误");
                    return operationResult;
                }
            }
            operationResult.ok("CAN回放成功");
            return operationResult;
        } catch (Exception e) {
            log.error("CAN重放发生未处理的异常", e);
            operationResult.fail("CAN回放发生未处理的异常");
            return operationResult;
        }
    }


    private void registerMessageEventListener(CanConfig canConfig, CanMessage message) throws BusError {
        if (message.getE2eType() == null) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            if (CanConstants.E2E_TYPE_GEELY_L946.equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
            if (CanConstants.E2E_TYPE_CHERY_E01.equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
            if (CanConstants.E2E_TYPE_XIAO_MI.equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
            if (CanConstants.E2E_TYPE_GAC.equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
        }
        // 获取全局E2E状态
        Boolean e2eGlobalStatus = Optional.ofNullable(canConfig)
                .map(CanConfig::getE2eGlobalStatus)
                .map(map -> map.get(message.getChannel()))
                .orElse(false); // 默认false

        // 如果全局E2E状态开启，则设置消息的E2E类型
        if (e2eGlobalStatus) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            message.setE2eEnabled(true);
        }
        // 当全局E2E状态关闭且消息未启用E2E时，直接返回
        if (!e2eGlobalStatus && !message.isE2eEnabled()) {
            return;
        }

        CanConfig deviceConfig = getDeviceConfig();
        DbcConfig dbcConfig = deviceConfig.getDbcConfigs().get(String.valueOf(message.getChannel()));
        if (dbcConfig == null) {
            throw new BusError("dbc文件未找到");
        }
        if (message.getE2eType() == null) {
            return;
        }
        ChannelMessageId taskId = TaskIdGenerator.generateTaskId(message.getChannel(), message.getArbitrationId());
        CyclicTask task = getCyclicTaskByTaskId(taskId);
        canMessageEventListener = deviceConfig.getE2eEvents().get(message.getChannel());
        if (canMessageEventListener == null) {
            if (message.getE2eType().equalsIgnoreCase("GeelyL946")) {
                canMessageEventListener = new GeelyE2EL946(deviceConfig, dbcConfig.getDbcPaths(), message.getChannel());
            } else if (message.getE2eType().equalsIgnoreCase("CheryE01")) {
                canMessageEventListener = new E2eCheryE0X(deviceConfig, message.getChannel());
            } else if (message.getE2eType().equalsIgnoreCase("Xiaomi")) {
                canMessageEventListener = new E2eXiaomi(deviceConfig, message.getChannel());
            } else if (message.getE2eType().equalsIgnoreCase("GAC")) {
                canMessageEventListener = new E2eGac(deviceConfig, message.getChannel());
            }
            deviceConfig.getE2eEvents().put(message.getChannel(), canMessageEventListener);
        }
        if (canMessageEventListener != null) {
            if (task == null) {
                canMessageEventListener.onMessageSend(message);
            } else {
                if (task instanceof ThreadBasedCyclicSendTask) {
                    ((ThreadBasedCyclicSendTask) task).setCanMessageEventListener(canMessageEventListener);
                }
            }
        }

    }

    @Override
    public CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
        log.info("{}停止CAN通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%X", messageId));
        return stopCanMessageInternal(deviceChannel, messageId);
    }

    @Override
    public CyclicTask stopCanMessage(Integer deviceChannel, String messageId) {
        return stopCanMessage(deviceChannel, DataUtils.parseHexString(messageId));
    }

    private CyclicTask stopCanMessageInternal(int channel, int messageId) {
        CyclicTask task = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(channel, messageId));
        if (task != null) {
            task.stop();
        }
        return task;
    }

    public List<Integer> fetchRunningCanMessage(Integer deviceChannel) {
        return getPeriodTasks().stream().map(CyclicTask::getTaskId).
                filter(channelMessageId -> channelMessageId.getChannel() == deviceChannel).
                map(ChannelMessageId::getArbitrationId).
                collect(Collectors.toList());
    }

    public void sendError(CanMessage message) {
        log.info("{}发送错误:{}", getDeviceName(), message);
        SseUtils.pubMsg(SseConstants.CAN_SEND_ERROR + message.getChannel(), message.getArbitrationId());
    }

    public void sendFinish(CanMessage message) {
        log.info("{}发送完成:{}", getDeviceName(), message);
        SseUtils.pubMsg(SseConstants.CAN_SEND_FINISH + message.getChannel(), message.getArbitrationId());
    }


    public CyclicTask sendFlexrayMessage(Integer deviceChannel, FlexrayMessage message) {
        if (!isConnected()) {
            throw new IllegalStateException("总线未打开，无法发送报文");
        }
        message.setChannel(deviceChannel);
        log.info("{}发送报文:{}", getDeviceName(), message);
        if (message.getSendTimes() == -1) {
            //一直发送
            log.info("0x{}(通道{})报文以周期{}ms一直发送报文:{}", String.format("%X", message.getSlotId()),
                    message.getChannel(),
                    message.getPeriod() * 1000,
                    StrUtils.getHexStringWithBlank(message.getData()));
            message.setDuration(null);
        } else {
            message.setDuration(message.getSendTimes() * message.getPeriod());
            log.info("0x{}(通道{})报文发送持续{}s，发送帧次数:{}，每次连发帧数:{}, 报文数据:{}", String.format("%X", message.getSlotId()),
                    message.getChannel(),
                    message.getDuration(),
                    message.getSendTimes(),
                    message.getFramesPerSendNum(),
                    StrUtils.getHexStringWithBlank(message.getData()));
        }
        return sendPeriodic(message, message.getPeriod(), message.getDuration());
    }

    public CyclicTask sendPeriodic(FlexrayMessage message, float period, Float duration) {
        return sendPeriodic(message, period, duration, true);
    }

    /**
     * 开始发送给定周期的报文
     * 任务处于活跃状态直到满足以下条件任意一个
     * - 超过可选的持续时间
     * - Bus超出范围
     * - Bus被关闭
     * - stopAllPeriodicTasks方法被调用
     * - CyclicTask.stop方法被调用
     *
     * @param message   待发送的报文
     * @param period    每条报文的间隔周期（秒）
     * @param duration  报文发送的持续时间（秒），如果为空，则无限期发送
     * @param storeTask 默认真，任务将会被关联到总线实例中，可以手动禁用总线实例管理
     * @return 已启动的任务实例
     */
    public CyclicTask sendPeriodic(FlexrayMessage message, float period, Float duration, boolean storeTask) {
        if (duration != null && duration < 0) {
            // duration为空，代表一直发送
            duration = null;
        }
        ChannelMessageId taskId = TaskIdGenerator.generateTaskId(message.getChannel(), message.getSlotId(), message.getOffest(), message.getRepetitior());
        CyclicTask task = getCyclicTaskByTaskId(taskId);
//        log.info("获取任务:{}", task);
        if (task == null) {
            //之前不存在该任务，新建任务
            task = sendPeriodicInternal(message, period, duration);
            task.setTaskId(taskId);
            final CyclicTask finalTask = task;
            task.setCyclicTaskListener(removeTask -> {
                if (removeTask) {
                    periodTasks.remove(finalTask);
                }
            });
            if (storeTask) {
                periodTasks.add(task);
            }
        } else {
            //存在相同任务，修改报文数据
            log.info(String.format("存在相同任务%s，修改报文数据和持续时间", taskId));
            if (task instanceof ThreadBasedCyclicSendTask) {
                ((ThreadBasedCyclicSendTask) task).modifyData(message);
                ((ThreadBasedCyclicSendTask) task).setDuration(duration);
            }
        }
        return task;
    }

    /**
     * 默认用线程实现周期发送报文
     *
     * @param message  待发送的报文
     * @param period   每条报文的间隔周期（秒）
     * @param duration 报文发送的持续时间（秒），如果为空，则无限期发送
     * @return 已启动的任务实例
     */
    private @Nonnull CyclicSendTask sendPeriodicInternal(FlexrayMessage message, float period, Float duration) {
        return new ThreadBasedCyclicSendTask(this, message, period, duration);
    }

    public List<String> fetchRunningFlexrayMessage(Integer deviceChannel) {
        return getPeriodTasks().stream()
                .map(CyclicTask::getTaskId)
                .filter(channelMessageId -> channelMessageId.getChannel() == deviceChannel)
                .map(channelMessageId -> channelMessageId.getSlotId() + "-" + channelMessageId.getBase() + "-" + channelMessageId.getRep())
                .collect(Collectors.toList());
    }

    public CyclicTask stopFlexrayMessage(Integer deviceChannel, FlexrayMessage flexrayMessage) {
        log.info("{}停止Flexray通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%s", flexrayMessage.getSlotId()));
        return stopFlexrayMessageInternal(deviceChannel, flexrayMessage.getSlotId(), flexrayMessage.getOffest(), flexrayMessage.getRepetitior());
    }

    private CyclicTask stopFlexrayMessageInternal(int channel, int slotId, int base, int rep) {
        CyclicTask task = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(channel, slotId, base, rep));
        if (task != null) {
            task.stop();
        }
        return task;
    }

    public void stopAllFlexrayMessage(Integer deviceChannel) throws TSCanException {
        stopAllFlexrayPeriodicTasks(deviceChannel);
    }

    public void stopAllFlexrayPeriodicTasks(Integer deviceChannel) throws TSCanException {
        stopAllFlexrayPeriodicTasks(deviceChannel, true);
    }

    public void stopAllFlexrayPeriodicTasks(Integer deviceChannel, boolean removeTasks) throws TSCanException {

    }

    public void send(FlexrayMessage message) throws BusError {
        Float timeout = null;
        send(message, timeout);
    }

    public abstract void send(FlexrayMessage message, Float timeout) throws BusError;

    @Override
    public OperationResult sendPtsCanMessage(Integer deviceChannel, CanPtsMessage message) throws BusError {
        CanMessage canMessage = new CanMessage();
        canMessage.setChannel(deviceChannel);
        canMessage.setArbitrationId(DataUtils.parseHexString(message.getSendMessageId()));
        canMessage.setData(ByteUtils.hexStringToByteArray(message.getSendMessage()));
        canMessage.setSendTimes(1);
        sendCanMessage(deviceChannel, canMessage);
        return OperationResult.staticOk();
    }

    @Override
    public OperationResult comparisonCanMessage(Integer deviceChannel, CanComparison canComparison) throws BusError {
        OperationResult operationResult = new OperationResult();
        //if (compareCanScript(deviceChannel, canComparison.getComparisonMessageId(), canComparison.getComparisonMessage()).isPass()) {
        if (setCanPTS(deviceChannel, null, canComparison.getSendMessageId(), canComparison.getSendMessage(),
                canComparison.getComparisonMessageId(), true)) {
            String receivePtsMessage = fetchCanPTS(deviceChannel, canComparison.getComparisonMessageId());
            if (receivePtsMessage.equals(canComparison.getComparisonMessage())) {
                return operationResult.ok(String.format("检测到报文ID：%s，期望报文数据:%s", canComparison.getComparisonMessageId(), canComparison.getComparisonMessage()));
            } else {
                return operationResult.fail(String.format("未检测到报文ID：%s，期望报文数据:%s，实际报文数据:%s",
                        canComparison.getComparisonMessageId(), canComparison.getComparisonMessage(), receivePtsMessage));
            }
        } else {
            operationResult.fail(String.format("Can PTS发送失败:%s->%s", canComparison.getSendMessageId(), canComparison.getSendMessage()));
        }
        return operationResult;
    }

    @Override
    public OperationResult inspectCanSignal(Integer deviceChannel, CanSignalInspectorRequest canSignalInspectorRequest) throws BusError {
        // 使用 CanSignalInspector 类进行信号检测
        return CanSignalInspector.inspectSignal(this, deviceChannel, canSignalInspectorRequest);
    }

    @Override
    public OperationResult inspectCanMessage(Integer deviceChannel, CanMessageInspectorRequest canMessageInspectorRequest) throws BusError {
        return CanMessageInspector.inspectMessage(this, deviceChannel, canMessageInspectorRequest);
    }

    /**
     * 检测是否存在匹配指定ID和数据的周期性报文
     *
     * @param messageId 报文ID (16进制字符串，可以带"0x"前缀)
     * @param data      报文数据 (16进制字符串，可以包含空格)
     * @return 如果找到匹配的报文返回true，否则返回false
     */
    public boolean isMatchingPeriodicMessage(String messageId, String data) {
        // 解析messageId为整数
        int arbitrationId = DataUtils.parseHexString(messageId);

        // 规范化报文数据 (移除空格)
        String normalizedData = data.replaceAll("\\s+", "").toLowerCase();

        // 遍历所有周期性任务查找匹配项
        synchronized (periodTasks) {
            for (CyclicTask task : periodTasks) {
                if (task instanceof ThreadBasedCyclicSendTask) {
                    ThreadBasedCyclicSendTask sendTask = (ThreadBasedCyclicSendTask) task;
                    CanMessage message = (CanMessage) sendTask.getCanMessage();

                    // 检查消息ID是否匹配
                    if (message.getArbitrationId() == arbitrationId) {
                        // 获取消息数据并规范化
                        String messageData = StrUtils.bytesToString(message.getData()).toLowerCase();

                        // 检查数据是否匹配
                        if (messageData.equals(normalizedData)) {
                            log.info("找到匹配的周期性CAN报文 ID: {}, 数据: {}", messageId, data);
                            return true;
                        }
                    }
                }
            }
        }

        log.info("未找到匹配的周期性CAN报文 ID: {}, 数据: {}", messageId, data);
        return false;
    }

    protected boolean sendAllIGModuleMessages(BusData busData, int deviceChannel, int command) {
        if (busData == null) {
            log.error("CAN设备通道{}的IG模块数据为空,无法执行", deviceChannel);
            return false;
        }
        List<Bus> busList = busData.getBusList();
        if (CollectionUtils.isEmpty(busList)) {
            log.error("通道{}的IG模块为空，无法执行该动作", deviceChannel);
            return false;
        }
        for (Bus bus : busList) {
            log.info("开始执行通道{}的IG模块名称为{}", deviceChannel, bus.getBusName());
            List<CANMessage> sendCanMessages = bus.getCanMessages();
            List<CANMessageR> selectedCanMessageRs = convertToCANMessageR(new EncoderUtil(), sendCanMessages);
            if (!CollectionUtils.isEmpty(selectedCanMessageRs)) {
                for (CANMessageR canMessageR : selectedCanMessageRs) {
                    try {
                        CanMessage canMsg = toMessage(deviceChannel, canMessageR);
                        if (command == 0) {
                            sendCanMessage(deviceChannel, canMsg);
                        } else {
                            stopCanMessage(deviceChannel, DataUtils.parseHexString(canMsg.getHexId()));
                        }
                    } catch (Exception e) {
                        log.error("通道{}报文{}执行{}. 失败", deviceChannel, canMessageR.getId(), command == 0 ? "发送" : "停止");
                        return false;
                    }
                }
            }
        }
        return true;


    }


    protected boolean sendSingleIGModuleMessages(BusData busData, int deviceChannel, String igTabName, int command) {
        if (busData.getBusList().stream().anyMatch(b -> b.getBusName().equals(igTabName))) {
            Bus bus = busData.getBusList().stream().filter(b -> igTabName.equals(b.getBusName())).findFirst().orElse(null);
            if (bus == null) {
                log.error("通道{}的IG模块名称为{}的bus数据为空，无法执行该动作", deviceChannel, igTabName);
                return false;
            }
            List<CANMessage> sendCanMessages = bus.getCanMessages();
            List<CANMessageR> selectedCanMessageRs = convertToCANMessageR(new EncoderUtil(), sendCanMessages);
            if (!CollectionUtils.isEmpty(selectedCanMessageRs)) {
                for (CANMessageR canMessageR : selectedCanMessageRs) {
                    try {
                        CanMessage canMsg = toMessage(deviceChannel, canMessageR);
                        if (command == 0) {
                            sendCanMessage(deviceChannel, canMsg);
                        } else {
                            stopCanMessage(deviceChannel, DataUtils.parseHexString(canMsg.getHexId()));
                        }
                    } catch (Exception e) {
                        log.error("通道{}报文{}执行{}. 失败", deviceChannel, canMessageR.getId(), command == 0 ? "发送" : "停止");
                        return false;
                    }
                }
                return true;
            }
        } else {
            log.error("没有找到通道{}的IG模块名称为{}，无法执行该动作", deviceChannel, igTabName);
        }
        return false;
    }

    public CanMessage toMessage(int channel, CANMessageR msg) {
        CanMessage canMessage = new CanMessage();
        canMessage.setDlc(msg.getDlc());
        canMessage.setCanFd("CANFD".equals(msg.getEventType()) || "CANFD加速".equals(msg.getEventType()));
        canMessage.setChannel(channel);
        canMessage.setArbitrationId(Integer.decode(msg.getIdHex()));
        canMessage.setSendMethod("正常发送");
//        float duration = msg.getSendCount() * msg.getInterval() / 1000.0f;
        canMessage.setSendTimes(msg.getSendCount() == 0 ? -1 : msg.getSendCount());
        canMessage.setPeriod(msg.getInterval() / 1000.0f);
        canMessage.setE2eEnabled(msg.isOpenE2E());
        canMessage.setE2eType(msg.getE2eType());
        if (msg.isLoop()) {//循环发送开关
            canMessage.setLoop(true);
            canMessage.setLoopDatas(msg.getLoopDatas());
        } else {
            canMessage.setData(msg.getData());
        }
        return canMessage;
    }

    public static List<CANMessageR> convertToCANMessageR(EncoderUtil encoderUtil, List<CANMessage> canMessages) {
        if (CollectionUtils.isEmpty(canMessages)) {
            return null;
        }
        List<CANMessageR> canMessageRList = new ArrayList<>();
        for (CANMessage canMessage : canMessages) {
            CANMessageR canMessageR = new CANMessageR();
            //BeanUtils.copyProperties(canMessageR, canMessage);
            canMessageR.setEventType(canMessage.getCanType());
            canMessageR.setDlc(canMessage.getDlc());
            canMessageR.setIdHex(canMessage.getIdHex());
            canMessageR.setInterval(canMessage.getInterval());
            canMessageR.setSendCount(canMessage.getSendCount());
            canMessageR.setId(canMessage.getId());
            List<Byte> dataList = new ArrayList<>();
            // 循环处理 B1 到 B39
//            for (int i = 1; i <= canMessageR.getDlc(); i++) {
            for (int i = 1; i <= canMessage.getDlc(); i++) {
                String bValue = getBFieldValueForCanMessage(canMessage, i);
                // 将16进制字符串转换为byte
                if (bValue != null && !bValue.isEmpty()) {
                    byte byteValue = (byte) HexConvertUtil.convertHexToDecimal(bValue);
                    dataList.add(byteValue);
                }
            }
            // 将List<Byte>转换为byte[]
            byte[] byteArray = new byte[dataList.size()];
            for (int i = 0; i < dataList.size(); i++) {
                byteArray[i] = dataList.get(i);
            }
            canMessageR.setData(byteArray);
            if (canMessage.isMultiplex()) {
                CANMultiplexSignal canMultiplexSignal = canMessage.getCanMultiplexSignal();
                if (canMultiplexSignal.isLoop()) {
                    canMessageR.setLoop(true);
                    canMessageR.setSendCount(-1);
                    List<byte[]> byteList = new ArrayList<>();
                    Message dbcMessage = encoderUtil.getMessage(canMessage.getDbcIndex(), canMessage.getIdHex());
                    for (Map.Entry<Long, List<CANSignal>> entry : canMultiplexSignal.getMuxGroups().entrySet()) {
                        Long multiplexor = entry.getKey();
                        List<CANSignal> canSignalList = entry.getValue();
                        Map<String, Object> rawDataMap = new HashMap<>();
                        CANSignal mainSignal = canMultiplexSignal.getMainSignal();
                        rawDataMap.put(mainSignal.getSignalName(), entry.getKey());
                        for (CANSignal canSignal : canSignalList) {
                            long value = HexConvertUtil.convertHexToDecimal(canSignal.getOriginalValue()); // 原始值是十六进制字符串
                            rawDataMap.put(canSignal.getSignalName(), value);
                        }
                        byte[] encode = dbcMessage.encode(multiplexor.intValue(), rawDataMap);
                        byteList.add(encode);
                    }
                    canMessageR.setLoopDatas(byteList);
                } else {
                    canMessageR.setLoop(false);
                    String originalValue = canMultiplexSignal.getMainSignal().getOriginalValue();
                    List<CANSignal> canSignals = canMultiplexSignal.getMuxGroups().get(Long.parseLong(originalValue));
                    List<CANSignalR> canSignalRList = new ArrayList<>();
                    for (CANSignal canSignal : canSignals) {
                        CANSignalR canSignalR = new CANSignalR(canSignal.getSignalName(), originalValue, canSignal.getActualValue(), canSignal.getValueRepresentation());
                        BeanUtils.copyProperties(canSignalR, canSignal);
                        canSignalRList.add(canSignalR);
                    }
                    canMessageR.setCanSignalList(canSignalRList);
                }
            } else {
                List<CANSignal> canSignalList = canMessage.getCanSignalList();
                List<CANSignalR> canSignalRList = new ArrayList<>();
                for (CANSignal canSignal : canSignalList) {
                    double originalValue = HexConvertUtil.convertHexToDecimal(canSignal.getOriginalValue());
                    CANSignalR canSignalR = new CANSignalR(canSignal.getSignalName(), String.valueOf(originalValue), canSignal.getActualValue(), canSignal.getValueRepresentation());
                    BeanUtils.copyProperties(canSignalR, canSignal);
                    canSignalRList.add(canSignalR);
                }
                canMessageR.setCanSignalList(canSignalRList);
            }
            canMessageRList.add(canMessageR);
        }
        return canMessageRList;
    }


    protected BusData importBusDataFile(String filePath) {
        try (Reader reader = new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8)) {
            Type type = new TypeToken<BusData>() {
            }.getType();
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            BusData data = gson.fromJson(reader, type);
            if (data != null) {
                return data;
            }
        } catch (IOException e) {
            log.error("还原CAN Data任务失败:{}", e.getMessage());
            return null;
        }
        return null;
    }

}
