package com.desaysv.workserver.devices.I2C;

import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;

/**
 * Clicker设备
 */
public class ConcreteI2CDevice extends I2CDevice {

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return super.close();
    }

}
