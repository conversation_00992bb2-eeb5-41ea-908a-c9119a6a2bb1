package com.desaysv.workserver.devices.power.kikusui;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class StartPulse extends WavePulse {
    private boolean constantRandom;
    private List<Float> constantValues;

    private double fromVoltage;
    private double toVoltage;
    private double fromTime;  //秒
    private double toTime; //秒
    private int fromCycle;
    private int toCycle;

}
