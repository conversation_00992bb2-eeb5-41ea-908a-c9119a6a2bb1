package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.mapper.TestDeviceTypeMapper;
import com.desaysv.workserver.model.TestDeviceType;
import com.desaysv.workserver.service.TestDeviceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-9 14:41
 * @description :
 * @modified By :
 * @since : 2022-5-9
 */
@Service
@Slf4j
//no lazy
public class TestDeviceTypeServiceImpl implements TestDeviceTypeService {
    private TestDeviceTypeMapper testDeviceTypeMapper;

    @Autowired
    public void setTestDeviceTypeMapper(TestDeviceTypeMapper testDeviceTypeMapper) {
        this.testDeviceTypeMapper = testDeviceTypeMapper;
    }

    @Async
    @Override
    public void initDB() {
//        log.info("testDeviceTypeMapper初始化db");
        try {
            List<String> allDeviceTypes = DeviceType.getAllDeviceTypes();
            for (String deviceType : allDeviceTypes) {
                TestDeviceType testDeviceType = testDeviceTypeMapper.selectByName(deviceType);
                if (testDeviceType == null) {
                    testDeviceType = new TestDeviceType();
                    testDeviceType.setName(deviceType);
                    testDeviceTypeMapper.insert(testDeviceType);
                }
            }
        } catch (IllegalAccessException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public TestDeviceType getDeviceTypeByName(String deviceTypeName) {
        return testDeviceTypeMapper.selectByName(deviceTypeName);
    }

    @Override
    public List<TestDeviceType> getAllDeviceTypes() {
        return testDeviceTypeMapper.selectAll();
    }
}
