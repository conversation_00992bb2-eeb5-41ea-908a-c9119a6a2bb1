package com.desaysv.workserver.stream.grabber;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import org.bytedeco.javacv.FrameGrabber;

public abstract class StreamGrabber extends FrameGrabber {

    public abstract ImageBuffer grabByteBuffer() throws FrameGrabberException;

    public void start(Integer width, Integer height) throws FrameGrabberException {

    }

    public void setExposureAuto(int autoExposureMode, int valueLower, int valueUpper) {
    }

    public void setReverseX(boolean reverseX) {
    }

    public void setReverseY(boolean reverseY) {
    }

    public void grabberSwitch(boolean status) {
    }
    public CameraSettings getCameraSettings(CameraSettings cameraSettings) {
        return cameraSettings;
    }
}
