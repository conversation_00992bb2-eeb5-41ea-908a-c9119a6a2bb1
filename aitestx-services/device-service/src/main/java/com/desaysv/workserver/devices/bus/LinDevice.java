package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.lin.LinMessage;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

/**
 * Lin设备
 */
@Getter
public abstract class LinDevice extends BaseLinDevice implements TestProcessListener {

    public LinDevice() {

    }

    public LinDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    public boolean stopAllLinMessage() {
        return stopAllLinMessage(null);
    }
    public abstract boolean stopAllLinMessage(Integer deviceChannel);

    public abstract boolean pauseAllLinMessage(Integer deviceChannel);

    /**
     * 停发报文
     *
     * @param message 报文
     * @return 是否停发报文成功
     */
    protected abstract void stopSend(LinMessage message);

    protected abstract CyclicTask sendLinMessage(Integer deviceChannel, LinMessage message) throws BusError;

    protected abstract CyclicTask stopLinMessage(Integer deviceChannel, Integer messageId);

    protected abstract CyclicTask stopLinMessage(Integer deviceChannel, String messageId);

}
