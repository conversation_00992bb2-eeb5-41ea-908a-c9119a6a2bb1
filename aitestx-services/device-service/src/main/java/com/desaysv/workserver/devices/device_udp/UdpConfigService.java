package com.desaysv.workserver.devices.device_udp;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.devices.device_udp.config.UdpConfig;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.filemanager.project.UdpA2lFileManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Getter
@Service
@Slf4j
@Lazy
public class UdpConfigService {
    private UdpConfig udpConfig;

    public UdpConfig loadConfig(String projectName) {
        UdpA2lFileManager fileManager = ProjectFileManager.of(projectName, UdpA2lFileManager.class);
        String jsonText = fileManager.readUdpA2lConfig();
        udpConfig = JSON.parseObject(jsonText, UdpConfig.class);
        if (udpConfig == null) {
            udpConfig = new UdpConfig();
        }
        return udpConfig;
    }

    public void writeConfig(UdpConfig udpConfig) {
        log.info("更新udpConfig文件配置:{}", udpConfig);
        UdpA2lFileManager fileManager = ProjectFileManager.of(udpConfig.getProject(), UdpA2lFileManager.class);
        fileManager.writeUdpA2lConfig(JSON.toJSONString(udpConfig));
        this.udpConfig = udpConfig;
    }

}
