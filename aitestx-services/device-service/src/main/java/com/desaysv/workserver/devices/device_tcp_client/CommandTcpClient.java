package com.desaysv.workserver.devices.device_tcp_client;

import com.desaysv.workserver.devices.bus.fdx.FdxUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.DatagramSocket;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.util.HashMap;
import java.util.Map;

import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.hexStringToHexBytes;
import static com.desaysv.workserver.devices.device_udp.XcpCommander.bytesToHexString;

@Setter
@Slf4j
public class CommandTcpClient extends DatagramSocket {
    private static CommandTcpClient INSTANCE;
    private String serverIP;
    private int serverPort;
    private Socket socket;
    public static final int BUFFER_SIZE = 1024;
    public static final int DEFAULT_TIMEOUT = 5000;
    public static final int RETRY_TIMES = 3;
    private static final String RESPONSE_CODE = "0f02";
    private static final String RESPONSE_FLAG = "10";
    private static final Map<String, String> commands = new HashMap<>();
    static {
        commands.put("initCommand", "02 fd 00 05 00 00 00 07 0F 02 00 00 00 00 00");
        commands.put("0xF187", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 87");
        commands.put("0xF18A", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8A");
        commands.put("0xF18C", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8C");
        commands.put("0xF18B", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8B");
        commands.put("0xF193", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 93");
        commands.put("0x03C1", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 03 C1");
        commands.put("0xF195", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 95");
        commands.put("0xF14A", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 4A");
        commands.put("0xF14B", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 4B");
        commands.put("0xF15F", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 5F");
        commands.put("0xF1A8", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 A8");
        commands.put("0xF026", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F0 26");
    }

    public CommandTcpClient() throws SocketException {
        super();
    }

    public static CommandTcpClient getInstance() {
        if (INSTANCE == null) {
            try {
                INSTANCE = new CommandTcpClient();
                INSTANCE.setSoTimeout(10000);
            } catch (SocketException e) {
                log.error("~~TCP超时~~");
                log.error(e.getMessage(), e);
            }
        }
        return INSTANCE;
    }

    public boolean initialize() {
        boolean flag = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            try {
                socket = new Socket();
                socket.connect(new InetSocketAddress(serverIP, serverPort), DEFAULT_TIMEOUT);
                // 发送初始化指令
                byte[] initCmd = hexStringToHexBytes(commands.get("initCommand").replaceAll(" ", ""));
                log.info("initCmd:{}" , Hex.encodeHexString(initCmd));
                OutputStream os = socket.getOutputStream();
                os.write(initCmd);
                os.flush();
                // 接收响应
                InputStream is = socket.getInputStream();
                byte[] buffer = new byte[BUFFER_SIZE];
                int len = is.read(buffer);
                if (len > 0) {
                    String response = bytesToHexString(buffer).substring(0, len * 2);
                    log.info("connect response:{}", response);
                    int index = response.indexOf(RESPONSE_CODE);
                    if (index != -1 && index + 8 < response.length()) {
                        String res = response.substring(index + 8, index + 10);
                        if (res.equals(RESPONSE_FLAG)) {
                            flag = true;
                            break;
                        }
                    }
                }
            } catch (IOException e) {
                log.error("初始化失败:{}" , e.getMessage());
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        return flag;
    }

    public String sendReceive(String items) {
        try {
            String cmd = commands.get(items);
            byte[] cmdBytes = hexStringToHexBytes(cmd.replace(" ", ""));
            OutputStream os = socket.getOutputStream();
            os.write(cmdBytes);
            os.flush();
            Thread.sleep(2000);
            InputStream is = socket.getInputStream();
            byte[] buffer = new byte[BUFFER_SIZE];
            int len = is.read(buffer);
            if (len > 0) {
                return FdxUtils.bytesToHex(buffer).substring(0, len * 2);
            }
            return null;
        } catch (IOException | InterruptedException e) {
            log.error("发送失败: {}" , e.getMessage());
            return null;
        }
    }

    public boolean closeDevice() {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
            return true;
        } catch (IOException e) {
            log.error("关闭连接失败: {}" , e.getMessage());
            return false;
        }
    }
}
