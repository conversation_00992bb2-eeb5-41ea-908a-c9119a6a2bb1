package com.desaysv.workserver.devices.bus.tosun.lin;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.tosun.TSLin;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * TC1016底层接口
 */
public class TC1016 extends TSLin {

    public TC1016(){

    }
    public TC1016(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.TC1016;
    }

}
