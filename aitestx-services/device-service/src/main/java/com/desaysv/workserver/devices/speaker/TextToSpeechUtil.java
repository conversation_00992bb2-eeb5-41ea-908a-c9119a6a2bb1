package com.desaysv.workserver.devices.speaker;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文本到语音转换工具类
 */
@Slf4j
public class TextToSpeechUtil {

    // 缓存，存储文本与其生成的音频文件路径
    private static final ConcurrentHashMap<String, String> ttsCache = new ConcurrentHashMap<>();

    // 缓存目录
    private static final String CACHE_DIR = System.getProperty("java.io.tmpdir") + File.separator + "tts_cache";

    static {
        // 确保缓存目录存在
        try {
            Files.createDirectories(Paths.get(CACHE_DIR));
            log.info("TTS缓存目录已创建: {}", CACHE_DIR);
        } catch (IOException e) {
            log.warn("创建TTS缓存目录失败: {}", e.getMessage());
        }
    }

    /**
     * 将文本合成为语音
     *
     * @param text 要合成的文本内容
     * @return 合成后的音频文件
     */
    public static File synthesize(String text) {
        log.info("合成文本为语音: {}", text);

        if (text == null || text.isEmpty()) {
            log.warn("文本内容为空，无法合成语音");
            return null;
        }

        try {
            // 根据文本生成一个稳定的文件名
            String fileId = generateTextHash(text);
            String cachedFilePath = ttsCache.get(fileId);
            File cachedFile = cachedFilePath != null ? new File(cachedFilePath) : null;

            // 检查缓存中是否存在该文本对应的音频文件
            if (cachedFile != null && cachedFile.exists() && cachedFile.length() > 0) {
                log.info("找到缓存的语音文件: {}", cachedFile.getAbsolutePath());
                return cachedFile;
            }

            // 创建新的音频文件
            File outputFile = new File(CACHE_DIR, "tts_" + fileId + ".wav");

            // 使用Windows TTS生成语音
            try {
                generateWindowsTTS(text, outputFile);

                // 添加到缓存
                ttsCache.put(fileId, outputFile.getAbsolutePath());
                log.info("语音合成完成并缓存: {}", outputFile.getAbsolutePath());

                return outputFile;
            } catch (Exception e) {
                log.warn("Windows TTS失败，回退到简单蜂鸣声: {}", e.getMessage());
                // 如果Windows TTS失败，回退到简单蜂鸣声
                generateSimpleBeepSound(outputFile);

                // 简单蜂鸣声也加入缓存
                ttsCache.put(fileId, outputFile.getAbsolutePath());
            }

            log.info("语音合成完成，保存到: {}", outputFile.getAbsolutePath());
            return outputFile;
        } catch (Exception e) {
            log.error("语音合成失败", e);
            return null;
        }
    }

    /**
     * 根据文本内容生成唯一的哈希值
     */
    private static String generateTextHash(String text) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(text.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.substring(0, 16); // 取前16位，避免文件名过长
        } catch (NoSuchAlgorithmException e) {
            log.warn("生成文本哈希失败，回退到UUID: {}", e.getMessage());
            // 如果哈希算法不可用，回退到UUID，但加上文本长度作为前缀，增加一些一致性
            return text.length() + "_" + UUID.nameUUIDFromBytes(text.getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 使用Windows系统TTS生成语音文件
     *
     * @param text       要转换的文本
     * @param outputFile 输出文件
     * @throws IOException 如果文件操作失败
     */
    private static void generateWindowsTTS(String text, File outputFile) throws IOException {
        try {
            log.info("使用Windows TTS转换文本: {}", text);

            // 创建临时bat文件
            File batFile = File.createTempFile("tts_script_", ".bat");

            try (FileOutputStream fos = new FileOutputStream(batFile)) {
                // PowerShell命令通过SAPI生成语音文件
                String command = "@echo off\r\n" +
                        "powershell -Command \"Add-Type -AssemblyName System.Speech; " +
                        "$speak = New-Object System.Speech.Synthesis.SpeechSynthesizer; " +
                        "$speak.SetOutputToWaveFile('" + outputFile.getAbsolutePath().replace("\\", "\\\\") + "'); " +
                        "$speak.Speak('" + text.replace("'", "''") + "'); " +
                        "$speak.Dispose()\"";

                fos.write(command.getBytes());
            }

            // 执行bat文件
            Process process = Runtime.getRuntime().exec("cmd /c " + batFile.getAbsolutePath());
            int exitCode = process.waitFor();

            // 删除临时bat文件
            batFile.delete();

            if (exitCode != 0) {
                log.error("Windows TTS执行失败，退出码: {}", exitCode);
                throw new IOException("Windows TTS执行失败");
            }

            log.info("Windows TTS语音生成完成: {}", outputFile.getAbsolutePath());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("Windows TTS处理被中断", e);
        }
    }

    /**
     * 生成一个简单的蜂鸣声音文件（仅用于示例）
     * 实际项目中应替换为真实的TTS实现
     */
    private static void generateSimpleBeepSound(File file) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 创建一个简单的WAV文件头
            byte[] header = createWavHeader(44100 * 2, 44100);
            fos.write(header);

            // 创建一个简单的音频波形（正弦波）
            byte[] audioData = generateSineWaveData(440, 1.0);  // 440Hz，持续1秒
            fos.write(audioData);

            fos.flush();
        }
    }

    /**
     * 创建WAV文件头
     */
    private static byte[] createWavHeader(int dataSize, int sampleRate) {
        byte[] header = new byte[44];

        // RIFF chunk
        header[0] = 'R';  // RIFF
        header[1] = 'I';
        header[2] = 'F';
        header[3] = 'F';

        // Chunk size
        int totalSize = dataSize + 36;
        header[4] = (byte) (totalSize & 0xff);
        header[5] = (byte) ((totalSize >> 8) & 0xff);
        header[6] = (byte) ((totalSize >> 16) & 0xff);
        header[7] = (byte) ((totalSize >> 24) & 0xff);

        // WAVE chunk
        header[8] = 'W';  // WAVE
        header[9] = 'A';
        header[10] = 'V';
        header[11] = 'E';

        // fmt subchunk
        header[12] = 'f';  // fmt
        header[13] = 'm';
        header[14] = 't';
        header[15] = ' ';

        // Subchunk1Size (16 for PCM)
        header[16] = 16;
        header[17] = 0;
        header[18] = 0;
        header[19] = 0;

        // AudioFormat (1 for PCM)
        header[20] = 1;
        header[21] = 0;

        // NumChannels (1 for mono)
        header[22] = 1;
        header[23] = 0;

        // SampleRate
        header[24] = (byte) (sampleRate & 0xff);
        header[25] = (byte) ((sampleRate >> 8) & 0xff);
        header[26] = (byte) ((sampleRate >> 16) & 0xff);
        header[27] = (byte) ((sampleRate >> 24) & 0xff);

        // ByteRate = SampleRate * NumChannels * BitsPerSample/8
        int byteRate = sampleRate * 1 * 16 / 8;
        header[28] = (byte) (byteRate & 0xff);
        header[29] = (byte) ((byteRate >> 8) & 0xff);
        header[30] = (byte) ((byteRate >> 16) & 0xff);
        header[31] = (byte) ((byteRate >> 24) & 0xff);

        // BlockAlign = NumChannels * BitsPerSample/8
        header[32] = 2;  // 1 * 16 / 8 = 2
        header[33] = 0;

        // BitsPerSample
        header[34] = 16;  // 16 bits
        header[35] = 0;

        // data subchunk
        header[36] = 'd';  // data
        header[37] = 'a';
        header[38] = 't';
        header[39] = 'a';

        // Subchunk2Size
        header[40] = (byte) (dataSize & 0xff);
        header[41] = (byte) ((dataSize >> 8) & 0xff);
        header[42] = (byte) ((dataSize >> 16) & 0xff);
        header[43] = (byte) ((dataSize >> 24) & 0xff);

        return header;
    }

    /**
     * 生成简单的正弦波音频数据
     */
    private static byte[] generateSineWaveData(double frequency, double durationInSeconds) {
        int sampleRate = 44100;
        int numSamples = (int) (sampleRate * durationInSeconds);
        byte[] buffer = new byte[numSamples * 2];  // 16-bit = 2 bytes per sample

        double amplitude = 32760;  // Max amplitude for 16-bit audio
        double angularFrequency = 2.0 * Math.PI * frequency / sampleRate;

        for (int i = 0; i < numSamples; i++) {
            double time = i / (double) sampleRate;
            short sample = (short) (amplitude * Math.sin(angularFrequency * i));

            // 写入小端序（low byte, high byte）
            buffer[2 * i] = (byte) (sample & 0xff);
            buffer[2 * i + 1] = (byte) ((sample >> 8) & 0xff);
        }

        return buffer;
    }

    /**
     * 清理缓存目录中的旧文件
     * 可以定时调用此方法避免缓存过大
     */
    public static void cleanupOldCacheFiles(long maxAgeMillis) {
        try {
            log.info("开始清理TTS缓存文件");
            File cacheDir = new File(CACHE_DIR);
            if (!cacheDir.exists() || !cacheDir.isDirectory()) {
                return;
            }

            long now = System.currentTimeMillis();
            File[] files = cacheDir.listFiles((dir, name) -> name.startsWith("tts_") && name.endsWith(".wav"));
            if (files != null) {
                int count = 0;
                for (File file : files) {
                    if (now - file.lastModified() > maxAgeMillis) {
                        String path = file.getAbsolutePath();
                        if (file.delete()) {
                            count++;
                            // 从缓存中移除
                            ttsCache.values().removeIf(cachedPath -> cachedPath.equals(path));
                        }
                    }
                }
                log.info("清理完成，共删除{}个过期的TTS缓存文件", count);
            }
        } catch (Exception e) {
            log.warn("清理TTS缓存文件失败: {}", e.getMessage());
        }
    }
}
