package com.desaysv.workserver.finder;

import com.desaysv.workserver.common.visa.ConcreteVisaDevice;
import com.desaysv.workserver.common.visa.VisaDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@Lazy
public class VisaInstrumentDeviceFinder extends BaseDeviceFinder {

    private static final String[] forbiddenHardwareName = {
            "PXI0:MEMACC", "ASRL\\d+::INSTR"
    };

    /**
     * 解析Visa仪器属性
     *
     * @param instrumentName 仪器名称
     * @return 仪器属性
     */
    public static HardwareProperty parseVisaInstrumentProperty(String instrumentName) {
        HardwareProperty hardwareProperty = new HardwareProperty();
        Pattern pattern = Pattern.compile("USB\\d+::(0[xX][\\da-fA-F]+)::(0[xX][\\da-fA-F]+)::\\d+::INSTR");
        Matcher matcher = pattern.matcher(instrumentName);
        String vid = "", pid = "";
        if (matcher.find()) {
            vid = matcher.group(1);
            pid = matcher.group(2);
        }
        hardwareProperty.setVid(vid);
        hardwareProperty.setPid(pid);
        return hardwareProperty;
    }

    /**
     * 查找所有VISA仪器
     *
     * @param deviceModel 设备型号
     * @param isLock      是否锁定
     * @return 所有VISA仪器
     */
    public List<Device> findPhysicalVisaInstruments(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();

        try (JVisaResourceManager resourceManager = new JVisaResourceManager()) {
            int deviceIndex;
            String[] resources = resourceManager.findResources();
            for (String address : resources) {
                if (isForbidden(address, forbiddenHardwareName)) {
                    continue;
                }
                VisaDevice device = new ConcreteVisaDevice();
                device.setDeviceName(address);
                device.setDeviceUniqueCode(address);
                deviceIndex = Device.getDeviceModelIndex(deviceModel);
                device.setAliasName(deviceModel + "#" + deviceIndex);
//                device.setDeviceAliasName(deviceModel + "#" + deviceIndex);
                device.setDeviceOperationParameter(new DeviceOperationParameter());
                devices.add(device);
            }
            if (isLock) {
                devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
            }
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
        }
        return devices;
    }

}
