package com.desaysv.workserver.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模板图片信息模型类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TemplateImageInfo {
    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板图片路径
     */
    private String templateImagePath;

    /**
     * 原图路径
     */
    private String originalImagePath;

    /**
     * 相似度
     */
    private double similarity;

    /**
     * UUID
     */
    private String uuid;
}
