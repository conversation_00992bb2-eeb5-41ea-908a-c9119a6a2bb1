package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * 电阻板卡工厂
 */
public interface AbstractResistanceFactory extends AbstractDeviceCreator {

    Device createResistanceDevice(DeviceRegisterForm deviceRegisterForm);

    Device createRemoteResistanceDevice(DeviceRegisterForm deviceRegisterForm);

    Device createResistanceQRDevice(DeviceRegisterForm deviceRegisterForm);
}
