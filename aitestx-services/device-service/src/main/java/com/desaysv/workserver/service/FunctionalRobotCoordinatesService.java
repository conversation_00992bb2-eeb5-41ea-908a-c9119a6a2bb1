package com.desaysv.workserver.service;

import com.desaysv.workserver.model.FunctionalRobotCoordinates;
import com.desaysv.workserver.vo.robot.FunctionalRobotCoordinatesQueryVo;

import java.util.List;

public interface FunctionalRobotCoordinatesService {

    Integer addFunctionalCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates);

    Integer updateFunctionalCoordinates(FunctionalRobotCoordinates functionalRobotCoordinates);

    FunctionalRobotCoordinates getFunctionalCoordinatesByUUID(String funcCoordinatesUUID);

    List<FunctionalRobotCoordinates> getFunctionalCoordinates(FunctionalRobotCoordinatesQueryVo funcCoordinatesQuery);
}
