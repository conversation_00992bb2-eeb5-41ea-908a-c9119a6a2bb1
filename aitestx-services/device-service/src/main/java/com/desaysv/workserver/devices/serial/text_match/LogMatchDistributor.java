package com.desaysv.workserver.devices.serial.text_match;

import com.desaysv.workserver.devices.serial.UsbSerialPortEventListener;
import com.desaysv.workserver.monitor.data.DataDistributor;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-8 11:41
 * @description :
 * @modified By :
 * @since : 2022-7-8
 */
public class LogMatchDistributor extends DataDistributor<String> {

    private final MatchDataConsumer consumer;

    private final UsbSerialPortEventListener serialPortEventListener;

    @Getter
    private boolean started;

    public LogMatchDistributor(MatchDataConsumer consumer, UsbSerialPortEventListener serialPortEventListener) {
        super(consumer);
        this.consumer = consumer;
        this.serialPortEventListener = serialPortEventListener;
        started = false;
    }

    public void init() {
        consumer.clear();
    }

    public List<String> getMatchingText() {
        return consumer.getMatchingText();
    }

    public void updateFindText(String findText) {
        consumer.setFindText(findText);
    }

    public void startDataConsumer() {
        serialPortEventListener.startDataConsumer(this);
        started = true;
    }

    public void pauseDataConsumer() {
        serialPortEventListener.pauseDataConsumer(this);
    }

    public void resumeDataConsumer() {
        serialPortEventListener.resumeDataConsumer(this);
    }

    public void stopDataConsumer() {
        serialPortEventListener.stopDataConsumer(this);
        started = false;
    }

    public void startMatch(String text) {
        init();
        if (!isStarted()) {
            startDataConsumer();
        } else {
            updateFindText(text);
            resumeDataConsumer();
        }
    }


}
