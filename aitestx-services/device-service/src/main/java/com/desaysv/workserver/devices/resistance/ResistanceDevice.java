package com.desaysv.workserver.devices.resistance;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.interfaces.IResistorBoard;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.fazecast.jSerialComm.SerialPort;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ResistanceDevice extends PortDevice implements IResistorBoard {

    private SerialPort serialPort;
    private int oldResistanceValue = 0;
    private volatile boolean canSend = true; // 用于控制是否可以发送消息的标志
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1); // 定时器

    public ResistanceDevice() {
        this(new DeviceOperationParameter());
    }

    public ResistanceDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        serialPort = SerialPort.getCommPort(getDevicePortName());
        if (!serialPort.isOpen()) {
            serialPort.openPort(1000);
        } else {
            return true;
        }
        // 串口的波特率等参数设置
        // 数据位：8
        // 停止位：1
        // 校验位：None
        serialPort.setFlowControl(SerialPort.FLOW_CONTROL_DISABLED);
        serialPort.setComPortParameters(9600,
                8,
                SerialPort.ONE_STOP_BIT,
                SerialPort.NO_PARITY);
        serialPort.setComPortTimeouts(SerialPort.TIMEOUT_READ_BLOCKING | SerialPort.TIMEOUT_WRITE_BLOCKING, 1000, 1000);
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (serialPort != null && serialPort.isOpen()) {
            serialPort.closePort();
        }
        return true;
    }

    /**
     * 释放设备资源
     */
    @Override
    public void dispose() {
        scheduler.shutdown(); // 关闭定时器
    }

    /**
     * 往串口发送数据
     */
    public boolean sendResistanceValue(int resistance) {
        if (isSimulated()) {
            return true;
        }
        if (oldResistanceValue == 0) { // 如果为0则是第一次进入
            oldResistanceValue = resistance;
            sendOrder(resistance);
            return true;
        }
        
        // 使用非递归方式处理等待
        if (!canSend) {
            log.debug("设备忙，等待可用...");
            try {
                // 等待直到可以发送，最多等待3秒
                int maxWaitTime = 3000; // 最大等待时间
                int waitInterval = 100; // 检查间隔
                int totalWaited = 0;
                
                while (!canSend && totalWaited < maxWaitTime) {
                    Thread.sleep(waitInterval);
                    totalWaited += waitInterval;
                }
                
                if (!canSend) {
                    log.warn("等待设备可用超时");
                    return false;
                }
            } catch (InterruptedException e) {
                log.error("等待过程被中断", e);
                Thread.currentThread().interrupt(); // 重置中断状态
                return false;
            }
        }
        
        // 发送指令
        sendOrder(resistance);
        oldResistanceValue = resistance;
        canSend = false;
        
        // 400ms后允许再次发送 - 保持原有延迟时间以确保稳定性
        scheduler.schedule(() -> canSend = true, 400, TimeUnit.MILLISECONDS);
        return true;
    }

    @SneakyThrows
    private void sendOrder(int resistance) {
        // 商数
        byte quotient = (byte) (resistance / 256);
        // 余数
        byte remainder = (byte) (resistance % 256);
        byte[] content = {0, 0, quotient, remainder, 0};
        log.info("发送指令: {}", byteArrayToHexString(content));
        if (!serialPort.isOpen()) {
            open();
        }
        serialPort.writeBytes(content, content.length);
    }

    private String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_RESISTANCE;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Resistance.INSTRUMENT_RESISTANCE;
    }

    @Override
    public boolean writeResistanceBoardCard(Integer deviceChannel, int resistance) {
        log.info("电阻板卡通道{}设置阻值{}Ω", deviceChannel, resistance);
        return sendResistanceValue(resistance);
    }
}
