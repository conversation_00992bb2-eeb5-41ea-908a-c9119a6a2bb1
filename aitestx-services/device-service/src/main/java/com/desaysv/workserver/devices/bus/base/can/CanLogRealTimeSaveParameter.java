package com.desaysv.workserver.devices.bus.base.can;

import com.desaysv.workserver.config.can.DbcConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
@AllArgsConstructor
public class CanLogRealTimeSaveParameter{
    private String filePath;
    private String fileType;
    private String filterType;
    private String filterText;
    private DbcConfig dbcConfig;
    public String getFriendlyString() {
        if (filterText.isEmpty()) {
            return String.format("保存目录：%s，文件类型：%s", filePath, fileType);
        } else {
            return String.format("保存路径：%s，文件类型：%s，过滤条件:%s+%s", filePath, fileType, filterType, filterText);

        }
    }

    public CanLogRealTimeSaveParameter(String filePath, String fileType, String filterType, String filterText) {
        this.filePath = filePath;
        this.fileType = fileType;
        this.filterType = filterType;
        this.filterText = filterText;
    }
}
