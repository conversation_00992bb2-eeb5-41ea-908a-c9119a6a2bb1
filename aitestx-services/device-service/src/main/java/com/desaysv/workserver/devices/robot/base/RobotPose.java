package com.desaysv.workserver.devices.robot.base;

import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import lombok.Data;

@Data
public class RobotPose  {
    private Double x;
    private Double y;
    private Double z;
    private Double r;
    private Double slideRail;
    private Double retain;
    private Integer user;
    private Integer tool;
    public RobotPose() {

    }

    public RobotPose(Double x, Double y, Double z, Double r) {
        this.x = x;
        this.y = y;
        this.z = z;
        this.r = r;
    }

    public MoveEntity toMoveEntity() {
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(x);
        moveEntity.setY(y);
        moveEntity.setZ(z);
        moveEntity.setR(r);
        moveEntity.setSlideRail(slideRail);
        return moveEntity;
    }
}
