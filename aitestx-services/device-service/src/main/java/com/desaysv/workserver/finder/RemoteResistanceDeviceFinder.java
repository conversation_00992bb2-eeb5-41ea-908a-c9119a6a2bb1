package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.resistance.RemoteResistanceDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@Lazy
public class RemoteResistanceDeviceFinder {

    public List<Device> findAllIpResistance(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        RemoteResistanceDevice device;
        int deviceIndex;
        try {
            for (String ipAddr : RemoteResistanceDevice.IP_ADDRESS) {
                device = new RemoteResistanceDevice();
                device.setDeviceName(ipAddr);
                device.setDeviceUniqueCode(device.getDeviceName());
                deviceIndex = Device.getDeviceModelIndex(deviceModel);
                device.setAliasName(deviceModel + "#" + deviceIndex);
//                device.setDeviceAliasName(deviceModel + "#" + deviceIndex);
                device.setDeviceOperationParameter(new DeviceOperationParameter());
                devices.add(device);
            }
        } catch (Exception e) {
            log.debug(e.getMessage(), e);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), deviceModel));
        }
        return devices;
    }
}
