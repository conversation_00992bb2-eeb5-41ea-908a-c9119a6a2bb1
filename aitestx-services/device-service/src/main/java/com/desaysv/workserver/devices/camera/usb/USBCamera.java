package com.desaysv.workserver.devices.camera.usb;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 14:24
 * @description : USB2.0相机
 * @modified By :
 * @since : 2022-4-12
 */
@Getter
@Slf4j
public class USBCamera extends CameraDevice {

    private final String deviceModel = DeviceModel.Camera.USB_CAMERA;

    public USBCamera() {
        this(new DeviceOperationParameter());
    }

    public USBCamera(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

}
