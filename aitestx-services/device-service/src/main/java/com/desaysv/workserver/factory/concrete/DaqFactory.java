package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.keysight.KeySight34461A;
import com.desaysv.workserver.devices.daq.art_daq.USB3200N;
import com.desaysv.workserver.devices.daq.ut8806.Ut8806;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.interfaces.AbstractDaqFactory;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class DaqFactory implements AbstractDaqFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Daq.KEYSIGHT_34461A)) {
            return createKeysight34461A(deviceRegisterForm);
        } else if (deviceModel.equals(DeviceModel.Daq.UT8806)) {
            return createUT8806(deviceRegisterForm);
        }
        else if (deviceModel.equals(DeviceModel.Daq.USB3200N)) {
            return createUSB3200N(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createKeysight34461A(DeviceRegisterForm deviceRegisterForm) {
        return new KeySight34461A(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createUT8806(DeviceRegisterForm deviceRegisterForm) {
        return new Ut8806(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createUSB3200N(DeviceRegisterForm deviceRegisterForm) {
        return new USB3200N(deviceRegisterForm.getDeviceOperationParameter());
    }
}
