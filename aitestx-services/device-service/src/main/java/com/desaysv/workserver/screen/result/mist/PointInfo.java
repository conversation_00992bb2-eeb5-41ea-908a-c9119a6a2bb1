package com.desaysv.workserver.screen.result.mist;

import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.screen.entity.TouchPoint;
import lombok.Data;

@Data
public class PointInfo {

    private PointInt point;
    private TouchPoint touchPoint;

    public PointInfo(PointInt point, TouchPoint touchPoint) {
        this.point = point;
        this.touchPoint = touchPoint;
    }

    @Override
    public String toString() {
        return String.format("%s->%s", point, touchPoint.toByteString());
    }
}
