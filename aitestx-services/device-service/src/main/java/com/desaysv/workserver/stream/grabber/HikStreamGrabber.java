package com.desaysv.workserver.stream.grabber;

import com.desaysv.workserver.constants.SystemEnv;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 海康相机捕捉
 */
@Slf4j
public class HikStreamGrabber extends StreamGrabber {

    private final HikCameraLayer hikCameraLayer;

    public HikStreamGrabber(int devicePort) {
        hikCameraLayer = new HikCameraLayer(devicePort);
    }

    private void checkCamera() throws FrameGrabberException {
        if (!hikCameraLayer.isValid()) {
            throw new FrameGrabberException(String.format("%s句柄为空", HikCameraLayer.class.getSimpleName()));
        }
    }

    @Override
    public Frame grab() throws FrameGrabberException {
        checkCamera();
        return hikCameraLayer.grabImageBuffer(true).getFrame();
    }

    @Override
    public ImageBuffer grabByteBuffer() throws FrameGrabberException {
        checkCamera();
        return hikCameraLayer.grabImageBuffer(false);
    }

    @Override
    public synchronized void close() throws FrameGrabberException {
        checkCamera();
        hikCameraLayer.close();
    }


    @Override
    public void setImageWidth(int width) {
        try {
            checkCamera();
            hikCameraLayer.getHikAttribute().setWidth(width);
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void setImageHeight(int height) {
        try {
            checkCamera();
            hikCameraLayer.getHikAttribute().setHeight(height);
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public int getImageWidth() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getWidth();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    @Override
    public int getImageHeight() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getHeight();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    @Override
    public void start() throws FrameGrabberException {
        checkCamera();
        start(null, null);
    }

    @Override
    public void start(Integer width, Integer height) throws FrameGrabberException {
        checkCamera();
        if (!hikCameraLayer.open()) {
            throw new FrameGrabberException("MV_CC_OpenDevice打开相机失败");
        }
        if (width != null) {
            setImageWidth(width);
        }
        if (height != null) {
            setImageHeight(height);
        }
        hikCameraLayer.config();
        if (!hikCameraLayer.startGrabbing()) {
            throw new FrameGrabberException("MV_CC_StartGrabbing采集视频流失败");
        }
        //设置曝光时间
        if (SystemEnv.isExposureTimeSet()) {
            hikCameraLayer.getHikAttribute().setExposureTime(SystemEnv.getCameraExposureTime());
        }
        //获取相机参数
        float gain = hikCameraLayer.getHikAttribute().getGain();
        log.info("增益:{}", gain);
        float exposureTime = hikCameraLayer.getHikAttribute().getExposureTime();
        log.info("曝光时间:{}us", exposureTime);
        float resultingFrameRate = hikCameraLayer.getHikAttribute().getResultingFrameRate();
        log.info("采集帧率:{}fps", resultingFrameRate);
    }

    @Override
    public void stop() {

    }

    @Override
    public void trigger() {

    }

    @Override
    public void release() {

    }


    public void setExposureAuto(int autoExposureMode, int valueLower, int valueUpper) {
        try {
            checkCamera();
            hikCameraLayer.getHikAttribute().setAutoExposure(autoExposureMode);
            hikCameraLayer.getHikAttribute().setAutoExposureTimeLowerLimit(valueLower);
            hikCameraLayer.getHikAttribute().setAutoExposureTimeUpperLimit(valueUpper);
//            System.out.println("相机自动曝光模式" + hikCameraLayer.getHikAttribute().getAutoExposure());
//            System.out.println("相机自动曝光下限" + hikCameraLayer.getHikAttribute().getAutoExposureTimeLowerLimit());
//            System.out.println("相机自动曝光上限" + hikCameraLayer.getHikAttribute().getAutoExposureTimeUpperLimit());
//            System.out.println("相机帧率" + hikCameraLayer.getHikAttribute().getCameraFrameRate());
//            System.out.println("相机曝光时间" + hikCameraLayer.getHikAttribute().getExposureTime());
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    public void setReverseX(boolean reverseX) {
        try {
            checkCamera();
            hikCameraLayer.getHikAttribute().setReverseX(reverseX);
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    public void setReverseY(boolean reverseY) {
        try {
            checkCamera();
            hikCameraLayer.getHikAttribute().setReverseY(reverseY);
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public CameraSettings getCameraSettings(CameraSettings cameraSettings) {
        cameraSettings.setAutoExposureMode(getAutoExposure());
        cameraSettings.setAutoExposureTimeLower(getAutoExposureTimeLowerLimit());
        cameraSettings.setAutoExposureTimeUpper(getAutoExposureTimeUpperLimit());
        cameraSettings.setExposureTime(getExposureTime());
        cameraSettings.setFrameRate(getCameraFrameRate());
        return cameraSettings;
    }

    public int getAutoExposure() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getAutoExposure();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    public int getAutoExposureTimeLowerLimit() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getAutoExposureTimeLowerLimit();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    public int getAutoExposureTimeUpperLimit() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getAutoExposureTimeUpperLimit();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return 0;
    }

    public Float getExposureTime() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getExposureTime();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public Float getCameraFrameRate() {
        try {
            checkCamera();
            return hikCameraLayer.getHikAttribute().getCameraFrameRate();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public void grabberSwitch(boolean status) {
        try {
            if (status) {
                if (!hikCameraLayer.startGrabbing()) {
                    throw new FrameGrabberException("MV_CC_StartGrabbing采集视频流失败");
                }
            } else {
                checkCamera();
                if (!hikCameraLayer.stopGrabbing()) {
                    throw new FrameGrabberException("MV_CC_StopGrabbing停止采集失败");
                }
            }

        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        HikStreamGrabber grabber = new HikStreamGrabber(0);
        try {
            grabber.start();
            while (true) {
                Frame frame = grabber.grab();
                ImageIO.write(Java2DFrameUtils.toBufferedImage(frame), "jpeg", new File("D:\\uidp4666\\Desktop\\haikang\\hik.jpeg"));
                TimeUnit.MILLISECONDS.sleep(500);
            }
        } catch (IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        try {
            grabber.close();
        } catch (FrameGrabberException e) {
            log.error(e.getMessage(), e);
        }
    }
}
