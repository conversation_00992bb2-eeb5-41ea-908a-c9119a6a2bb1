package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.TestDeviceMapper;
import com.desaysv.workserver.service.TestDeviceService;
import com.desaysv.workserver.model.TestDevice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 11:11
 * @description :
 * @modified By :
 * @since : 2022-5-6
 */
@Service
public class TestDeviceServiceImpl implements TestDeviceService {

    @Autowired
    private TestDeviceMapper testDeviceMapper;


    @Override
    public Integer removeDevice(Integer deviceId) {
        return testDeviceMapper.deleteByPrimaryKey(deviceId);
    }

    @Override
    public Integer addDevice(TestDevice device) {
        return testDeviceMapper.insert(device);
    }

    @Override
    public TestDevice getDevice(Integer deviceId) {
        return testDeviceMapper.selectByPrimaryKey(deviceId);
    }

    @Override
    public TestDevice getDeviceByUniqueCode(String deviceUniqueCode) {
        return testDeviceMapper.selectByUniqueCode(deviceUniqueCode);
    }

    @Override
    public List<TestDevice> getAllDevices() {
        return testDeviceMapper.selectAll();
    }

    @Override
    public Integer updateDevice(TestDevice device) {
        return testDeviceMapper.updateByPrimaryKey(device);
    }


}
