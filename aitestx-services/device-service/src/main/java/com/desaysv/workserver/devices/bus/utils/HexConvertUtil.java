package com.desaysv.workserver.devices.bus.utils;

public class HexConvertUtil {

    public static void main(String[] args) {
        String hexStr = "112233445566778899";
        try {
            long result = convertHexToDecimal(hexStr);
            System.out.println("转换结果: " + result);
        } catch (IllegalArgumentException e) {
            System.err.println(e.getMessage());
        }
    }

    /**
     * 将十六进制字符串转换为十进制整数值。
     *
     * @param hexStr 十六进制字符串。
     * @return 十进制整数值。
     */
    public static long convertHexToDecimal(String hexStr) {
        try {
            return Long.parseLong(hexStr, 16);
        } catch (NumberFormatException e) {
            if (hexStr.length() > 2) {
                hexStr = hexStr.substring(2);
                try {
                    return Long.parseLong(hexStr, 16);
                } catch (NumberFormatException ne) {
                    return convertHexToDecimal(hexStr);
                }
            } else {
                throw new IllegalArgumentException("原始十六进制字符串长度不足，无法去掉两位");
            }
        }
    }


    /**
     * 将十进制的双精度数转换为十六进制字符串。
     * 该方法将双精度数先四舍五入为整数，然后转换为十六进制。
     *
     * @param originalValue 十进制的双精度数。
     * @return 十六进制的字符串表示。
     */
    public static String convertDecimalToHex(double originalValue) {
        // 四舍五入双精度数为最接近的整数
        long integerValue = Math.round(originalValue);

        // 将整数值转换为十六进制字符串
        return Long.toHexString(integerValue).toUpperCase();
    }

    /**
     * 判断是否是16进制字符
     *
     * @param str
     * @return
     */
    public static boolean isHexNumeric(String str) {
        // 正则表达式匹配16进制数字
        String regex = "^[0-9a-fA-F]+$";
        // 检查字符串是否匹配正则表达式
        if (str != null && str.matches(regex)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * byte转换字符串
     *
     * @param bytes
     * @return
     */
    public static String convertBytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            hexString.append(String.format("%02X", b));
        }
        return hexString.toString();
    }
}
