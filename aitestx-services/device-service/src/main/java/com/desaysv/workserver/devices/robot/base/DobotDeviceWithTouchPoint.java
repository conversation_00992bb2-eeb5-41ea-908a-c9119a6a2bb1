package com.desaysv.workserver.devices.robot.base;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.MonitorType;
import com.desaysv.workserver.WebSocketServer;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.robot.interfaces.IRobotWithTouchPoint;
import com.desaysv.workserver.devices.robot.type.mg400.entity.ArcEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.LongTouchRobotCoordinate;
import com.desaysv.workserver.devices.robot.type.mg400.entity.NamedRobotCoordinates;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.finder.DeviceFinderManager;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.monitor.data.DataDistributor;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.monitor.data.MonitorAction;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.screen.ScreenService;
import com.desaysv.workserver.screen.config.ScreenConfig;
import com.desaysv.workserver.screen.distributor.EventBasedTouchPointDistributor;
import com.desaysv.workserver.screen.entity.PointManager;
import com.desaysv.workserver.screen.entity.PreciousPoint2D;
import com.desaysv.workserver.screen.entity.PreciousPoint3D;
import com.desaysv.workserver.screen.events.ArcTouchEvent;
import com.desaysv.workserver.screen.events.ClickEvent;
import com.desaysv.workserver.screen.events.LineTouchEvent;
import com.desaysv.workserver.screen.result.mist.PointInfo;
import com.desaysv.workserver.screen.result.report.TouchReport;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.awt.geom.Rectangle2D;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 具备报点检测功能的机械臂接口
 */
@Slf4j
public abstract class DobotDeviceWithTouchPoint extends DefaultDobotDevice implements IRobotWithTouchPoint {
    @JSONField(serialize = false)
    private DeviceDataDispatcher<TouchReport> touchPointReportDispatcher;

    public DobotDeviceWithTouchPoint(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public boolean close() throws DeviceCloseException {
        //清理touchPointReportDispatcher
        if (touchPointReportDispatcher != null) {
            touchPointReportDispatcher.stopAll();
            touchPointReportDispatcher = null;
        }
        return super.close();
    }

    public void monitorTouchReport(MonitorAction monitorAction) {
        // 初始化分发程序
        if (touchPointReportDispatcher == null) {
            touchPointReportDispatcher = new DeviceDataDispatcher<>();
            pushTouchReportToWebsocket(monitorAction);
        }
    }

    private void productTouchReportToDispatcher(OperationResult operationResult) {
        if (touchPointReportDispatcher == null) {
            return;
        }
        TouchReport touchReport = (TouchReport) operationResult.getData();
        if (touchReport == null) {
            touchReport = new TouchReport();
            touchReport.setOk(false);
            touchReport.setTouchType(TouchReport.TouchType.UNDEFINED);
        }
        touchPointReportDispatcher.product(MonitorType.TOUCH_REPORT, touchReport);
    }

    private void pushTouchReportToWebsocket(MonitorAction monitorAction) {
        DataDistributor<TouchReport> dataDistributor = new DataDistributor<>(data -> {
            List<WebSocketServer> webSocketServers = WebSocketServer.getWebsocketServers().get(MonitorType.TOUCH_REPORT);
            if (webSocketServers != null) {
                for (WebSocketServer webSocketServer : webSocketServers) {
                    webSocketServer.sendMessageByObject(data);
                }
            }
        });
        touchPointReportDispatcher.start(monitorAction, dataDistributor);
    }

    @Override
    public OperationResult autoScreenCalibration(String serialAliasName) {
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        Device device = deviceFinderManager.findDeviceByAliasName(serialAliasName);
        if (device != null) {
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            EventBasedTouchPointDistributor eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);
            eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
            moveLineDistance("X+", 2);
            List<PointInfo> pointList = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd();
            log.info("pointList:{}", pointList);
            operationResult.ok("自动校准成功");
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }

    private void prepare() {
        MonitorAction monitorAction = MonitorAction.of(getAliasName(), MonitorType.TOUCH_REPORT);
        monitorTouchReport(monitorAction);
    }

    /**
     * @param longTouchRobotCoordinate: 长按按下报点检测
     * @return OperationResult
     * <AUTHOR>
     * @description
     * @date 2024/1/4 17:20
     */
    @Override
    public OperationResult longTouchAndCheckTouchPoint(LongTouchRobotCoordinate longTouchRobotCoordinate) {
        String coordinateName = longTouchRobotCoordinate.getCoordinateName();
        float milliseconds = longTouchRobotCoordinate.getTime();
        prepare();
        log.info("点击并校验坐标名\"{}\"", coordinateName);
        ClickEvent clickEvent = new ClickEvent();
        NamedRobotCoordinates namedRobotCoordinates = new NamedRobotCoordinates();
        namedRobotCoordinates.setDeviceUniqueCode(getDeviceUniqueCode());
        namedRobotCoordinates.setProjectName(getExecutionContext().get().getProjectName());
        namedRobotCoordinates.setCoordinateName(coordinateName);
        //TODO：改成直接调用robotCoordinates controller的query函数
        ResponseEntity<RobotCoordinates> entity = query(namedRobotCoordinates);
        RobotCoordinates robotCoordinates = entity.getBody();
        if (robotCoordinates == null) {
            return OperationResult.staticFail(String.format("\"%s\"坐标名在数据库无法找到", coordinateName));
        }
        PreciousPoint3D preciousPoint3D = new PreciousPoint3D();
        preciousPoint3D.setX(BigDecimal.valueOf(robotCoordinates.getX()));
        preciousPoint3D.setY(BigDecimal.valueOf(robotCoordinates.getY()));
        preciousPoint3D.setZ(BigDecimal.valueOf(robotCoordinates.getZ()));
        preciousPoint3D.setR(BigDecimal.valueOf(robotCoordinates.getR()));
        clickEvent.setPoint(preciousPoint3D);
        clickEvent.setPointName(coordinateName);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        ScreenConfig screenConfig = screenService.getScreenConfig();
        clickEvent.setSerialAliasName(screenConfig.getSerialAliasName());
        return longTouchAndCheckTouchPoint(clickEvent, milliseconds);
    }

    public OperationResult longTouchAndCheckTouchPoint(ClickEvent clickEvent, float milliseconds) {
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        Device device = deviceFinderManager.findDeviceByAliasName(clickEvent.getSerialAliasName());
        if (device != null) {
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            EventBasedTouchPointDistributor eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);
            eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
            if (isSimulated()) {
                try {
                    TimeUnit.MILLISECONDS.sleep((long) milliseconds);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                touch(clickEvent.getPoint().toMoveEntity());
            }
            operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(clickEvent);
            productTouchReportToDispatcher(operationResult);
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }

    /**
     * 点击并校验
     *
     * @param coordinateName 坐标名
     * @return OperationResult
     */
    @Override
    public OperationResult touchAndCheckTouchPoint(String coordinateName) {
        prepare();
        log.info("点击并校验坐标名\"{}\"", coordinateName);
        ClickEvent clickEvent = new ClickEvent();
        NamedRobotCoordinates namedRobotCoordinates = new NamedRobotCoordinates();
        namedRobotCoordinates.setDeviceUniqueCode(getDeviceUniqueCode());
        namedRobotCoordinates.setProjectName(getExecutionContext().get().getProjectName());
        namedRobotCoordinates.setCoordinateName(coordinateName);
        //TODO：改成直接调用robotCoordinates controller的query函数
        ResponseEntity<RobotCoordinates> entity = query(namedRobotCoordinates);
        RobotCoordinates robotCoordinates = entity.getBody();
        if (robotCoordinates == null) {
            return OperationResult.staticFail(String.format("\"%s\"坐标名在数据库无法找到", coordinateName));
        }
        PreciousPoint3D preciousPoint3D = new PreciousPoint3D();
        preciousPoint3D.setX(BigDecimal.valueOf(robotCoordinates.getX()));
        preciousPoint3D.setY(BigDecimal.valueOf(robotCoordinates.getY()));
        preciousPoint3D.setZ(BigDecimal.valueOf(robotCoordinates.getZ()));
        preciousPoint3D.setR(BigDecimal.valueOf(robotCoordinates.getR()));
        clickEvent.setPoint(preciousPoint3D);
        clickEvent.setPointName(coordinateName);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        ScreenConfig screenConfig = screenService.getScreenConfig();
        clickEvent.setSerialAliasName(screenConfig.getSerialAliasName());
        return touchAndCheckTouchPoint(clickEvent);
    }

    @Override
    public OperationResult randomAndCheckTouchPoint(List<String> robotCoordinatesRandomList) {
        prepare();
        log.info("随机点击并校验范围\"{}\"", robotCoordinatesRandomList);
        ClickEvent clickEvent = new ClickEvent();
        double minX = Double.MAX_VALUE;
        double minY = Double.MAX_VALUE;
        double minZ = Double.MAX_VALUE;
        double minR = Double.MAX_VALUE;
        double maxX = Double.MIN_VALUE;
        double maxY = Double.MIN_VALUE;
        for (String robotCoordinateName : robotCoordinatesRandomList) {
            NamedRobotCoordinates namedRobotCoordinates = new NamedRobotCoordinates();
            namedRobotCoordinates.setDeviceUniqueCode(getDeviceUniqueCode());
            namedRobotCoordinates.setProjectName(getExecutionContext().get().getProjectName());
            namedRobotCoordinates.setCoordinateName(robotCoordinateName);
            //TODO：改成直接调用robotCoordinates controller的query函数
            ResponseEntity<RobotCoordinates> entity = query(namedRobotCoordinates);
            RobotCoordinates robotCoordinates = entity.getBody();
            if (robotCoordinates == null) {
                return OperationResult.staticFail(String.format("\"%s\"坐标名在数据库无法找到", robotCoordinateName));
            }
            double x = robotCoordinates.getX();
            double y = robotCoordinates.getY();
            double z = robotCoordinates.getZ();
            double r = robotCoordinates.getR();
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            minZ = Math.min(minZ, z);
            minR = Math.min(minR, r);
            maxX = Math.max(maxX, x);
            maxY = Math.max(maxY, y);
        }
        double width = Math.abs(maxX - minX);
        double height = Math.abs(maxY - minY);
        Rectangle2D.Double rectangle = new Rectangle2D.Double(minX, minY, width, height);
        Random random = new Random();
        double touchX = rectangle.x + random.nextDouble() * rectangle.width;
        double touchY = rectangle.y + random.nextDouble() * rectangle.height;
        PreciousPoint3D preciousPoint3D = new PreciousPoint3D();
        preciousPoint3D.setX(BigDecimal.valueOf(touchX));
        preciousPoint3D.setY(BigDecimal.valueOf(touchY));
        preciousPoint3D.setZ(BigDecimal.valueOf(minZ));
        preciousPoint3D.setR(BigDecimal.valueOf(minR));
        clickEvent.setPoint(preciousPoint3D);
        clickEvent.setPointName("randomPoint");
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        ScreenConfig screenConfig = screenService.getScreenConfig();
        clickEvent.setSerialAliasName(screenConfig.getSerialAliasName());
        return touchAndCheckTouchPoint(clickEvent);
    }

    @Override
    public OperationResult touchAndCheckTouchPoint(ClickEvent clickEvent) {
        //TODO：这个MG400类改成Spring容器管理
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        Device device = deviceFinderManager.findDeviceByAliasName(clickEvent.getSerialAliasName());
        if (device != null) {
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            EventBasedTouchPointDistributor eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);
            eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
            if (isSimulated()) {
                //模拟点击1s
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                touch(clickEvent.getPoint().toMoveEntity());
            }
            operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(clickEvent);
            productTouchReportToDispatcher(operationResult);
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }

    private PreciousPoint3D getPreciousPoint3D(String coordinateName) {
        NamedRobotCoordinates namedRobotCoordinates = new NamedRobotCoordinates();
        namedRobotCoordinates.setDeviceUniqueCode(getDeviceUniqueCode());
        namedRobotCoordinates.setProjectName(getExecutionContext().get().getProjectName());
        namedRobotCoordinates.setCoordinateName(coordinateName);
        //TODO：改成直接调用robotCoordinates controller的query函数
        ResponseEntity<RobotCoordinates> entity = query(namedRobotCoordinates);
        RobotCoordinates robotCoordinates = entity.getBody();
        assert robotCoordinates != null;
        PreciousPoint3D preciousPoint3D = new PreciousPoint3D();
        preciousPoint3D.setX(BigDecimal.valueOf(robotCoordinates.getX()));
        preciousPoint3D.setY(BigDecimal.valueOf(robotCoordinates.getY()));
        preciousPoint3D.setZ(BigDecimal.valueOf(robotCoordinates.getZ()));
        preciousPoint3D.setR(BigDecimal.valueOf(robotCoordinates.getR()));
        return preciousPoint3D;
    }


    // TODO: 排查 List<String>参数使用JsonArray
    @Override
    public OperationResult swipeAndCheckTouchPoint(JSONArray swipeNameJsonArray) {
        prepare();
        log.info("滑动并校验坐标列表\"{}\"", swipeNameJsonArray);
        List<String> swipeNameArray = swipeNameJsonArray.toJavaList(String.class);
        OperationResult operationResult = new OperationResult();
        for (int i = 0; i < swipeNameArray.size() - 1; i++) {
            String beginCoordinateName = swipeNameArray.get(i);
            String endCoordinateName = swipeNameArray.get(i + 1);
            LineTouchEvent lineTouchEvent = new LineTouchEvent();
            ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
            ScreenConfig screenConfig = screenService.getScreenConfig();
            lineTouchEvent.setSerialAliasName(screenConfig.getSerialAliasName());
            lineTouchEvent.setStartPoint(getPreciousPoint3D(beginCoordinateName));
            lineTouchEvent.setEndPoint(getPreciousPoint3D(endCoordinateName));
            lineTouchEvent.setStartPointName(beginCoordinateName);
            lineTouchEvent.setEndPointName(endCoordinateName);
            operationResult = swipeAndCheckTouchPoint(lineTouchEvent);
        }
        return operationResult;
    }

    @Deprecated
    private OperationResult segmentLineTouchEvent(EventBasedTouchPointDistributor eventBasedTouchPointDistributor, LineTouchEvent lineTouchEvent) {
        PreciousPoint2D startPoint2D = lineTouchEvent.getStartPoint().toPreciousPoint2D();
        PreciousPoint2D endPoint2D = lineTouchEvent.getEndPoint().toPreciousPoint2D();
        //分割线段
        PreciousPoint2D[] lineSplitPoint2DList = PointManager.splitLine(startPoint2D, endPoint2D, BigDecimal.valueOf(lineTouchEvent.getSegmentLength()));
        log.info("lineSplitPoint2DList:{}", Arrays.toString(lineSplitPoint2DList));
        double z = (lineTouchEvent.getStartPoint().getZ().add(lineTouchEvent.getEndPoint().getZ())).divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP).doubleValue();
        double r = (lineTouchEvent.getStartPoint().getR().add(lineTouchEvent.getEndPoint().getR())).divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP).doubleValue();
        LineTouchEvent segmentLineTouchEvent = new LineTouchEvent();
        segmentLineTouchEvent.setSegmentLength(lineTouchEvent.getSegmentLength());
        segmentLineTouchEvent.setSerialAliasName(lineTouchEvent.getSerialAliasName());
        OperationResult operationResult = new OperationResult();
        for (int i = 0; i < lineSplitPoint2DList.length - 1; i++) {
            PreciousPoint2D startPoint = lineSplitPoint2DList[i];
            PreciousPoint2D nextPoint = lineSplitPoint2DList[i + 1];

            if (i == 0) {
                //点击起点
                touch(startPoint.toMoveEntity(z, r), false);
                log.info("准备开始滑动");
            }
            //分段开始监控
            eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
            //移动下一个点
            moveLine(nextPoint.toMoveEntity(z, r));
            if (i == lineSplitPoint2DList.length - 2) {
                //最后一个点
                moveLine(nextPoint.toMoveEntity(z + AUTO_UP_HEIGHT, r));
            }
            segmentLineTouchEvent.setStartPoint(startPoint.toPreciousPoint3D(z, r));
            segmentLineTouchEvent.setEndPoint(nextPoint.toPreciousPoint3D(z, r));
            //分段结束监控
            operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(segmentLineTouchEvent);
        }
        return operationResult;
    }

    private OperationResult wholeLineTouchEvent(EventBasedTouchPointDistributor eventBasedTouchPointDistributor, LineTouchEvent lineTouchEvent) {
        //开始监控
        eventBasedTouchPointDistributor.notifyTouchDataListenerStart();
        if (isSimulated()) {
            //模拟滑动1s
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        } else {
            //滑动
            RobotReply robotReply = swipeTwoPoint(lineTouchEvent.getStartPoint().toMoveEntity(),
                    lineTouchEvent.getEndPoint().toMoveEntity(),
                    true);
            log.info("motion持续时间:{}ms", robotReply.getMotionDuration());
            lineTouchEvent.setTouchDuration(robotReply.getMotionDuration());
        }
        //结束监控
        OperationResult operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(lineTouchEvent);
        productTouchReportToDispatcher(operationResult);
        return operationResult;
    }

    @Override
    public OperationResult swipeAndCheckTouchPoint(LineTouchEvent lineTouchEvent) {
        OperationResult operationResult = new OperationResult();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        Device device = deviceFinderManager.findDeviceByAliasName(lineTouchEvent.getSerialAliasName());
        if (device != null) {
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            EventBasedTouchPointDistributor eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);
            //报告开始
            eventBasedTouchPointDistributor.notifyTouchDataReportStart();
            if (lineTouchEvent.getSegmentLength() == 0) {
                //整体运行
                operationResult = wholeLineTouchEvent(eventBasedTouchPointDistributor, lineTouchEvent);
            } else {
                //分段运行
//                operationResult = segmentLineTouchEvent(eventBasedTouchPointDistributor, lineTouchEvent);
            }

            //报告结束
            eventBasedTouchPointDistributor.notifyTouchDataReportEnd();
            operationResult.ok("滑动并检查报点成功");
        } else {
            operationResult.fail("设备未找到");
        }
        return operationResult;
    }

    @Override
    public OperationResult arcAndCheckTouchPoint(ArcTouchEvent arcTouchEvent) {
        OperationResult operationResult = new OperationResult();
        prepare();
        DeviceFinderManager deviceFinderManager = SpringContextHolder.getBean(DeviceFinderManager.class);
        ScreenService screenService = SpringContextHolder.getBean(ScreenService.class);
        Device device = deviceFinderManager.findDeviceByAliasName(arcTouchEvent.getSerialAliasName());
        if (device != null) {
            operationResult.setOk(true);
            SerialPortDevice serialDevice = (SerialPortDevice) device;
            EventBasedTouchPointDistributor eventBasedTouchPointDistributor = serialDevice.getEventBasedTouchPointDistributor(screenService);

            double degree = 0.5;
            ArcEntity arcEntity = new ArcEntity();
            PreciousPoint2D[] circleSplitPointList = PointManager.splitCircle(arcTouchEvent.getCenterPoint().toPreciousPoint2D(), arcTouchEvent.getRadius(), degree);
            log.info("circleSplitPointList:{}", Arrays.toString(circleSplitPointList));

            double z = arcTouchEvent.getCenterPoint().getZ().doubleValue();
            double r = arcTouchEvent.getCenterPoint().getR().doubleValue();
            ArcTouchEvent segmentArcTouchEvent = new ArcTouchEvent();
            for (int i = 0; i < circleSplitPointList.length; i++) {
                eventBasedTouchPointDistributor.notifyTouchDataListenerStart();

                PreciousPoint2D startPoint = circleSplitPointList[i];
                PreciousPoint2D endPoint = circleSplitPointList[i + 1];

                arcEntity.setX1(startPoint.getX().doubleValue());
                arcEntity.setY1(startPoint.getY().doubleValue());
                arcEntity.setZ1(z);
                arcEntity.setR1(r);

                arcEntity.setX2(endPoint.getX().doubleValue());
                arcEntity.setY2(endPoint.getY().doubleValue());
                arcEntity.setZ2(z);
                arcEntity.setR2(r);
                arc(arcEntity);

//                segmentArcTouchEvent.setCenterPoint();
                operationResult = eventBasedTouchPointDistributor.notifyTouchDataListenerEnd(segmentArcTouchEvent);
                if (!operationResult.isOk()) {
                    // 失败点
                    System.out.println("失败圆弧:" + segmentArcTouchEvent);
                }

            }
        } else {
            operationResult.fail("设备不存在");
        }
        return operationResult;
    }

}
