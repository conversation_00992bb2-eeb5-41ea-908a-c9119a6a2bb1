package com.desaysv.workserver.devices.bus.base.lin;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.devices.bus.LinDevice;
import com.desaysv.workserver.devices.bus.base.*;
import com.desaysv.workserver.devices.bus.interfaces.IBusDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.DataUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 具体Lin接口的抽象总线
 */
@Slf4j
public abstract class LinBus extends LinDevice implements IBusDevice {

//    private final String channelInfo = "unknown";

    @Getter
    @JSONField(serialize = false)
    private final List<CyclicTask> periodTasks = Collections.synchronizedList(new ArrayList<>());

    // 创建一个复用的临时列表
    @JSONField(serialize = false)
    private final List<CyclicTask> tempTasks = new ArrayList<>();

    @JSONField(serialize = false)
    private List<LinFilter> filters;

    @Setter
    @JSONField(serialize = false)
    private BusState state;

    @Setter
    private boolean channelConfigured; //是否配置通道

    @Setter
    @JSONField(serialize = false)
    private LinMessageEventListener linMessageEventListener;

    public LinBus() {

    }

    @Override
    public boolean stopAllLinMessage(Integer deviceChannel) {
        return stopAllPeriodicTasks(deviceChannel);
    }

    @Override
    public boolean pauseAllLinMessage(Integer deviceChannel) {
        return pauseAllPeriodicTasks(deviceChannel);
    }

    /**
     * 构建特定类型的Lin总线接口
     *
     * @param filters          过滤器列表
     * @param configParameters 配置参数
     */
    public LinBus(List<LinFilter> filters, DeviceOperationParameter configParameters) {
        this(configParameters);
        setFilters(filters);
        state = BusState.ACTIVE;
    }

    /**
     * 构建特定类型的lin总线接口
     *
     * @param deviceOperationParameter 配置参数
     */
    public LinBus(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setSimulated(Boolean.parseBoolean(SpringContextHolder.getEnvironmentProperty("appConfig.mockEnabled")));
    }

    @Override
    public void closeOpenButNotConfigDevice() {
        try {
            if (isConnected() && !channelConfigured) {
                log.info("通道未配置，关闭设备");
                close();
            }
        } catch (DeviceCloseException e) {
            log.error(e.getMessage(), e);
        }
    }

    public boolean taskRunning() {
        return !periodTasks.isEmpty();
    }

    private void setFilters(List<LinFilter> filters) {
        this.filters = filters;
        applyFilters(filters);
    }

    /**
     * 检查给定的报文是否匹配到至少一个设定好的过滤器
     *
     * @param message 待检查的报文
     * @return 否匹配到至少一个设定好的过滤器
     */
    private boolean matchesFilters(LinMessage message) {
        //如果没有设置任何过滤器，所有消息都匹配
        if (filters == null) {
            return true;
        }
        for (LinFilter filter : filters) {
            //检查id
            String id = filter.getIdHex();
            //计算
            if (id.equals(message.getIdHex())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 应用过滤器作为Lin接口的硬件过滤
     *
     * @param filters 过滤器列表
     */
    public void applyFilters(List<LinFilter> filters) {

    }

    public void send(LinMessage message) throws BusError {
        Float timeout = null;
        send(message, timeout);
    }

    public abstract void send(LinMessage message, Float timeout) throws BusError;

    public CyclicTask sendPeriodic(LinMessage message, float period) {
        return sendPeriodic(message, period, null, true);
    }

    public CyclicTask sendPeriodic(LinMessage message, float period, Float duration) {
        return sendPeriodic(message, period, duration, true);
    }

    /**
     * 开始发送给定周期的报文
     * 任务处于活跃状态直到满足以下条件任意一个
     * - 超过可选的持续时间
     * - Bus超出范围
     * - Bus被关闭
     * - stopAllPeriodicTasks方法被调用
     * - CyclicTask.stop方法被调用
     *
     * @param message   待发送的报文
     * @param period    每条报文的间隔周期（秒）
     * @param duration  报文发送的持续时间（秒），如果为空，则无限期发送
     * @param storeTask 默认真，任务将会被关联到总线实例中，可以手动禁用总线实例管理
     * @return 已启动的任务实例
     */
    public CyclicTask sendPeriodic(LinMessage message, float period, Float duration, boolean storeTask) {
        if (duration != null && duration < 0) {
            // duration为空，代表一直发送
            duration = null;
        }
        ChannelMessageId taskId = TaskIdGenerator.generateTaskId(message.getChannel(), DataUtils.parseHexString(message.getIdHex()));
        CyclicTask task = getCyclicTaskByTaskId(taskId);
//        log.info("获取任务:{}", task);
        if (task == null) {
            //之前不存在该任务，新建任务
            task = sendPeriodicInternal(message, period, duration);
            task.setTaskId(taskId);
            final CyclicTask finalTask = task;
            task.setCyclicTaskListener(removeTask -> {
                if (removeTask) {
                    periodTasks.remove(finalTask);
                }
            });
            if (storeTask) {
                periodTasks.add(task);
            }
        } else {
            //存在相同任务，修改报文数据
            log.info(String.format("存在相同任务%s，修改报文数据和持续时间", taskId));
            if (task instanceof ThreadBasedCyclicSendTask) {
                LinThreadBasedCyclicSendTask threadBasedCyclicSendTask = (LinThreadBasedCyclicSendTask) task;
                threadBasedCyclicSendTask.modifyData(message);
                threadBasedCyclicSendTask.setDuration(duration);
                threadBasedCyclicSendTask.resume();
            }
        }
        return task;
    }

    public CyclicTask getCyclicTaskByTaskId(ChannelMessageId taskId) {
        synchronized (periodTasks) {
            for (CyclicTask task : periodTasks) {
                ChannelMessageId tId = task.getTaskId();
                if (tId != null && tId.equals(taskId)) {
                    return task;
                }
            }
        }
        return null;
    }


    /**
     * 默认用线程实现周期发送报文
     *
     * @param message  待发送的报文
     * @param period   每条报文的间隔周期（秒）
     * @param duration 报文发送的持续时间（秒），如果为空，则无限期发送
     * @return 已启动的任务实例
     */
    private @Nonnull CyclicSendTask sendPeriodicInternal(LinMessage message, float period, Float duration) {
        return new LinThreadBasedCyclicSendTask(this, message, period, duration, linMessageEventListener);
    }

    public boolean stopAllPeriodicTasks(Integer deviceChannel) {
        return stopAllPeriodicTasks(deviceChannel, true);
    }

    public boolean pauseAllPeriodicTasks(Integer deviceChannel) {
        if (!taskRunning()) {
            return false;
        }
        if (deviceChannel == null) {
            log.info("{}暂停通道所有LIN报文", getDeviceName());
        } else {
            log.info("{}暂停通道{}所有LIN报文", getDeviceName(), deviceChannel);
        }
        for (CyclicTask task : periodTasks) {
            if (deviceChannel == null || task.getTaskId().getChannel() == deviceChannel) {
                task.pause();
            }
        }
        return true;
    }

    /**
     * 停止发送已启动的任何报文发送
     *
     * @param removeTasks 是否停止跟踪已停止的任务
     */
    public boolean stopAllPeriodicTasks(Integer deviceChannel, boolean removeTasks) {
        if (!taskRunning()) {
            return true;
        }
        if (deviceChannel == null) {
            log.info("{}停止通道所有LIN报文", getDeviceName());
        } else {
            log.info("{}停止通道{}所有LIN报文", getDeviceName(), deviceChannel);
        }
        synchronized (tempTasks) {  // 确保临时列表的线程安全
            tempTasks.clear();  // 清空临时列表
            tempTasks.addAll(periodTasks);  // 添加当前任务

            for (CyclicTask task : tempTasks) {
                if (deviceChannel == null || task.getTaskId().getChannel() == deviceChannel) {
                    task.stop(removeTasks);
                }
            }

            tempTasks.clear();  // 使用完后清空，避免内存泄漏
        }
        return true;
    }

    /**
     * 阻塞等待总线的消息
     *
     * @param timeout 超时时间（秒）
     */
    public LinMessage recv(Integer channel, Float timeout) throws BusError {
        long start = System.currentTimeMillis();
        Float timeLeft = timeout;
        while (true) {
            //获取Lin报文
            FilterLinMessage message = recvInternal(channel, timeLeft);
            if (message != null && (message.isAlreadyFiltered() || matchesFilters(message))) {
                log.info("Received:{}", message);
                return message;
            } else if (timeout != null) {
                timeLeft = timeout - (System.currentTimeMillis() - start);
                if (timeLeft <= 0) {
                    return null;
                }
            }
        }
    }

    /**
     * 读取总线的报文并且获取是否被过滤
     * 当过滤器设置后，没有匹配到，或者超时时间还没到，这个方法可能被多次调用
     * 这个方法不能直接调用
     *
     * @param timeout 等待报文的时间（秒）
     * @return FilterLinMessage 报文
     * @throws BusError 读取过程中出错，抛出LinError
     */
    public abstract FilterLinMessage recvInternal(Integer channel, Float timeout) throws BusError;

    /**
     * 丢弃可能在输出缓冲区队列化的每个报文
     */
    public void flushTxBuffer() {

    }

    /**
     * 关闭总线的清理代码
     */
    public void shutdown() {

    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        stopAllPeriodicTasks(null);
        return super.close();
    }

    @Override
    protected void stopSend(LinMessage message) {

    }

    @Override
    public CyclicTask sendLinMessage(Integer deviceChannel, LinMessage message) throws BusError {
        if (!isConnected()) {
            throw new IllegalStateException("LIN总线未打开，无法发送报文");
        }
        message.setChannel(deviceChannel);
        log.info("{}发送Lin报文:{}", getDeviceName(), message);

        if (message.getSendTimes() == -1) {
            //一直发送
            ActionSequencesLoggerUtil.info("{}(通道{})以周期{}ms一直发送Lin报文:{}", message.getIdHex(),
                    message.getChannel(),
                    message.getPeriod() * 1000,
                    StrUtils.getHexStringWithBlank(message.getData()));
            message.setDuration(null);
        } else {
            message.setDuration(message.getSendTimes() * message.getPeriod());
            ActionSequencesLoggerUtil.info("{}(通道{})报文发送持续{}s，发送帧次数:{}，每次连发帧数:{}, 报文数据:{}", message.getIdHex(),
                    message.getChannel(),
                    message.getDuration(),
                    message.getSendTimes(),
                    message.getFramesPerSendNum(),
                    StrUtils.getHexStringWithBlank(message.getData()));
        }
        return sendPeriodic(message, message.getPeriod(), message.getDuration());
    }

    @Override
    public CyclicTask stopLinMessage(Integer deviceChannel, Integer messageId) {
        log.info("{}停止Lin通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%X", messageId));
        return stopLinMessageInternal(deviceChannel, messageId);
    }

    @Override
    public CyclicTask stopLinMessage(Integer deviceChannel, String messageId) {
        return stopLinMessage(deviceChannel, DataUtils.parseHexString(messageId));
    }

    private CyclicTask stopLinMessageInternal(int channel, int messageId) {
        CyclicTask task = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(channel, messageId));
        if (task != null) {
            task.stop();
        }
        // 取消掉此ID的发布
        stopLinSubscibeMessage(channel, messageId);
        return task;
    }

    public abstract void stopLinSubscibeMessage(Integer channel, Integer messageId);

    public List<Integer> fetchRunningLinMessage(Integer deviceChannel) {
        return getPeriodTasks().stream().map(CyclicTask::getTaskId).
                filter(channelMessageId -> channelMessageId.getChannel() == deviceChannel).
                map(ChannelMessageId::getArbitrationId).
                collect(Collectors.toList());
    }

    public void sendError(LinMessage message) {
        log.info("{}发送错误:{}", getDeviceName(), message);
        SseUtils.pubMsg(SseConstants.CAN_SEND_ERROR + message.getChannel(), message.getIdHex());
    }

    public void sendFinish(LinMessage message) {
        log.info("{}发送完成:{}", getDeviceName(), message);
        SseUtils.pubMsg(SseConstants.CAN_SEND_FINISH + message.getChannel(), message.getIdHex());
    }

}
