package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.factory.DeviceRegisterForm;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 11:04
 * @description : 抽象设备注册工厂接口
 * @modified By :
 * @since : 2022-3-16
 */
public interface AbstractDeviceRegisterFactory {

    /**
     * 注册设备
     *
     * @param deviceRegisterForm 设备注册表单
     * @return 设备接口
     */
    Device registerDevice(DeviceRegisterForm deviceRegisterForm);

    /**
     * 断开设备
     *
     * @param deviceName 设备名称
     * @return 设备是否成功断开
     */
    boolean closeDevice(String deviceName) throws DeviceCloseException;

    boolean removeDevice(String deviceName);

    /**
     * 注销并断开设备
     *
     * @param deviceName 设备名称
     * @return 设备是否成功注销
     */
    boolean unregisterAndCloseDevice(String deviceName) throws DeviceCloseException;


    /**
     * 注销所有设备
     *
     * @return
     */
    boolean unregisterAndCloseAllDevices();

    /**
     * 打开设备
     *
     * @param device 设备接口
     * @return 设备是否打开成功
     */
    boolean openDevice(Device device) throws DeviceOpenException, DeviceOpenRepeatException;

    /**
     * 关闭设备
     *
     * @param device 设备接口
     * @return 设备是否关闭成功
     */
    boolean closeDevice(Device device) throws DeviceCloseException;


    /**
     * 获取所有设备
     *
     * @return 所有设备列表
     */
    List<Device> getAllDevices();

    /**
     * 获取设备
     *
     * @param deviceName 设备名称
     * @return 设备接口
     */
    Device getDevice(String deviceName);

    /**
     * 销毁设备时 释放设备资源
     *
     * @param deviceName 设备名称
     */
    void disposeDevice(String deviceName);
}
