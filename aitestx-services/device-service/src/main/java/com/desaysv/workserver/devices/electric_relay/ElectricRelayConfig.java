package com.desaysv.workserver.devices.electric_relay;

import com.desaysv.workserver.config.DeviceConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ElectricRelayConfig extends DeviceConfig {

    private List<String> phyText; // 文本标签

    private List<String> switchOnList; // 开关状态列表

    /**
     * 设备别名
     */
    private String aliasName;

    /**
     * 设备连接状态
     * 0表示断开，1表示连接
     */
    private String connectedStatus;

    /**
     * 通道
     */
    private Integer channel;

    /**
     * 开关状态 on打开 off关闭
     */
    private String switchOn;
}
