package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ILightResistorBoard {
    Logger log = LogManager.getLogger(ILightResistorBoard.class.getSimpleName());

    /**
     * 改变电阻值（44个通道）
     *
     * @param resistance    电阻值（欧姆）
     */
    //TODO:考虑用异常代替boolean返回值失败场景
    boolean writeResistanceBoardCard(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, int resistance) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE_WITH_LIGHT_TEST_BOX"})
    default void changeResistance(Integer deviceChannel, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, int resistance) throws OperationFailNotification {
        ActionSequencesLoggerUtil.info("电阻板卡通道{}-{}-{}，设置阻值{}Ω", deviceChannel,slotNumber,channelNumber,resistance);
        try {
            boolean isWriteOk = writeResistanceBoardCard(deviceChannel, slotNumber, channelNumber, pullUpNumber, resistance);
            ActionSequencesLoggerUtil.info("电阻板卡设置阻值设置:{}", isWriteOk ? "成功" : "失败");
            if (!isWriteOk) {
                throw new OperationFailNotification("电阻板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("电阻板卡写入失败:" + e.getMessage());
        }
    }

}
