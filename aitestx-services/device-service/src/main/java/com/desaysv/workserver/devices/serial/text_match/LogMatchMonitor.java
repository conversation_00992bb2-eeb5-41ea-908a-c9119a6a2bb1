package com.desaysv.workserver.devices.serial.text_match;

import com.desaysv.workserver.monitor.Monitor;
import com.desaysv.workserver.devices.serial.UsbSerialPortEventListener;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-8 11:09
 * @description :
 * @modified By :
 * @since : 2022-7-8
 */
@Slf4j
public class LogMatchMonitor extends Monitor {

    @Data
    @AllArgsConstructor
    private static class MonitorUnit {
        private String monitorType;
        private String monitorText;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MonitorUnit monitorUnit = (MonitorUnit) o;
            return monitorType.equals(monitorUnit.monitorType) && monitorText.equals(monitorUnit.monitorText);
        }

        @Override
        public int hashCode() {
            return Objects.hash(monitorType, monitorText);
        }
    }

    public static final String MUST_EXIST = "mustExist";
    public static final String FORBID_EXIST = "forbidExist";

    private final Map<MonitorUnit, LogMatchDistributor> monitorMap = new ConcurrentHashMap<>();

    private final UsbSerialPortEventListener serialPortEventListener;

    public LogMatchMonitor(UsbSerialPortEventListener serialPortEventListener) {
        this.serialPortEventListener = serialPortEventListener;
    }

    public LogMatchDistributor getLogMatchDistributor(String monitorType, String monitorText) {
        return getLogMatchDistributor(monitorType, monitorText, new RegexMatchDataConsumer(monitorText));
    }

    public LogMatchDistributor getLogMatchDistributor(String monitorType, String monitorText, MatchDataConsumer matchDataConsumer) {
        MonitorUnit monitorUnit = new MonitorUnit(monitorType, monitorText);
        if (!monitorMap.containsKey(monitorUnit)) {
            //首次加载
            LogMatchDistributor logMatchDistributor = new LogMatchDistributor(matchDataConsumer, serialPortEventListener);
            monitorMap.put(monitorUnit, logMatchDistributor);
        }
        return monitorMap.get(monitorUnit);
    }


    public void stopAll() {
        monitorMap.values().forEach(LogMatchDistributor::stopDataConsumer);
    }

    public List<LogMatchDistributor> getRunningMatchDistributors() {
        return new ArrayList<>(monitorMap.values());
    }

}
