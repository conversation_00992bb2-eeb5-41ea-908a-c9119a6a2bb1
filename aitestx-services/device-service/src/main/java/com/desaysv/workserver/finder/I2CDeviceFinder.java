package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.I2C.ConcreteI2CDevice;
import com.desaysv.workserver.devices.I2C.I2CDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * clicker设备查找
 */
@Component
@Slf4j
@Lazy
public class I2CDeviceFinder {

    private static final List<Integer> simulateI2CDevicePorts = Arrays.asList(0, 1, 2);

    /**
     * 查找所有I2C设备
     *
     * @param deviceModel 设备型号
     * @return 所有I2C设备
     */

    public List<Device> findI2CDevice(String deviceModel) {
        List<Device> devices = new ArrayList<>();
        I2CDevice i2CDevice;
        for (int port : simulateI2CDevicePorts) {
            i2CDevice = new ConcreteI2CDevice();
            i2CDevice.setAliasName(deviceModel);
//            i2CDevice.setDeviceAliasName(deviceModel);
            i2CDevice.setDeviceOperationParameter(new DeviceOperationParameter());
            String deviceName = "I2C_";
            i2CDevice.setDeviceName(deviceName +port);
            i2CDevice.setDeviceUniqueCode(deviceName);
            devices.add(i2CDevice);
        }

        return devices;
    }

}
