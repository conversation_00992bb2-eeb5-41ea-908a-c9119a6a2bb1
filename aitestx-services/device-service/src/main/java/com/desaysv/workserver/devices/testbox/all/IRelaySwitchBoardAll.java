package com.desaysv.workserver.devices.testbox.all;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.interfaces.IRelaySwitchBoard;
import com.desaysv.workserver.devices.testbox.interfaces.light.ILightRelaySwitchBoard;
import com.desaysv.workserver.devices.testbox.interfaces.rzcu.IRZCURelaySwitchBoard;

public interface IRelaySwitchBoardAll extends IRelaySwitchBoard, ILightRelaySwitchBoard, IRZCURelaySwitchBoard {

    @Override
    default void writeRelaySwitchBoardCard(Integer deviceChannel, String command, String physicalMeaning) throws OperationFailNotification {
        IRelaySwitchBoard.super.writeRelaySwitchBoardCard(deviceChannel, command, physicalMeaning);
    }

}
