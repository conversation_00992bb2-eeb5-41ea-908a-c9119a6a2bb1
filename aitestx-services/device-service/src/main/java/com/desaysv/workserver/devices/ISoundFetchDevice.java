package com.desaysv.workserver.devices;

import com.desaysv.workserver.exceptions.device.DeviceReadException;

public interface ISoundFetchDevice {

    /**
     * 获取音量
     *
     * @return 电流
     */
//    @JsonIgnore
    float fetchVolume(Integer deviceChannel, String currentDirection) throws DeviceReadException;

    void startRecording(Integer deviceChannel);

    void stopRecording();

    String saveRecordingToFile();

    default float fetchVolume(Integer deviceChannel) throws DeviceReadException {
        return fetchVolume(deviceChannel, null);
    }

    default float fetchVolume() throws DeviceReadException {
        return fetchVolume(null, null);
    }

}
