package com.desaysv.workserver.service;

import com.desaysv.workserver.model.TestDevice;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 11:15
 * @description :
 * @modified By :
 * @since : 2022-5-6
 */
public interface TestDeviceService {

    Integer removeDevice(Integer deviceId);

    Integer addDevice(TestDevice device);

    TestDevice getDevice(Integer deviceId);

    TestDevice getDeviceByUniqueCode(String deviceUniqueCode);

    List<TestDevice> getAllDevices();

    Integer updateDevice(TestDevice device);

}
