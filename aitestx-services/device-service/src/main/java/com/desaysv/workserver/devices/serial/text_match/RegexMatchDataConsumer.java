package com.desaysv.workserver.devices.serial.text_match;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class RegexMatchDataConsumer extends MatchDataConsumer {

    private final Map<String, List<String>> textMatcher;

    public RegexMatchDataConsumer(String findText) {
        super(findText);
        textMatcher = new HashMap<>();
    }

    @Override
    public void accept(String line) {
        String findText = getFindText();
        log.info("在{} 中查找文本:{}", line, findText);
        Matcher matcher = Pattern.compile(findText).matcher(line);
        if (matcher.find()) {
            log.info("匹配到:{}", line);
            if (!textMatcher.containsKey(findText)) {
                textMatcher.put(findText, new ArrayList<>());
            }
            textMatcher.get(findText).add(line);

        }
    }

    public void clear() {
        textMatcher.remove(getFindText());
    }

    public List<String> getMatchingText() {
        return textMatcher.getOrDefault(getFindText(), new ArrayList<>());
    }
}