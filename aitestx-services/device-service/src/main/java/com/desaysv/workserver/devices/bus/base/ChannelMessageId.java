package com.desaysv.workserver.devices.bus.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class ChannelMessageId {

    private int channel;

    private int arbitrationId;
    //flexray用
    private int slotId;
    private int base;
    private int rep;
    //Lin用
    private String linId;


    public ChannelMessageId(int channel, int arbitrationId) {
        this.channel = channel;
        this.arbitrationId = arbitrationId;
    }

    /**
     * flexray用
     */
    public ChannelMessageId(int channel, int slotId, int base, int rep) {
        this.channel = channel;
        this.slotId = slotId;
        this.base = base;
        this.rep = rep;
    }

    /**
     * Lin用
     */
    public ChannelMessageId(int channel, String linId) {
        this.channel = channel;
        this.linId = linId;
    }

    @Override
    public String toString() {
        return String.format("Ch%d-0x%x", channel, arbitrationId);
    }
}
