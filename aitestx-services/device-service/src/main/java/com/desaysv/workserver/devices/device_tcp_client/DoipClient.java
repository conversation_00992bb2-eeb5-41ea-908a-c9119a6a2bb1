package com.desaysv.workserver.devices.device_tcp_client;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

public class DoipClient {
    private static final String DUT_IP = "************";
    private static final int PORT = 13400;

    static class HexUtils {
        public static byte[] hexStringToBytes(String hex) {
            hex = hex.replaceAll("\\s+", "");
            if (hex.length() % 2 != 0) {
                throw new IllegalArgumentException("Invalid hex string");
            }
            byte[] bytes = new byte[hex.length() / 2];
            for (int i = 0; i < hex.length(); i += 2) {
                String byteStr = hex.substring(i, i + 2);
                bytes[i / 2] = (byte) Integer.parseInt(byteStr, 16);
            }
            return bytes;
        }

        public static String bytesToHex(byte[] bytes) {
            StringBuilder hex = new StringBuilder(2 * bytes.length);
            for (byte b : bytes) {
                int v = b & 0xFF;
                hex.append(String.format("%02x", v));
            }
            return hex.toString();
        }
    }

    static class TcpSocket {
        private final String targetIp;
        private final int targetPort;
        private Socket socket;

        public TcpSocket(String targetIp, int targetPort) {
            this.targetIp = targetIp;
            this.targetPort = targetPort;
        }

        public boolean initialize() {
            boolean flag = false;
            for (int i = 0; i < 3; i++) {
                try {
                    socket = new Socket();
                    socket.connect(new InetSocketAddress(targetIp, targetPort), 5000);
                    
                    // 发送初始化指令
                    byte[] initCmd = HexUtils.hexStringToBytes("02 fd 00 05 00 00 00 07 0F 02 00 00 00 00 00");
                    OutputStream os = socket.getOutputStream();
                    os.write(initCmd);
                    os.flush();

                    // 接收响应
                    InputStream is = socket.getInputStream();
                    byte[] buffer = new byte[1024];
                    int len = is.read(buffer);
                    if (len > 0) {
                        String response = HexUtils.bytesToHex(buffer).substring(0, len * 2);
                        int index = response.indexOf("0f02");
                        if (index != -1 && index + 8 < response.length()) {
                            String res = response.substring(index + 8, index + 10);
                            if (res.equals("10")) {
                                flag = true;
                                break;
                            }
                        }
                    }
                } catch (IOException e) {
                    System.err.println("初始化失败: " + e.getMessage());
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            return flag;
        }

        public String sendRecv(String cmd) {
            try {
                byte[] cmdBytes = HexUtils.hexStringToBytes(cmd.replace(" ", ""));
                OutputStream os = socket.getOutputStream();
                os.write(cmdBytes);
                os.flush();

                Thread.sleep(2000);

                InputStream is = socket.getInputStream();
                byte[] buffer = new byte[1024];
                int len = is.read(buffer);
                if (len > 0) {
                    return HexUtils.bytesToHex(buffer).substring(0, len * 2);
                }
                return "";
            } catch (IOException | InterruptedException e) {
                return e.getMessage();
            }
        }

        public void close() {
            try {
                if (socket != null && !socket.isClosed()) {
                    socket.close();
                }
            } catch (IOException e) {
                System.err.println("关闭连接失败: " + e.getMessage());
            }
        }
    }

    public static void main(String[] args) {
        TcpSocket doip = new TcpSocket(DUT_IP, PORT);
        try {
            boolean flag = doip.initialize();
            if (!flag) {
                System.out.println("Fail to establish DoIP connection.");
                return;
            }

            String[][] commands = {
                {"硬件版本", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 93"},
                {"供应商代码", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8A"},
                {"制造日期", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8B"},
                {"车辆制造商零件号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 87"},
                {"流水号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 8C"},
                {"ECU软件版本号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 95"},
                {"软件ID", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F0 26"},
                {"引导程序版本号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 4A"},
                {"PreBoot引导程序版本号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 4B"},
                {"启动参数", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 5F"},
                {"底层软件版本号", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 F1 A8"},
                {"BOM", "02 fd 80 01 00 00 00 07 0F 02 04 0D 22 03 C1"}
            };

            for (String[] cmd : commands) {
                String result = doip.sendRecv(cmd[1]);
                System.out.printf("%-20s: %s%n", cmd[0], result);
            }
        } finally {
            doip.close();
        }
    }
}