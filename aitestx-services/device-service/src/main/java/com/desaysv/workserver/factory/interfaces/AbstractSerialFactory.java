package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 11:22
 * @description : 抽象串口工厂
 * @modified By :
 * @since : 2022-6-7
 */
public interface AbstractSerialFactory extends AbstractDeviceCreator {

    Device createSerialPortDevice(DeviceRegisterForm deviceRegisterForm);

    Device createUsbI2CDevice(DeviceRegisterForm deviceRegisterForm);

}
