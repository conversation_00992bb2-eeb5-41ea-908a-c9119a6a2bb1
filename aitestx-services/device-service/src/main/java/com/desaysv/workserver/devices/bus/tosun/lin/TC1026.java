package com.desaysv.workserver.devices.bus.tosun.lin;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.tosun.TSLin;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * TC1026底层接口
 */
public class TC1026 extends TSLin {

    public TC1026(){

    }
    public TC1026(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.TC1026;
    }

}
