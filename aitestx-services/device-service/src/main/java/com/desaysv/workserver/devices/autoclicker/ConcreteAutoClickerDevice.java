package com.desaysv.workserver.devices.autoclicker;

import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;

/**
 * Clicker设备
 */
public class ConcreteAutoClickerDevice extends AutoClickerDevice {

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return super.close();
    }

}
