package com.desaysv.workserver.devices.device_udp.test;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.Scanner;

public class UDPClient {
    public static void main(String[] args) {
        try {
            // 1. 创建客户端Socket（随机端口）
            DatagramSocket clientSocket = new DatagramSocket();
            
            // 2. 获取服务器地址（本机测试）
            InetAddress serverAddress = InetAddress.getByName("localhost");
            
            Scanner scanner = new Scanner(System.in);
            while (true) {
                // 3. 获取用户输入
                System.out.print("请输入要发送的消息（输入exit退出）: ");
                String message = scanner.nextLine();
                
                if ("exit".equalsIgnoreCase(message)) {
                    break;
                }

                // 4. 转换消息为字节数组
                byte[] sendData = message.getBytes("UTF-8");

                // 5. 创建发送数据包（目标端口8888）
                DatagramPacket sendPacket = new DatagramPacket(
                    sendData,
                    sendData.length,
                    serverAddress,
                    9999
                );

                // 6. 发送数据
                clientSocket.send(sendPacket);
                System.out.println("消息已发送，等待服务器响应...");

                // 7. 准备接收缓冲区
                byte[] receiveData = new byte[1024];
                DatagramPacket receivePacket = 
                    new DatagramPacket(receiveData, receiveData.length);

                // 8. 设置超时时间（2秒）
                clientSocket.setSoTimeout(2000);
                
                // 9. 尝试接收回复
                try {
                    clientSocket.receive(receivePacket);
                    String serverResponse = new String(
                        receivePacket.getData(),
                        0,
                        receivePacket.getLength(),
                        "UTF-8"
                    );
                    System.out.println("收到服务器回复: " + serverResponse);
                } catch (Exception e) {
                    System.out.println("未收到服务器响应，可能已丢失数据包");
                }
            }
            
            // 10. 关闭资源
            scanner.close();
            clientSocket.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}