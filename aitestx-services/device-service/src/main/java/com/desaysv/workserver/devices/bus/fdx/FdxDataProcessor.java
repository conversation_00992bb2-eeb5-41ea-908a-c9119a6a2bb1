package com.desaysv.workserver.devices.bus.fdx;

import com.desaysv.workserver.entity.NotificationManager;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.*;
import static com.desaysv.workserver.utils.FileUtils.findAppFilePath;
import static com.desaysv.workserver.utils.FileUtils.findFlashDriverFilePath;

public class FdxDataProcessor {
    public static byte[] createSetSignalDataBytes(String nodeName, String msgName, String signalName, double signalValue) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] msgNameBytes = stringBytesPaddedSize(msgName, 32);
        byte[] signalNameBytes = stringBytesPaddedSize(signalName, 32);
        byte[] signalValueBytes = doubleToHexBytes(signalValue);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, msgNameBytes, signalNameBytes, signalValueBytes});
    }

    public static byte[] createSetCanMsgDataBytes(String nodeName, String msgName, int msgStatus) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] msgStatusBytes = int32ToHexBytes(msgStatus, 8);
        if (StringUtils.isNotEmpty(msgName)) {
            byte[] msgNameBytes = stringBytesPaddedSize(msgName, 32);
            return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, msgNameBytes, msgStatusBytes});
        }
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, msgStatusBytes});
    }

    public static byte[] setCanMessageCSRollingDataBytes(String nodeName, int messageID, int checksumStatus, int rollingCounterStatus) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] messageIDBytes = int32ToHexBytes(messageID, 8);
        byte[] checksumStatusBytes = int32ToHexBytes(checksumStatus);
        byte[] rollingCounterStatusBytes = int32ToHexBytes(rollingCounterStatus);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, messageIDBytes, checksumStatusBytes, rollingCounterStatusBytes});
    }

    public static byte[] createSetCanMessageCycleTimeDataBytes(String nodeName, String messageName, double cycleTime) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] messageNameBytes = stringBytesPaddedSize(messageName, 32);
        byte[] cycleTimeBytes = doubleToHexBytes(cycleTime);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, messageNameBytes, cycleTimeBytes});
    }

    public static byte[] createSetXCPDataBytes(String nodeName, String xcpName, double xcpValue) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] xcpNameBytes = stringBytesPaddedSize(xcpName, 32);
        byte[] xcpValueBytes = doubleToHexBytes(xcpValue);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, xcpNameBytes, xcpValueBytes});
    }

    public static byte[] createFetchXCPRXDataBytes(String nodeName, String getXcpName, double getXcpValue) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] getXcpNameBytes = stringBytesPaddedSize(getXcpName, 32);
        byte[] getXcpValueBytes = doubleToHexBytes(getXcpValue);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, getXcpNameBytes, getXcpValueBytes});
    }


    public static byte[] createSetCanPTSDataBytes(String nodeName, String byteInstruction) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] byteInstructionBytes = stringBytesToCANoeDataType(byteInstruction);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, byteInstructionBytes});  //数组类型长度最大为8的数据
    }

    public static byte[] createSetCanMessageDLCDataBytes(String nodeName, String messageName, double msgDLC) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] messageNameBytes = stringBytesPaddedSize(messageName, 32);
        byte[] msgDLCBytes = doubleToHexBytes(msgDLC);
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, messageNameBytes, msgDLCBytes});
    }

    public static byte[] createSetLinMsgDataBytes(String nodeName, int msgId, int msgStatus) {
        byte[] nodeNameBytes = stringBytesPaddedSize(nodeName, 16);
        byte[] msgStatusBytes = int32ToHexBytes(msgStatus, 8);
        if (msgId != -1) {
            byte[] msgIdBytes = int32ToHexBytes(msgId, 16);
            return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, msgIdBytes, msgStatusBytes});
        }
        return FdxUtils.mergeHexByteArrays(new byte[][]{nodeNameBytes, msgStatusBytes});
    }

    public static byte[] createNotificationUpgradeDataBytes() {
        String upgradeFileDirectoryPath = NotificationManager.getInstance().getFilePath();
        String appFilePath= findAppFilePath(upgradeFileDirectoryPath);
        String flashDriverFilePath= findFlashDriverFilePath(upgradeFileDirectoryPath);
        byte[] appFilePathBytes = stringBytesPaddedSize(appFilePath, 300);
        byte[] flashDriverFilePathBytes = stringBytesPaddedSize(flashDriverFilePath, 300);
        return FdxUtils.mergeHexByteArrays(new byte[][]{appFilePathBytes, flashDriverFilePathBytes});
    }


    public static byte[] createSetTestCaseDataBytes(String caseIdString) {
        return FdxUtils.mergeHexByteArrays(new byte[][]{stringBytesPaddedSize(caseIdString, 24)});
    }


    public static double parseCanXCPValue(byte[] data) {
        int startIndex = data.length - 8;
        return hexBytesToDouble(Arrays.copyOfRange(data, startIndex, startIndex + 8));
    }

    public static  String parseCanPTSRxValue(byte[] data) {
        int startIndex = data.length - 16;
        return hexBytesToString(Arrays.copyOfRange(data, startIndex, startIndex + 16));
    }

    public static double parseSignalValue(byte[] data) {
        int startIndex = data.length - 8;
        return hexBytesToDouble(Arrays.copyOfRange(data, startIndex, startIndex + 8));
    }

    public static int parseIntValueFromBytes(byte[] data) {
        int length = data.length;
        byte[] lastFour = new byte[4];
        System.arraycopy(data, length - 4, lastFour, 0, 4);
        return hexBytesToInt32(lastFour);
    }

    public static int parseCounter(byte[] data) {
        int length = data.length;
        return hexBytesToInt32(new byte[]{data[length - 8], data[length - 7], data[length - 6], data[length - 5]});
    }

}
