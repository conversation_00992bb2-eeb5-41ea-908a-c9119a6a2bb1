package com.desaysv.workserver.finder;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceFileManager;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
@Lazy
public class DeviceFinderManager {


    @Autowired
    @Getter
    private RobotDeviceFinder robotDeviceFinder;

    @Autowired
    @Getter
    private CameraDeviceFinder cameraDeviceFinder;

    @Autowired
    @Getter
    private AndroidDeviceFinder androidDeviceFinder;

    @Autowired
    @Getter
    private VisaInstrumentDeviceFinder visaInstrumentDeviceFinder;

    @Autowired
    @Getter
    private AutoClickerDeviceFinder autoClickerDeviceFinder;

    @Autowired
    @Getter
    private DaqDeviceFinder DaqDeviceFinder;

    @Autowired
    @Getter
    private I2CDeviceFinder i2CDeviceFinder;

    @Autowired
    @Getter
    private RemoteResistanceDeviceFinder remoteResistanceDeviceFinder;

    @Autowired
    @Getter
    private TcpServerFinder tcpServerFinder;

    @Autowired
    @Getter
    private SoundDeviceFinder soundDeviceFinder;

    @Autowired
    @Getter
    private VideoCaptureDeviceFinder videoCaptureDeviceFinder;

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;


    public Device findDeviceByDeviceType(String deviceType, int deviceOrder) {
        //使用deviceType
        Map<Integer, Device> deviceMap = Device.getDeviceInstancesMap().get(deviceType);
        if (deviceMap != null) {
            return deviceMap.get(deviceOrder);
        }

        return null;
    }

    /**
     * 查找设备
     *
     * @param aliasName 设备别名
     * @return 操作结果（data属性=设备实例）
     */
    public Device findDeviceByAliasName(String aliasName) {
        Device device = null;

        //TODO: 减少每次读设备的时间
        DeviceRegisterForm registerForm = DeviceFileManager.getInstance().readDeviceRegisterForm(aliasName);
        if (registerForm != null) {
            // 存在设备注册信息
            String deviceName = registerForm.getDeviceName();
            //TODO 如ZLG200 LIN CAN都有用 这里会有问题
            device = deviceRegisterManager.getDevice(deviceName);
//            if (device == null) {
//                log.info("新建设备注册信息:{}", registerForm);
//                //TODO：利用connectDevice更新DeviceRegisterForm
//                device = deviceRegisterManager.registerDevice(registerForm);
//                operationResult = device.openForOperationResult();
//            }
        }
        return device;
    }

}
