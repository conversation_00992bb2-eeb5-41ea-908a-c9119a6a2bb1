package com.desaysv.workserver.common.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.javassist.*;
import org.apache.ibatis.javassist.bytecode.CodeAttribute;
import org.apache.ibatis.javassist.bytecode.LocalVariableAttribute;
import org.apache.ibatis.javassist.bytecode.MethodInfo;
import org.aspectj.lang.JoinPoint;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 18:27
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
//@Component
//@Aspect
//@ComponentScan
//@EnableAspectJAutoProxy
@Slf4j
public class LogAop {

    public void printMethodParams(JoinPoint joinPoint) {
        if (joinPoint == null) {
            return;
        }
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        log.info("className = {}", className);
        log.info("methodName = {}", methodName);

        Object[] methodArgs = joinPoint.getArgs();

        try {
            String[] paramNames = getFieldsName(className, methodName);
            logParam(paramNames, methodArgs);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private String[] getFieldsName(String class_name, String method_name) throws Exception {
        Class<?> clazz = Class.forName(class_name);
        String className = clazz.getName();
        ClassPool pool = ClassPool.getDefault();
        ClassClassPath classPath = new ClassClassPath(clazz);
        pool.insertClassPath(classPath);

        CtClass ctClass = pool.get(className);
        CtMethod ctMethod = ctClass.getDeclaredMethod(method_name);
        MethodInfo methodInfo = ctMethod.getMethodInfo();
        CodeAttribute codeAttribute = methodInfo.getCodeAttribute();
        LocalVariableAttribute attr = (LocalVariableAttribute) codeAttribute.getAttribute(LocalVariableAttribute.tag);
        if (attr == null) {
            return null;
        }
        String[] paramsArgsName = new String[ctMethod.getParameterTypes().length];
        int pos = Modifier.isStatic(ctMethod.getModifiers()) ? 0 : 1;

        for (int i = 0; i < paramsArgsName.length; i++) {
            paramsArgsName[i] = attr.variableName(i + pos);
        }
        return paramsArgsName;
    }

    private boolean isPrimitive(Class<?> clazz) {
        return clazz.isPrimitive() || clazz == String.class;
    }

    private void logParam(String[] paramsArgsName, Object[] paramsArgsValue) {
        if (ArrayUtils.isEmpty(paramsArgsName) || ArrayUtils.isEmpty(paramsArgsValue)) {
            log.info("该方法没有参数");
            return;
        }
        StringBuilder buffer = new StringBuilder();
        for (int i = 0; i < paramsArgsName.length; i++) {
            //参数名
            String name = paramsArgsName[i];
            //参数值
            Object value = paramsArgsValue[i];
            buffer.append(name).append(" = ");
            if (isPrimitive(value.getClass())) {
                buffer.append(value).append(" ,");
            } else {
                buffer.append(value).append(" ,");
            }
        }
        log.info(buffer.toString());
    }

//    @Before("execution(public * com.desaysv.mainserver.struct.device.concrete.power..*.*(..))")
//    public void before(JoinPoint joinPoint) {
//        this.printMethodParams(joinPoint);
//    }
}
