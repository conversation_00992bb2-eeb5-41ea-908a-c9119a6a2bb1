package com.desaysv.workserver.devices.dc_collector.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IDeviceDcCollector {
    Logger log = LogManager.getLogger(IDeviceDcCollector.class.getSimpleName());

    float fetchCurrent(int deviceChannel) throws DeviceReadException;

    float fetchVoltage(int deviceChannel) throws DeviceReadException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_CURRENT"})
    default ActualExpectedResult GetCurrent(Integer deviceChannel, float current) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float getCurrent = -1;
        try {
            getCurrent = fetchCurrent(deviceChannel);
            pass = getCurrent == current;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCC", pass, getCurrent);
        log.info("采集电流:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_VOLTAGE"})
    default ActualExpectedResult GetVoltage(Integer deviceChannel, float voltage) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        float getVoltage = -1;
        try {
            getVoltage = fetchVoltage(deviceChannel);
            pass = getVoltage == voltage;
        } catch (DeviceReadException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCC", pass, getVoltage);
        log.info("采集电压:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

}
