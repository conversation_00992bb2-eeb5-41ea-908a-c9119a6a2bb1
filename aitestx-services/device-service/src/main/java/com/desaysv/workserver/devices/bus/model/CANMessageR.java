package com.desaysv.workserver.devices.bus.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CANMessageR {
    /**
     * 时间戳
     */
    private long time;

    /**
     * 通道
     */
    private String chn;

    /**
     * ID
     */
    private String id;

    /**
     * idHex
     */
    private String idHex;

    /**
     * 名称
     */
    private String name;

    /**
     * CAN类型 CAN CANFD CANFD加速
     */
    private String eventType;

    /**
     * 流向 TX发送方向 RX接收方向
     */
    private String dir;

    /**
     * DLC
     */
    private int dlc;

    /**
     * 发送次数
     */
    private int sendCount;

    /**
     * 是否开启E2E
     */
    private boolean openE2E;

    /**
     * e2e类型
     */
    private String e2eType;

    /**
     * 间隔ms
     */
    private int interval;

    /**
     * Data
     */
    private byte[] data;    //CAN消息数据

    /**
     * 是否循环发送
     */
    private boolean isLoop;//循环发送

    /**
     * 循环发送报文数据
     */
    private List<byte[]> loopDatas;//循环发送报文数据

    /**
     * Can信号
     */
    private List<CANSignalR> canSignalList;
}
