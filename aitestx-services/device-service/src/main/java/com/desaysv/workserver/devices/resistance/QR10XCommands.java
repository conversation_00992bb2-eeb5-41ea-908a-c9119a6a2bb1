package com.desaysv.workserver.devices.resistance;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/2/26 19:45
 */
public class QR10XCommands {
    //查询设定值(SP)
    public static final String QUERY_SET_SP = "AT+USER.SP?";
    //设置SP
    public static final String SET_SP = "AT+USER.SP=";
    //设置SP(递增)
    public static final String SET_SP_INCREMENT = "AT+USER.SP+=";
    //设置SP(递减)
    public static final String SET_SP_DECREMENT = "AT+USER.SP-=";
    //查询输出值PV
    public static final String QUERY_OUTPUT_PV = "AT+USER.PV?";
    //查询最小输出限制值
    public static final String QUERY_MIN_OUTPUT_LIMIT = "AT+USER.RLIMIT?";
    //设置最小输出限制值
    public static final String SET_MIN_OUTPUT_LIMIT = "AT+USER.RLIMIT=";
    //获取内部温度
    public static final String QUERY_INTERNAL_TEMPERATURE = "AT+USER.T_SENSOR?";
    //获取设备型号
    public static final String QUERY_DEVICE_MODEL = "AT+DEV.TYPE?";

}
