package com.desaysv.workserver.stream.rtmp;


import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.stream.IStreamRecorder;
import com.desaysv.workserver.stream.PortStreamProducerAdapter;
import com.desaysv.workserver.utils.command.ShortCommandExecutor;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FrameRecorder;


public class RtmpPortStreamProducer extends PortStreamProducerAdapter implements IStreamRecorder {
    /// 推流
    private FrameRecorder recorder;

    public RtmpPortStreamProducer(int w, int h) {
        super(w, h);
    }

    @Override
    public void startRecorder() {
        recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264); // 28
        recorder.setFormat("flv"); // rtmp的类型
    }

    @Override
    public void stopRecorder() throws FrameRecorder.Exception {
        if (recorder != null) {
            recorder.stop();
        }
    }

    @Override
    public void closeRecorder() throws FrameRecorder.Exception {
        if (recorder != null) {
            recorder.close();
            recorder = null;
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        closeRecorder();
    }


    @Override
    protected void startStream(String deviceModel, int devicePort) throws Exception {

    }

    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {

    }

    public void startNginx() {
        String nginxOpen = "D:\\nginx 1.7.11.3 Gryphon\\start_rtmp.bat";
        ShortCommandExecutor.defaultExecuteCommand(nginxOpen);
    }

    public static void main(String[] args) {
//        String url = "rtmp://127.0.0.1:1935/live/stream";
//        RtmpStreamProducer rtmpStreamProducer = new RtmpStreamProducer(url);
//
////        pusher.startNginx();
//
//        ///采集摄像头
//        FrameGrabber grabber = new OpenCVFrameGrabber(0);
////        FrameGrabber grabber = new FFmpegFrameGrabber("src/main/resources/videos/4k.mp4");
////        rtmpPusher.setWidth(3840);
////        rtmpPusher.setHeight(2160);
//        rtmpStreamProducer.setGrabber(grabber);
//        rtmpStreamProducer.push(System.out::println);
//        rtmpStreamProducer.start();
    }

}