package com.desaysv.workserver.screen.entity;

import com.desaysv.workserver.screen.config.CheckSumAlgorithm;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 显示屏触摸报点
 */
@Getter
@Setter
public class TouchPoint implements Cloneable {

    private int[] headers; //表头数组（可能表示多个字节）

//    private byte sync; //表头，例如0x5A
//
//    private byte direction; //发送方向，01->to test board, 02->to pc

    private byte orderLength;//命令长度

    private byte dataLength; //数据长度

    private int[] data; //数据

    private int checkSum;//校验，其他字节相加

    private String checkSumAlgorithm; //ADD8 ADD16

    private int calHeaderCheckSum() {
        int cs = 0;
        for (int b : headers) {
            cs += b & 0xFF;
        }
        return cs;
    }

    public int calCheckSum() {
        int dataSum = 0;
        for (int d : data) {
            dataSum += d & 0xFF;
        }
        checkSum = calHeaderCheckSum() + dataSum;
        if (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD8)) {
            checkSum &= 0xFF;
        } else if (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD16)) {
            checkSum &= 0xFFFF;
        } else {
            checkSum &= 0xFF;
        }
//        checkSum = (byte) (sync + direction + orderLength + dataLength + dataSum);
        return checkSum;
    }

    public List<String> toByteArray() {
        List<String> byteList = new ArrayList<>();
        for (int b : headers) {
            byteList.add(ByteUtils.byteToHex((byte) b));
        }
        for (int d : data) {
            byteList.add(ByteUtils.byteToHex((byte) d));
        }
        if (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD8)) {
            byteList.add(ByteUtils.byteToHex((byte) checkSum));
        } else if (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD16)) {
            byteList.add(ByteUtils.intToHex(checkSum));
        } else {
            byteList.add(ByteUtils.byteToHex((byte) checkSum));
        }
        return byteList;
    }

    public String toByteString() {
        return String.join(" ", toByteArray());
    }

    @Override
    public TouchPoint clone() {
        try {
            TouchPoint touchPoint = (TouchPoint) super.clone();
            touchPoint.setData(data.clone());
            return touchPoint;
        } catch (CloneNotSupportedException e) {
            TouchPoint touchPoint = new TouchPoint();
            touchPoint.setHeaders(headers.clone());
//            touchPoint.setSync(sync);
//            touchPoint.setDirection(direction);
            touchPoint.setOrderLength(orderLength);
            touchPoint.setDataLength(dataLength);
            touchPoint.setData(data.clone());
            touchPoint.setCheckSum(checkSum);
            return touchPoint;
        }
    }

    @Override
    public String toString() {
        return String.format("TouchPoint(headers=%s, orderLength=%d, dataLength=%d, data=%s, checkSum=%X",
                ByteUtils.intArrayToHexString(headers), orderLength, dataLength, ByteUtils.intArrayToHexString(data), checkSum);
    }

    public static void main(String[] args) {
        byte[] list = new byte[]{0x41, 0x01, 0x01, (byte) 0x90, 0x03, (byte) 0xE8, 0x02, 0x7E};
        int sum = 0;
        for (byte b : list) {
            sum += b;
        }
        System.out.println(sum);
    }
}
