package com.desaysv.workserver.stream;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceUnknownException;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.model.TestDevice;
import com.desaysv.workserver.model.roi.AbsoluteRoiRect;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.stream.rtsp.RtspAndroidStreamProducer;
import com.desaysv.workserver.stream.rtsp.RtspQnxStreamProducer;
import com.desaysv.workserver.stream.rtsp.RtspStream;
import com.desaysv.workserver.stream.rtsp.RtspVideoCaptureStreamProducer;
import com.desaysv.workserver.stream.sharedmemory.SharedMemoryPortStreamProducer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 15:31
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Service
@Slf4j
@Lazy
public class StreamService {

    @Autowired
    private StreamDistributor streamDistributor;
    public static final String VIDEO_CAPTURE_NAME = "UGREEN";

    // 缓存暂停时候的快照帧
    private final Map<String, Frame> snapshotFrameCache = new ConcurrentHashMap<>();

    private final Map<String, BlockingQueue<Frame>> frameBuffers = new ConcurrentHashMap<>();
    private final Map<String, Integer> frameRates = new ConcurrentHashMap<>();
    private final Map<String, Boolean> recordingFlags = new ConcurrentHashMap<>();
    private final Map<String, Thread> recordingThreads = new ConcurrentHashMap<>();


    private void throwMediaServerNotRunningException() {
        throw new IllegalStateException("流媒体服务器还未工作");
    }

    /**
     * 根据ROI捕获图像
     *
     * @param deviceUUID 相机uuid
     * @param rect       ROI
     * @return
     */
    private Frame grabWithRoi(String deviceUUID, AbsoluteRoiRect rect) throws FrameGrabberException {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.grabFrame(rect);
    }

    private Frame grabWithRoi(String deviceUUID, ScaledRoiRect roi) throws FrameGrabberException {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.grabFrame(roi);
    }

    public Frame cropWithRoi(TestDevice device, Frame frame, AbsoluteRoiRect rect) {
        StreamProducer streamProducer = getStreamProducer(device.getUniqueCode());
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.cropFrame(frame, rect);
    }

    public Frame cropWithRoi(Device device, Frame frame, ScaledRoiRect roi) {
        StreamProducer streamProducer = getStreamProducer(device.getDeviceUniqueCode());
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.cropFrame(frame, roi);
    }


    private RectSize getImageSize(String deviceUUID) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.getSize();
    }

    /**
     * 捕获图像
     *
     * @param deviceUUID 相机uuid
     * @return
     */
    public Frame grab(String deviceUUID) throws FrameGrabberException {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }
        return streamProducer.grabFrame();
    }

    public Frame grab(Device device) throws FrameGrabberException {
        return grab(device.getDeviceUniqueCode());
    }

    public Frame grab(TestDevice device) throws FrameGrabberException {
        return grab(device.getUniqueCode());
    }

    public Frame grab(TestDevice device, AbsoluteRoiRect roi) throws FrameGrabberException {
        return grabWithRoi(device.getUniqueCode(), roi);
    }

    public Frame grab(Device device, ScaledRoiRect roi) throws FrameGrabberException {
        return grabWithRoi(device.getDeviceUniqueCode(), roi);
    }

    public Frame grab(Device device, AbsoluteRoiRect roi) throws FrameGrabberException {
        return grabWithRoi(device.getDeviceUniqueCode(), roi);
    }

    public Frame grab(TestDevice device, ScaledRoiRect roi) throws FrameGrabberException {
        return grabWithRoi(device.getUniqueCode(), roi);
    }

    public RectSize getImageSize(Device device) {
        return getImageSize(device.getDeviceUniqueCode());
    }

    public boolean isStreamDevice(Device device) {
        return getStreamProducer(device.getDeviceUniqueCode()) != null;
    }

    public RectSize getImageSize(TestDevice device) {
        return getImageSize(device.getUniqueCode());
    }

    public boolean isDistributed(String deviceUUID) {
        return streamDistributor.isDistributed(deviceUUID);
    }

    public void outputDynamicFrame(String deviceUUID, boolean isOutput) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer != null) {
            streamProducer.outputDynamicFrame(isOutput);
        }
    }

    public StreamProducer getStreamProducer(String deviceUUID) {
        return streamDistributor.getStreamProducer(deviceUUID);
    }

    public StreamProducer registerStreamProducer(GrabRequest grabRequest) {
        StreamProducer streamProducer;
        if (DeviceModel.Utils.contains(DeviceModel.Android.class, grabRequest.getDeviceModel())) {
            streamProducer = new RtspAndroidStreamProducer(
                    grabRequest.getWidth(),
                    grabRequest.getHeight(),
                    grabRequest.getDeviceName()
            );
//        } else if (grabRequest.getDeviceModel().equals(DeviceModel.Camera.HIK_CAMERA)) {
        } else if (DeviceModel.Utils.contains(DeviceModel.VideoCapture.class, grabRequest.getDeviceModel())) {
            streamProducer = new RtspVideoCaptureStreamProducer(
                    grabRequest.getWidth(),
                    grabRequest.getHeight(),
                    grabRequest.getDeviceName());
        } else if (DeviceModel.Utils.contains(DeviceModel.Qnx.class, grabRequest.getDeviceModel())) {
            streamProducer = new RtspQnxStreamProducer(
                    grabRequest.getWidth(),
                    grabRequest.getHeight(),
                    grabRequest.getDeviceName());
        } else if (grabRequest.getDeviceName().contains(VIDEO_CAPTURE_NAME)) {
            streamProducer = new SharedMemoryPortStreamProducer(
                    1920, 1080);
            streamProducer.setFrameRate(60);

        } else {
            streamProducer = new SharedMemoryPortStreamProducer(
                    grabRequest.getWidth(),
                    grabRequest.getHeight());
        }
        log.info("注册视频流生产工厂:{}", streamProducer);
        streamDistributor.register(grabRequest.getDeviceUUID(), streamProducer);
        return streamProducer;
    }

    public String pushStream(GrabRequest grabRequest) throws Exception {
        String deviceUUID = grabRequest.getDeviceUUID();
        String url;
        if (isDistributed(deviceUUID)) {
            //已推送
            outputDynamicFrame(deviceUUID, true);
            url = getStreamProducer(deviceUUID).getUrl();
        } else {
            if (DeviceModel.Utils.contains(DeviceModel.Camera.class, grabRequest.getDeviceModel())) {
                PortStreamProducer streamProducer = (PortStreamProducer) registerStreamProducer(grabRequest);
                url = streamProducer.pushStream(grabRequest.getDeviceModel(), grabRequest.getDevicePort());
            } else if (DeviceModel.Utils.contains(DeviceModel.Android.class, grabRequest.getDeviceModel())) {
                AndroidStreamProducer streamProducer = (AndroidStreamProducer) registerStreamProducer(grabRequest);
                url = streamProducer.pushStream(grabRequest.getDeviceModel(), grabRequest.getDeviceUUID());
            } else if (DeviceModel.Utils.contains(DeviceModel.VideoCapture.class, grabRequest.getDeviceModel())) {
                VideoCaptureStreamProducer streamProducer = (VideoCaptureStreamProducer) registerStreamProducer(grabRequest);
                url = streamProducer.pushStream(grabRequest.getDeviceModel(), grabRequest.getDeviceUUID());
            } else if (DeviceModel.Utils.contains(DeviceModel.Qnx.class, grabRequest.getDeviceModel())) {
                QnxStreamProducer streamProducer = (QnxStreamProducer) registerStreamProducer(grabRequest);
                url = streamProducer.pushStream(grabRequest.getDeviceModel(), grabRequest.getDeviceUUID());
            } else {
                throw new DeviceUnknownException(String.format("设备型号%s未知", grabRequest.getDeviceModel()));
            }
        }
        return url;
    }
    public void setFrameRate(String deviceUUID, float frameRate){
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        streamProducer.setFrameRate(frameRate);
    }

    public void setExposureAuto(String deviceUUID, CameraSettings cameraSettings) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        streamProducer.setExposureAuto(cameraSettings);
    }

    public CameraSettings getCameraSettings(String deviceUUID) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        return streamProducer.getCameraSettings();
    }

    public void setReverseX(String deviceUUID, boolean status) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        streamProducer.setReverseX(status);
    }

    public void setReverseY(String deviceUUID, boolean status) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        streamProducer.setReverseY(status);
    }

    public void grabberSwitch(String deviceUUID, boolean status) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        streamProducer.grabberSwitch(status);
    }

    public RtspStream getRtspStream(String url) {
        RtspStream rtspStream = new RtspStream();
        rtspStream.setUrl(url);
        return rtspStream;
    }

    public RectSize getDefaultSize(String deviceUUID) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer != null) {
            return streamProducer.getDefaultSize();
        }
        return null;
    }

    public void sync(String deviceUUID) {

    }

    public boolean stopStream(String deviceUUID) {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer != null) {
            streamProducer.closeStream();
            streamDistributor.unregister(deviceUUID);
            return true;
        }
        return false;
    }


    public Queue<Frame> recordFrames(String deviceUUID, long recordingTime) throws OperationFailNotification {
        StreamProducer streamProducer = getStreamProducer(deviceUUID);
        if (streamProducer == null) {
            throwMediaServerNotRunningException();
        }

        Queue<Frame> frameQueue = new LinkedBlockingQueue<>();
        long startTime = System.currentTimeMillis();

        try {
            while (System.currentTimeMillis() - startTime < recordingTime) {
                Frame frame = streamProducer.grabFrame();
                if (frame != null) {
                    frameQueue.add(frame.clone());
                }
            }
        } catch (Exception e) {
            throw new OperationFailNotification("Error while recording frames: " + e.getMessage());
        } finally {
//            canvas.dispose();
        }

        return frameQueue;
    }
    public double getFrameRate(String deviceUUID) {
        return getStreamProducer(deviceUUID).getGrabberFrameRate();
    }


    /**
     * 保存帧到缓存
     *
     * @param deviceUUID 相机uuid
     */
    @SneakyThrows
    public void saveSnapshotFrameToCache(String deviceUUID) {
        Frame frame = grab(deviceUUID);
        snapshotFrameCache.put(deviceUUID, frame.clone());
        log.info("保存帧到缓存，设备UUID: {}", deviceUUID);
    }

    /**
     * 从缓存获取帧
     *
     * @param deviceUUID 相机uuid
     * @return 缓存中的帧，如果不存在则返回 null
     */
    public Frame getSnapshotFrameFromCache(String deviceUUID) {
        log.info("读取帧缓存:{}", deviceUUID);
        return snapshotFrameCache.get(deviceUUID);
    }
    public void saveFramesToVideo(Queue<Frame> frameQueue, double frameRate, String projectName, String filePath) throws Exception {
        if (frameQueue.isEmpty()) {
            throw new IllegalArgumentException("帧队列为空");
        }
        Frame firstFrame = frameQueue.peek();
        int width = firstFrame.imageWidth;
        int height = firstFrame.imageHeight;
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String fullFilePath = filePath + "\\" + projectName + "_" + timestamp + ".mp4";
        FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(fullFilePath, width, height);
        recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
        recorder.setVideoOption("preset", "medium");
        recorder.setVideoOption("level", "3.1");
        recorder.setVideoOption("bEnableFrameSkip", "1");
        recorder.setFormat("mp4");
        recorder.setFrameRate(frameRate);
        recorder.start();
        try {
            for (Frame frame : frameQueue) {
                recorder.record(frame);
            }
        } finally {
            recorder.stop();
            recorder.release();
        }
    }


    public void startRecording(String deviceUUID, int recordingTime, int resolution) throws OperationFailNotification {
        StreamProducer producer = getStreamProducer(deviceUUID);
        if (producer == null) {
            throw new OperationFailNotification("设备未注册");
        }

        // 停止已有线程
        stopExistingRecording(deviceUUID);

        // 计算缓冲区大小：时间(秒) * 帧率
        int bufferSize = recordingTime * resolution;
        BlockingQueue<Frame> buffer = new LinkedBlockingQueue<>(bufferSize);

        frameBuffers.put(deviceUUID, buffer);
        frameRates.put(deviceUUID, resolution);
        recordingFlags.put(deviceUUID, true);

        // 创建并保存线程引用
        Thread recordingThread = new Thread(() -> recordingTask(deviceUUID, producer, buffer, resolution));
        recordingThreads.put(deviceUUID, recordingThread);
        recordingThread.start();
    }

    private void recordingTask(String deviceUUID, StreamProducer producer,
                               BlockingQueue<Frame> buffer, int resolution) {
        try {
            long interval = 1000 / resolution;
            while (recordingFlags.getOrDefault(deviceUUID, false)
                    && !Thread.currentThread().isInterrupted()) {
                long start = System.currentTimeMillis();
                Frame frame = producer.grabFrame();
                if (frame != null) {
                    synchronized (buffer) {
                        if (buffer.remainingCapacity() == 0) {
                            buffer.poll();
                        }
                        buffer.offer(frame.clone());
                    }
                }
                long sleepTime = interval - (System.currentTimeMillis() - start);
                if (sleepTime > 0) {
                    Thread.sleep(sleepTime);
                }
            }
        } catch (InterruptedException e) {
            log.debug("采集线程被正常中断");
        } catch (Exception e) {
            log.error("采集线程异常", e);
        } finally {
            cleanUpResources(deviceUUID);
        }
    }

    public Queue<Frame> stopRecording(String deviceUUID) throws OperationFailNotification {
        // 设置停止标志
        recordingFlags.put(deviceUUID, false);

        // 中断线程
        Thread thread = recordingThreads.get(deviceUUID);
        if (thread != null && thread.isAlive()) {
            thread.interrupt();
        }

        recordingThreads.remove(deviceUUID);
        BlockingQueue<Frame> buffer = frameBuffers.remove(deviceUUID);
        frameRates.remove(deviceUUID);

        if (buffer == null) {
            throw new OperationFailNotification("未找到录制缓冲区");
        }
        return new LinkedBlockingQueue<>(buffer);
    }

    private void stopExistingRecording(String deviceUUID) {
        if (recordingThreads.containsKey(deviceUUID)) {
            Thread oldThread = recordingThreads.get(deviceUUID);
            if (oldThread != null && oldThread.isAlive()) {
                oldThread.interrupt();
                try {
                    oldThread.join(1000); // 等待1秒确保线程终止
                } catch (InterruptedException e) {
                    log.warn("等待线程终止时被中断");
                }
            }
            recordingThreads.remove(deviceUUID);
            frameBuffers.remove(deviceUUID);
            frameRates.remove(deviceUUID);
        }
    }

    private void cleanUpResources(String deviceUUID) {
        recordingFlags.remove(deviceUUID);
        recordingThreads.remove(deviceUUID);
        frameBuffers.remove(deviceUUID);
        frameRates.remove(deviceUUID);
    }

    // 开始录制方法
// 开始录制方法
    public void startVideoRecording(String deviceUUID) throws OperationFailNotification {
        StreamProducer producer = getStreamProducer(deviceUUID);
        if (producer == null) {
            throw new OperationFailNotification("设备未注册");
        }

        // 停止已有录制线程
        stopExistingRecording(deviceUUID);

        // 初始化动态队列
        BlockingQueue<Frame> frameQueue = new LinkedBlockingQueue<>();
        frameBuffers.put(deviceUUID, frameQueue);
        recordingFlags.put(deviceUUID, true);

        // 创建录制线程
        Thread recordingThread = new Thread(() -> {
            try {
                while (recordingFlags.getOrDefault(deviceUUID, false)) {
                    Frame frame = producer.grabFrame();
                    if (frame != null) {
                        frameQueue.offer(frame.clone());
                    }
                }
            } catch (Exception e) {
                log.error("录制线程异常", e);
            }
        });

        recordingThreads.put(deviceUUID, recordingThread);
        recordingThread.start();
        log.info("开始录制视频，设备UUID: {}", deviceUUID);
    }

    // 停止录制并保存视频方法
    public void stopVideoRecordingAndSave(String deviceUUID, String savePath) throws Exception {
        // 停止录制
        recordingFlags.put(deviceUUID, false);
        Thread thread = recordingThreads.get(deviceUUID);
        if (thread != null && thread.isAlive()) {
            thread.interrupt();
            thread.join();
        }
        recordingThreads.remove(deviceUUID);

        // 获取录制的帧
        Queue<Frame> frameQueue = frameBuffers.remove(deviceUUID);
        if (frameQueue == null || frameQueue.isEmpty()) {
            throw new IllegalArgumentException("录制队列为空或未找到");
        }

        // 保存视频
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String fullFilePath = savePath + "\\" + timestamp + ".mp4";
        Frame firstFrame = frameQueue.peek();
        int width = firstFrame.imageWidth;
        int height = firstFrame.imageHeight;

        FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(fullFilePath, width, height);
        recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
        recorder.setFormat("mp4");
        recorder.setFrameRate(24);
        recorder.setVideoOption("preset", "medium"); // 设置编码预设
        recorder.setVideoOption("bEnableFrameSkip", "1"); // 启用跳帧功能
        recorder.start();

        try {
            for (Frame frame : frameQueue) {
                recorder.record(frame);
            }
        } finally {
            recorder.stop();
            recorder.release();
        }

        log.info("视频保存成功，路径: {}", fullFilePath);
    }


}
