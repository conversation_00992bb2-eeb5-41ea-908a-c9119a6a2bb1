package com.desaysv.workserver.stream;

public abstract class QnxStreamProducer extends StreamProducer {

    public QnxStreamProducer() {
    }

    public QnxStreamProducer(String url) {
        super(url);
    }

    public QnxStreamProducer(String url, int w, int h) {
        super(url, w, h);
    }

    public QnxStreamProducer(int w, int h) {
        super(w, h);
    }

    protected abstract void startStream(String deviceModel, String deviceUUID) throws Exception;

    public abstract String pushStream(String deviceModel, String deviceUUID) throws Exception;
}