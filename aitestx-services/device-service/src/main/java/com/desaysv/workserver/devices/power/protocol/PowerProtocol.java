package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.devices.power.base.PowerDataDecoder;
import com.desaysv.workserver.exceptions.device.DeviceDataDecodeException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-11 10:57
 * @description :
 * @modified By :
 * @since : 2022-7-11
 */
public interface PowerProtocol {

    /**
     * 获取命令长度
     *
     * @return
     */
    int getDataLength();

    /**
     * 解析数据
     *
     * @param data             byte字节数组
     * @param powerDataDecoder 电源数据解码器
     * @throws DeviceDataDecodeException
     */
    void decodeData(byte[] data, PowerDataDecoder powerDataDecoder) throws DeviceDataDecodeException;

    /**
     * 选择通道
     *
     * @param channel 通道
     * @return
     */
    default boolean selectChannel(Integer channel) {
        return false;
    }

    /**
     * 控制电源的操作模式(若电源在校准模式时，不能控制电源为面板操作状态)
     *
     * @param isRemote 操作模式（0为面板操作模式，1为远程操作模式）
     * @return
     */
    boolean setRemoteMode(boolean isRemote);

    boolean fetchOutput();

    /**
     * 控制电源输出状态
     *
     * @param channel  电源通道（可选）
     * @param isOutput 电源输出状态（0为输出OFF，1为输出ON）
     * @return
     */
    boolean setOutput(Integer channel, boolean isOutput);

    /**
     * 设置电源的电压上限
     *
     * @param channel 电源通道（可选）
     * @param voltage 电压(V)
     * @return
     */
    boolean setVoltageMax(Integer channel, float voltage);

    /**
     * 设置电源的输出电压
     *
     * @param channel 电源通道（可选）
     * @param voltage 电压(V)
     * @return
     */
    boolean setVoltage(Integer channel, float voltage);

    /**
     * 设置电源的输出电流
     *
     * @param channel 电源通道（可选）
     * @param current 电流(A)
     * @return
     */
    boolean setCurrent(Integer channel, float current);

    /**
     * 读取电源数据
     *
     * @return
     */
    boolean readData();

    /**
     * 读取电压
     *
     * @param channel 电源通道（可选）
     * @return
     */
    boolean readVoltage(Integer channel);

    /**
     * 读取电流
     *
     * @param channel 电源通道（可选）
     * @return
     */
    boolean readCurrent(Integer channel);
}
