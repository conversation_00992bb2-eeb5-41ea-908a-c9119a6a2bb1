package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ILightTriStateOutputBoard {
    Logger log = LogManager.getLogger(ILightTriStateOutputBoard.class.getSimpleName());

    /**
     * 电平高低悬空电平输出
     *
//     * @param deviceChannel 板卡通道
     * @param state   0:悬空，1：高电平，2：低电平
     */
    boolean writeTriStateOutputBoardCard(int chassisNumber, int slotNumber, int channelNumber, int state) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).CHANGE_LIGHT_HL_OUTPUT_VALUE"})
    default void writeTriStateOutputBoardCardInfo(Integer deviceChannel, Integer slotNumber, Integer channelNumber, int state) throws OperationFailNotification {
        log.info("电平输出板卡{}-{}-{}，设置{}", deviceChannel, slotNumber, channelNumber,
                state == 0 ? "浮空" : (state == 1 ? "高电平" : "低电平"));
        try {
            boolean isWriteOk = writeTriStateOutputBoardCard(deviceChannel, slotNumber, channelNumber, state);
            log.info("电平输出板卡设置{}", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info("电平输出板卡设置{}", isWriteOk ? "成功" : "失败", isWriteOk ? "成功" : "失败");
            if (!isWriteOk) {
                throw new OperationFailNotification("电平输出板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("电平输出板卡写入失败:" + e.getMessage());
        }
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).CHANGE_LIGHT_HL_OUTPUT_VALUE_WITH_TIME"})
    default void writeTriStateOutputBoardCardWithTimeInfo(Integer deviceChannel, Integer slotNumber, Integer channelNumber, int state, String timeWithUnit, int lastState) throws OperationFailNotification {
        log.info("电平输出板卡{}-{}-{}，设置{}，{}后恢复{}", deviceChannel, slotNumber, channelNumber,
                state == 0 ? "浮空" : (state == 1 ? "高电平" : "低电平"), timeWithUnit, lastState == 0 ? "浮空" : (lastState == 1 ? "高电平" : "低电平"));
        try {
            boolean firstWriteOk = writeTriStateOutputBoardCard(deviceChannel, slotNumber, channelNumber, state);
            log.info("电平输出板卡第一次设置状态：{}, {}", state, firstWriteOk ? "成功" : "失败");
            Float seconds = BaseRegexRule.getSecondsOfDefaultMills(timeWithUnit);
            Thread.sleep((long) (seconds * 1000L));
            boolean lastWriteOk = writeTriStateOutputBoardCard(deviceChannel, slotNumber, channelNumber, lastState);
            log.info("电平输出板卡最后一次设置状态：{}, {}", lastState, lastWriteOk ? "成功" : "失败");
            if (!firstWriteOk && !lastWriteOk) {
                throw new OperationFailNotification("电平输出板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException | InterruptedException e) {
            throw new OperationFailNotification("电平输出板卡写入失败:" + e.getMessage());
        }
    }
}
