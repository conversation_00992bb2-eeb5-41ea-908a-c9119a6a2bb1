package com.desaysv.workserver.operation.factory;

import com.desaysv.workserver.base.operation.base.OperationAbstractFactory;
import com.desaysv.workserver.base.operation.method.DeviceOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.DeviceOperationTarget;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.MapToObj;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 13:19
 * @description : 操作方法简单工厂
 * @modified By :
 * @since : 2022-3-18
 */
@Component
@Lazy
public class DeviceOperationFactory implements OperationAbstractFactory {

    @Override
    public OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        OperationTarget deviceOperationTarget;
        try {
            deviceOperationTarget = MapToObj.mapToObj(operationTarget, DeviceOperationTarget.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            deviceOperationTarget = new DeviceOperationTarget();
        }
        return deviceOperationTarget;
    }

    @Override
    public OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        OperationMethod deviceOperationMethod;
        try {
            deviceOperationMethod = MapToObj.mapToObj(operationMethod, DeviceOperationMethod.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            deviceOperationMethod = new DeviceOperationMethod();
        }
        return deviceOperationMethod;
    }

    @Override
    public OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        OperationParameter deviceOperationParameter;
        try {
            deviceOperationParameter = MapToObj.mapToObj(operationParameter, DeviceOperationParameter.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            deviceOperationParameter = new DeviceOperationParameter();
            deviceOperationParameter.putAll(operationParameter);
        }
        return deviceOperationParameter;
    }

}
