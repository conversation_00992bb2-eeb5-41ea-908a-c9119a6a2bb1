package com.desaysv.workserver.canlog.blflog.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CanLogParserService {

    public ResultEntity<List<CanMessage>> parseCanLogToJsonResponse(String filePath) {
        try {
            List<CanMessage> canMessages = parseCanLog(filePath);
            return ResultEntity.ok(canMessages);
        } catch (Exception e) {
            log.error("解析失败: ", e);
            return ResultEntity.fail(e.getMessage());
        }
    }

    public List<CanMessage> parseCanLog(String filePath) throws IOException, InterruptedException {
        String jsonOutput = parseCanLogToJson(filePath);
        List<CanMessageDTO> dtos = JSON.parseObject(jsonOutput, new TypeReference<List<CanMessageDTO>>() {});
        return convertDtosToCanMessages(dtos);
    }

    private String parseCanLogToJson(String filePath) throws IOException, InterruptedException {
//        String exePath = "D:\\daima\\aitestxserver\\aitestx-web\\src\\main\\resources\\library\\dlls\\blf\\can_log_parser.exe";
        String exePath = "library\\dlls\\blf\\can_log_parser.exe";
        ProcessBuilder processBuilder = new ProcessBuilder(exePath, filePath);
        Process process = processBuilder.start();

        String output = readStream(process.getInputStream());
        String error = readStream(process.getErrorStream());

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("解析失败: " + error);
        }
        return output;
    }

    private List<CanMessage> convertDtosToCanMessages(List<CanMessageDTO> dtos) {
        List<CanMessage> canMessages = new ArrayList<>();
        for (CanMessageDTO dto : dtos) {
            CanMessage canMessage = new CanMessage();
            canMessage.setArbitrationId(ByteUtils.hexToInt(dto.getArbitration_id()));
            canMessage.setExtendedId(dto.is_extended_id());
            canMessage.setCanFd(dto.is_fd());
            canMessage.setData(ByteUtils.hexStringToByteArray(dto.getData()));
            canMessage.setDlc(dto.getDlc());
            canMessage.setPeriod(0.002f);//存储的距离下一条报文的间隔时间，单位ms
            canMessage.setChannel(dto.getChannel()+1);
            canMessage.setDirection(dto.getDirection());
            canMessage.setSendTimes(1);
            canMessage.setFramesPerSendNum(1);
            canMessage.setTimestamp(Double.parseDouble(dto.getTimestamp()));
            canMessages.add(canMessage);
        }
        return canMessages;
    }

    private static String readStream(InputStream inputStream) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, "GBK"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        CanLogParserService canLogParserService = new CanLogParserService();
        try {
            List<CanMessage> canMessages = canLogParserService.parseCanLog("D:\\UIDS1050\\Desktop\\工作需要\\近期开发任务用的一些文件夹\\can_20250415194905.asc");
            System.out.println(canMessages);
            String jsonOutput = canLogParserService.parseCanLogToJson("D:\\UIDS1050\\Desktop\\工作需要\\近期开发任务用的一些文件夹\\can_20250415194905.asc");
            System.out.println(jsonOutput);
        } catch (Exception e) {
            log.info("解析失败: ", e);
        }
    }
}