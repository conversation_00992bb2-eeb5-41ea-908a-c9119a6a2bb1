package com.desaysv.workserver.devices.bus.canoe;

import com.jacob.com.Dispatch;
import com.jacob.com.Variant;
import lombok.extern.slf4j.Slf4j;

import java.util.Random;

@Slf4j
public class VectorUtils {
    public static final int NOT_RESPONSE = 255;
    public static final int RETURN_VALUE_PASS = 0;
    public static final int TEST_RESULT_FAIL = 0;
    public static final int TEST_RESULT_PASS = 1;
    public static final int TEST_RESULT_INVALID = 2;
    public static final int FLYTEST_CONTROL_FLAG = 1;
    public static final int FINISH_FLAG = 1;
    public static final int GET_FBL_TEST_RETURN_TIMEOUT = 10 * 60 * 1000; //获取fbl testcase执行的超时时间
    public static final int GET_TEST_RETURN_TIMEOUT = 5 * 60 * 1000; //获取除了fbl之外的其他testcase执行的超时时间
    public static final int GET_TEST_RESULT_TIMEOUT = 5000; //获取testResult超时时间
    public static final int GET_RETURN_VALUE_TIMEOUT = 2000; //获取returnValue超时时间
    public static final int GET_SUPPLIER_VALUE_TIMEOUT = 5000; //获取returnValue超时时间
    public static final int PUT_RETURN_VALUE_TIMEOUT = 1000; //设置returnValue超时时间
    public static final int GET_VARIANT_TIMEOUT = 1000; //获取信号超时时间
    public static final int SOCKET_TIMEOUT = 10 * 1000; //Socket 通信时间
    public static final int GET_PTS_SW_VERSION_TIMEOUT = 10 * 60 * 1000; //获取升级PtsSwVersion超时时间
    public static final String UDS_LOG_FINISH = "LOG_FINISH";
    public static final String TEST_CASE_END_FLAG = "0";
    public static final String PTS_SW_VERSION_INIT_VALUE = "NULL";
    public static final String SET_FLYTEST_CONTROL_FAIL = "设置<FLYTEST_CONTROL=1>失败!";
    public static final String SET_TEST_RESULT_INVALID_FAIL = "设置<TEST_RESULT=2>失败!";
    public static final String SET_RETURN_VALUE_FAIL = "设置<ReturnValue=255>失败!";
    public static final String GET_RETURN_VALUE_FAIL = "获取<ReturnValue=0>失败!";
    public static final String GET_TEST_RESULT_VALUE_FAIL = "获取<testResult==pass>失败!";
    public static final String FAIL = "失败";
    public static final String SUCCESS = "成功";
    public static final String SET_XCP_FUN_SWITCH_LOG = "设置XCPFunSwitch为:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒!";
    public static final String SET_SETRDEFOGSTS_LOG = "设置setRDefogSts为:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒!";
    public static final String SET_KEY_BUTTON_LOG = "设置SetKeyButton为:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒!";
    public static final String SET_KEY_POSITION_LOG = "设置SetKeyPosition为:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒!";
    public static final String FETCH_CAN_SIGNAL_LOG = "获取CAN信号{}的信号值:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_SIGNAL_LOG = "设置CAN信号{}的信号值:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_MESSAGE_CSROLLING_LOG = "设置CAN报文CSRolling messageID:{}, checksumStatus:{}, rollingCounterStatus:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_PTS_LOG = "设置CAN PTS字节:{}，返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_SINGLE_MSG_STATUS_LOG = "设置CAN报文名{}的报文状态:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_ECU_ALL_MSG_STATUS_LOG = "设置CAN所有通道报文的报文状态:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_MSG_DLC_LOG = "设置CAN报文:{}, DLC:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_MSG_CYCLE_TIME_LOG = "设置CAN报文周期:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_PTS_VERSION_INIT_LOG = "设置ptsSwVersion初始值:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_APP_FLASHDRIVER_LOG = "设置APP和FlashDriver返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_UPGRADE_COUNTER_LOG = "设置Upgrade Counter返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String RETURN_PTS_VERSION_LOG = "返回的ptsSwVersion:{}, 执行时长:{}毫秒";
    public static final String COMPARE_VERSION_LOG = "版本检测，期望升级版本号：{}， pts读取版本号：{}，执行时长:{}毫秒, 返回结果:{}";
    public static final String FETCH_CAN_XCP_LOG = "获取CAN xcpName:{}, xcpValue:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_CAN_XCP_LOG = "设置CAN xcpName:{}, xcpValue:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_LIN_SIGNAL_LOG = "设置LIN报文信号{}的信号值:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_LIN_SINGLE_MSG_STATUS_LOG = "设置Lin报文名{}的报文状态:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_LIN_ECU_ALL_MSG_STATUS_LOG = "设置LIN所有报文的报文状态:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String FETCH_LIN_SIGNAL_LOG = "获取LIN报文信号{}的信号值:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String CHECK_TURN_LAMP_LOG = "持续检测转向灯：{}-{}-{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_MIRROR_FOLDSTS_LOG = "setMirrorFoldSTS 状态=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_LAMP_SWITCH_LOG = "setLampSwitch 状态=={}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String CHECK_FOUR_DOOR_LOG = "检测四门：{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_DOIP_FAIL = "设置EthDoIPFun address:{}, ethDoIPCaseIdString:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_DOCAN_FAIL = "设置DoCAN 地址:{}, UDS数据:{}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String GET_FINISH_FLAG_FAIL = "获取FinishFlag, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String LAST_CHECK_MSG_ID_FAIL = "持续检测报文ID：{}， {}, 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SEND_EVENT_MSG_ID_FAIL = "发送事件报文ID：{}， 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String CHECK_VOLTAGE_ID_FAIL = "电平采集板卡检测单Pin的工作周期， 返回结果:{}, 原因:{}, 执行时长:{}毫秒";
    public static final String SET_TEST_CASE_FAIL = "设置test case失败!";
    public static final String SET_ADDRESS_FAIL = "设置诊断寻址失败!";
    public static final String SET_DATA_FAIL = "设置CANoe系统变量失败!!";
    public static final String GET_DATA_FAIL = "获取CANoe系统变量失败!";
    public static final String SET_COUNTER_FAIL = "设置CAPL响应系统变量失败!";
    public static final String SET_XCP_VAR_BTN_FAIL = "设置系统变量XcpVarSetBotn失败!";
    public static final String CHECK_XCP_VAR_BTN_FAIL = "设置系统变量XcpVarGetBotn失败!";
    public static final String SET_SUCCESS = "脚本设置成功!"; //获取升级PtsSwVersion超时时间



    public static boolean putReturnValue(Dispatch dispatchParent, String dispatchTarget) {
        boolean flag;
        long startMills = System.currentTimeMillis();
        putVariant(dispatchParent, dispatchTarget, NOT_RESPONSE);     //设置returnValue为255
        while (true) {
            int returnValue = getVariant(dispatchParent, dispatchTarget, "Value").getInt();
            if (returnValue == NOT_RESPONSE) {
                log.info("设置returnVal:{},共耗时:{}ms", returnValue, System.currentTimeMillis() - startMills);
                flag = true;
                break;
            }
            if ((System.currentTimeMillis() - startMills) > PUT_RETURN_VALUE_TIMEOUT) {
                log.info("设置counter前读取returnVal:{}, 超时:{}ms", returnValue, System.currentTimeMillis() - startMills);
                flag = false;
                break;
            }
        }
        return flag;
    }

    public static double fetchLatestSystemVariant(Dispatch dispatchParent, String dispatchTarget, int timeout) {
        long startMills = System.currentTimeMillis();
        double readSystemVariant;
        int count = 0;
        double data = getVariant(dispatchParent, dispatchTarget, "Value").getDouble();
        log.info("第一次读取系统变量:{}的值:{}, 共耗时:{}ms", dispatchTarget, data, System.currentTimeMillis() - startMills);
        while (true) {
            count++;
            readSystemVariant = getVariant(dispatchParent, dispatchTarget, "Value").getDouble();
            if (readSystemVariant != data) {
                log.info("读取系统变量:{}的最新值:{}，成功！！！读取系统变量次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > timeout) {
                log.info("读取系统变量:{}的最新值:{}, 存在读取时间超时！！！读取系统变量次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
        }
        return readSystemVariant;
    }

    public static <T extends Number> T fetchAnyType(Dispatch dispatch, String target, Class<T> tClass) {
        return (T) getVariant(dispatch, target, "Value").getVariant();
    }

    public static void main(String[] args) {
        Integer o = VectorUtils.fetchAnyType(new Dispatch(), "", Integer.class);
    }

    public static int fetchLatestSystemVariantInt(Dispatch dispatchParent, String dispatchTarget) {
        long startMills = System.currentTimeMillis();
        int readSystemVariant;
        int count = 0;
        Variant value = getVariant(dispatchParent, dispatchTarget, "Value");
        System.out.println("value===" + value);
        int data = getVariant(dispatchParent, dispatchTarget, "Value").getInt();
        log.info("第一次读取系统变量:{}的值:{}, 共耗时:{}ms", dispatchTarget, data, System.currentTimeMillis() - startMills);
        while (true) {
            count++;
            readSystemVariant = getVariant(dispatchParent, dispatchTarget, "Value").getInt();
            if (readSystemVariant != data) {
                log.info("读取系统变量:{}的最新值:{}，成功！！！读取Signal次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > GET_VARIANT_TIMEOUT) {
                log.info("读取系统变量:{}的最新值:{}, 存在读取时间超时！！！读取Signal次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
        }
        return readSystemVariant;
    }

    public static String fetchLatestSystemVariantString(Dispatch dispatchParent, String dispatchTarget, String initValue, int timeout) {
        long startMills = System.currentTimeMillis();
        String readSystemVariant;
        int count = 0;
        String data = getVariant(dispatchParent, dispatchTarget, "Value").getString();
        log.info("第一次读取系统变量:{}的值:{}, 共耗时:{}ms", dispatchTarget, data, System.currentTimeMillis() - startMills);
        while (true) {
            count++;
            readSystemVariant = getVariant(dispatchParent, dispatchTarget, "Value").getString();
            if (initValue != null && !data.equals(initValue) && readSystemVariant.equals(initValue)) {
                log.info("读取系统变量:{}的初始值:{}，成功！", dispatchParent, readSystemVariant);
                data = readSystemVariant;
            }
            if (!readSystemVariant.equals(data)) {
                log.info("读取系统变量:{}的最新值:{}，成功！！！读取SystemVariant次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > timeout) {
                log.info("读取系统变量:{}的最新值:{}, 存在读取时间超时！！！读取SystemVariant次数:{}, 共耗时:{}ms", dispatchTarget, readSystemVariant, count, System.currentTimeMillis() - startMills);
                break;
            }
        }
        return readSystemVariant;
    }


    public static void putCounter(Dispatch dispatchParent, String dispatchTarget) {
        int targetCounterValue = getVariant(dispatchParent, dispatchTarget, "Value").getInt();
        putVariant(dispatchParent, dispatchTarget, randomInt(targetCounterValue));
    }


    /**
     * 读取CANoe返回returnValue的结果
     */
    public static boolean getResult(Dispatch dispatch) {
        long startMills = System.currentTimeMillis();
        int returnVal;
        while (true) {
            returnVal = getVariant(dispatch, "returnValue", "Value").getInt();
            if (returnVal != NOT_RESPONSE) {
                log.info("读取returnVal响应事件完毕，returnVal：{}, 共耗时:{}ms", returnVal, System.currentTimeMillis() - startMills);
                break;
            }
            if ((System.currentTimeMillis() - startMills) > GET_RETURN_VALUE_TIMEOUT) {
                log.info("读取returnVal响应事件超时:{}ms, returnVal：{}", System.currentTimeMillis() - startMills, returnVal);
                break;
            }
        }
        return returnVal == 0;
    }

    public static void putVariant(Dispatch dispatchParent, String dispatchTarget, Object value) {
        Dispatch dispatch = Dispatch.call(dispatchParent, "Item", dispatchTarget).toDispatch();
        Dispatch.put(dispatch, "value", value);
    }

    public static Variant getVariant(Dispatch dispatchParent, String dispatchTarget, String variables) {
        Dispatch dispatch = Dispatch.call(dispatchParent, "Item", dispatchTarget).toDispatch();
        return Dispatch.get(dispatch, variables);
    }

    public static Dispatch getVariantDispatch(Dispatch dispatchParent, String dispatchTarget, String variables) {
        return getVariant(dispatchParent, dispatchTarget, variables).toDispatch();
    }

    public static void waitTime(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static int randomInt(int exceptValue) {
        Random random = new Random();
        int randomInt = random.nextInt(Integer.MAX_VALUE - 1) + 1; // 生成1到Integer.MAX_VALUE之间的随机整数
        while (randomInt == exceptValue) { // 如果生成的随机整数等于要排除的值，则重新生成
            randomInt = random.nextInt(Integer.MAX_VALUE - 1) + 1;
        }
//        System.out.println("生成的随机整数为：" + randomInt);
        return randomInt;
    }
}
