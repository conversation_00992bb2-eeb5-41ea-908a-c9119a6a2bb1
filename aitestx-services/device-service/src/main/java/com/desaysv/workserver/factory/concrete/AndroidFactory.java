package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.android.AdbHudDevice;
import com.desaysv.workserver.devices.android.UsbAndroid;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractAndroidFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class AndroidFactory implements AbstractAndroidFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Android.USB_ANDROID:
                return createUsbAndroidDevice(deviceRegisterForm);
            case DeviceModel.Android.ADB_HUD:
                return createAdbHudDevice(deviceRegisterForm);
        }
        return null;
    }


    @Override
    public Device createUsbAndroidDevice(DeviceRegisterForm deviceRegisterForm) {
        return new UsbAndroid(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createAdbHudDevice(DeviceRegisterForm deviceRegisterForm) {
        return new AdbHudDevice(deviceRegisterForm.getDeviceOperationParameter());
    }
}
