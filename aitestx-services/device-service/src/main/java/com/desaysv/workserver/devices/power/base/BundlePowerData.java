package com.desaysv.workserver.devices.power.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-30 13:22
 * @description :
 * @modified By :
 * @since : 2022-6-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BundlePowerData extends PowerData{
    private Float voltage;

    private Float current;

    public void clear() {
        voltage = null;
        current = null;
    }

    public static BundlePowerData invalid() {
        return new BundlePowerData();
    }

}
