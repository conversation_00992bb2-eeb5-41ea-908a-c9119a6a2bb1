package com.desaysv.workserver.devices.daq.base;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.daq.keysight.CurrentStatistics;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.DecimalFormat;

public interface IDaqCompare extends IVoltCurrDaqAcquireHandler {
    Logger log = LogManager.getLogger(IDaqCompare.class.getSimpleName());

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_CURRENT"})
    default ActualExpectedResult compareCurrentDC(float lower, float upper) throws DeviceReadException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float currentDC = getCurrentDC();
        DecimalFormat decimalFormat = new DecimalFormat("0.######");
        log.info("采集直流电流:{}A，要求{}~{}A", decimalFormat.format(currentDC), lower, upper);
        boolean pass = currentDC >= lower && currentDC <= upper;
        actualExpectedResult.put("compareCurrentDC", pass, decimalFormat.format(currentDC));
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_CURRENT_FLUCTUATION_START"})
    default void startCurrentCollection() {
        startCurrentCollection("100ms");
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_CURRENT_FLUCTUATION_START_INTERVAL"})
    default void startCurrentCollection(String interval) {
    }

    default CurrentStatistics stopCurrentCollection(float baseValue, float threshold) {
        return new CurrentStatistics(0, 0, 0, false, 0);
    }

    default void clearCurrentCache() {
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).COMPARE_CURRENT_FLUCTUATION_STOP"})
    default ActualExpectedResult compareCurrentDCProcess(float baseValue, float threshold) {
        ActualExpectedResult result = new ActualExpectedResult();
        DecimalFormat fmt = new DecimalFormat("0.######");
        CurrentStatistics stats = stopCurrentCollection(baseValue, threshold);
        String formattedStats = String.join("-",
                fmt.format(stats.getMaxCurrent()),
                fmt.format(stats.getMinCurrent()),
                fmt.format(stats.getAvgCurrent())
        );
        log.info("采集直流电流(最大值-最小值-平均值):{}，要求{}~{}A", formattedStats, baseValue, threshold);
        result.put("compareCurrentDCProcess", stats.isStable(), formattedStats);
        clearCurrentCache();
        return result;
    }

}
