package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLObjectHeader2 extends Structure {
    public VBLObjectHeaderBase mBase;
    public byte mObjectFlags;
    public byte mTimeStampStatus;
    public byte mReserved1;
    public short mObjectVersion;
    public long mObjectTimeStamp;
    public long mOriginalTimeStamp;

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mBase", "mObjectFlags", "mTimeStampStatus", "mReserved1", "mObjectVersion","mObjectTimeStamp","mOriginalTimeStamp");
    }
}
