package com.desaysv.workserver.devices.bus.base;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.SerializeDisabled;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 所有周期任务
 */
@Data
@Slf4j
public abstract class CyclicTask implements SerializeDisabled {

    //标识符
    private ChannelMessageId taskId;

    @JSONField(serialize = false)
    private CyclicTaskListener cyclicTaskListener;

    /**
     * 取消周期任务
     *
     * @throws BusError 如果在一个已经停止的任务调用该方法，会抛出该异常
     */
    //TODO:建议提供通过channel/id来停止的方法
    public void stop(boolean removeTask) {
        cyclicTaskListener.stopTask(removeTask);
        _stopTask();
        log.info("停止任务:{}", taskId);
    }

    public void stop() {
        stop(true);
    }

    protected abstract void _stopTask();

    public abstract void pause();

    public abstract void resume();

}
