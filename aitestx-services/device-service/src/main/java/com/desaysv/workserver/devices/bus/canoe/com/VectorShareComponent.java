package com.desaysv.workserver.devices.bus.canoe.com;

import com.jacob.activeX.ActiveXComponent;
import com.jacob.com.Dispatch;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class VectorShareComponent extends ActiveXComponent {
    private static VectorShareComponent INSTANCE;
    private boolean close;
    public static Dispatch namespaces;

    public VectorShareComponent() {
        super("CANoe.Application");
    }

    public static VectorShareComponent getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new VectorShareComponent();
            Dispatch system = Dispatch.call(INSTANCE, "System").toDispatch();  // 获取 CAPL 模块对象
            namespaces = Dispatch.call(system, "Namespaces").toDispatch();
        }
        return INSTANCE;
    }


    public static void releaseCanApp() {
        INSTANCE = null;
    }


}
