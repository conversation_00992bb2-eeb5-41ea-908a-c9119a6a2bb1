package com.desaysv.workserver.cicd;

import com.sun.jna.platform.win32.Kernel32;
import com.sun.jna.platform.win32.WinBase;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;
import java.nio.file.FileStore;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
public class FileUtil {

    /**
     * 查找U盘路径
     *
     * @return U盘路径列表
     */
    public static List<String> findUDiskPath() {
        List<String> uDiskPaths = new ArrayList<>();
        log.info("正在查找U盘路径");
        for (char driveLetter = 'A'; driveLetter <= 'Z'; driveLetter++) {
            String rootPath = driveLetter + ":\\";

            IntByReference volumeSerialNumber = new IntByReference(0);
            IntByReference maximumComponentLength = new IntByReference(0);
            IntByReference fileSystemFlags = new IntByReference(0);

            char[] fileSystemNameBuffer = new char[1024];

            boolean result = Kernel32.INSTANCE.GetVolumeInformation(
                    rootPath,
                    null, 0,
                    volumeSerialNumber,
                    maximumComponentLength,
                    fileSystemFlags,
                    fileSystemNameBuffer,
                    fileSystemNameBuffer.length);
            // 增加有效性判断
            if (!result || fileSystemNameBuffer[0] == 0) {
//                log.debug("跳过无效驱动器: {}", rootPath);
                continue;
            }
            int driveType = Kernel32.INSTANCE.GetDriveType(rootPath);
            if (driveType == WinBase.DRIVE_REMOVABLE) {
                uDiskPaths.add(rootPath);
                log.info("找到U盘的路径为: {}", rootPath);
            }
        }
        return uDiskPaths;
    }

    /**
     * 拷贝大文件（支持网络路径和本地路径）
     *
     * @param sourceFilePath 源文件完整路径（包含文件名）
     * @param targetDirPath  目标目录路径
     * @throws IOException 当拷贝过程中发生错误时抛出
     */
    public static void copyLargeFile(Path sourceFilePath, Path targetDirPath) throws IOException {
        File sourceFile = sourceFilePath.toFile();
        String fileName = sourceFile.getName();

        log.info("安装包文件路径: {}", sourceFile.getAbsolutePath());
        log.info("目标目录路径: {}", targetDirPath);

        // 获取源文件大小（字节）
        long sourceFileSizeBytes = sourceFile.length();
        double sourceFileSizeGB = sourceFileSizeBytes / 1024.0 / 1024.0 / 1024.0;
        String formattedFileSize = String.format("%.2f", sourceFileSizeGB);
        log.info("文件大小: {} GB", formattedFileSize);

        // 获取目标磁盘信息
        Path targetDrive = targetDirPath.getRoot();
        FileStore store = Files.getFileStore(targetDrive);

        // 1. 检查磁盘空间
        long availableSpace = store.getUsableSpace();
        double availableSpaceGB = availableSpace / (1024.0 * 1024 * 1024);
        log.info("需要空间: {} GB, 可用空间: {} GB", formattedFileSize, String.format("%.2f", availableSpaceGB));

        // 增加10%的安全缓冲空间
        if (availableSpace < sourceFileSizeBytes * 1.1) {
            double requiredExtraGB = (sourceFileSizeBytes * 1.1 - availableSpace) / (1024.0 * 1024 * 1024);
            log.error("磁盘空间不足! 需要至少{}GB 额外空间", String.format("%.2f", requiredExtraGB));
            throw new IOException(String.format("磁盘空间不足! 需要至少 %.2f GB 额外空间", requiredExtraGB));
        }

        // 2. 检查文件系统类型
        String fileSystemType = store.type();
        log.info("目标磁盘文件系统: {}", fileSystemType);
        if ("FAT32".equalsIgnoreCase(fileSystemType) && sourceFileSizeBytes > 4L * 1024 * 1024 * 1024) {
            log.error("FAT32文件系统不支持大于4GB的文件，请转换为NTFS");
            throw new IOException("FAT32文件系统不支持大于4GB的文件，请转换为NTFS\n转换命令: convert "
                    + targetDrive + " /FS:NTFS");
        }

        // 3. 确保目标目录存在
        if (!Files.exists(targetDirPath)) {
            Files.createDirectories(targetDirPath);
        }
        File targetFile = new File(targetDirPath.toFile(), fileName);

        // 删除已存在的零字节文件（如果存在）
        if (targetFile.exists() && targetFile.length() == 0) {
            log.warn("发现零字节的旧目标文件，正在删除: {}", targetFile.getAbsolutePath());
            if (!targetFile.delete()) {
                log.error("无法删除零字节文件: {}", targetFile.getAbsolutePath());
            }
        }

        // 4. 使用FileOutputStream进行传输
        try (FileInputStream fis = new FileInputStream(sourceFile);
             FileOutputStream fos = new FileOutputStream(targetFile)) {

            long fileSize = sourceFile.length();
            long position = 0;
            final long startTime = System.currentTimeMillis();

            // 进度报告设置
            final long reportInterval = 100 * 1024 * 1024; // 每100MB报告一次
            long nextReport = reportInterval;
            final int bufferSize = 8 * 1024 * 1024; // 8MB缓冲区
            byte[] buffer = new byte[bufferSize];

            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
                position += bytesRead;

                // 进度报告
                if (position >= nextReport || position == fileSize) {
                    double percent = (double) position / fileSize * 100;
                    double elapsedSec = (System.currentTimeMillis() - startTime) / 1000.0;
                    double speedMBs = position / (elapsedSec * 1024 * 1024);

                    log.info("拷贝进度: {}% ({}/{}) - 速度: {} MB/s",
                            String.format("%.2f", percent),
                            formatSize(position),
                            formatSize(fileSize),
                            String.format("%.2f", speedMBs));

                    nextReport = position + reportInterval;
                }
            }

            // 强制刷新文件系统缓存
            fos.flush();
            fos.getFD().sync();

            validateFileCopy(sourceFile, targetFile); // 校验文件完整性

            // 拷贝完成统计
            double totalTimeSec = (System.currentTimeMillis() - startTime) / 1000.0;
            double avgSpeedMBs = fileSize / (totalTimeSec * 1024 * 1024);
            log.info("文件拷贝成功! 总耗时: {}秒, 平均速度: {} MB/s",
                    String.format("%.2f", totalTimeSec),
                    String.format("%.2f", avgSpeedMBs));

        } catch (IOException e) {
            throw new IOException("文件拷贝失败: " + e.getMessage(), e);
        }
    }


//        // 4. 使用FileChannel进行高效传输
//        try (FileInputStream fis = new FileInputStream(sourceFile);
//             FileChannel inChannel = fis.getChannel();
//             FileOutputStream fos = new FileOutputStream(targetFile);
//             FileChannel outChannel = fos.getChannel()) {
//
//            long fileSize = inChannel.size();
//            long position = 0;
//            final long startTime = System.currentTimeMillis();
//
//            // 进度报告设置
//            final long reportInterval = 100 * 1024 * 1024; // 每100MB报告一次
//            long nextReport = reportInterval;
//            final long maxChunk = 16 * 1024 * 1024; // 16MB传输块
//
//            while (position < fileSize) {
//                // 动态计算传输块大小
//                long chunk = Math.min(maxChunk, fileSize - position);
//
//                // 核心传输方法
//                long transferred = inChannel.transferTo(position, chunk, outChannel);
//
//                // 处理零传输情况
//                if (transferred == 0) {
//                    // 添加传输失败计数器
//                    int zeroTransferCount = 0;
//                    while (transferred == 0 && zeroTransferCount < 10) {
//                        Thread.sleep(100);
//                        transferred = inChannel.transferTo(position, chunk, outChannel);
//                        zeroTransferCount++;
//                    }
//                    if (transferred == 0) {
//                        throw new IOException("连续传输失败，位置: " + position + "/" + fileSize);
//                    }
//                }
//
//                position += transferred;
//
//                // 进度报告
//                if (position >= nextReport || position == fileSize) {
//                    double percent = (double) position / fileSize * 100;
//                    double elapsedSec = (System.currentTimeMillis() - startTime) / 1000.0;
//                    double speedMBs = position / (elapsedSec * 1024 * 1024);
//
//                    log.info("拷贝进度: {}% ({}/{}) - 速度: {} MB/s",
//                            String.format("%.2f", percent),
//                            formatSize(position),
//                            formatSize(fileSize),
//                            String.format("%.2f", speedMBs));
//
//                    nextReport = position + reportInterval;
//                }
//            }
//
//            // 强制刷新文件系统缓存
//            outChannel.force(true);
//            fos.getFD().sync();
//
//            validateFileCopy(sourceFile, targetFile); // 校验文件完整性
//
//            // 拷贝完成统计
//            double totalTimeSec = (System.currentTimeMillis() - startTime) / 1000.0;
//            double avgSpeedMBs = fileSize / (totalTimeSec * 1024 * 1024);
//            log.info("文件拷贝成功! 总耗时: {}秒, 平均速度: {} MB/s",
//                    String.format("%.2f", totalTimeSec),
//                    String.format("%.2f", avgSpeedMBs));
//
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt(); // 恢复中断状态
//            throw new IOException("文件拷贝被中断", e);
//        }
//    }

    // 辅助方法：格式化文件大小显示
    private static String formatSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        char unit = "KMGTPE".charAt(exp-1);
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), unit);
    }

    /**
     * 校验文件拷贝完整性
     */
    public static void validateFileCopy(File sourceFile, File targetFile) throws IOException {
        long sourceSize = sourceFile.length();
        long targetSize = targetFile.length();

        // 等待文件系统更新（处理延迟）
        int retryCount = 0;
        while (targetSize == 0 && retryCount < 5) {
            log.warn("检测到零字节文件，等待文件系统更新... (重试 {}/{})", retryCount + 1, 5);
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            targetSize = targetFile.length(); // 重新获取大小
            retryCount++;
        }

        if (targetSize == 0) {
            log.error("目标文件大小为0字节! 源文件: {} ({}字节), 目标文件: {}",
                    sourceFile.getAbsolutePath(), sourceSize,
                    targetFile.getAbsolutePath());

            // 尝试删除无效文件
            if (targetFile.exists() && !targetFile.delete()) {
                log.error("无法删除无效的目标文件");
            }

            throw new IOException("文件拷贝失败: 目标文件大小为0字节");
        }

        if (targetSize != sourceSize) {
            log.error("文件大小不匹配! 源文件: {} ({}字节), 目标文件: {} ({}字节), 差异: {}字节",
                    sourceFile.getAbsolutePath(), sourceSize,
                    targetFile.getAbsolutePath(), targetSize,
                    Math.abs(sourceSize - targetSize));

            throw new IOException("文件拷贝不完整: 目标文件大小(" + targetSize +
                    "字节)不等于源文件大小(" + sourceSize + "字节)");
        }

        log.info("文件校验成功: 源文件大小={}字节, 目标文件大小={}字节", sourceSize, targetSize);
    }

    /**
     * 清空目录内容（保留目录本身）
     */
    public static void deleteDirectoryContents(Path dir) throws IOException {
        if (Files.exists(dir)) {
            Files.walk(dir)
                    .sorted(Comparator.reverseOrder())
                    .forEach(path -> {
                        try {
                            if (!path.equals(dir)) { // 不删除目录本身
                                Files.deleteIfExists(path);
                            }
                            log.info("删除文件成功：{}", path);
                        } catch (IOException e) {
                            log.warn("删除文件失败：{}", path, e);
                        }
                    });
        }
    }

}
