package com.desaysv.workserver.devices.testbox.interfaces;

import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.testbox.AcquisitionEntity;
import com.desaysv.workserver.devices.testbox.TestBoardBoxUtils;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IVoltageAcquireBoard {
    Logger log = LogManager.getLogger(IVoltageAcquireBoard.class.getSimpleName());

    String VOLTAGE = "voltage";

    /**
     * 采集电压（180个通道）
     *
     * @return Map(板卡号, 电压数组)
     */
    AcquisitionEntity fetchVoltageBoardCard() throws BoardCardTransportException;

    float fetchVoltageByChannel(int boardCardNumber, int deviceChannel) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TestBoxRegexRule).VOLTAGE_ACQUIRE"})
    default ActualExpectedResult fetchVoltageBoardCard(Integer deviceChannel, int boardChannel, float voltageExpectation, float voltageDeviation, String physicalMeaning) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        int boardCardNumber = deviceChannel;
        log.info("采集电压板卡{}通道{}{}期望电压{}V, 要求偏差不超过{}%", boardCardNumber, boardChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning), voltageExpectation, voltageDeviation);
        try {
            float actualVoltage = fetchVoltageByChannel(boardCardNumber, boardChannel);
            actualExpectedResult.put(VOLTAGE, true, String.format("%.2fV", actualVoltage));
            if (!TestBoardBoxUtils.isWithinDeviation(actualVoltage, voltageExpectation, voltageDeviation)) {
                actualExpectedResult.put(VOLTAGE, false);
                String info = String.format("采集电压板卡实际电压%fV不在允许偏差范围内", actualVoltage);
                ActionSequencesLoggerUtil.info(info);
            } else {
                String info = String.format("采集电压板卡实际电压%fV在允许偏差范围内", actualVoltage);
                ActionSequencesLoggerUtil.info(info);
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("采集电压板卡读取失败:" + e.getMessage());
        }
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TestBoxRegexRule).VOLTAGE_ACQUIRE_RANGE"})
    default ActualExpectedResult compareVoltageBoardCardRange(Integer deviceChannel, int boardChannel, float lowerVoltageExpectation, float upperVoltageExpectation, String physicalMeaning) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        int boardCardNumber = deviceChannel;
        log.info("采集电压板卡{}通道{}{},期望电压{}~{}V", boardCardNumber, boardChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning), lowerVoltageExpectation, upperVoltageExpectation);
        try {
            float actualVoltage = fetchVoltageByChannel(boardCardNumber, boardChannel);
            boolean pass = actualVoltage >= lowerVoltageExpectation && actualVoltage <= upperVoltageExpectation;
            actualExpectedResult.put(VOLTAGE, pass, String.format("%.2fV", actualVoltage));
            String info = String.format("采集电压板卡实际采集电压：%fV", actualVoltage);
            log.info(info);
            ActionSequencesLoggerUtil.info(info);
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("采集电压板卡读取失败:" + e.getMessage());
        }
        return actualExpectedResult;
    }
}
