package com.desaysv.workserver.monitor.data;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-1 16:08
 * @description :
 * @modified By :
 * @since : 2022-7-1
 */
public interface MonitorListener<T> {

    void start(DeviceDataDispatcher<T> deviceDataDispatcher);

    void pause(DeviceDataDispatcher<T> deviceDataDispatcher);

    void resume(DeviceDataDispatcher<T> deviceDataDispatcher);

    void stop(DeviceDataDispatcher<T> deviceDataDispatcher);

//    default void action(MonitorAction monitorAction, DeviceDataDispatcher<T> deviceDataDispatcher) {
//        switch (monitorAction.getCommand()) {
//            case DataMonitor.START:
//                start(deviceDataDispatcher);
//                break;
//            case DataMonitor.PAUSE:
//                pause(deviceDataDispatcher);
//                break;
//            case DataMonitor.RESUME:
//                resume(deviceDataDispatcher);
//                break;
//            case DataMonitor.STOP:
//                stop(deviceDataDispatcher);
//                break;
//        }
//    }
}
