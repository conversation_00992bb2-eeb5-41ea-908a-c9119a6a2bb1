package com.desaysv.workserver.screen;

import com.desaysv.workserver.entity.EnhancedPointInt;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.screen.config.CheckSumAlgorithm;
import com.desaysv.workserver.screen.config.GestureCode;
import com.desaysv.workserver.screen.config.TouchPointMatrixConfig;
import com.desaysv.workserver.screen.entity.Cell;
import com.desaysv.workserver.screen.entity.ColorCellCollection;
import com.desaysv.workserver.screen.entity.TouchPoint;
import com.desaysv.workserver.screen.result.mist.PointInfo;
import com.desaysv.workserver.screen.result.report.GestureReport;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.ExceptionUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class TouchPointUtils {
    private static final int BIT_SIZE = 8;
    private static final String XPos = "X";
    private static final String YPos = "Y";
    private static final String GESTURE_CODE = "GestureCode";

    /**
     * 获取单元格的bit位置
     *
     * @param cell 单元格
     * @return bit位置
     */
    private static int getBitPos(Cell cell) {
        return cell.getRow() * BIT_SIZE + cell.getColumn();
    }

    /**
     * 获取十六进制数组的位集合
     *
     * @param data 十六进制数组
     * @return 位集合
     */
    private static List<Integer> getBitList(List<Integer> data) {
        int byteLength = data.size();
        List<Integer> bitList = new ArrayList<>(byteLength * BIT_SIZE);
        for (Integer byteValue : data) {
            //轮询每一个字节
            char[] binArray = String.format("%8s",
                    Integer.toBinaryString(byteValue)).replace(" ", "0").toCharArray();
            for (int j = 0; j < BIT_SIZE; j++) {
                //轮询每一个位
                bitList.add(Character.getNumericValue(binArray[j]));
            }
        }
        return bitList;
    }

    public static List<Integer> byteArrayToIntList(int[] byteArray) {
        List<Integer> list = new ArrayList<>();
        for (int b : byteArray) {
            list.add(0xFF & b);
        }
        return list;
    }

    /**
     * 解析串口获取的十六进制字节数据（例如：FF 00 AA）
     *
     * @param touchPoint             触摸数据
     * @param touchPointMatrixConfig 用户自定义矩阵配置
     * @return 自定义标签映射关系
     */
    public static Map<String, Integer> parseHexDataForHumanReading(TouchPoint touchPoint, TouchPointMatrixConfig touchPointMatrixConfig) {
        if (touchPointMatrixConfig.getUserTouchDataDefinition() == null || touchPointMatrixConfig.getDataLength() <= 0) {
            return new HashMap<>();
        }
        List<Integer> data = byteArrayToIntList(touchPoint.getData());
        data = data.subList(data.size() - touchPointMatrixConfig.getDataLength(), data.size());
        List<Integer> bitList = getBitList(data);
        Map<String, Integer> valueMap = new HashMap<>();
        for (Map.Entry<String, ColorCellCollection> entry : touchPointMatrixConfig.getUserTouchDataDefinition().entrySet()) {
            String label = entry.getKey();
            StringBuilder binaryBuilder = new StringBuilder();
            List<Cell> cells = entry.getValue().getCells();
            for (Cell cell : cells) {
                // 获取单元格位置
                int bitPos = getBitPos(cell);
                binaryBuilder.append(bitList.get(bitPos));
            }
//            System.out.println(String.format("%s->%8s", label, binaryBuilder).replace(" ", "0"));
            int oct = Integer.parseUnsignedInt(binaryBuilder.toString(), 2);
            valueMap.put(label, oct);
        }
        return valueMap;
    }

    public static List<PointInt> parseStringToPoints(String text, TouchPointMatrixConfig touchPointMatrixConfig) {
        return parseByteArrayToPoints(ByteUtils.hexStringToByteArray(text), touchPointMatrixConfig);
    }

    public static List<PointInt> parseByteArrayToPoints(byte[] byteArray, TouchPointMatrixConfig touchPointMatrixConfig) {
        List<PointInt> pointList = new ArrayList<>();
        List<TouchPoint> touchPointList = parseByteArrayToTouchPoints(byteArray, touchPointMatrixConfig, true).getTouchPointList();
        for (TouchPoint touchPoint : touchPointList) {
            pointList.add(parseTouchPointToPoint(touchPoint, touchPointMatrixConfig));
        }
        return pointList;
    }

    public static EnhancedPointInt parseTouchPointToPoint(TouchPoint touchPoint,
                                                          TouchPointMatrixConfig touchPointMatrixConfig) {
        Map<String, Integer> resultMap = parseHexDataForHumanReading(touchPoint, touchPointMatrixConfig);
        Integer x = resultMap.get(XPos);
        Integer y = resultMap.get(YPos);
        Integer gestureCode = resultMap.get(GESTURE_CODE);
        EnhancedPointInt enhancedPointInt = new EnhancedPointInt(x, y, gestureCode);
        if (touchPointMatrixConfig.getGestureCode() != null) {
            enhancedPointInt.setGesture(touchPointMatrixConfig.getGestureCode().getGestureString(enhancedPointInt.getGestureCode()));
        }
        return enhancedPointInt;
    }

    public static GestureReport checkGesture(List<PointInfo> pointInts,
                                             String eventName) {
        return checkGesture(pointInts, -1, eventName);
    }

    public static GestureReport checkGesture(List<PointInfo> pointInts,
                                             int allowDragPointCount,
                                             String eventName) {
        GestureReport gestureReport = new GestureReport();
        gestureReport.setOk(true);
        List<PointInt> pointIntList = pointInts.stream().map(PointInfo::getPoint).collect(Collectors.toList());
        List<EnhancedPointInt> enhancedPointIntList = pointIntList.stream().map(p -> (EnhancedPointInt) p).collect(Collectors.toList());
        List<String> gestureArray = enhancedPointIntList.stream().map(EnhancedPointInt::getGesture).collect(Collectors.toList());
        log.info("触摸事件流:{}", gestureArray);
        if (gestureArray.size() == 1) {
            String onlyTouchEvent = gestureArray.get(0);
            gestureReport.setOk(false);
            gestureReport.setErrorMessage(String.format("整个%s过程的触摸事件只出现了\"%s\"事件，" +
                            "要求必须同时出现\"%s\",\"%s\"两种事件",
                    eventName, onlyTouchEvent, GestureCode.PRESS, GestureCode.RELEASE));
        } else {
            String firstTouchEvent = gestureArray.get(0);
            String lastTouchEvent = gestureArray.get(gestureArray.size() - 1);
            if (!GestureCode.PRESS.equals(firstTouchEvent)) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s的第一个触摸事件必须为\"%s\"事件," +
                        "检测到的却是\"%s\"事件", eventName, GestureCode.PRESS, firstTouchEvent));
            } else if (!GestureCode.RELEASE.equals(lastTouchEvent)) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s的最后一个触摸事件必须为\"%s\"事件" +
                        "检测到的却是\"%s\"事件", eventName, GestureCode.RELEASE, lastTouchEvent));
            } else if (getElementCountOfList(gestureArray, GestureCode.PRESS) > 1) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.PRESS, GestureCode.PRESS));
            } else if (getElementCountOfList(gestureArray, GestureCode.RELEASE) > 1) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.RELEASE, GestureCode.RELEASE));
            } else {
                if (allowDragPointCount == -1) {
                    log.warn("忽略拖动事件");
                } else if (getElementCountOfList(gestureArray, GestureCode.DRAG)
                        != allowDragPointCount) {
                    gestureReport.setOk(false);
                    if (allowDragPointCount == 0) {
                        gestureReport.setErrorMessage(String.format("%s过程出现\"%s\"事件，不允许\"%s\"事件产生",
                                eventName, GestureCode.DRAG, GestureCode.DRAG));
                    } else {
                        gestureReport.setErrorMessage(String.format("%s过程\"%s\"事件出现的数量不足",
                                eventName, GestureCode.DRAG));
                    }
                }
            }
        }
        return gestureReport;
    }

    public static GestureReport checkSingleGesture(List<PointInfo> pointInts,
                                                   String eventName, int fingers,boolean ignoreMiddle) {
        return checkSingleGesture(pointInts, -1, eventName, fingers, ignoreMiddle);
    }

    public static GestureReport checkSingleGesture(List<PointInfo> pointInts,
                                                   int allowDragPointCount,
                                                   String eventName) {
        GestureReport gestureReport = new GestureReport();
        gestureReport.setOk(true);
        List<PointInt> pointIntList = pointInts.stream().map(PointInfo::getPoint).collect(Collectors.toList());
        List<EnhancedPointInt> enhancedPointIntList = pointIntList.stream().map(p -> (EnhancedPointInt) p).collect(Collectors.toList());
        List<String> gestureArray = enhancedPointIntList.stream().map(EnhancedPointInt::getGesture).collect(Collectors.toList());
        log.info("触摸事件流:{}", gestureArray);
        if (gestureArray.size() == 1) {
            String onlyTouchEvent = gestureArray.get(0);
            if (onlyTouchEvent.equals(eventName)) {
                gestureReport.setOk(true);
            } else {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程应该出现\"%s\"事件，但出现了\"%s\"事件",
                        eventName, eventName, onlyTouchEvent));
            }
        } else {
            if (getElementCountOfList(gestureArray, GestureCode.PRESS) > 1) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.PRESS, eventName));
            } else if (getElementCountOfList(gestureArray, GestureCode.RELEASE) > 1) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.RELEASE, eventName));
            } else {
                if (allowDragPointCount == -1) {
                    log.warn("忽略拖动事件");
                } else if (getElementCountOfList(gestureArray, GestureCode.DRAG)
                        != allowDragPointCount) {
                    gestureReport.setOk(false);
                    if (allowDragPointCount == 0) {
                        gestureReport.setErrorMessage(String.format("%s过程出现\"%s\"事件，不允许\"%s\"事件产生",
                                eventName, GestureCode.DRAG, GestureCode.DRAG));
                    } else {
                        gestureReport.setErrorMessage(String.format("%s过程\"%s\"事件出现的数量不足",
                                eventName, GestureCode.DRAG));
                    }
                }
            }
        }
        return gestureReport;
    }

    public static GestureReport checkSingleGesture(List<PointInfo> pointInts,
                                                   int allowDragPointCount,
                                                   String eventName, int fingers, boolean ignoreMiddle) {
        GestureReport gestureReport = new GestureReport();
        gestureReport.setOk(true);

        List<String> gestureArray = pointInts.stream()
                .map(PointInfo::getPoint)
                .map(p -> (EnhancedPointInt) p)
                .map(EnhancedPointInt::getGesture)
                .collect(Collectors.toList());

        log.info("触摸事件流:{}", gestureArray);

        if (fingers == 1) {
            handleSingleFingerGesture(gestureArray, eventName, allowDragPointCount, ignoreMiddle, gestureReport);
        } else {
            handleMultiFingerGesture(gestureArray, eventName, fingers, gestureReport);
        }

        return gestureReport;
    }

    private static void handleSingleFingerGesture(List<String> gestureArray, String eventName, int allowDragPointCount,
                                                  boolean ignoreMiddle, GestureReport gestureReport) {
        if (gestureArray.size() == 1) {
            String onlyTouchEvent = gestureArray.get(0);
            if (!onlyTouchEvent.equals(eventName)) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程应该出现\"%s\"事件，但出现了\"%s\"事件",
                        eventName, eventName, onlyTouchEvent));
            }
        } else {
            if (getElementCountOfList(gestureArray, GestureCode.PRESS) > 1 && !ignoreMiddle) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.PRESS, eventName));
            } else if (getElementCountOfList(gestureArray, GestureCode.RELEASE) > 1) {
                gestureReport.setOk(false);
                gestureReport.setErrorMessage(String.format("%s过程出现多个\"%s\"事件，只允许一个\"%s\"事件",
                        eventName, GestureCode.RELEASE, eventName));
            } else {
                handleDragEvents(gestureArray, eventName, allowDragPointCount, gestureReport);
            }
        }
    }

    private static void handleMultiFingerGesture(List<String> gestureArray, String eventName, int fingers,
                                                 GestureReport gestureReport) {
        int eventNum = getElementCountOfList(gestureArray, eventName);
        if (eventNum != fingers) {
            gestureReport.setOk(false);
            gestureReport.setErrorMessage(String.format("%s过程\"%s\"事件次数为%d次，应为%d次",
                    eventName, eventName, eventNum, fingers));
        }
    }

    private static void handleDragEvents(List<String> gestureArray, String eventName, int allowDragPointCount,
                                         GestureReport gestureReport) {
        if (allowDragPointCount == -1) {
            log.warn("忽略拖动事件");
        } else if (getElementCountOfList(gestureArray, GestureCode.DRAG) != allowDragPointCount) {
            gestureReport.setOk(false);
            if (allowDragPointCount == 0) {
                gestureReport.setErrorMessage(String.format("%s过程出现\"%s\"事件，不允许\"%s\"事件产生",
                        eventName, GestureCode.DRAG, GestureCode.DRAG));
            } else {
                gestureReport.setErrorMessage(String.format("%s过程\"%s\"事件出现的数量不足",
                        eventName, GestureCode.DRAG));
            }
        }
    }
    private static int getElementCountOfList(List<String> list, String element) {
        int count = 0;
        for (String ele : list) {
            if (Objects.equals(ele, element)) {
                count++;
            }
        }
        return count;
    }

    public static List<PointInt> parseTouchPointToPointList(List<TouchPoint> touchPointList, TouchPointMatrixConfig touchPointMatrixConfig) {
        List<PointInt> pointList = new ArrayList<>();
        for (TouchPoint touchPoint : touchPointList) {
            pointList.add(parseTouchPointToPoint(touchPoint, touchPointMatrixConfig));
        }
        return pointList;
    }

    @Data
    public static class TouchPointCollection {
        private List<TouchPoint> touchPointList;

        private byte[] remainByteArray;
    }

    @Data
    private static class MatchPointer {
        private boolean matched;
        private int pos;
    }

    private static MatchPointer checkHeader(byte[] byteArray, int[] headers, int pointerPos) {
        MatchPointer matchPointer = new MatchPointer();
        boolean matched = true;
        for (int header : headers) {
            matched = matched & (byteArray[pointerPos++] == (byte) header);
        }
        matchPointer.setMatched(matched);
        matchPointer.setPos(pointerPos);
        return matchPointer;
    }

    private static boolean checkData(TouchPointMatrixConfig touchPointMatrixConfig, int[] data) {
        Map<String, ColorCellCollection> userTouchDataDefinition = touchPointMatrixConfig.getUserTouchDataDefinition();
        boolean matched = true;
        for (Map.Entry<String, ColorCellCollection> entry : userTouchDataDefinition.entrySet()) {
            String label = entry.getKey();
            ColorCellCollection colorCellCollection = entry.getValue();
            if (StringUtils.isNumeric(label)) {
                int byteOrder = Integer.parseInt(label);
                matched = matched & (data[byteOrder] ==
                        (byte) Integer.parseInt(colorCellCollection.getText().replace("0x", ""), 16));
            }
        }
        return matched;
    }

    private static MatchPointer checkTheCheckSum(String checkSumAlgorithm, int expectedChecksum, byte[] byteArray, int pointer) {
        MatchPointer matchPointer = new MatchPointer();
        boolean matched;
        if (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD16)) {
            byte b1 = byteArray[pointer++];
            byte b2 = byteArray[pointer];
            String hex = Integer.toHexString(b1 & 0xFF) + Integer.toHexString(b2 & 0xFF);
            try {
                int cs = Integer.parseInt(hex, 16);
                matched = cs == expectedChecksum;
            } catch (NumberFormatException e) {
                log.error(e.getMessage(), e);
                matched = false;
            }
        } else {
            matched = byteArray[pointer] == (byte) expectedChecksum;
        }
        matchPointer.setPos(pointer);
        matchPointer.setMatched(matched);
        return matchPointer;
    }

    /**
     * 解析报点
     *
     * @param byteArray 报点字节数组
     * @return 报点列表
     */
    public static TouchPointCollection parseByteArrayToTouchPoints(byte[] byteArray,
                                                                   TouchPointMatrixConfig touchPointMatrixConfig,
                                                                   boolean keepRemainByteData) {
        byte[] buffer = byteArray.clone();
        TouchPointCollection touchPointCollection = new TouchPointCollection();
        List<TouchPoint> touchPointList = new ArrayList<>();
        //命令长度大于0 && byte数组长度大于0 && byte数组长度大于等于设定的长度
        if (touchPointMatrixConfig.getOrderLength() > 0 && byteArray.length > 0 && byteArray.length >= touchPointMatrixConfig.getMinLength()) {
            TouchPoint touchPoint = new TouchPoint();
            touchPoint.setCheckSumAlgorithm(touchPointMatrixConfig.getCheckSumAlgorithm());
            touchPoint.setHeaders(touchPointMatrixConfig.getHeaderByte());
//            touchPoint.setSync((byte) 0x5A);
//            touchPoint.setDirection((byte) 0x02);
            touchPoint.setOrderLength((byte) touchPointMatrixConfig.getOrderLength());
            touchPoint.setDataLength((byte) touchPointMatrixConfig.getDataLength());
            //创建可变数据内存空间
            touchPoint.setData(new int[touchPoint.getDataLength()]);
            int pos = 0;
            while (byteArray.length - pos >= touchPoint.getOrderLength()) {
                int ptr = pos;
                MatchPointer matchPointer = checkHeader(byteArray, touchPoint.getHeaders(), ptr);
                ptr = matchPointer.getPos();
                if (matchPointer.isMatched()) {
                    // 检测到header，开始计算后面的命令
                    int[] data = touchPoint.getData();
                    if (touchPoint.getOrderLength() < touchPoint.getDataLength()) {
                        for (int j = 0; j < touchPoint.getOrderLength(); j++) {
                            data[j] = byteArray[ptr++];
                        }
                    } else {
                        for (int j = 0; j < data.length; j++) {
                            data[j] = byteArray[ptr++];
                        }
                    }

                    if (checkData(touchPointMatrixConfig, data)) {
                        MatchPointer mp;
                        if (touchPointMatrixConfig.getCheckSumAlgorithm().equals(CheckSumAlgorithm.ANY_BYTE1)) {
                            mp = new MatchPointer();
                            mp.setMatched(true);
                            mp.setPos(ptr);
                        } else if (touchPointMatrixConfig.getCheckSumAlgorithm().equals(CheckSumAlgorithm.ANY_BYTE2)) {
                            mp = new MatchPointer();
                            mp.setMatched(true);
                            mp.setPos(++ptr);
                        } else {
                            int checksum = touchPoint.calCheckSum();
                            log.info("checksum:{}({})", checksum, ByteUtils.intToHex(checksum));
                            mp = checkTheCheckSum(touchPointMatrixConfig.getCheckSumAlgorithm(), checksum, byteArray, ptr);
                        }
                        ptr = mp.getPos();
                        if (mp.isMatched()) {
//                        if (checksum == byteArray[ptr]) {
                            //校验ok，报点数据完全匹配
                            log.info("完全匹配:{}", touchPoint);
                            System.out.println(touchPoint.toByteString());
                            touchPointList.add(touchPoint.clone());
                            //完全匹配，减去匹配的命令长度，得到剩余长度
                            int remainByteLength = byteArray.length - touchPoint.getOrderLength();
                            if (remainByteLength >= 0) {
                                //还有数据未处理，新建数组存放剩余数据
                                byte[] tempArray = new byte[remainByteLength];
                                System.arraycopy(byteArray, 0, tempArray, 0, pos);
                                // 确保源数组索引和目标数组索引都在有效范围内
                                if (ptr + 1 < byteArray.length && pos < tempArray.length) {
                                    // 计算实际可以复制的元素数量，确保不会超出目标数组的界限
                                    int numElementsToCopy = Math.min(byteArray.length - ptr - 1, tempArray.length - pos);
                                    // 执行复制操作
                                    if (numElementsToCopy > 0) {
                                        System.arraycopy(byteArray, ptr + 1, tempArray, pos, numElementsToCopy);
                                        byteArray = Arrays.copyOf(tempArray, tempArray.length);
                                    }
                                } else {
                                    // 处理索引超出界限的情况
                                    log.warn("索引超出界限");
                                }
                                byteArray = tempArray;
                                log.debug("剩余数组:" + ByteUtils.byteArrayToHexString(byteArray));
                            }
                            pos = 0;
                            continue;
                        }
                    }
                }
                //TODO：看log
                log.debug("结束报点分析:{},{}", buffer, ExceptionUtils.getStackTrace(3));
                pos++;
            }
        }
        if (byteArray.length > 0) {
            log.info("报点解析->最后数组:{}", ByteUtils.byteArrayToHexString(byteArray));
        }
        touchPointCollection.setTouchPointList(touchPointList);
        if (keepRemainByteData) {
            touchPointCollection.setRemainByteArray(byteArray);
        } else {
            touchPointCollection.setRemainByteArray(new byte[0]);
        }
        return touchPointCollection;
    }

}
