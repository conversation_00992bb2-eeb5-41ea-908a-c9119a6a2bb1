package com.desaysv.workserver.cicd;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.electric_relay.ChannelSwitch;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.serial.text_match.WaitFilterParameter;
import com.desaysv.workserver.devices.testbox.interfaces.IRelaySwitchBoard;
import com.desaysv.workserver.devices.usbswtich.UsbSwitchDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class DeviceOperateUtil {

    private  DeviceManager deviceManager;

    public DeviceOperateUtil(DeviceManager deviceManager) {
        this.deviceManager = deviceManager;
    }

    /**
     * 获取所有USB切换板
     *
     * @return 所有USB切换板列表
     */
    public List<Device> listUsbSwitches() {
        log.info("正在获取所有USB切换板");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> usbSwitches = devices.stream() // 使用流来过滤设备
                    .filter(device -> DeviceType.DEVICE_USB_SWITCH.equals(device.getDeviceType())) // 筛选出deviceType为usbSwitchType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个USB切换板,分别是：{}", usbSwitches.size(), usbSwitches);
            return usbSwitches;
        }catch (Exception e) {
            log.error("获取USB切换板失败", e);
            throw new RuntimeException("获取USB切换板失败", e);
        }
    }

    /**
     * 获取所有继电器
     *
     * @return 所有继电器列表
     */
    public List<Device> listRelays() {
        log.info("正在获取所有继电器");
        try {
            List<Device> devices = deviceManager.getAllDevices();
            List<Device> relays = devices.stream() // 使用流来过滤设备
                    .filter(device -> DeviceType.DEVICE_ELECTRIC_RELAY.equals(device.getDeviceType())) // 筛选出deviceType为relayType的设备
                    .collect(Collectors.toList());//  收集结果到列表

            log.info("成功获取了 {} 个继电器,分别是：{}", relays.size(), relays);
            return relays;
        }catch (Exception e){
            log.error("获取继电器失败", e);
            throw new RuntimeException("获取继电器失败", e);
        }
    }

    /**
     * 获取所有串口
     * @return 所有串口列表
     * */
    public List<Device> listSerialPorts() {
        log.info("正在获取所有串口");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> serialPorts = devices.stream() // 使用流来过滤设备
                    .filter(device -> DeviceType.DEVICE_SERIAL.equals(device.getDeviceType())) // 筛选出deviceType为serialPort的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了{}个串口，分别是：{}", serialPorts.size(), serialPorts);
            return serialPorts;
        }catch (Exception e){
            log.error("获取串口失败", e);
            throw new RuntimeException("获取串口失败", e);
        }
    }

     /**
     * 打开USB切换板
     *
     * @param channel 通道编号
     * @return 是否成功打开USB切换板
     */
    public boolean openUsbSwitches(List<Device> usbSwitches,int channel) {
        boolean success = true;
        try{
            for (Device usbSwitch : usbSwitches){
                log.info("打开USB切换板: {}", usbSwitch);
                //把Device usbSwitch转换为UsbSwitchDevice类型
                UsbSwitchDevice usbSwitchDevice = (UsbSwitchDevice) usbSwitch;
                switch (channel) {
                    case 1:
                        usbSwitchDevice.open1();
                        break;
                    case 2:
                        usbSwitchDevice.open2();
                        break;
                    case 3:
                        usbSwitchDevice.open3();
                        break;
                    case 4:
                        usbSwitchDevice.open4();
                        break;
                    case 5:
                        usbSwitchDevice.open5();
                        break;
                    default:
                        log.error("无效的通道编号: {}", channel);
                        success = false;
                }
            }
        }catch (DeviceSendException e){
            log.error("USB切换板打开失败", e);
            throw new RuntimeException("USB切换板打开失败", e);
        }
        return success;
    }

    /**
     * 操作继电器（打开或关闭）
     *
     * @param channelSwitch 通道信息
     * @return 是否成功操作继电器
     */
    public boolean operateRelays(List<Device> relays, ChannelSwitch channelSwitch) {
        boolean success = true;
        try{
            for (Device relay : relays){
                log.info("操作继电器: {}", relay);
                //把Device relay转换为RelayDevice类型
                IRelaySwitchBoard  relayDevice = (IRelaySwitchBoard) relay;
                try {
                    relayDevice.writeRelaySwitchBoardCard(channelSwitch.getChannel(), channelSwitch.isSwitchOn() ? 1 : 0);
                }
                catch (BoardCardTransportException e){
                    log.error("继电器操作失败", e);
                    throw new RuntimeException("继电器操作失败", e);
                }

            }
        }catch (Exception e){
            log.error("继电器操作失败", e);
            throw new RuntimeException("继电器操作失败", e);
        }
        return success;
    }


    /**
     * 关闭USB切换板
     *
     * @param usbSwitches USB切换板列表
     * @return 是否成功关闭USB切换板
     */
    public boolean closeUsbSwitches(List<Device> usbSwitches) {
        boolean success = true;
        try{
            for (Device usbSwitch : usbSwitches){
                log.info("打开USB切换板: {}", usbSwitch);
                //把Device usbSwitch转换为UsbSwitchDevice类型
                UsbSwitchDevice usbSwitchDevice = (UsbSwitchDevice) usbSwitch;
                usbSwitchDevice.closeAllChannels();
            }
        }catch (DeviceSendException e){
            log.error("USB切换板关闭失败", e);
            throw new RuntimeException("USB切换板关闭失败", e);
        }
        return success;
    }


    /**
     * 向串口发送消息并接收返回数据
     *
     * @param serialPorts 串口列表
     * @param command 要发送的消息
     * @param checkedContext 要检查的返回数据
     * @return 返回数据
     */
    public String sendAndReceiveToSerialPort(List<Device> serialPorts,String command,String checkedContext)throws OperationFailNotification  {
        try{
            for (Device serialPort : serialPorts) {
                log.info("向串口: {}发送消息", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                MessageText messageText = new MessageText();
                messageText.setSendText(command);
                messageText.setMatchText(checkedContext);
                messageText.setTimeoutRetry(3);
                messageText.setReceiveTimeout(5);
                messageText.setHex(false);
                return serialPortDevice.sendAndMatchReturnData(messageText);
            }
        }catch (OperationFailNotification e){
            log.error("查找目标字符串失败！");
            throw e;
        }
        return null;
    }

    /**
     * 向串口发送消息
     *
     * @param serialPorts 串口列表
     * @param command 要发送的消息
     * @return 返回是否成功
     */
    public boolean sendToSerialPort(List<Device> serialPorts,String command)throws OperationFailNotification  {
        for (Device serialPort : serialPorts) {
            log.info("向串口: {}发送消息", serialPort);
            SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
            MessageText messageText = new MessageText();
            messageText.setSendText(command);
            messageText.setTimeoutRetry(3);
            messageText.setHex(false);
            return serialPortDevice.sendReturnResult(messageText);
        }
        return false;
    }


    //等待出现关键字
    public boolean monitorSerialPort(List<Device> serialPorts,String checkedContext)throws OperationFailNotification {
        try{
            for (Device serialPort : serialPorts) {
                log.info("开始监控串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.waitFilter(new WaitFilterParameter(checkedContext,18*60));//超时时间设置为18分钟
            }
        }catch (Exception e){
            log.error("监控目标字符串失败！");
            throw e;
        }
        return false;
    }


    public boolean monitorStartSerialPort(List<Device> serialPorts,String checkedContext) {
        try{
            for (Device serialPort : serialPorts) {
                log.info("开始监控串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.mustExistMonitorStart(checkedContext);
            }
        }catch (Exception e){
            log.error("监控串口失败", e);
        }
        return false;
    }

    public boolean monitorEndSerialPort(List<Device> serialPorts) {
        try{
            for (Device serialPort : serialPorts) {
                log.info("结束监控串口: {}", serialPort);
                SerialPortDevice serialPortDevice = (SerialPortDevice) serialPort;
                return serialPortDevice.mustExistMonitorEnd().isOk();
            }
        }catch (Exception e){
            log.error("结束监控串口失败", e);
        }
        return false;
    }

}
