package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.device_tcp_client.TcpClientDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractTcpClientDeviceFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 10:42
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Component
@Lazy
public class TcpClientClientDeviceFactory implements AbstractTcpClientDeviceFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.TcpClient.TCP_CLIENT:
                return createTcpClientDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createTcpClientDevice(DeviceRegisterForm deviceRegisterForm) {
        return new TcpClientDevice(deviceRegisterForm.getDeviceOperationParameter());
    }
}
