package com.desaysv.workserver.devices;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.manager.DeviceRegisterManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Lazy
public class CommonDeviceHandler extends OperationTarget implements ICommonDevice {

    @Autowired
    private DeviceRegisterManager deviceRegisterManager;

    public ActualExpectedResult deviceOnOff(String deviceName, String command) throws DeviceCloseException {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean isSuccess = false;
        if (!deviceRegisterManager.isRegistered(deviceName)) {
            String message = String.format("%s设备未注册", deviceName);
            log.warn(message);
            actualExpectedResult.put("deviceNotRegister", false, "设备" + deviceName + "未注册");
            return actualExpectedResult;
        }
        boolean isOpen = command.equalsIgnoreCase("ON");
        Device device = deviceRegisterManager.getDevice(deviceName);
        if (device != null) {
            if (isOpen) {
                log.info("打开设备：{}", deviceName);
                try {
                    isSuccess = device.openForOperationResult(true).isOk();
                } catch (Exception e) {
                    log.error("设备{}打开失败", deviceName);
                }
                actualExpectedResult.put("deviceOn", isSuccess, deviceName);
            } else {
                log.info("关闭设备：{}", deviceName);
                try {
                    isSuccess = device.closeForOperationResult().isOk();
                } catch (DeviceCloseException e) {
                    log.error("设备{}断开失败", deviceName);
                }
                actualExpectedResult.put("deviceOff", isSuccess, deviceName);
            }
        } else {
            actualExpectedResult.put("deviceNotFound", false, "设备" + deviceName + "找不到");
        }
        return actualExpectedResult;
    }

}
