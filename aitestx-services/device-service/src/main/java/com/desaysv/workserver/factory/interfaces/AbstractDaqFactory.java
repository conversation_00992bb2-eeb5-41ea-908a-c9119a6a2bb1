package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractDaqFactory extends AbstractDeviceCreator {

    Device createKeysight34461A(DeviceRegisterForm deviceRegisterForm);

    Device createUSB3200N(DeviceRegisterForm deviceRegisterForm);

    Device createUT8806(DeviceRegisterForm deviceRegisterForm);
}
