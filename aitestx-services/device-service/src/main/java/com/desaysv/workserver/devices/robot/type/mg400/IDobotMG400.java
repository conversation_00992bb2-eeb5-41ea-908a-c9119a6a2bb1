package com.desaysv.workserver.devices.robot.type.mg400;

import com.desaysv.workserver.devices.robot.type.mg400.entity.MovJExtEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-22 9:40
 * @description :
 * @modified By :
 * @since : 2022-7-22
 */
public interface IDobotMG400 {
    RobotReply enableRobot();

    RobotReply disableRobot();

    RobotReply resetRobot();

    RobotReply clearError();

    RobotReply searchError();

    RobotReply setPayLoad(int weight);

    RobotReply setSpeedFactor(int factor);

    RobotReply setUserCoordinate(int userIndex);

    /**
     * 移动滑轨
     *
     * @param movJExtEntity 滑轨坐标
     * @return
     */
    RobotReply moveJointExt(MovJExtEntity movJExtEntity);

    RobotReply moveSlideRail(double slideRailDistance);
}
