package com.desaysv.workserver.common.utils.library;

import lombok.extern.slf4j.Slf4j;

import javax.usb.*;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 19:23
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
@Slf4j
public class UsbDeviceFinder {

    public static void main(String[] args) throws UsbException, UnsupportedEncodingException {
        UsbDeviceFinder usbDeviceFinder = new UsbDeviceFinder();
        List<UsbDevice> device = usbDeviceFinder.findDevice(UsbHostManager.getUsbServices().getRootUsbHub());
        System.out.println(device);
    }

    public List<UsbDevice> findDevice(UsbHub hub) {
        return (List<UsbDevice>) hub.getAttachedUsbDevices();
    }

    public UsbDevice findDevice(UsbHub hub, int vid, int pid) {
        List<UsbDevice> list = findDevice(hub);

        for (UsbDevice device : list) {
            UsbDeviceDescriptor desc = device.getUsbDeviceDescriptor();
            if (desc.idVendor() == vid && desc.idProduct() == pid) {
                return device;
            }
            if (device.isUsbHub()) {
                device = findDevice((UsbHub) device, vid, pid);
                if (device != null) {
                    return device;
                }
            }
        }
        return null;
    }

    // 初始化设备，请求获取、打开并接收数据
    public void initUseDevice(UsbDevice device) throws UsbException {
        UsbInterface iface = null;
        boolean connectSuccess = false;
        if (!device.getActiveUsbConfiguration().getUsbInterfaces().isEmpty()) {
            iface = (UsbInterface) device.getActiveUsbConfiguration().getUsbInterfaces().get(0);
        }
        if (iface != null) {
            try {
                if (!iface.isClaimed()) {
                    iface.claim(usbInterface -> true);
                }
                connectSuccess = true;
                UsbEndpoint receivedUsbEndpoint, sendUsbEndpoint;
                sendUsbEndpoint = (UsbEndpoint) iface.getUsbEndpoints().get(0);
                // 注意管道区别，OUT输usb设备输出数据的管道
                if (!sendUsbEndpoint.getUsbEndpointDescriptor().toString().contains("OUT")) {
                    receivedUsbEndpoint = sendUsbEndpoint;
                    sendUsbEndpoint = (UsbEndpoint) iface.getUsbEndpoints().get(1);
                } else {
                    receivedUsbEndpoint = (UsbEndpoint) iface.getUsbEndpoints().get(1);
                }
                //发送
                UsbPipe sendUsbPipe = sendUsbEndpoint.getUsbPipe();
                if (!sendUsbPipe.isOpen()) {
                    sendUsbPipe.open();
                }
                //接收
                final UsbPipe receivedUsbPipe = receivedUsbEndpoint.getUsbPipe();

                if (!receivedUsbPipe.isOpen()) {
                    receivedUsbPipe.open();
                }
                // 接收操作放到子线程实现异步回调
                new Thread(() -> {
                    try {
                        receivedMassge(receivedUsbPipe);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }).start();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            } finally {
                // 最后一定要关闭usb设备释放资源
                iface.release();
            }
        }
    }

    // 轮询读取USB发送回来的数据
    private void receivedMassge(UsbPipe usbPipe) throws Exception {
        byte[] buffer = new byte[64];
        int length;
        while (true) {
            length = usbPipe.syncSubmit(buffer);
            for (int i = 0; i < length; i++) {
                // do something
                System.out.println(i);
            }
        }
    }

    public int send(UsbPipe usbPipe, byte[] buffer) throws UsbException {
        return usbPipe.syncSubmit(buffer);//阻塞
        //usbPipe.asyncSubmit(buff);//非阻塞
    }


}
