package com.desaysv.workserver.devices.testbox.interfaces.rzcu;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.*;

public interface IRZCUDcCollector {
    Logger log = LogManager.getLogger(IRZCUDcCollector.class.getSimpleName());

    float fetchCurrent(int slaveAddress, Integer channelNumber) throws DeviceReadException, ModbusTransportException;

    float fetchVoltage(int slaveAddress, Integer channelNumber) throws DeviceReadException, ModbusTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_CURRENT"})
    default ActualExpectedResult getCurrent(Integer deviceChannel, Integer channelNumber, String currentWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        float getCurrent = -1;
        try {
            unit = getUnit(currentWithUnit, "A");
            getCurrent = valueUnitConvert(fetchCurrent(deviceChannel, channelNumber), "A", unit);
            float current = parseCurrent(currentWithUnit, unit);
            pass = getCurrent == current;
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetCurrent", pass, String.format("%s%s",getCurrent, unit));
        log.info("采集器采集到电流:{}{}, {}毫秒", getCurrent, unit, System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_VOLTAGE"})
    default ActualExpectedResult getVoltage(Integer deviceChannel, Integer channelNumber, String voltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        double getVoltage = -1;
        try {
            unit = getUnit(voltageWithUnit, "V");  //获取中序列的单位
            getVoltage = BaseRegexRule.valueUnitConvert(fetchVoltage(deviceChannel, channelNumber), "V", unit);  //采集卡获取的是V, 需要转换成unit
            float voltage = BaseRegexRule.getVoltage(voltageWithUnit, unit);
            pass = getVoltage == voltage;
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetVoltage", pass, String.format("%s%s",getVoltage, unit));
        log.info("采集器采集到电压:{}{}， {}毫秒", getVoltage, unit, System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_VOLTAGE_RANDOM"})
    default ActualExpectedResult compareVoltageRandom(Integer deviceChannel, Integer channelNumber, String lowerVoltageWithUnit, String upperVoltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        float getVoltage = -1;
        try {
            unit = getUnit(lowerVoltageWithUnit, "V");
            getVoltage = BaseRegexRule.valueUnitConvert(fetchVoltage(deviceChannel, channelNumber),"V", unit);   //默认单位是V
            float voltageLowerLimit = BaseRegexRule.getVoltage(lowerVoltageWithUnit, unit);
            float voltageUpperLimit = BaseRegexRule.getVoltage(upperVoltageWithUnit, unit);
            pass = getVoltage >= voltageLowerLimit && getVoltage <= voltageUpperLimit;
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("GetVoltage", pass, String.format("%s%s",getVoltage, unit));
        log.info("采集器采集到电压:{}{}， {}毫秒", getVoltage, unit, System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }



    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_CURRENT_RANDOM"})
    default ActualExpectedResult compareCurrentRandom(Integer deviceChannel, Integer channelNumber, String lowerCurrentWithUnit, String upperCurrentWithUnit) {
//    default ActualExpectedResult getCurrentCompare(Integer deviceChannel, Integer channelNumber, double lowerCurrent, double upperCurrent) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        double getCurrent = -1;
        try {
            unit = getUnit(lowerCurrentWithUnit, "A");
            getCurrent = BaseRegexRule.valueUnitConvert(fetchCurrent(deviceChannel, channelNumber),  "A", unit);
            float lowerCurrent = BaseRegexRule.parseCurrent(lowerCurrentWithUnit, unit);
            float upperCurrent = BaseRegexRule.parseCurrent(upperCurrentWithUnit, unit);
            pass = getCurrent >= lowerCurrent && getCurrent <= upperCurrent;
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("compareCurrentRandom", pass, String.format("%s%s",getCurrent, unit));
        log.info("采集器采集到电流:{}{}, {}毫秒", getCurrent, unit,System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_VOLTAGE_RANGE"})
    default ActualExpectedResult compareVoltageRange(Integer deviceChannel, Integer channelNumber, String voltageBaseValueWithUnit,
                                                     String voltageRangeWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        float getVoltage = -1;
        try {
            unit = getUnit(voltageBaseValueWithUnit, "V");
            getVoltage = BaseRegexRule.valueUnitConvert(fetchVoltage(deviceChannel, channelNumber), "V",unit);
            float voltageBaseValue = BaseRegexRule.getVoltage(voltageBaseValueWithUnit, unit);
            float voltageRange = BaseRegexRule.getVoltage(voltageRangeWithUnit, unit);
            pass = getVoltage >= voltageBaseValue - voltageRange && getVoltage <= voltageBaseValue + voltageRange;
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("compareVoltageRange", pass, String.format("%s%s",getVoltage, unit));
        log.info("采集器采集到电压:{}{}， {}毫秒", getVoltage, unit,System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.DcCollectorRegexRule).GET_CURRENT_RANGE"})
    default ActualExpectedResult compareCurrentRange(Integer deviceChannel, Integer channelNumber,
                String currentBaseValueWithUnit, String currentRangeWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String unit = "";
        boolean pass = false;
        float getCurrent = -1;
        try {
            unit = getUnit(currentBaseValueWithUnit, "A");
            getCurrent = valueUnitConvert(fetchCurrent(deviceChannel, channelNumber),"A", unit);
            float currentBaseValue = parseCurrent(currentBaseValueWithUnit, unit);
            float currentRange = parseCurrent(currentRangeWithUnit, unit);
            pass = getCurrent >= currentBaseValue - currentRange && getCurrent <= currentBaseValue + currentRange;
            actualExpectedResult.put("compareCurrentRange", pass, String.format("%s%s",getCurrent, unit));
        } catch (DeviceReadException | ModbusTransportException e) {
            log.error(e.getMessage());
        }
        actualExpectedResult.put("compareCurrentRange", pass, String.format("%s%s",getCurrent, unit));
        log.info("采集器采集到电流:{}{}, {}毫秒", getCurrent, unit, System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

}
