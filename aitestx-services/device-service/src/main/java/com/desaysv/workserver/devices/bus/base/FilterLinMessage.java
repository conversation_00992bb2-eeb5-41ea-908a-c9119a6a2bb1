package com.desaysv.workserver.devices.bus.base;

import com.desaysv.workserver.devices.bus.base.lin.LinMessage;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class FilterLinMessage extends LinMessage {

    private boolean alreadyFiltered;//是否已经过滤

    public FilterLinMessage(float timestamp, String idHex, Integer channel, String dir, int length, byte[] data, Float duration, float period, int sendTimes,int framesPerSendNum,String status, boolean alreadyFiltered) {
        super(timestamp, idHex, channel, dir, length, data, duration, period, sendTimes,framesPerSendNum,status);
        this.alreadyFiltered = alreadyFiltered;
    }

}
