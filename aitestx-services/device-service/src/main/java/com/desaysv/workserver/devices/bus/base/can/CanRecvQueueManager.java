package com.desaysv.workserver.devices.bus.base.can;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

public class CanRecvQueueManager {
    private final Map<CanIdentifier, byte[]> dataQueue = new ConcurrentHashMap<>();

    @AllArgsConstructor
    @Data
    static class CanIdentifier {
        private int channel;
        private int id;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CanIdentifier that = (CanIdentifier) o;
            return id == that.id && channel == that.channel;
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, channel);
        }
    }

    public void put(int channel, int id, byte[] data) {
        dataQueue.put(new CanIdentifier(channel, id), data);
    }

    public byte[] get(int channel, int id) {
        return dataQueue.get(new CanIdentifier(channel, id));
    }

    public boolean contains(int channel, int id) {
        return dataQueue.containsKey(new CanIdentifier(channel, id));
    }

    public void clear() {
        dataQueue.clear();
    }
}
