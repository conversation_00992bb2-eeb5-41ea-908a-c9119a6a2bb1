package com.desaysv.workserver.devices.bus.nican;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CanMessageVo {
    /**
     * 时间戳
     */
    private double time;

    /**
     * 通道
     */
    private String chn;

    /**
     * ID
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * CAN类型 CAN CANFD CANFD加速
     */
    private String eventType;

    /**
     * 流向 TX发送方向 RX接收方向
     */
    private String dir;

    /**
     * DLC
     */
    private int dlc;

    /**
     * Data
     */
    private byte[] data;    //CAN消息数据

    /**
     * Can信号
     */
    private List<CanSignalVo> canSignalList;


}
