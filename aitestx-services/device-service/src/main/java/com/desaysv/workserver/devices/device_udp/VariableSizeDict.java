package com.desaysv.workserver.devices.device_udp;

import java.util.HashMap;
import java.util.Map;

public class VariableSizeDict {
    private static final Map<String, Integer> dictSizeMap = new HashMap<>();

    static {
        // 初始化字典数据
        dictSizeMap.put("FLOAT64_IEEE", 8);
        dictSizeMap.put("FLOAT32_IEEE", 4);
        dictSizeMap.put("UWORD", 2);
        dictSizeMap.put("SLONG_S", 4);
        dictSizeMap.put("SLONG", 4);
        dictSizeMap.put("ULONG", 4);
        dictSizeMap.put("__ULONG_S", 4);
        dictSizeMap.put("SWORD_S", 2);
        dictSizeMap.put("SWORD", 2);
        dictSizeMap.put("__UBYTE_S", 1);
        dictSizeMap.put("UBYTE", 1);
        dictSizeMap.put("SBYTE_S", 1);
        dictSizeMap.put("SBYTE", 1);
    }

    public static int getVariableSize(String varName) {
        if (!dictSizeMap.containsKey(varName)) {
            throw new IllegalArgumentException("Unknown variable name: " + varName);
        }
        return dictSizeMap.get(varName);
    }

    // 调用示例
    public static void main(String[] args) {
        int size = getVariableSize("__UBYTE_S");
        System.out.println(size); // 输出 1
    }
}