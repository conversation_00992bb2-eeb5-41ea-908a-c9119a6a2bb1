package com.desaysv.workserver.devices.bus.utils;

import cantools.dbc.DbcReader;
import cantools.dbc.Message;
import lombok.Getter;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EncoderUtil {
    private final List<String> dbcPaths;

    @Getter
    private List<DbcReader> dbcReaders;

    @Getter
    private final Map<Integer, List<Message>> messages;

    public EncoderUtil(List<String> paths) {
        this.dbcPaths = new ArrayList<>(paths);
        this.dbcReaders = new ArrayList<>();
        this.messages = new HashMap<>();
        for (int i = 0; i < paths.size(); i++) {
            DbcReader dbcReader = new DbcReader();
            dbcReader.parseFile(new File(paths.get(i)));
            messages.put(i + 1, dbcReader.getBus().getMessages());
            dbcReaders.add(dbcReader);
        }
    }

    public EncoderUtil() {
        this.dbcPaths = new ArrayList<>();
        this.dbcReaders = new ArrayList<>();
        this.messages = new HashMap<>();
    }

    public void addDBCPath(String path) {
        DbcReader dbcReader = new DbcReader();
        dbcReader.parseFile(new File(path));
        messages.put(messages.size() + 1, dbcReader.getBus().getMessages());
        dbcReaders.add(dbcReader);
        dbcPaths.add(path);
    }

    public void removeDBCPath(int index) {
        if(index < dbcPaths.size()){
            dbcPaths.remove(index);
        }
        dbcReaders.remove(index);
        messages.remove(index + 1);
    }

    public void replaceDBCPath(int index, String newPath) {
        //先判断dbcPaths有没index 没有则添加
        if (dbcPaths.size() <= index) {
            dbcPaths.add(newPath);
            dbcReaders.add(new DbcReader(new File(newPath)));
        } else {
            dbcPaths.set(index, newPath);
            dbcReaders.set(index, new DbcReader(new File(newPath)));
        }
        messages.clear();
        for (DbcReader dbcReader : dbcReaders) {
            messages.put(messages.size() + 1, dbcReader.getBus().getMessages());
        }
    }

    public Message getMessage(int dbcIndex, String idHex) {
        //兼容旧版本没有dbcIndex
        return messages.get(dbcIndex == 0 ? 1 : dbcIndex).stream().filter(o -> o.getId().equals(idHex)).findFirst().orElse(null);
    }

    public List<Message> getMessages(int dbcIndex) {
        //兼容旧版本没有dbcIndex
        return messages.get(dbcIndex == 0 ? 1 : dbcIndex);
    }

    public List<String> getDbcPaths() {
        return dbcPaths;
    }

    public DbcReader getDbcReaderById(String id) {
        for (DbcReader dbcReader : dbcReaders) {
            List<Message> messages = dbcReader.getBus().getMessages();
            for (Message message : messages) {
                if (message.getId().equals(id)) {
                    return dbcReader;
                }
            }
        }
        return null;
    }
}
