package com.desaysv.workserver.manager;

import lombok.Getter;

import java.io.File;

public class AndroidImageFileManager extends ImageFileManager {

    @Getter
    private File screenshotFile;


    public AndroidImageFileManager(String projectName) {
        super(projectName);
    }

    public AndroidImageFileManager(String projectName, String dynamicFolderName) {
        super(projectName, dynamicFolderName);
    }

    @Override
    protected String getImageFolderName() {
        return "android";
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        super.initSubPaths(dynamicFolderName);
        screenshotFile = createFolder(imageConstants.basePath, "screenshot");
    }

}
