package com.desaysv.workserver.finder;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.android.AdbHudDevice;
import com.desaysv.workserver.devices.android.AdbStatus;
import com.desaysv.workserver.devices.android.AndroidDevice;
import com.desaysv.workserver.devices.android.UsbAndroid;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Android设备查找
 */
@Component
@Slf4j
@Lazy
public class AndroidDeviceFinder {

    public List<Device> findPhysicalAndroids(String deviceModel, boolean isLock) {
        List<Device> devices = new ArrayList<>();
        List<AdbStatus> adbStatusList = AndroidDevice.getAllAndroids();
        log.info("adb列表:{}", adbStatusList);
        AndroidDevice device;
        for (AdbStatus adbStatus : adbStatusList) {
            if ((DeviceModel.Android.ADB_HUD).equals(deviceModel)) {
                device = new AdbHudDevice();
            } else {
                device = new UsbAndroid();
            }
            device.setDeviceUniqueCode(adbStatus.getSerialNumber());
            int deviceIndex = Device.getDeviceModelIndex(device.getDeviceModel());
            String androidName = "Android#" + deviceIndex;
            if (DeviceModel.Android.ADB_HUD.equals(deviceModel)) {
                androidName = "HUD#" + deviceIndex;
            }
            device.setDeviceName(adbStatus.getSerialNumber());
            device.setAliasName(androidName);
//            device.setDeviceAliasName(androidName);
            device.setDeviceOperationParameter(new DeviceOperationParameter());
            devices.add(device);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), d.getDeviceModel()));
        }
        return devices;
    }
}
