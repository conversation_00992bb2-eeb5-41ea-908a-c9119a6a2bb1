package com.desaysv.workserver.screen;

import com.desaysv.workserver.devices.serial.UsbSerialPortEventListener;
import com.desaysv.workserver.monitor.Monitor;
import com.desaysv.workserver.monitor.data.DeviceDataDispatcher;
import com.desaysv.workserver.screen.consumers.EventBasedTouchPointDataConsumer;
import com.desaysv.workserver.screen.consumers.KeepAliveTouchPointDataConsumer;
import com.desaysv.workserver.screen.distributor.EventBasedTouchPointDistributor;
import com.desaysv.workserver.screen.distributor.KeepAliveTouchPointDistributor;
import com.desaysv.workserver.entity.PointInt;

import java.util.List;

/**
 * 报点监控
 */
public class TouchPointMonitor extends Monitor {

    private final UsbSerialPortEventListener usbSerialPortEventListener;

    private EventBasedTouchPointDistributor eventBasedTouchPointDistributor;
    private KeepAliveTouchPointDistributor keepAliveTouchPointDistributor;

    public TouchPointMonitor(UsbSerialPortEventListener usbSerialPortEventListener) {
        this.usbSerialPortEventListener = usbSerialPortEventListener;
    }

    public EventBasedTouchPointDistributor getEventBasedTouchPointDistributor(ScreenService screenService) {
        if (eventBasedTouchPointDistributor == null) {
            eventBasedTouchPointDistributor = new EventBasedTouchPointDistributor(new EventBasedTouchPointDataConsumer(), screenService);
            usbSerialPortEventListener.addByteDistributor(eventBasedTouchPointDistributor);
            eventBasedTouchPointDistributor.start();
        }
        return eventBasedTouchPointDistributor;
    }

    public KeepAliveTouchPointDistributor getKeepAliveTouchPointDistributor(ScreenService screenService,
                                                                            DeviceDataDispatcher<List<PointInt>> deviceDataDispatcher) {
        if (keepAliveTouchPointDistributor == null) {
            keepAliveTouchPointDistributor = new KeepAliveTouchPointDistributor(
                    new KeepAliveTouchPointDataConsumer(deviceDataDispatcher, screenService));
            usbSerialPortEventListener.addByteDistributor(keepAliveTouchPointDistributor);
            keepAliveTouchPointDistributor.start();
        }
        return keepAliveTouchPointDistributor;
    }
}
