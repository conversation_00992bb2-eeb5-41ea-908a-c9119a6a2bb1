package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.electronic_load.N68000ElectronicLoad;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractElectronicLoadFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class ElectronicLoadFactory implements AbstractElectronicLoadFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.ElectronicLoad.N68000:
                return createN68000ELoadDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createN68000ELoadDevice(DeviceRegisterForm deviceRegisterForm) {
        return new N68000ElectronicLoad(deviceRegisterForm.getDeviceOperationParameter());
    }
}
