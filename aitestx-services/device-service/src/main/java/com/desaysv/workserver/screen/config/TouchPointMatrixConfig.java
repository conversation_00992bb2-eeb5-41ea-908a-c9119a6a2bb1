package com.desaysv.workserver.screen.config;

import com.desaysv.workserver.screen.entity.ColorCellCollection;
import com.desaysv.workserver.utils.ByteUtils;
import lombok.Data;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Data
public class TouchPointMatrixConfig {

//    private int orderLength;
//    private int dataLength;

    private String protocolHeader;
    private int dataLength;
    private int pressLength;
    private int releaseLength;
    private boolean ignoreMiddle;
    private String checkSumAlgorithm = CheckSumAlgorithm.ADD8;
    private GestureCode gestureCode;
    private Map<String, ColorCellCollection> userTouchDataDefinition;

    public int[] getHeaderByte() {
        return ByteUtils.hexStringToIntArray(protocolHeader);
    }

    public int getOrderLength() {
        int minLength = Math.min(Math.min(dataLength, pressLength), releaseLength);
        return getHeaderByte().length + minLength + (checkSumAlgorithm.equals(CheckSumAlgorithm.ADD16) ? 2 : 1);
    }

    public int getMinLength() {
        return Math.min(Math.min(dataLength, pressLength), releaseLength);

    }

    public TouchPointMatrixConfig() {
        userTouchDataDefinition = new HashMap<>();
    }

    public boolean invalid() {
        return userTouchDataDefinition.isEmpty() || dataLength == 0;
    }

    public static void main(String[] args) {
        System.out.println(Arrays.toString(ByteUtils.hexStringToByteArray("")));
    }

}
