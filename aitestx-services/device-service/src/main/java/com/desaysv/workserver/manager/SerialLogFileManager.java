package com.desaysv.workserver.manager;

import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import lombok.Getter;

import java.io.File;

/**
 * 串口日志文件管理器
 */
@Getter
public class SerialLogFileManager extends ProjectFileManager {

    private File serialLoggerFolder; // 串口日志文件夹

    public SerialLogFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        serialLoggerFolder = createFolder(fileDbPath, "serialLogs");
    }

    /**
     * 获取日志文件夹
     ** @return
     */
    public File getFolder() {
        return serialLoggerFolder;
    }


}
