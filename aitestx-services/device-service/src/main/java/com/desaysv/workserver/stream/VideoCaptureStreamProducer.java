package com.desaysv.workserver.stream;


//TODO:统一PortStreamProducer和AndroidStreamProducer
public abstract class VideoCaptureStreamProducer extends StreamProducer {

    public VideoCaptureStreamProducer() {
    }

    public VideoCaptureStreamProducer(String url) {
        super(url);
    }

    public VideoCaptureStreamProducer(String url, int w, int h) {
        super(url, w, h);
    }

    public VideoCaptureStreamProducer(int w, int h) {
        super(w, h);
    }

    protected abstract void startStream(String deviceModel, String deviceName) throws Exception;

    public abstract String pushStream(String deviceModel, String deviceName) throws Exception;
}
