package com.desaysv.workserver.devices.electric_relay;

import com.desaysv.electric_relay.utils.ModbusUtil;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.RelayConfigManager;
import com.desaysv.workserver.devices.testbox.interfaces.IRelaySwitchBoard;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.desaysv.workserver.common.constant.RelayConstants.RELAY_DELAY_TIME;

@Slf4j
public class JYDAM1600CDevice extends PortDevice<ElectricRelayConfig> implements IRelaySwitchBoard {

    private ModbusUtil modbusUtil;
    private float delayTime = 0;

    public JYDAM1600CDevice() {
        this(new DeviceOperationParameter());
    }

    public JYDAM1600CDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil == null) {
            modbusUtil = new ModbusUtil();
        }
        return modbusUtil.openSerialPort(getDevicePortName(), 9600, getDeviceOperationParameter().getAddress());
        //这里判断连接一定是false 因为没有init
        //boolean connected = modbusUtil.checkConnection();
        //if (connected) {
        //return modbusUtil.openSerialPort(getDevicePortName(), 9600, getDeviceOperationParameter().getAddress());
        //}
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil != null) {
            log.info("关闭继电器");
            modbusUtil.closeSerialPort();
        }
        return super.close();
    }

    /**
     * 切换继电器
     *
     * @param channelSwitch 继电器通道
     * @return 切换结果
     */
    public boolean switchRelay(ChannelSwitch channelSwitch) {
        if (isSimulated()) {
            return true;
        }
        log.info("通道{}{}{}", channelSwitch.getChannel(),
                StringUtils.isEmpty(channelSwitch.getPhyLabel()) ? "" : String.format("(%s)", channelSwitch.getPhyLabel()),
                channelSwitch.isSwitchOn() ? "开" : "关");
        if (modbusUtil != null) {
            boolean relayStateChanged = modbusUtil.controlRelay(channelSwitch.getChannel(), channelSwitch.isSwitchOn());

            // 等待延时时间
            RelayConfigManager.waitForDelay(channelSwitch.getDelayTime());
            return relayStateChanged;
        }

        return false;
    }

    /**
     * 设置继电器延时
     *
     * @param delayTime 延时秒
     */
    public void setRelayTime(float delayTime) {
        this.delayTime = delayTime;
    }

    @Override
    public boolean writeRelaySwitchBoardCard(Integer deviceChannel, int value) {
        if (isSimulated()) {
            return true;
        }
        ChannelSwitch channelSwitch = new ChannelSwitch();
        channelSwitch.setChannel(deviceChannel);
        channelSwitch.setSwitchOn(value == 1);

        // 设置等待时间
        String configValue = RelayConfigManager.getConfigValue(getDevice().getAliasName(), RELAY_DELAY_TIME);
        channelSwitch.setDelayTime(Integer.parseInt(configValue));
        return switchRelay(channelSwitch);
    }

    @Override
    public boolean writeRelaySwitchAll(int value) throws BoardCardTransportException {
        return false;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ELECTRIC_RELAY;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.ElectricRelay.JYDAM1600C;
    }

    @Override
    public void updateConfig(ElectricRelayConfig electricRelayConfig) {
        super.updateConfig(electricRelayConfig);
    }

    @Override
    public Class<ElectricRelayConfig> getDeviceConfigClass() {
        return ElectricRelayConfig.class;
    }
}
