package com.desaysv.workserver.devices.oscilloscope;

import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.HashMap;
import java.util.Map;

public class OscilloscopeController implements AutoCloseable {

    private static final String VISA_ADDRESS = "USB0::0xF4EC::0xEE38::SDS5XCBX3R0127::INSTR";
    private static final int TIMEOUT_MS = 10000;
    private static final int CHANNEL = 1;

    private final JVisaResourceManager rm;
    private final JVisaInstrument scope;

    public static void main(String[] args) {
        try (OscilloscopeController controller = new OscilloscopeController()) {
            // 设备初始化
            controller.initializeDevice();
            
            // 自动设置流程
            controller.performAutoSetup();
            
            // 执行测量
            Map<String, Double> measurements = controller.getMeasurements();
            
            // 输出结果
            printMeasurements(measurements);
            
        } catch (Exception e) {
            System.err.println("仪器控制错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public OscilloscopeController() throws Exception {
        rm = new JVisaResourceManager();
        scope = rm.openInstrument(VISA_ADDRESS);
        scope.setTimeout(TIMEOUT_MS);
    }

    private void initializeDevice() throws Exception {
        sendCommand("*RST");  // 重置设备到默认状态
        sendCommand("*CLS");  // 清除状态寄存器
        configureChannel();
    }

    private void configureChannel() throws Exception {
        sendCommand(String.format(":CHAN%d:DISP ON", CHANNEL));  // 开启通道
        sendCommand(String.format(":CHAN%d:COUP DC", CHANNEL));  // 设置直流耦合
        sendCommand(String.format(":CHAN%d:PROBE 1X", CHANNEL)); // 设置1X探头衰减
    }

    private void performAutoSetup() throws Exception {
        sendCommand(":AUToscale");  // 执行自动设置
        waitForOperationComplete(15);
        verifySignalIntegrity();
    }

    private Map<String, Double> getMeasurements() throws Exception {
        Map<String, Double> results = new HashMap<>();
        results.put("frequency", queryMeasurement("FREQ"));
        results.put("duty_cycle", queryMeasurement("DUTY"));
        return results;
    }

    private double queryMeasurement(String type) throws Exception {
        final String cmd = String.format(":MEAS:%s? CHAN%d", type, CHANNEL);
        return parseMeasurement(sendQuery(cmd));
    }

    // 核心通信方法
    private void sendCommand(String cmd) throws Exception {
        System.out.printf("[TX] %s\n", cmd);
        scope.write(cmd);
    }

    private String sendQuery(String query) throws Exception {
        System.out.printf("[TX] %s\n", query);
        final String response = scope.queryString(query).trim();
        System.out.printf("[RX] %s\n", response);
        return response;
    }

    // 辅助方法
    private void waitForOperationComplete(int timeoutSec) throws Exception {
        long endTime = System.currentTimeMillis() + timeoutSec * 1000L;
        while (System.currentTimeMillis() < endTime) {
            if ("1".equals(sendQuery("*OPC?"))) {
                return;
            }
            Thread.sleep(100);
        }
        throw new Exception("操作超时 (" + timeoutSec + "秒)");
    }

    private void verifySignalIntegrity() throws Exception {
        final String triggerStatus = sendQuery(":TRIG:STAT?").trim();
        if (!triggerStatus.equalsIgnoreCase("READY") && !triggerStatus.equalsIgnoreCase("AUTO")) {
            throw new Exception("信号不稳定，当前触发状态: " + triggerStatus);
        }
    }

    private static double parseMeasurement(String response) throws Exception {
        try {
            // 处理不同地区的小数点格式
            return Double.parseDouble(response.replace(",", "."));
        } catch (NumberFormatException e) {
            throw new Exception("无效的测量值: " + response);
        }
    }

    private static void printMeasurements(Map<String, Double> results) {
        System.out.println("\n=== 测量结果 ===");
        System.out.printf("频率: \t\t%.3f Hz\n", results.get("frequency"));
        System.out.printf("占空比: \t%.1f%%\n", results.get("duty_cycle"));
    }

    @Override
    public void close() throws Exception {
        if (scope != null ) {
            try {
                sendCommand(":STOP");  // 停止采集
            } finally {
                scope.close();
            }
        }
        if (rm != null) {
            rm.close();
        }
    }
}