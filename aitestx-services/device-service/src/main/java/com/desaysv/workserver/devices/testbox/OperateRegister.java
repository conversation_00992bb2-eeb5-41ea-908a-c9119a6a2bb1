package com.desaysv.workserver.devices.testbox;

import cn.hutool.core.util.ArrayUtil;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.utils.DataUtils;
import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.exception.ModbusTransportException;
import com.serotonin.modbus4j.msg.ReadHoldingRegistersRequest;
import com.serotonin.modbus4j.msg.ReadHoldingRegistersResponse;
import com.serotonin.modbus4j.msg.WriteRegistersRequest;
import com.serotonin.modbus4j.msg.WriteRegistersResponse;
import com.serotonin.modbus4j.serial.SerialPortWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.desaysv.workserver.utils.DataUtils.*;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/26 15:46
 * @description :
 * @modified By :
 * @since : 2023/5/26
 **/
@Slf4j
public class OperateRegister {
    /**
     * 读取11块电阻板卡(44个通道)的数据，slaveId还不确定
     */
    public List<Integer> readResistanceBoardCardData(ModbusMaster master) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId;
        int startAddress = 5;
        int len = 8;
        byte[] tempBytes = {};
        List<Integer> dataList = new ArrayList<>();
        for (int i = 6; i < 17; i++) {
            slaveId = i;
            byte[] resultDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
            tempBytes = mergeArrays(tempBytes, resultDataBytes);
        }
        int count = tempBytes.length / 4;
        for (int i = 0; i < count; i++) {
            byte[] bytes = DataUtils.subByte(tempBytes, i * 4, 4);
            int value = DataUtils.bytesToInt(bytes);    //测试电阻板卡返回
            dataList.add(value);
        }
        long eTime = System.currentTimeMillis();
        log.info("读取电阻板卡全部通道, 执行时长：{} 毫秒, 返回结果:{}", eTime - sTime, ArrayUtil.toString(dataList));
        return dataList;
    }


    /**
     * 通过通道去读取电阻值
     */
    public int readResistanceBoardCardByChannel(ModbusMaster master, Integer deviceChannel) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int value = 0;
        byte[] tempBytes = {};
        int len = 2;
        int channelQuantity = 4;
        int boardQuantity = 11;
        int boardNumber = (deviceChannel - 1) / channelQuantity + 1;
        int channelNumber = (deviceChannel - 1) % channelQuantity + 1;
        if (boardNumber > boardQuantity) {
            log.info("No such resistance board card:{}", boardNumber);
            return -1;
        }
        //前面是从站地址，后面是通道  板卡地址从6开始 6~16
        int slaveId = boardNumber + 5;
        int startAddress = 5 + 2 * (channelNumber - 1);   //通道一的寄存器地址是5，通道二是7，通道三是9，通道四是11
        byte[] resultDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        tempBytes = mergeArrays(tempBytes, resultDataBytes);
        int count = tempBytes.length / 4;
        for (int i = 0; i < count; i++) {
            byte[] bytes = DataUtils.subByte(tempBytes, i * 4, 4);
            value = DataUtils.bytesToInt(bytes);    //测试电阻板卡返回
        }
        long eTime = System.currentTimeMillis();
        log.info("读取电阻板卡, 通道:{}, 阻值：{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
        return value;
    }


    /**
     * 读取1块继电器板卡(8个通道)的数据
     */
    public Map<String, Boolean> readRelayBoardCardData(ModbusMaster master) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        Map<String, Boolean> dataMap = new HashMap<>();
        byte[] resultDataBytes = readRelayBoardCardBytes(master);
        if (resultDataBytes != null) {
            try {
                String value = DataUtils.convertBigEndianTo16Bit(resultDataBytes); //测试继电器板卡返回
                char[] chars = value.substring(8).toCharArray();
                boolean isOpen;
                for (int i = 0; i < chars.length; i++) {
                    int key = 8 - i;
                    isOpen = chars[i] == '1';
                    dataMap.put(String.valueOf(key), isOpen);
                }
            } catch (IllegalArgumentException e) {
                log.error(e.getMessage(), e);
            }
        }
        log.info("读取继电器板卡返回结果:{}", dataMap);
        return dataMap;
    }


    public byte[] readRelayBoardCardBytes(ModbusMaster master) throws BoardCardTransportException {
        int slaveId = 4;
        int startAddress = 5;
        int len = 1;
        return readHoldingRegisters(master, slaveId, startAddress, len);
    }

    public Integer readRelayBoardCardByChannel(ModbusMaster master, int deviceChannel) throws BoardCardTransportException {
        long sTime = System.currentTimeMillis();
        Map<String, Boolean> map = readRelayBoardCardData(master);
        int value = map.getOrDefault(String.valueOf(deviceChannel), Boolean.FALSE) ? 1 : 0;
        long eTime = System.currentTimeMillis();
        log.info("读取继电器板卡, 通道:{}, 阻值：{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
        return value;
    }

    /**
     * 读取3块模拟量采集板卡(180个通道)的数据
     */
    public AcquisitionEntity readAllAcquisitionBoardCard(ModbusMaster master) {
        Map<Integer, List<Float>> dataReadMap = new HashMap<>();
        int totalBoardNumbers = 3;
        for (int boardNumber = 1; boardNumber <= totalBoardNumbers; boardNumber++) {
            try {
                dataReadMap.put(boardNumber, readSingleAcquisitionBoardCard(master, boardNumber));
            } catch (BoardCardTransportException e) {
                log.warn("第{}块板卡异常:{}", boardNumber, e.getMessage());
                dataReadMap.put(boardNumber, new ArrayList<>());
            }
        }
        AcquisitionEntity acquisitionEntity = new AcquisitionEntity();
        acquisitionEntity.setVoltageAcquisitionOne(dataReadMap.get(1));
        acquisitionEntity.setVoltageAcquisitionTwo(dataReadMap.get(2));
        acquisitionEntity.setVoltageAcquisitionThree(dataReadMap.get(3));
        log.info("读取全部电压采集板卡返回结果:{}", acquisitionEntity);
        return acquisitionEntity;
    }

    public List<Float> readSingleAcquisitionBoardCard(ModbusMaster master, int slaveId) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int startAddress = 256;
        int len = 120;
        List<Float> dataList;
        byte[] tempBytes = {};
        byte[] resultDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        tempBytes = mergeArrays(tempBytes, resultDataBytes);
        dataList = parseByteArrayToFloat(tempBytes);
        long eTime = System.currentTimeMillis();
        log.info("读取电压采集板卡,板卡号:{},执行时长：{} 毫秒.", slaveId, (eTime - sTime));
        return dataList;
    }

    /**
     * 读取模拟量采集板卡规则： 板卡号-通道号
     */
    public float readAcquisitionBoardCardByChannel(ModbusMaster master, int slaveId, int channel) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int startAddress = 256 + (channel - 1) * 2;
        int len = 2;
        byte[] tempBytes = {};
        byte[] resultDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        tempBytes = mergeArrays(tempBytes, resultDataBytes);
        byte[] subBytes = DataUtils.subByte(tempBytes, 0, 4);
        float value = bytesToFloat(subBytes);
        long eTime = System.currentTimeMillis();
        log.info("读取电压采集板卡,板卡号:{}, 通道:{}, 执行时长：{} 毫秒.", slaveId, channel, (eTime - sTime));
        return value;
    }

    /**
     * 三态输出&PWM输出板卡&PWM输入板卡是同一块板卡
     * 读取1块PWM输出板卡(10个通道)的数据
     */
    public Map<String, PWMEntity> readPWMOutputBoardCardData(ModbusMaster master) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 5;
        int len = 40;
        Map<String, PWMEntity> dataMap = new HashMap<>();
        byte[] pwmOutputDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (pwmOutputDataBytes != null) {
            List<Float> pwmOutputDataList = OperateRegister.parseByteArrayToFloat(pwmOutputDataBytes);
            dataMap = OperateRegister.parsePWMControlList(pwmOutputDataList);
        }
        long eTime = System.currentTimeMillis();
        log.info("读取PWM输出板卡全部通道, 执行时长：{} 毫秒, 返回结果:{}", eTime - sTime, dataMap);
        return dataMap;
    }


    public PWMEntity readPWMOutputBoardCardByChannel(ModbusMaster master, int deviceChannel) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 5 + (deviceChannel - 1) * 4;
        int len = 4;
        PWMEntity pwmEntity = null;
        byte[] pwmOutputDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (pwmOutputDataBytes != null) {
            List<Float> pwmOutputDataList = OperateRegister.parseByteArrayToFloat(pwmOutputDataBytes);
            pwmEntity = new PWMEntity(pwmOutputDataList.get(0), pwmOutputDataList.get(1));
        }
        long eTime = System.currentTimeMillis();
        log.info("读取PWM输出板卡通道:{}, 执行时长：{} 毫秒, 返回结果:{}", deviceChannel, eTime - sTime, pwmEntity);
        return pwmEntity;
    }


    /**
     * 读取1块PWM输入板卡(10个通道)的数据
     */
    public Map<String, PWMEntity> readPWMInputBoardCardData(ModbusMaster master) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 256;
        int len = 40;
        Map<String, PWMEntity> dataMap = new HashMap<>();
        byte[] pwmInputDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (pwmInputDataBytes != null) {
            List<Float> pwmInputDataList = OperateRegister.parseByteArrayToFloat(pwmInputDataBytes);
            dataMap = OperateRegister.parsePWMControlList(pwmInputDataList);
        }
        long eTime = System.currentTimeMillis();
        log.info("读取PWM输入板卡全部通道, 执行时长：{} 毫秒, 返回结果:{}", eTime - sTime, dataMap);
        return dataMap;
    }

    /**
     * 通过通道去读取PWM输入板卡数据
     */
    public PWMEntity readPWMInputBoardCardDataByChannel(ModbusMaster master, int deviceChannel) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 256 + (deviceChannel - 1) * 4;
        int len = 4;
        byte[] pwmInputDataBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (pwmInputDataBytes != null) {
            List<Float> pwmInputDataList = OperateRegister.parseByteArrayToFloat(pwmInputDataBytes);
            Float frequencyValue = pwmInputDataList.get(0);
            Float dutyCycleValue = pwmInputDataList.get(1);
            return new PWMEntity(frequencyValue, dutyCycleValue);
        }
        long eTime = System.currentTimeMillis();
        log.info("读取PWM输入板卡, 通道:{}, 执行时长：{} 毫秒.", deviceChannel, (eTime - sTime));
        return null;
    }


    /**
     * 读取1块三态输出板卡(64个通道)的数据
     */
    public List<Integer> readTriStateOutputBoardCardData(ModbusMaster master) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 45;
        int len = 64;
        List<Integer> dataList = new ArrayList<>();
        byte[] triStateOutputBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (triStateOutputBytes != null) {
            int count = triStateOutputBytes.length / 2;
            for (int i = 0; i < count; i++) {
                byte[] bytes = DataUtils.subByte(triStateOutputBytes, i * 2, 2);
                int value = DataUtils.byteToInt16(bytes);
                dataList.add(value);
            }
        }
        long eTime = System.currentTimeMillis();
        log.info("读取三态输出板卡全部通道, 执行时长：{} 毫秒, 返回结果:{}", eTime - sTime, ArrayUtil.toString(dataList));
        return dataList;
    }


    public int readTriStateOutputBoardCardByChannel(ModbusMaster master, int deviceChannel) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 44 + deviceChannel;
        int len = 1;
        int value = 0;
        byte[] triStateOutputBytes = readHoldingRegisters(master, slaveId, startAddress, len);
        if (triStateOutputBytes != null) {
            byte[] bytes = DataUtils.subByte(triStateOutputBytes, 0, 2);
            value = DataUtils.byteToInt16(bytes);
        }
        long eTime = System.currentTimeMillis();
        log.info("写入电阻板卡, 通道:{}, 值:{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
        return value;
    }


    /**
     * 写入某一块电阻板卡(某一个通道)的数据，用channel值来计算要写入slaveId和startAddress
     */
    public boolean writeResistanceBoardCardData(ModbusMaster master, int deviceChannel, int value) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int channelQuantity = 4;
        int boardQuantity = 11;
        int boardNumber = (deviceChannel - 1) / channelQuantity + 1;
        int channelNumber = (deviceChannel - 1) % channelQuantity + 1;
        if (boardNumber > boardQuantity) {
            log.info("No such resistance board card:{}", boardNumber);
            return false;
        }
        //前面是从站地址，后面是通道  板卡地址从6开始 6~16
        int slaveId = boardNumber + 5;
        int startAddress = 5 + 2 * (channelNumber - 1);   //通道一的寄存器地址是5，通道二是7，通道三是9，通道四是11
        short[] shorts = int32ToShorts(value);
        boolean isWrite = writeHoldingRegisters(master, slaveId, startAddress, shorts);
        long eTime = System.currentTimeMillis();
        log.info("写入电阻板卡, 通道:{}, 电阻值:{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
        return isWrite;
    }


    /**
     * 写入继电器板卡(8个通道)的数据，全部一起写入
     */
    public boolean writeRelayBoardCardData(ModbusMaster master, int deviceChannel, int value) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 4;
        int startAddress = 5;
        byte[] resultDataBytes = readRelayBoardCardBytes(master);
        try {
            String resultDataStr = convertBigEndianTo16Bit(resultDataBytes); //测试继电器板卡返回
            StringBuilder sb = new StringBuilder(resultDataStr);
            int replaceIndex = 16 - deviceChannel;
            char replaceChar = (char) (value + '0');
            sb.setCharAt(replaceIndex, replaceChar);
            String bitStringNew = sb.toString();
            short shortValue = bitStringToShort(bitStringNew);
            short[] shorts = new short[]{shortValue};
            writeHoldingRegisters(master, slaveId, startAddress, shorts);
            long eTime = System.currentTimeMillis();
            log.info("写入继电器板卡, 通道:{}, 值:{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
            return true;
        } catch (IllegalArgumentException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 写入三态输出板卡(某一个通道)的数据，用chanel值来计算要写入startAddress
     * value包含通道的电压值，值为0表示浮空；值为1表示输出高电平；值为2表示输出低电平；
     */
    public boolean writeTriStateOutputBoardCardData(ModbusMaster master, int deviceChannel, int value) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 44 + deviceChannel;
        short[] shorts = int16ToShorts(value);
        writeHoldingRegisters(master, slaveId, startAddress, shorts);
        long eTime = System.currentTimeMillis();
        log.info("写入三态输出板卡, 通道:{}, 值:{}, 执行时长：{} 毫秒.", deviceChannel, value, (eTime - sTime));
        return true;
    }


    /**
     * 写入PWM输出板卡(某一个通道)的数据，用chanel值来计算要写入startAddress, values包含某个通道的占空比和频率
     */
    public boolean writePWMOutputBoardCardData(ModbusMaster master, int deviceChannel, PWMEntity pwmEntity) throws BoardCardTransportException {
        if (!master.isConnected()) {
            throw new BoardCardTransportException("Modbus状态未连接");
        }
        long sTime = System.currentTimeMillis();
        int slaveId = 5;
        int startAddress = 5 + 4 * (deviceChannel - 1);
        float[] floats = new float[2];
        floats[0] = pwmEntity.getFrequency();
        floats[1] = pwmEntity.getDutyCycle();
        byte[] bytes = DataUtils.floatToBytes(floats, true);
        short[] shorts = convertToShorts(bytes);
        writeHoldingRegisters(master, slaveId, startAddress, shorts);
        long eTime = System.currentTimeMillis();
        log.info("写入PWM输出板卡, 通道:{}, 值:{}, 执行时长：{} 毫秒.", deviceChannel, pwmEntity, (eTime - sTime));
        return true;
    }

    /**
     * 解析数据，保留小数点后两位
     */
    public static List<Float> parseByteArrayToFloat(byte[] bytes) {
        List<Float> dataList = new ArrayList<>();
        int count = bytes.length / 4;
        for (int i = 0; i < count; i++) {
            byte[] subBytes = DataUtils.subByte(bytes, i * 4, 4);
            float value = DataUtils.bytesToFloat(subBytes);  //测试模拟量采集板卡和PWM输出板卡返回和PWM输入板卡
            dataList.add(value);
        }
        return dataList;
    }

    public static Map<String, PWMEntity> parsePWMControlList(List<Float> pwmDataList) {
        Map<String, PWMEntity> dataMap = new HashMap<>();
        int index = 0;
        int chanel = 1;
        while (index < pwmDataList.size()) {
            Float frequencyValue = pwmDataList.get(index);
            Float dutyCycleValue = pwmDataList.get(index + 1);
            PWMEntity pwmEntity = new PWMEntity(frequencyValue, dutyCycleValue);
            dataMap.put(String.valueOf(chanel), pwmEntity);
            chanel++;
            index += 2;
        }
        return dataMap;
    }


    /**
     * 读保持寄存器上的内容
     *
     * @param master  主站
     * @param slaveId 从站地址
     * @param start   起始地址的偏移量
     * @param len     待读寄存器的个数
     */

    public static byte[] readHoldingRegisters(ModbusMaster master, int slaveId, int start, int len) throws BoardCardTransportException {
        log.info("读取寄存器 slaveId:{} , start:{}, len:{}, master:{}", slaveId, start, len, master.hashCode());
        try {
            ReadHoldingRegistersRequest request = new ReadHoldingRegistersRequest(slaveId, start, len);
            ReadHoldingRegistersResponse response = (ReadHoldingRegistersResponse) master.send(request);
            if (response.isException()) {
                throw new BoardCardTransportException(response.getExceptionMessage());
            }
            log.info("读取寄存器成功!");
            return response.getData();
        } catch (ModbusTransportException e) {
            throw new BoardCardTransportException(e.getMessage());
        }
    }


    /**
     * 写数据到保持寄存器
     *
     * @param master  主站
     * @param slaveId 从站地址
     * @param start   起始地址的偏移量
     * @param values  待写数据
     */

    public static boolean writeHoldingRegisters(ModbusMaster master, int slaveId, int start, short[] values) throws BoardCardTransportException {
        try {
            log.info("写入寄存器 slaveId:{} , start:{}, values:{}, master:{}", slaveId, start, values, master.hashCode());
            WriteRegistersRequest request = new WriteRegistersRequest(slaveId, start, values);
            WriteRegistersResponse response = (WriteRegistersResponse) master.send(request);
            if (response.isException()) {
                throw new BoardCardTransportException(response.getExceptionMessage());
            } else {
                log.info("写入寄存器成功!");
                return true;
            }
        } catch (ModbusTransportException e) {
            throw new BoardCardTransportException(e.getMessage());
        }
    }

    private static final int baudRate = 115200;
    private static final int dataBits = 8;
    private static final int stopBits = 2;
    private static final int parity = 0;
    private static final int flowControlIn = 0;
    private static final int flowControlOut = 0;
    private static ModbusMaster master;

    public static void main(String[] args) throws DeviceOpenException {
        String commPortId = "COM8";
        log.info("open testBox Port: {}, baudRate:{}", commPortId, baudRate);
        SerialPortWrapper serialPortWrapper = new SerialPortWrapperImpl(
                commPortId, baudRate, dataBits, stopBits, parity, flowControlIn, flowControlOut);
        ModbusFactory modbusFactory = new ModbusFactory();
        master = modbusFactory.createRtuMaster(serialPortWrapper);
        try {
            master.init();
            long sTime = System.currentTimeMillis();
            int startAddress = 256;
            int len = 120;
            List<Float> dataList;
            byte[] tempBytes = {};
            byte[] tempBytes2 = {};
            byte[] tempBytes3 = {};

            byte[] resultDataBytes = readHoldingRegisters(master, 1, startAddress, len);
            tempBytes = mergeArrays(tempBytes, resultDataBytes);
            dataList = parseByteArrayToFloat(tempBytes);
            long eTime = System.currentTimeMillis();
            log.info("读取电压采集板卡,板卡号:{},执行时长：{} 毫秒.", 1, (eTime - sTime));
            Thread.sleep(1);
            byte[] resultDataBytes2 = readHoldingRegisters(master, 3, startAddress, len);
            tempBytes2 = mergeArrays(tempBytes2, resultDataBytes2);
            List<Float> dataList2 = parseByteArrayToFloat(tempBytes2);
//                Thread.sleep(10000);
            byte[] resultDataBytes3 = readHoldingRegisters(master, 2, startAddress, len);
            tempBytes3 = mergeArrays(tempBytes3, resultDataBytes3);
            List<Float> dataList3 = parseByteArrayToFloat(tempBytes3);
        } catch (ModbusInitException e) {
            throw new DeviceOpenException(e);
        } catch (BoardCardTransportException e) {
            log.error(e.getMessage(), e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            if (master != null) {
                master.destroy();
            }
        }
    }
}
