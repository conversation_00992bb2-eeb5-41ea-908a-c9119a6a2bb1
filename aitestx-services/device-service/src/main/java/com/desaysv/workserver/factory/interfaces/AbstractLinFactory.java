package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

public interface AbstractLinFactory extends AbstractDeviceCreator {
    Device createVectorLinDevice(DeviceRegisterForm deviceRegisterForm);

    Device createTC1016Device(DeviceRegisterForm deviceRegisterForm);

    Device createTC1026Device(DeviceRegisterForm deviceRegisterForm);

    Device createToZlg200ULinDevice(DeviceRegisterForm deviceRegisterForm);

    Device createZlgLinFdDtu400UEwgrDevice(DeviceRegisterForm deviceRegisterForm);
}
