package com.desaysv.workserver.devices.signal_generator;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class SignalGeneratorDevice extends Device {
    public SignalGeneratorDevice() {
        this(new DeviceOperationParameter());
    }

    public SignalGeneratorDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SIGNAL_GENERATOR;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.SignalGenerator.TK_AFG_1022;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return false;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return false;
    }
}
