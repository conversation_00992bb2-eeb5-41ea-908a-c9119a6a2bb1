package com.desaysv.workserver.devices;

import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * 插拔设备
 */
public abstract class PlugDevice extends PortDevice {

    public PlugDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    /**
     * 插入设备
     *
     * @return 是否插入设备成功
     */
    protected abstract boolean plugIn();

    /**
     * 拔出设备
     *
     * @return 是否拔出设备成功
     */
    protected abstract boolean plugOut();

}
