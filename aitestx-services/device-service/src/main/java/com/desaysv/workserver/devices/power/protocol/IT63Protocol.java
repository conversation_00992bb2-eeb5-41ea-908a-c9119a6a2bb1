package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.devices.power.base.PowerDevice;

/**
 * 适用于IT6322A/IT6332A/IT6333A/IT6322B/IT6332B/IT6333B
 */
public class IT63Protocol extends PowerSCPIProtocol {

    public IT63Protocol(PowerDevice device) {
        super(device);
    }

    @Override
    public boolean selectChannel(Integer channel) {
        if (channel != null) {
            return sendCommand(ScpiCommandConstants.selectChannel(channel, true));
        }
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand(isOutput ? ScpiCommandConstants.CHANNEL_OUTPUT_ON : ScpiCommandConstants.CHANNEL_OUTPUT_OFF);
    }

}
