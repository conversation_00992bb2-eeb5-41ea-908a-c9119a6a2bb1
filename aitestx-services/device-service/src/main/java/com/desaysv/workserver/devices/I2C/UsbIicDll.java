package com.desaysv.workserver.devices.I2C;

import com.sun.jna.Library;
import com.sun.jna.Native;

/**
 * @projectName: aitestxserver
 * @package: com.desaysv.workserver.devices.I2C
 * @className: UsbIICDLL
 * @author: <PERSON><PERSON><PERSON>
 * @description: IIC底层库
 * @date: 2024/4/10 8:53
 * @version: 1.0
 */
public class UsbIicDll {


    public interface Usb2UartSpiIicDll extends Library {
        String filePath = "\\library\\iic\\USB2UARTSPIIICDLL.dll";
        Usb2UartSpiIicDll INSTANCE = Native.loadLibrary(filePath, Usb2UartSpiIicDll.class);

        //打开USB
        int OpenUsb(int UsbIndex);

        //关闭USB
        int CloseUsb(int UsbIndex);

        //读取转接板第一次上电标志函数
        int IsFirstPowerUp(int UsbIndex);

        //设置I2C 主模式参数
        int ConfigIICParam(int rate, int clkSLevel, int UsbIndex);

        //I2C 主模式发送接收数据
        int IICSendAndRcvData(byte strartBit, byte stopBit,
                              byte[] sendBuf, byte[] rcvBuf,
                              int slen, int rlen,
                              int UsbIndex);

        //I2C 主模式发送数据
        int IICSendData(byte strartBit, byte stopBit,
                        byte[] sendBuf, int slen,
                        int UsbIndex);

        //I2C 主模式接收数据
        int IICRcvData(byte stopBit, byte[] rcvBuf,
                       int rlen, int UsbIndex);

        //I2C 主模式检测从机地址
        int IICCheckSlaveAddr(byte addrMod, int addr,
                              int UsbIndex);

        //I2C 主模式寄存器发送
        int IICRegisterSend(byte addrMod, int addr,
                            byte[] regBuf, byte[] sendBuf,
                            byte reglen, int slen,
                            int UsbIndex);

        //I2C 主模式寄存器读取
        int IICRegisterRead(byte addrMod, int addr,
                            byte[] regBuf, byte[] rcvBuf,
                            byte reglen, int rlen,
                            int UsbIndex);

        //I2C 主模式直接发送
        int IICDirectSend(byte addrMod, int addr,
                          byte[] sendBuf, int slen,
                          int UsbIndex);

        //I2C 主模式直接读取
        int IICDirectRead(byte addrMod, int addr,
                          byte[] rcvBuf, int rlen,
                          int UsbIndex);

        //设置I2C 从模式参数
        int ConfigIICParamSlave(byte addrMod, int addr,
                                int UsbIndex);

        //I2C 从模式预装数据
        int IICSlavePreloadData(byte[] pBuf, int len, int UsbIndex);

        //I2C 从模式读取数据
        int IICSlaveRcvData(byte[] rcvBuf, int len,
                            int UsbIndex);

        //ADC 读取
        int GetADCVal(byte ch, int UsbIndex);

        //IO 口读写
        int IOSetAndRead(int IONum, int IODir,
                         int IOBit, int UsbIndex);

        //PWM 输出
        int PWMOut(int prescaler, int period,
                   int pulse, int PulseNum,
                   int UsbIndex);

        //关闭PWM 输出
        int PWMClose(int UsbIndex);

        int ConfigSPIParam(int rate, int fistBit, int subMode, int UsbIndex);

        int SPISendData(int startCS, int endCS, byte[] sendBuf, int len, int UsbIndex);


    }

    public static void main(String[] args) {
        int usbIndex = 0;
        byte[] sendBuf = new byte[5];
        sendBuf[0] = 'a';
        sendBuf[1] = 'b';
        sendBuf[2] = 'c';
        sendBuf[3] = 'd';
        sendBuf[4] = 'e';
        System.out.printf("OpenUsb\r\n");
        Usb2UartSpiIicDll.INSTANCE.OpenUsb(usbIndex);
        System.out.printf("ConfigSPIParam\r\n");
        Usb2UartSpiIicDll.INSTANCE.ConfigSPIParam(0, 0, 0, usbIndex);
        System.out.printf("SPISendData\r\n");
        Usb2UartSpiIicDll.INSTANCE.SPISendData(0, 1, sendBuf, 5, usbIndex);
        Usb2UartSpiIicDll.INSTANCE.CloseUsb(usbIndex);
        System.out.printf("CloseUsb\r\n");
    }
}
