package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Callback;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.WinBase;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinNT;
import com.sun.jna.win32.StdCallLibrary;

/**
 * 文档来源CAN and General BLF Logging Format Version 1.52 of 2022-01-13
 * 在CANoe16版本下的binlog.dll /Doc/LoggingFormat_BLF/下的文档编写
 */
public interface binlog_jna extends StdCallLibrary {

    String filePath = "binlog.dll";

    binlog_jna instanceDll = Native.load(filePath, binlog_jna.class);

    /**
     * 使用此函数创建一个带有所需访问权限的BL文件句柄。
     *
     * @param lpFileName      文件名，指定要创建或打开的文件的名称。
     * @param dwDesiredAccess 指定对文件的访问类型。应用程序可以获得读取访问权限或写入访问权限。可以是GENERIC_READ或GENERIC_WRITE。
     * @return 如果函数执行成功，则返回一个指向指定文件的打开句柄。如果函数执行失败，则返回INVALID_HANDLE_VALUE。
     */
    WinNT.HANDLE BLCreateFile(String lpFileName, WinDef.DWORD dwDesiredAccess);

    /**
     * 使用此函数关闭已经通过BLCreateFile打开的BL文件句柄。
     *
     * @param hFile BLCreateFile返回的文件句柄。
     * @return 如果函数执行成功，则返回值为true。如果函数执行失败，则返回值为false。
     */
    boolean BLCloseHandle(WinNT.HANDLE hFile);

    /**
     * 使用此函数将一个BL对象写入文件中。
     *
     * @param hFile BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_WRITE访问权限。
     * @param pBase 指向包含要写入文件的数据的BL对象结构的指针。
     * @return 如果函数执行成功，则返回值为true。如果函数执行失败，则返回值为false。
     */
    boolean BLWriteObject(WinNT.HANDLE hFile, Pointer pBase);


    /**
     * 使用此函数读取BL对象的基本头部信息。
     *
     * @param hFile 通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @param pBase 指向接收对象头部描述的BL对象结构体的指针。
     * @return 如果函数执行成功，则返回值为非零。如果函数执行失败，则返回值为零。
     */
    boolean BLPeekObject(WinNT.HANDLE hFile, VBLObjectHeaderBase pBase);


    /**
     * 使用此函数跳过一个BL对象。
     *
     * @param hFile 通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @return 如果函数执行成功，则返回值为非零。如果函数执行失败，则返回值为零。
     */
    boolean BLSkipObject(WinNT.HANDLE hFile, Pointer pBase);

    /**
     * 使用此函数读取一个BL对象。
     *
     * @param hFile        通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @param pBase        指向描述要读取的对象的BL对象结构的指针。
     * @param expectedSize BL对象结构的大小，由指针pBase提供。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLReadObjectSecure(WinNT.HANDLE hFile, Pointer pBase, int expectedSize);

    /**
     * 使用此函数释放为先前读取的BL对象分配的内存。虽然这只对动态大小对象（如环境变量）是必需的，
     * 但对于固定大小对象（如CAN消息）也没有害处。
     *
     * @param hFile 通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @param pBase 指向描述要释放的对象的BL对象结构的指针。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLFreeObject(WinNT.HANDLE hFile, Pointer pBase);


    /**
     * 使用此函数指定写入文件的应用程序。
     *
     * @param hFile    通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_WRITE访问权限。
     * @param appID    应用程序标识符。
     * @param appMajor 应用程序主要版本号。
     * @param appMinor 应用程序次要版本号。
     * @param appBuild 应用程序构建版本号。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLSetApplication(WinNT.HANDLE hFile, WinDef.BYTE appID, WinDef.BYTE appMajor, WinDef.BYTE appMinor, WinDef.BYTE appBuild);

    /**
     * 使用此函数设置写入选项。
     *
     * @param hFile         通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_WRITE访问权限。
     * @param dwCompression 要在写入过程中使用的压缩级别。有效值范围从0（无压缩）到10（最大压缩）。
     * @param dwReserved    保留字段，必须为零。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLSetWriteOptions(WinNT.HANDLE hFile, WinDef.DWORD dwCompression, WinDef.DWORD dwReserved);

    /**
     * 使用此函数检索文件统计信息。
     *
     * @param hFile       通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @param pStatistics 指向文件统计信息结构体的指针。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLGetFileStatistics(WinNT.HANDLE hFile, Pointer pStatistics);

    boolean BLGetFileStatisticsEx(WinNT.HANDLE hFile, Pointer pStatistics);

    /**
     * 使用此函数刷新文件缓冲区。
     *
     * @param hFile   通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_WRITE访问权限。
     * @param dwFlags 指示如何刷新的标志。有效值包括：BL_FLUSH_STREAM - 刷新所有内部流，BL_FLUSH_FILE - 刷新文件及其组合。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLFlushFileBuffers(WinNT.HANDLE hFile, WinDef.DWORD dwFlags);


    /**
     * 使用此函数在BLF文件中向前查找到具有特定时间戳的第一个对象。
     *
     * @param hFile             通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_READ访问权限。
     * @param timeStamp         要查找的时间戳值。
     * @param arg               传递给pProgressCallback回调函数的参数。可以用作C样式binlog接口和C++之间的桥梁（通过传递类的this指针）。
     * @param pProgressCallback 回调函数，用于传递arg指针和进度值（0到1.0之间）。
     * @param callbackRate      回调函数被调用的速率（毫秒为单位）。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLSeekTime(WinNT.HANDLE hFile, WinDef.ULONGLONG timeStamp, Pointer arg, ProgressCallback pProgressCallback, WinDef.WORD callbackRate);


    /**
     * 回调函数接口，用于传递进度信息和参数。
     */
    interface ProgressCallback extends Callback {
        boolean invoke(Pointer arg, float progress);
    }

    /**
     * 使用此函数设置测量开始时间。
     *
     * @param hFile       通过BLCreateFile返回的文件句柄。文件句柄必须已经具有对文件的GENERIC_WRITE访问权限。
     * @param lpStartTime 指向Windows系统时间结构的指针。
     * @return 如果函数执行成功，则返回值为非零；如果函数执行失败，则返回值为零。
     */
    boolean BLSetMeasurementStartTime(WinNT.HANDLE hFile, WinBase.SYSTEMTIME lpStartTime);

}
