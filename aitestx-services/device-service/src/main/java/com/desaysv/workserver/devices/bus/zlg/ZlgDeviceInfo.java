package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.utils.ReflectUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.Data;

@Data
public class ZlgDeviceInfo {

    public String  hwVersion; // 硬件版本号，16进制，比如0x0100表示V1.00。
    public String fwVersion; // 固件版本号，16进制。
    public String drVersion; // 驱动程序版本号，16进制。
    public String inVersion; // 接口库版本号，16进制。
    public short irqNum;    // 板卡所使用的中断号。
    public byte canNum;     // 表示有几路通道。
    public String strSerialNum; // 此板卡的序列号，比如” USBCAN V1.00”（注意：包括字符串结束符’\0’）。
    public String strHwType; // 硬件类型

    private static String getVersion(int version) {
        if (version / 0xFF >= 9) {
            return String.format("V%02x.%02x", version / 0xFF, version & 0xFF);
        } else {
            return String.format("V%d.%02x", version / 0xFF, version & 0xFF);
        }
    }

    public static String format(ZlgCanLib.ZCAN_DEVICE_INFO deviceInfo) {
        ZlgDeviceInfo info = new ZlgDeviceInfo();
        info.setHwVersion(getVersion(deviceInfo.hwVersion));
        info.setFwVersion(getVersion(deviceInfo.fwVersion));
        info.setDrVersion(getVersion(deviceInfo.drVersion));
        info.setInVersion(getVersion(deviceInfo.inVersion));
        info.setIrqNum(deviceInfo.irqNum);
        info.setCanNum(deviceInfo.canNum);
        info.setStrSerialNum(StrUtils.bytesToString(deviceInfo.strSerialNum));
        info.setStrHwType(StrUtils.bytesToString(deviceInfo.strHwType));
        return ReflectUtils.toElegantString(info);
    }
}
