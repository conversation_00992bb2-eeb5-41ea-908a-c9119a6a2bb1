package com.desaysv.workserver.devices.camera.config;

import lombok.Builder;
import lombok.Data;

/**
 * @description:
 * @date: 2024/11/25 11:45
 */
@Data
@Builder
public class BackTrackConfig {
    private boolean backtrack;
    private int backtrackTime;
    private int backResolution;
    private String backSavePath;

    public BackTrackConfig() {
        this.backtrack = false;
        this.backtrackTime = 0;
        this.backResolution = 0;
        this.backSavePath = "";
    }

    public BackTrackConfig(boolean backtrack, int backtrackTime, int backResolution, String backSavePath) {
        this.backtrack = backtrack;
        this.backtrackTime = backtrackTime;
        this.backResolution = backResolution;
        this.backSavePath = backSavePath;
    }
}
