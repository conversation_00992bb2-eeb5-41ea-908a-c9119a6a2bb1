package com.desaysv.workserver.devices.serial;

import com.desaysv.workserver.entity.BaseMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class SerialReceiveMessage extends BaseMessage {

    private long timestamp;

    private boolean send;

    private byte[] data;

    private String deviceAliasName;

}
