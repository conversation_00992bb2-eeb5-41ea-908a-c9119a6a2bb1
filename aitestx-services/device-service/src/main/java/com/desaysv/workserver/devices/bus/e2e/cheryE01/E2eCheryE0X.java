package com.desaysv.workserver.devices.bus.e2e.cheryE01;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ICI2 奇瑞E01项目 E2E
 * <p>
 * 采用的是Profile1的E2E算法
 *
 * <AUTHOR>
 */
@Slf4j
public class E2eCheryE0X implements CanMessageEventListener {

    private static final Map<Integer, Integer> counters = new ConcurrentHashMap<>();
    // 初始计数器值
    private int initialCounterValue = 0;
    private final CanConfig canConfig;
    private final int channel;
    private static final int COUNTER_BYTE = 1;
    private static final int COUNTER_BIT = 0;
    private static final int CHECKSUM_BYTE = 0;
    private final int MIN_COUNTER_LENGTH = 0;
    private final int MAX_COUNTER_LENGTH = 14;
    // 多项式值
    private final byte CRC_POLY = 0x1D;
    // 初始值
    private final byte CRC_INIT = 0x00;
    // 异或值
    private final byte CRC_XOR_VALUE = 0x00;


    public E2eCheryE0X(CanConfig canConfig, int channel) {
        this.canConfig = canConfig;
        this.channel = channel;
    }

    public byte[] e2e_calculate_e0x(int canId, byte[] canData) {
        try {
            // 获取 E2eCheryE0XTableManager 的单例实例
            E2eCheryE0XTableManager tableManager = E2eCheryE0XTableManager.getInstance();
            if (tableManager == null) {
                log.info("E2eCheryE0XTableManager instance is null");
                return canData;
            }
            // 获取 e2eMap
            Map<Integer, Integer> e2eMap = tableManager.e2eMap;
            if (e2eMap == null) {
                log.info("e2eMap is null");
                return canData;
            }

            // 获取 dataId
            Integer dataId = e2eMap.get(canId);
            if (dataId != null && dataId != 0) {
                if (!counters.containsKey(canId)) {
                    counters.put(canId, initialCounterValue);
                }
                canData = e2eProfile1(canId, canData, dataId);
            }
        } catch (Exception e) {
            log.info("E2E Calculation Exception: {}", e.getMessage());
        }

        return canData;
    }

    private byte[] e2eProfile1(int canId, byte[] data, int dataID) {
        try {
            data[COUNTER_BYTE] = e2eCounter(canId, data[COUNTER_BYTE], COUNTER_BIT);
            byte[] dataId = new byte[2];
            dataId[0] = (byte) (dataID & 0x00ff);
            dataId[1] = (byte) ((dataID & 0xff00) >> 8);
            data[CHECKSUM_BYTE] = e2eCRC8(data, dataId, 1, 7);
        } catch (Exception e) {
            log.info("e2eProfile1Exception:", e);
        }
        return data;
    }

    /**
     * 计算counter 值
     *
     * @param data
     * @return
     */
    public byte e2eCounter(int canId, byte data, int bitPos) {
        int counterValue = counters.get(canId);
        counterValue++;
        if (counterValue >= MAX_COUNTER_LENGTH) {
            counterValue = MIN_COUNTER_LENGTH;
        }
        counters.put(canId, counterValue);
        data = (byte) ((data & 0xF0) | (counterValue & 0x0F));
        return data;
    }


    /**
     * 直接计算CRC8
     * CRC8校验
     *
     * @return
     */
    public byte e2eCRC8(byte[] data, byte[] dataId, int startPos, int endPos) {
        int crc8 = CRC_INIT;
        // 计算dataId的CRC8
        for (int i = 0; i <= dataId.length - 1; i++) {
            crc8 ^= dataId[i];
            for (int j = 0; j < 8; j++) {
                if ((crc8 & 0x80) == 0x80) {
                    crc8 <<= 1;
                    crc8 ^= CRC_POLY;
                } else {
                    crc8 <<= 1;
                }
            }
        }
        // 计算data的CRC8
        for (int i = startPos; i <= endPos; i++) {
            crc8 ^= data[i];
            for (int j = 0; j < 8; j++) {
                if ((crc8 & 0x80) == 0x80) {
                    crc8 <<= 1;
                    crc8 ^= CRC_POLY;
                } else {
                    crc8 <<= 1;
                }
            }
        }
        // 取反
        return (byte) (crc8 ^ CRC_XOR_VALUE);
    }

    @Override
    public void onMessageSend(CanMessage message) {
        if (message.getE2eType() == null) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            if ("CheryE01".equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
        }
        if (!message.isE2eEnabled()) {
            return;
        }
        if (message.getE2eType() != null && message.getE2eType().equalsIgnoreCase("CheryE01")) {
            message.setData(e2e_calculate_e0x(message.getArbitrationId(), message.getData()));
        }
    }

    public static void main(String[] args) {
        // 模拟的 CAN 配置
        CanConfig canConfig = new CanConfig();
        // 假设 CAN 配置中已经配置了 E2E 类型为 "CheryE01"
        canConfig.getE2eTypes().put(1, "CheryE01");

        // 创建 E2eCheryE0X 实例
        E2eCheryE0X e2eCheryE0X = new E2eCheryE0X(canConfig, 1);

        // 测试数据
        int canId = 0x49C; // 示例 CAN ID
        byte[] canData = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId1 = 0x49C; // 示例 CAN ID
        byte[] canData1 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId2 = 0x49C; // 示例 CAN ID
        byte[] canData2 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId4 = 0x49C; // 示例 CAN ID
        byte[] canData4 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId5 = 0x49C; // 示例 CAN ID
        byte[] canData5 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId6 = 0x49C; // 示例 CAN ID
        byte[] canData6 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId7 = 0x49C; // 示例 CAN ID
        byte[] canData7 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 测试数据
        int canId8 = 0x49C; // 示例 CAN ID
        byte[] canData8 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId9 = 0x314; // 示例 CAN ID
        byte[] canData9 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId10 = 0x49C; // 示例 CAN ID
        byte[] canData10 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId11 = 0x314; // 示例 CAN ID
        byte[] canData11 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId12 = 0x49C; // 示例 CAN ID
        byte[] canData12 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId13 = 0x49C; // 示例 CAN ID
        byte[] canData13 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId3 = 0x49C; // 示例 CAN ID
        byte[] canData3 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId14 = 0x49C; // 示例 CAN ID
        byte[] canData14 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId15 = 0x49C; // 示例 CAN ID
        byte[] canData15 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId16 = 0x49C; // 示例 CAN ID
        byte[] canData16 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据
        // 测试数据
        int canId17 = 0x49C; // 示例 CAN ID
        byte[] canData17 = new byte[]{0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}; // 示例 CAN 数据

        // 调用 e2e_calculate_e0x 方法
        byte[] processedData = e2eCheryE0X.e2e_calculate_e0x(canId, canData);
        byte[] processedData1 = e2eCheryE0X.e2e_calculate_e0x(canId1, canData1);
        byte[] processedData2 = e2eCheryE0X.e2e_calculate_e0x(canId2, canData2);
        byte[] processedData3 = e2eCheryE0X.e2e_calculate_e0x(canId3, canData3);
        byte[] processedData4 = e2eCheryE0X.e2e_calculate_e0x(canId4, canData4);
        byte[] processedData5 = e2eCheryE0X.e2e_calculate_e0x(canId5, canData5);
        byte[] processedData6 = e2eCheryE0X.e2e_calculate_e0x(canId6, canData6);
        byte[] processedData7 = e2eCheryE0X.e2e_calculate_e0x(canId7, canData7);
        byte[] processedData8 = e2eCheryE0X.e2e_calculate_e0x(canId8, canData8);
        byte[] processedData9 = e2eCheryE0X.e2e_calculate_e0x(canId9, canData9);
        byte[] processedData10 = e2eCheryE0X.e2e_calculate_e0x(canId10, canData10);
        byte[] processedData11 = e2eCheryE0X.e2e_calculate_e0x(canId11, canData11);
        byte[] processedData12 = e2eCheryE0X.e2e_calculate_e0x(canId12, canData12);
        byte[] processedData13 = e2eCheryE0X.e2e_calculate_e0x(canId13, canData13);
        byte[] processedData14 = e2eCheryE0X.e2e_calculate_e0x(canId14, canData14);
        byte[] processedData15 = e2eCheryE0X.e2e_calculate_e0x(canId15, canData15);
        byte[] processedData16 = e2eCheryE0X.e2e_calculate_e0x(canId16, canData16);
        byte[] processedData17 = e2eCheryE0X.e2e_calculate_e0x(canId17, canData17);
        System.out.println("Processed CAN Data:");
        for (byte b : processedData1) {
            System.out.printf("%02X ", b);
        }
        System.out.println();
        // 输出处理后的数据
        System.out.println("Processed CAN Data:");
        for (byte b : processedData) {
            System.out.printf("%02X ", b);
        }
        System.out.println();
        System.out.println("Processed CAN Data:");
        for (byte b : processedData3) {
            System.out.printf("%02X ", b);
        }
        System.out.println();
        System.out.println("Processed CAN Data:");
        for (byte b : processedData2) {
            System.out.printf("%02X ", b);
        }
        System.out.println();
        System.out.println("Processed CAN Data:");
        for (byte b : processedData15) {
            System.out.printf("%02X ", b);
        }
        System.out.println();
    }
}
