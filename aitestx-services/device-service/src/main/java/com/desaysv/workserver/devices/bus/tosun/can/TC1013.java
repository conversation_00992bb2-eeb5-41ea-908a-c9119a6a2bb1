package com.desaysv.workserver.devices.bus.tosun.can;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.tosun.TSCan;
import com.desaysv.workserver.entity.ActionSequenceExecutionContextInfo;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * TC1013底层接口
 */
@Component
@Lazy
@Scope("prototype")
public class TC1013 extends TSCan {
    @Autowired
    private ActionSequenceExecutionContextInfo context;

    public TC1013(){

    }
    public TC1013(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.TC1013;
    }

}
