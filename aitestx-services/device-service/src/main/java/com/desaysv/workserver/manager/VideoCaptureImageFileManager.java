package com.desaysv.workserver.manager;

import lombok.Getter;

import java.io.File;

public class VideoCaptureImageFileManager extends ImageFileManager {

    @Getter
    private File screenshotFile;


    public VideoCaptureImageFileManager(String projectName) {
        super(projectName);
    }

    public VideoCaptureImageFileManager(String projectName, String dynamicFolderName) {
        super(projectName, dynamicFolderName);
    }

    @Override
    protected String getImageFolderName() {
        return "videoCapture";
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        super.initSubPaths(dynamicFolderName);
        screenshotFile = createFolder(imageConstants.basePath, "screenshot");
    }

}
