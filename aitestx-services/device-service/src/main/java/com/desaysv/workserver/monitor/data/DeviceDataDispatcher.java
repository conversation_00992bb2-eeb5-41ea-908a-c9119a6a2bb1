package com.desaysv.workserver.monitor.data;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-14 16:05
 * @description :
 * @modified By :
 * @since : 2022-6-14
 */
//TODO：改成dataDistributor
@Slf4j
public class DeviceDataDispatcher<T> {
    public static final String DEFAULT_MONITOR_TYPE = "<def_monitor_type>";
    private final Map<MonitorAction, List<DataDistributor<T>>> dataDistributorMap = new HashMap<>();

//    private final ExecutorService executor = Executors.newCachedThreadPool();

    public void start(MonitorAction monitorAction, DataDistributor<T> dataDistributor) {
        if (monitorAction.getMonitorType() == null) {
            monitorAction.setMonitorType(DEFAULT_MONITOR_TYPE);
        }
        if (!dataDistributorMap.containsKey(monitorAction)) {
            dataDistributorMap.put(monitorAction, new ArrayList<>());
        }
        List<DataDistributor<T>> dataDistributorList = dataDistributorMap.get(monitorAction);
        if (!dataDistributorList.contains(dataDistributor)) {
            dataDistributorList.add(dataDistributor);
            log.info("start:{}", monitorAction);
            dataDistributor.start();
//            start(dataDistributor);
        }
    }

    private void start(DataDistributor<T> dataDistributor) {
        dataDistributor.start();
    }

    public void resume(MonitorAction monitorAction) {
        List<DataDistributor<T>> list = dataDistributorMap.get(monitorAction);
        if (list != null) {
            for (DataDistributor<T> dataDistributor : list) {
                dataDistributor.resume();
                log.info("resume:{}", monitorAction);
            }
        }
    }

    public void pause(MonitorAction monitorAction) {
        List<DataDistributor<T>> list = dataDistributorMap.get(monitorAction);
        if (list != null) {
            for (DataDistributor<T> dataDistributor : list) {
                dataDistributor.pause();
                log.info("pause:{}", monitorAction);
            }
        }
    }


    public void stop(MonitorAction monitorAction) {
        List<DataDistributor<T>> list = dataDistributorMap.get(monitorAction);
        if (list != null) {
            for (DataDistributor<T> dataDistributor : list) {
                dataDistributor.stop();
                log.info("stop:{}", monitorAction);
            }
        }
    }

    public void stopAll() {
        dataDistributorMap.values().forEach(list -> list.forEach(DataDistributor::stop));
    }

//    public void product(T data) {
//        product(DEFAULT_MONITOR_TYPE, data);
//    }

    public void product(String monitorType, T data) {
        for (Map.Entry<MonitorAction, List<DataDistributor<T>>> entry : dataDistributorMap.entrySet()) {
            if (entry.getKey().getMonitorType().equals(monitorType)) {
                for (DataDistributor<T> dataDistributor : entry.getValue()) {
                    dataDistributor.accept(data);
                }
            }
        }
    }

}
