package com.desaysv.workserver.devices.testbox.interfaces;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IRelaySwitchBoard {
    Logger log = LogManager.getLogger(IRelaySwitchBoard.class.getSimpleName());

    /**
     * 改变继电器状态（8个通道）
     *
     * @param deviceChannel 板卡通道
     * @param value         1：打开， 0：关闭
     */
    boolean writeRelaySwitchBoardCard(Integer deviceChannel, int value) throws BoardCardTransportException;

    boolean writeRelaySwitchAll(int value) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).OPTIONAL_CHANNEL_ON_OFF"})
    default void writeRelaySwitchBoardCard(Integer deviceChannel, String command) throws OperationFailNotification {
        writeRelaySwitchBoardCard(deviceChannel, command, "");
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANNEL_ON_OFF"})
    default void writeRelaySwitchBoardCard(Integer deviceChannel, String command, String physicalMeaning) throws OperationFailNotification {
        boolean isOpen = command.equalsIgnoreCase("ON");
        log.info("继电器板卡通道{}{}{}", deviceChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                isOpen ? "打开" : "关闭");
        try {
            boolean isWriteOk = writeRelaySwitchBoardCard(deviceChannel, isOpen ? 1 : 0);
//            log.info("继电器板卡通道{}{}{}--{}", deviceChannel,
//                    StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
//                    isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("继电器通道%s%s--%s", deviceChannel, isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
        }
    }


//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).ALL_CHANNEL_ON_OFF"})
//    default void writeRelaySwitchAllChannel(Integer deviceChannel, String command, String physicalMeaning) throws OperationFailNotification {
//        boolean isOpen = command.equalsIgnoreCase("ON");
//        log.info("继电器板卡所有通道{}", isOpen ? "打开" : "关闭");
//        try {
//            boolean isWriteOk = writeRelaySwitchAll(isOpen ? 1 : 0);
//            log.info("继电器板卡所有通道{}，{}", isOpen ? "打开" : "关闭",
//                    isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败");
//            ActionSequencesLoggerUtil.info(String.format("继电器通道%s%s--%s", deviceChannel, isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败"));
//            if (!isWriteOk) {
//                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
//            }
//        } catch (BoardCardTransportException e) {
//            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
//        }
//    }
}
