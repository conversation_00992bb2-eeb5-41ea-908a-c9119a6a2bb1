package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.IT69Protocol;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Arrays;

/**
 * IT69系列电源
 */
public class IT69xx extends DefaultPower {
    public IT69xx() {
        this(new DeviceOperationParameter());
    }

    public IT69xx(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        String commProtocol = String.valueOf(deviceOperationParameter.get("protocol"));
        commProtocol = commProtocol == null ? CommProtocol.USB : commProtocol;
        setCommProtocol(commProtocol); //TODO:setCommProtocol尝试合并到setPowerProtocol
        setPowerProtocol(new IT69Protocol(this));
    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT69xx;
    }

    public static void main(String[] args) throws DeviceOpenException, DeviceCloseException, JVisaException, DeviceReadException {
        //FIXME：测试JVISA报错乱码
        //TODO：测试JVISA 官方highlevel代码
        //TODO：测试JVISA对ASRL13::INSTR RS232的getVoltage()支持
        JVisaResourceManager resourceManager = new JVisaResourceManager();
        String[] res = resourceManager.findResources();
        System.out.println(Arrays.toString(res));

        DeviceOperationParameter operationParameter = new DeviceOperationParameter();
        operationParameter.put("protocol", CommProtocol.USB);
        IT69xx power = new IT69xx(operationParameter);
        power.setDeviceName(res[0]);
        power.open();
        int channel = 0;
        power.setVoltage(channel, 13);
        power.outputOff(channel);
        power.outputOn(channel);
        power.fetchVoltage(channel);
//        System.out.println(power.getVoltage());
        power.close();
        resourceManager.close();
    }
}
