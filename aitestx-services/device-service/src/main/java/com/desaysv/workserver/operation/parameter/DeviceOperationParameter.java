package com.desaysv.workserver.operation.parameter;

import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import com.fazecast.jSerialComm.SerialPort;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 16:00
 * @description : 设备参数
 * @modified By :
 * @since : 2022-3-16
 */
public class DeviceOperationParameter extends OperationParameter {

    private static final String CHANNEL = "channel";
    private static final String VOLTAGE = "voltage";
    private static final String CURRENT = "current";

    private static final String WIDTH = "width";
    private static final String HEIGHT = "height";

//    private static final String HEX = "hex";

    private static final String parity = "parity";
    private static final String project = "project";
    private static final String address = "address";
    private static final String ipPort = "ipPort";
    private static final String commands = "commands";

    public Object setWidth(int width) {
        return put(WIDTH, width);
    }

    public Object setHeight(int height) {
        return put(HEIGHT, height);
    }

    public Integer getWidth() throws OperationParameterExtractException {
        return getValue(WIDTH, 0, Integer.class);
    }

    public Integer getHeight() throws OperationParameterExtractException {
        return getValue(HEIGHT, 0, Integer.class);
    }

    public Integer getChannel() throws OperationParameterExtractException {
        return getValue(CHANNEL, 1, Integer.class);
    }

    public Float getVoltage() throws OperationParameterExtractException {
        return getValue(VOLTAGE, Float.class);
    }

    public Float getCurrent() throws OperationParameterExtractException {
        return getValue(CURRENT, Float.class);
    }

//    public boolean isHex() {
//        return getBoolean(HEX);
//    }

    public Integer getParity() {
        Integer parityValue;
        try {
            parityValue = getValue(parity, Integer.class);
            if (parityValue == null) {
                return SerialPort.NO_PARITY;
            }
        } catch (OperationParameterExtractException e) {
            return SerialPort.NO_PARITY;
        }
        return parityValue;
    }

    //FIXME：去除project耦合
    public String getProject() {
        return getString(project);
    }

    public Integer getAddress() {
        try {
            return getValue(address, Integer.class);
        } catch (OperationParameterExtractException e) {
            return 1;
        }
    }

    public int getPort() {
        try {
            Integer port = getValue(ipPort, Integer.class);
            return port != null ? port : 1;
        } catch (OperationParameterExtractException e) {
            return 1;
        }
    }
    public String getCommands() {
        return getString(commands);
    }

    public DeviceOperationParameter setParameter(String key, Object value) {
        put(key, value);
        return this;
    }

}
