package com.desaysv.workserver.devices.bus.e2e.xiaomiE2E;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Data
public class XiaomiE2eData {
    private int canId; // 报文ID
    private int crc_position; // CRC位置
    private AtomicInteger protect_L = new AtomicInteger(0); // 保护字节低位
    private AtomicInteger protect_H = new AtomicInteger(0); // 保护字节高位
    private int dataId; // 校验ID

    public XiaomiE2eData(String dataId, String canId, String crcPosition, String protect) {
        // 初始化逻辑保持不变
        try {
            this.dataId = Integer.parseInt(dataId.trim().substring(2), 16);
        } catch (Exception e) {
            log.error("dataId 转换错误：{}", dataId);
        }

        try {
            this.canId = Integer.parseInt(canId.trim().substring(2), 16);
        } catch (Exception e) {
            log.error("id 转换错误：{}", canId);
        }

        try {
            this.crc_position = Integer.parseInt(crcPosition.trim().substring(4));
        } catch (Exception e) {
            log.error("crcPosition 转换错误：{}", crcPosition);
        }

        try {
            String[] temp = protect.trim().split("~");
            this.protect_L.set(Integer.parseInt(temp[0].trim().substring(4)));
            try {
                this.protect_H.set(Integer.parseInt(temp[1].trim()));
            } catch (Exception e) {
                this.protect_H.set(this.protect_L.get());
            }
        } catch (Exception e) {
            log.error("protect 转换错误：{}", protect);
        }
    }

    /**
     * 计算CRC并更新数据包中的计数器和CRC值。
     * 使用synchronized确保同一时间只有一个线程可以执行此方法。
     */
    public synchronized byte[] crc(byte pData[], AtomicInteger counter) {
        int crc_init = 0; // 初始值
        int crc_xorout = 0; // 异或值
        int crc_poly = 0x1d; // 多项式值

        int startbit = protect_L.get();
        int endbit = protect_H.get();

        // 1、获取计数器数值
        int temp_counter = counter.get();

        // 2、counter 送入到保护低字节的低4位中
        pData[startbit] &= 0xf0;
        pData[startbit] |= (byte) (temp_counter & 0x0f);

        // 3、计算CRC 包含 DATA ID
        byte value = crc(crc_init, crc_xorout, crc_poly, pData, startbit, endbit);

        // 4、CRC 数据填回到crc 位置
        pData[startbit - 1] = value;

        return pData;
    }

    private byte crc(int initial, int xorout, int polynomial, byte pData[], int startbit, int endbit) {
        byte u8Crc = (byte) initial;
        byte dataid[] = new byte[2];
        dataid[0] = (byte) (dataId & 0x00ff);
        dataid[1] = (byte) ((dataId & 0xff00) >> 8);

        // 1、先计算 data id 的crc , 低位在前，高位在后；
        for (int i = 0; i <= 1; i++) {
            u8Crc ^= dataid[i];
            for (int j = 0; j < 8; j++) {
                if ((u8Crc & 0x80) == 0x80) {
                    u8Crc <<= 1;
                    u8Crc ^= polynomial;
                } else {
                    u8Crc <<= 1;
                }
            }
        }

        // 2、计算规定范围域的 crc , 低位在前，高位在后；
        for (int i = startbit; i <= endbit; i++) {
            u8Crc ^= pData[i];
            for (int j = 0; j < 8; j++) {
                if ((u8Crc & 0x80) == 0x80) {
                    u8Crc <<= 1;
                    u8Crc ^= polynomial;
                } else {
                    u8Crc <<= 1;
                }
            }
        }
        u8Crc ^= (byte) xorout;
        return u8Crc;
    }
}
