package com.desaysv.workserver.devices.autoclicker;

import com.sun.jna.Structure;

import java.util.Collections;
import java.util.List;

public class WideStringBuffer extends Structure implements Structure.ByReference {
    public byte[] buffer = null;

    public WideStringBuffer(int len) {
        this.buffer = new byte[len];
    }

    public WideStringBuffer(byte[] bytes) {
        this.buffer = bytes;
    }

    protected List<String> getFieldOrder() {
        return Collections.singletonList("buffer");
    }

    public String toString() {
        String str = "";

        for(int i = 0; i < this.buffer.length && this.buffer[i] != 0; i += 2) {
            str = str + (char)(this.buffer[i] | this.buffer[i + 1] << 8);
        }

        return str;
    }
}