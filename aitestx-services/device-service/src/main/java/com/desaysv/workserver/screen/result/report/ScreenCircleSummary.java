package com.desaysv.workserver.screen.result.report;

import com.desaysv.workserver.screen.result.mist.BackTracePoint;
import com.desaysv.workserver.screen.result.mist.TestPoint;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 画圆报点报告
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ScreenCircleSummary extends TouchSummary {

    private TestPoint testPoint; //实际测试点

    private BackTracePoint reportPoint; //失败测试报点

    private Double radiusDeviation; //报点和实际测试半径偏差
}
