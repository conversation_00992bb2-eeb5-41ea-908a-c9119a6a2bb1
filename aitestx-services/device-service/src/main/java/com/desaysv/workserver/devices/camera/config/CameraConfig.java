package com.desaysv.workserver.devices.camera.config;

import com.desaysv.workserver.config.DeviceConfig;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/11/21 20:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CameraConfig extends DeviceConfig {

    private String cameraAliasName;
    private String cameraType;
    private CameraSettings cameraSettings;
    private BackTrackConfig backTrackConfig;
    private FailVideoConfig failVideoConfig;
    private TakePhotoConfig takePhotoConfig;

    public CameraConfig() {
        cameraSettings = new CameraSettings();
        backTrackConfig = new BackTrackConfig();
        failVideoConfig = new FailVideoConfig();
        takePhotoConfig = new TakePhotoConfig();
    }

}
