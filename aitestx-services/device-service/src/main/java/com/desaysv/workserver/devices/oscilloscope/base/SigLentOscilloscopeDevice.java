package com.desaysv.workserver.devices.oscilloscope.base;

import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class SigLentOscilloscopeDevice extends OscilloscopeDevice{

    public SigLentOscilloscopeDevice() {
    }

    public SigLentOscilloscopeDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return null;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return true;
    }
}
