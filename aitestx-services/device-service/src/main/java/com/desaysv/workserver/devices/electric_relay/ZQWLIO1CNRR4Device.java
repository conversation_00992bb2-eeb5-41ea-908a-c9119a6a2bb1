package com.desaysv.workserver.devices.electric_relay;

import com.desaysv.electric_relay.utils.ModbusUtil;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.testbox.RelayConfigManager;
import com.desaysv.workserver.devices.testbox.interfaces.IRelaySwitchBoard;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import org.apache.commons.lang3.StringUtils;

import static com.desaysv.workserver.common.constant.RelayConstants.RELAY_DELAY_TIME;

/**
 * ZQWLIO1CNRR4型号的继电器控制开关指令，与其他的相反，因为调用开闭继电器接口需要反向
 * */

public class ZQWLIO1CNRR4Device extends Device implements IRelaySwitchBoard {
    private ModbusUtil modbusUtil;
    private final static int NUMBER_CHANNELS = 4;
    private final static int BAUD_RATE = 9600;

    public ZQWLIO1CNRR4Device() {
        this(new DeviceOperationParameter());
    }

    public ZQWLIO1CNRR4Device(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil == null) {
            modbusUtil = new ModbusUtil();
        }
        return modbusUtil.openSerialPort(getDevicePortName(), BAUD_RATE, getDeviceOperationParameter().getAddress(), NUMBER_CHANNELS);
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (modbusUtil != null) {
            log.info("关闭继电器");
            modbusUtil.closeSerialPort();
        }
        return true;
    }


    public boolean switchRelay(ChannelSwitch channelSwitch) {
        if (isSimulated()) {
            return true;
        }
        log.info("通道{}{}{}", channelSwitch.getChannel(),
                StringUtils.isEmpty(channelSwitch.getPhyLabel()) ? "" : String.format("(%s)", channelSwitch.getPhyLabel()),
                channelSwitch.isSwitchOn() ? "开" : "关");
        if (channelSwitch.getChannel() > NUMBER_CHANNELS) {
            log.error("继电器通道{}超出范围", channelSwitch.getChannel());
            return false;
        }
        if (modbusUtil != null) {
            boolean relayStateChanged = modbusUtil.controlRelay(channelSwitch.getChannel(), !channelSwitch.isSwitchOn());

            // 等待延时时间
            RelayConfigManager.waitForDelay(channelSwitch.getDelayTime());
            return relayStateChanged;
        }
        return false;
    }


    public boolean closeAllRelays() {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil != null) {
            try {
//                modbusUtil.closeAllRelays();
                modbusUtil.openAllRelays();
                return true;
            } catch (Exception e) {
                log.error("关闭所有继电器失败", e);
                return false;
            }
        }
        return false;
    }

    public boolean openAllRelays() {
        if (isSimulated()) {
            return true;
        }
        if (modbusUtil != null) {
            try {
                //modbusUtil.openAllRelays();
                modbusUtil.closeAllRelays();
                return true;
            } catch (Exception e) {
                log.error("打开所有继电器失败", e);
                return false;
            }
        }
        return false;
    }


    @Override
    public boolean writeRelaySwitchBoardCard(Integer deviceChannel, int value) {
        if (isSimulated()) {
            return true;
        }
        ChannelSwitch channelSwitch = new ChannelSwitch();
        channelSwitch.setChannel(deviceChannel);
        channelSwitch.setSwitchOn(value == 1);

        // 设置等待时间
        String delayTime = RelayConfigManager.getConfigValue(getDevice().getAliasName(), RELAY_DELAY_TIME);
        channelSwitch.setDelayTime(Integer.parseInt(delayTime));
        return switchRelay(channelSwitch);
    }

    @Override
    public boolean writeRelaySwitchAll(int value) throws BoardCardTransportException {
        return value == 1 ? openAllRelays() : closeAllRelays();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ELECTRIC_RELAY;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.ElectricRelay.ZQWL_IO_1BXRC32;
    }
}

