package com.desaysv.workserver.devices.testbox.light;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.filemanager.project.LightTestBoxFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Map;

@Slf4j
@Data
public class LightTestBoxConfig {
    private Map<String, TestBoxDeviceConfig> lightTestBoxConfigMap;
    private volatile static LightTestBoxConfig instance;
    @JSONField(serialize = false)
    private File lightTestBoxJsonConfigFile;
    private File loadConfigExcelFile;

    public static LightTestBoxConfig getInstance() {
        if (instance == null) {
            synchronized (LightTestBoxConfig.class) {
                if (instance == null) {
                    instance = new LightTestBoxConfig();
                }
            }
        }
        return instance;
    }

    public LightTestBoxConfig readJsonConfigFile() {
        try {
            if (lightTestBoxJsonConfigFile == null) {
                lightTestBoxJsonConfigFile = ProjectFileManager.of(LightTestBoxFileManager.class).getLightTestBoxFilePath();
            }
            log.info("读取轻量化测试箱配置文件:{}", lightTestBoxJsonConfigFile.getAbsoluteFile());
            String readStringFromFile = ThreadSafeFileUtils.readFileToString(lightTestBoxJsonConfigFile);
            return JSON.parseObject(readStringFromFile, LightTestBoxConfig.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public void writeJsonConfigFile() {
        try {
            if (lightTestBoxJsonConfigFile == null) {
                lightTestBoxJsonConfigFile = ProjectFileManager.of(LightTestBoxFileManager.class).getLightTestBoxFilePath();
            }
            ThreadSafeFileUtils.writeFileFromString(lightTestBoxJsonConfigFile, JSON.toJSONString(LightTestBoxConfig.getInstance()), false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void initLightTestBoxConfig() {
        LightTestBoxConfig lightTestBoxConfig = readJsonConfigFile();
        if (lightTestBoxConfig == null) {
            log.info("lightTestBoxConfigModel 为 null，初始化新实例并写入文件");
            writeJsonConfigFile();
        } else {
            log.info("lightTestBoxConfigModel 不为 null，更新配置");
            lightTestBoxConfigMap = lightTestBoxConfig.getLightTestBoxConfigMap();
            lightTestBoxJsonConfigFile = lightTestBoxConfig.getLightTestBoxJsonConfigFile();
            loadConfigExcelFile = lightTestBoxConfig.getLoadConfigExcelFile();
        }
    }

}
