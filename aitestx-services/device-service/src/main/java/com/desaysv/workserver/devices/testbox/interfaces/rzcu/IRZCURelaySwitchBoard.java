package com.desaysv.workserver.devices.testbox.interfaces.rzcu;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IRZCURelaySwitchBoard {
    Logger log = LogManager.getLogger(IRZCURelaySwitchBoard.class.getSimpleName());

    /**
     * 改变继电器状态
     *
     * @param slaveAddress  从站地址
     * @param channelNumber 通道
     * @param command       ON：打开， OFF：关闭
     */
    boolean writeRelaySwitchBoardCard(Integer slaveAddress, Integer channelNumber, String command) throws BoardCardTransportException;

    boolean writeRelaySwitchAll(int slaveAddress, int channelTotal, int value) throws BoardCardTransportException;

    boolean writeRelaySwitchAllAddressChannel(int value) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SwitchRelayRegexRule).SWITCH_ON_OR_OFF"})
    default void writeRelaySwitchBoardCardInfo(Integer deviceChannel, Integer channelNumber, String command) throws OperationFailNotification {
        boolean isOpen = command.equalsIgnoreCase("ON");
        log.info("继电器地址:{}-通道:{}，设置{}", deviceChannel, channelNumber, isOpen ? "打开" : "关闭");
        try {
            boolean isWriteOk = writeRelaySwitchBoardCard(deviceChannel, channelNumber, command);
            log.info("继电器板卡通道设置:{}", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("继电器板卡通道设置:%s", isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SwitchRelayRegexRule).ALL_CHANNEL_ON_OR_OFF"})
    default void writeRelaySwitchAllChannel(Integer deviceChannel, Integer channelTotal, String command) throws OperationFailNotification {
        boolean isOpen = command.equalsIgnoreCase("ON");
        log.info("继电器地址:{},所有通道{}", deviceChannel, isOpen ? "打开" : "关闭");
        try {
            boolean isWriteOk = writeRelaySwitchAll(deviceChannel, channelTotal, isOpen ? 1 : 0);
            log.info("继电器板卡所有通道{}，{}", isOpen ? "打开" : "关闭",
                    isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("继电器通道%s%s--%s", deviceChannel, isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SwitchRelayRegexRule).ALL_ADDRESS_CHANNEL_ON_OR_OFF"})
    default void writeRelaySwitchAllAddressChannelInfo(Integer deviceChannel, String command) throws OperationFailNotification {
        boolean isOpen = command.equalsIgnoreCase("ON");
        log.info("继电器所有地址的所有通道{}", isOpen ? "打开" : "关闭");
        try {
            boolean isWriteOk = writeRelaySwitchAllAddressChannel(isOpen ? 1 : 0);
            log.info("继电器所有地址的所有通道{}， {}", isOpen ? "打开" : "关闭",
                    isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("继电器通道%s%s--%s", deviceChannel, isOpen ? "打开" : "关闭", isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
        }
    }


}
