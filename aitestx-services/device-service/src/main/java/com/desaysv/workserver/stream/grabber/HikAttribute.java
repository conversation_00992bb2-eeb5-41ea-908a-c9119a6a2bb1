package com.desaysv.workserver.stream.grabber;

import MvCameraControlWrapper.MvCameraControl;

import static MvCameraControlWrapper.MvCameraControlDefines.*;

/**
 * 海康相机属性
 */
public class HikAttribute {
    private final Handle handle;
    private int nRet = MV_OK;

    public HikAttribute(Handle handle) {
        this.handle = handle;
    }

    private void explainErrorCode(String functionName, int nRet) {
        if (MV_OK != nRet) {
            System.err.printf("%s -> Set attribute failed, errCode: [%#x]\n", functionName, nRet);
        }
    }

    /**
     * 获取增益
     *
     * @return 增益
     */
    public float getGain() {
        MVCC_FLOATVALUE value = new MVCC_FLOATVALUE();
        MvCameraControl.MV_CC_GetFloatValue(handle, "Gain", value);
        return value.curValue;
    }

    /**
     * 设置增益
     *
     * @param gain 增益
     */
    public void setGain(float gain) {
        nRet = MvCameraControl.MV_CC_SetFloatValue(handle, "Gain", gain);
        explainErrorCode("setGain", nRet);
    }

    /**
     * 获取曝光时间
     *
     * @return 曝光时间
     */
    public float getExposureTime() {
        MVCC_FLOATVALUE value = new MVCC_FLOATVALUE();
        MvCameraControl.MV_CC_GetFloatValue(handle, "ExposureTime", value);
        return value.curValue;
    }

    /**
     * 设置曝光时间
     *
     * @param exposureTime 曝光时间
     */
    public void setExposureTime(float exposureTime) {
        nRet = MvCameraControl.MV_CC_SetFloatValue(handle, "ExposureTime", exposureTime);
        explainErrorCode("setExposureTime", nRet);
    }

    /**
     * 获取帧率fps
     *
     * @return 帧率fps
     */
    public float getResultingFrameRate() {
        MVCC_FLOATVALUE value = new MVCC_FLOATVALUE();
        MvCameraControl.MV_CC_GetFloatValue(handle, "ResultingFrameRate", value);
        return value.curValue;
    }

    /**
     * 获取分辨率宽度
     *
     * @return 分辨率宽度
     */
    public int getWidth() {
        MVCC_INTVALUE width = new MVCC_INTVALUE();
        MvCameraControl.MV_CC_GetIntValue(handle, "Width", width);
        return (int) width.curValue;
    }

    /**
     * 获取分辨率高度
     *
     * @return 分辨率高度
     */
    public int getHeight() {
        MVCC_INTVALUE height = new MVCC_INTVALUE();
        MvCameraControl.MV_CC_GetIntValue(handle, "Height", height);
        return (int) height.curValue;
    }

    /**
     * 设置分辨率宽度
     *
     * @param width 分辨率宽度
     */
    public void setWidth(int width) {
        nRet = MvCameraControl.MV_CC_SetIntValue(handle, "Width", width);
        explainErrorCode("setWidth", nRet);
    }

    /**
     * 设置分辨率高度
     *
     * @param height 分辨率高度
     */
    public void setHeight(int height) {
        nRet = MvCameraControl.MV_CC_SetIntValue(handle, "Height", height);
        explainErrorCode("setHeight", nRet);
    }

    /**
     * @param type: 类型
     * @description 设置自动曝光模式
     */
    public void setAutoExposure(int type) {
        nRet = MvCameraControl.MV_CC_SetEnumValue(handle, "ExposureAuto", type);
        explainErrorCode("setExposureAuto", nRet);
    }

    public int getAutoExposure() {
        MVCC_ENUMVALUE value = new MVCC_ENUMVALUE();
        MvCameraControl.MV_CC_GetEnumValue(handle, "ExposureAuto", value);
        return value.curValue;
    }

    /**
     * @param value: 时间
     * @description 设置自动曝光时间下限μs
     */
    public void setAutoExposureTimeLowerLimit(int value) {
        nRet = MvCameraControl.MV_CC_SetIntValue(handle, "AutoExposureTimeLowerLimit", value);
        explainErrorCode("setAutoExposureTimeLowerLimit", nRet);
    }

    public int getAutoExposureTimeLowerLimit() {
        MVCC_INTVALUE value = new MVCC_INTVALUE();
        MvCameraControl.MV_CC_GetIntValue(handle, "AutoExposureTimeLowerLimit", value);
        return (int) value.curValue;
    }

    /**
     * @param value: 时间
     * @description 设置自动曝光模式上限μs
     */
    public void setAutoExposureTimeUpperLimit(int value) {
        nRet = MvCameraControl.MV_CC_SetIntValue(handle, "AutoExposureTimeUpperLimit", value);
        explainErrorCode("setAutoExposureTimeUpperLimit", nRet);
    }

    public int getAutoExposureTimeUpperLimit() {
        MVCC_INTVALUE value = new MVCC_INTVALUE();
        MvCameraControl.MV_CC_GetIntValue(handle, "AutoExposureTimeUpperLimit", value);
        return (int) value.curValue;
    }

    /**
     * @param value:
     * @description 设置水平镜像
     */
    public void setReverseX(boolean value) {
        nRet = MvCameraControl.MV_CC_SetBoolValue(handle, "ReverseX", value);
        explainErrorCode("setReverseX", nRet);
    }

    /**
     * @param value:
     * @description 设置垂直镜像
     */
    public void setReverseY(boolean value) {
        nRet = MvCameraControl.MV_CC_SetBoolValue(handle, "ReverseY", value);
        explainErrorCode("setReverseY", nRet);
    }

    public float getCameraFrameRate() {
        MVCC_FLOATVALUE value = new MVCC_FLOATVALUE();
        MvCameraControl.MV_CC_GetFloatValue(handle, "ResultingFrameRate", value);
        return value.curValue;
    }

}

