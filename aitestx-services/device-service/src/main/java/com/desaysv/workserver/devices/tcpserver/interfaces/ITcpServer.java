package com.desaysv.workserver.devices.tcpserver.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.tcpserver.TcpMessage;

public interface ITcpServer {

    OperationResult tcpServerSend(String messageText);

    OperationResult tcpServerSend(TcpMessage tcpMessage);

    boolean tcpServerJudge(String messageText);

    //FIXME:多传参数问题
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TcpServerRegexRule).SEND"})
    default OperationResult sendMessage(String text, String parenthesisContext, String checkedContext) throws OperationFailNotification {
        return tcpServerSend(parenthesisContext);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TcpServerRegexRule).RECV"})
    default ActualExpectedResult tcpServerJudge(String text, String parenthesisContext) throws OperationFailNotification {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        actualExpectedResult.put("tcpServerJudge", tcpServerJudge(parenthesisContext));
        return actualExpectedResult;
    }

}
