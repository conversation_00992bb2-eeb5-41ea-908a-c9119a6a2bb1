package com.desaysv.workserver.devices.soundcard;

import com.desaysv.SoundCollectService;
import com.desaysv.impl.SoundCollectServiceImpl;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.soundcard.interfaces.ISound;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.entity.MonitorDataPackage;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.manager.SoundFileManager;
import com.desaysv.workserver.monitor.MeasureIndicator;
import com.desaysv.workserver.monitor.SoundMeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
public class AuxInSoundDevice extends Device implements SoundMeasurementComparator, ISound {

    private final SoundCollectService soundCollectService;
    private volatile boolean isRecording = false;
    private final Map<Integer, List<Float>> recordedVolumes = new HashMap<>(); // 存储录音期间的音量
    private final ScheduledExecutorService scheduler;
    private ScheduledFuture<?> volumeMonitoringTaskFuture;

    public AuxInSoundDevice() {
        this(new DeviceOperationParameter());
    }

    public AuxInSoundDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        soundCollectService = new SoundCollectServiceImpl();
        scheduler = Executors.newSingleThreadScheduledExecutor();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_SOUND_CARD;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.SoundCard.AUXIN_SOUND_DEVICE;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        soundCollectService.setSampleRate(getSampleRate());//采样率
        //SoundCollectService.setSampleSizeInBits(16);//采样位数
        //SoundCollectService.setChannels(1);//单声道
        return soundCollectService.open();
    }

    @Override
    public boolean close() throws DeviceCloseException {
        // 关闭调度器
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(1, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        return soundCollectService.close();
    }

    /**
     * 监控音量
     *
     * @param monitorDataPackage 监控数据
     * @return 操作结果
     */
    public OperationResult monitorVolume(Integer deviceChannel, MonitorDataPackage monitorDataPackage) {
        return measurementMonitor(deviceChannel, MeasureIndicator.VOLUME, monitorDataPackage);
    }

    public OperationResult soundCapture(Integer deviceChannel) {
        OperationResult operationResult = new OperationResult();
        try {
            float volume = fetchVolume(deviceChannel);
            operationResult.setOk(true);
            operationResult.setMessage("音量获取成功");
            operationResult.setData(volume);
        } catch (DeviceReadException e) {
            log.error("音量获取过程中发生异常: {}", e.getMessage());
            operationResult.setOk(false);
            operationResult.setMessage(e.getMessage());
        }
        return operationResult;
    }

    @Override
    public boolean detectBeepFrequency(Integer deviceChannel, double recordSeconds, double minFrequency, double maxFrequency) {
        return false;
    }

    @Override
    public boolean detectBeeperCycle(Integer deviceChannel, double threshold, int minIntervalMillis, double recordSeconds, double targetPeriod) {
        return false;
    }

    @Override
    public float fetchVolume(Integer deviceChannel, String currentDirection) {
        return (float) soundCollectService.getCurrentVolume();
    }


    @Override
    public List<Float> getRecordedVolumes(Integer deviceChannel) {
        return recordedVolumes.getOrDefault(deviceChannel, new ArrayList<>()); // 返回指定通道的音量记录
    }

    @Override
    public String saveRecordingToFile() {
        SoundFileManager soundFileManager = ProjectFileManager.of(SoundFileManager.class);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String formattedDate = dateFormat.format(new Date());
        String fileName = formattedDate + ".wav";

        soundCollectService.saveRecordingToFile(fileName, soundFileManager.getFailSoundFileFolderPath());
        log.info("录音文件保存成功 文件名：{} 路径:{}", fileName, soundFileManager.getFailSoundFileFolderPath());
        return String.format("录音文件保存成功 文件名：%s 路径:%s", fileName, soundFileManager.getFailSoundFileFolderPath());
    }

    public float getVolume(Integer deviceChannel) throws DeviceReadException {
        return fetchVolume(deviceChannel);
    }

    @Override
    public void startRecording(Integer deviceChannel) {
        isRecording = true;
        recordedVolumes.putIfAbsent(deviceChannel, new ArrayList<>());
        // 清空所有通道的音量记录
        recordedVolumes.get(deviceChannel).clear();
        soundCollectService.startRecording();//开始录音
        //使用调度器
        Runnable volumeMonitoringTask = () -> {
            Thread.currentThread().setPriority(Thread.MAX_PRIORITY - 1); //音量线程等级最高
            long startTime = System.currentTimeMillis();
            try {
                if (isRecording) {
                    float currentVolume = getVolume(deviceChannel);
                    recordedVolumes.get(deviceChannel).add(currentVolume); // 记录当前通道的音量
                    log.info("声卡通道 {} 当前音量: {}dB, 任务执行时间: {}ms", deviceChannel, currentVolume, System.currentTimeMillis() - startTime);
                }
            } catch (DeviceReadException e) {
                log.error("音量监测过程中发生异常: {}", e.getMessage());
            }
        };
        volumeMonitoringTaskFuture = scheduler.scheduleAtFixedRate(volumeMonitoringTask, 0, 10, TimeUnit.MILLISECONDS);
        log.info("声卡通道 {} 启动音量监测线程", deviceChannel);
    }

    /**
     * 停止录音
     */
    @Override
    public void stopRecording() {
        isRecording = false;
        soundCollectService.stopRecording();

        // 取消任务
        if (volumeMonitoringTaskFuture != null) {
            volumeMonitoringTaskFuture.cancel(false); // 取消任务，不中断正在执行的任务
        }

        log.info("录音停止");
    }

    @Override
    public void beginSoundMonitor(Integer deviceChannel) {
        log.info("开始声音监控");
        startRecording(deviceChannel);
    }

    @Override
    public boolean endSoundMonitor(Integer deviceChannel, SoundMonitor soundMonitor) throws OperationFailNotification {
        log.info("结束声音监控，表达式: {}, 要求数量: {}", soundMonitor.getExpression(), soundMonitor.getRequiredNums());
        // 停止录音
        stopRecording();

        // 检查是否满足监控条件
        int matchCount = 0;

        List<Float> volumes = recordedVolumes.get(deviceChannel);

        if (volumes.isEmpty()) {
            throw new OperationFailNotification(String.format("声卡通道 %d 没有记录任何音量数据", deviceChannel));
        }
        try {
            boolean ok = false;
            // 遍历所有通道的音量记录
            for (float volume : volumes) {
                // 使用表达式判断是否满足条件
                String condition = soundMonitor.getExpression();
                boolean pass;

                if (condition.contains("&") || condition.contains("|")) {
                    // 处理多个与（&）条件
                    if (condition.contains("&") && !condition.contains("|")) {
                        String[] expressions = condition.split("&");
                        pass = true; // 初始为true，所有条件都要满足
                        for (String expr : expressions) {
                            boolean resultOk = StrUtils.judgeExpressResult(volume + expr);
                            if (!resultOk) {
                                pass = false;
                                break; // 一个条件不满足，整体就不满足
                            }
                        }
                    }
                    // 处理多个或（|）条件
                    else if (condition.contains("|") && !condition.contains("&")) {
                        String[] expressions = condition.split("\\|");
                        pass = false; // 初始为false，只要有一个条件满足即可
                        for (String expr : expressions) {
                            boolean resultOk = StrUtils.judgeExpressResult(volume + expr);
                            if (resultOk) {
                                pass = true;
                                break; // 一个条件满足，整体就满足
                            }
                        }
                    }
                    // 处理混合条件（既有&又有|）
                    else {
                        // 对于复杂表达式，直接构建完整表达式进行评估
                        // 将maxVolume替换到表达式中的每个比较操作前
                        String fullExpr = condition;
                        // 查找所有比较操作符（>, <, >=, <=, ==, !=）前的位置
                        String[] operators = {">=", "<=", "==", "!=", ">", "<"};
                        for (String op : operators) {
                            fullExpr = fullExpr.replace(op, volume + op);
                        }
                        // 评估完整表达式
                        pass = StrUtils.judgeExpressResult(fullExpr);
                    }
                } else {
                    pass = StrUtils.judgeExpressResult(volume + condition);
                }

                if (soundMonitor.isAlwaysSatisfyExpression()) {
                    if (!pass) {
                        //有一次不通过，就不行
                        throw new OperationFailNotification("声卡通道 {} 的音量 {} 不满足条件: {}", deviceChannel, volume, condition);
                    }
                } else {
                    if (pass) {
                        matchCount++;
                        log.info("声卡通道 {} 的音量 {} 满足条件: {}", deviceChannel, volume, condition);
                    }
                    if (matchCount >= soundMonitor.getRequiredNums()) {
                        //匹配数量大于等于要求数量
                        ok = true;
                        break;
                    }
                }
            }

            // 判断满足条件的通道数是否达到要求
            if (ok) {
                log.info("声音监控成功: 满足条件的音量值达到要求数量 {}", soundMonitor.getRequiredNums());
            } else {
                throw new OperationFailNotification("声音监控失败: 满足条件的音量值 {}，未达到要求数量 {}", matchCount, soundMonitor.getRequiredNums());
            }
        } catch (Exception e) {
            // 保存失败的声音文件
            saveRecordingToFile();
            throw new OperationFailNotification(e.getMessage());
        }

        return true;
    }
}
