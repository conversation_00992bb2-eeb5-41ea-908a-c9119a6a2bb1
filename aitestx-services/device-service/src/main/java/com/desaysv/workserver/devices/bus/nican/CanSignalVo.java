package com.desaysv.workserver.devices.bus.nican;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CanSignalVo {

    /**
     * 信号名
     */
    private String signalName;

    /**
     * 原始值(Hex)
     */
    private String originalValue;

    /**
     * 实际值
     */
    private BigDecimal actualValue;

    /**
     * 值描述
     */
    private String valueRepresentation;

    public CanSignalVo(String signalName,String originalValue,BigDecimal actualValue,String valueRepresentation){
        this.signalName = signalName;
        this.originalValue = originalValue;
        this.actualValue = actualValue == null ? BigDecimal.ZERO : actualValue;
        this.valueRepresentation = valueRepresentation;
    }

}
