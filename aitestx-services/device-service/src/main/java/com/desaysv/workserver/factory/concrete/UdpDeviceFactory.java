package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.device_udp.UdpDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractUdpDeviceFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 10:42
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Component
@Lazy
public class UdpDeviceFactory implements AbstractUdpDeviceFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.UdpDevice.UDP_DEVICE:
                return createUdpDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createUdpDevice(DeviceRegisterForm deviceRegisterForm) {
        return new UdpDevice(deviceRegisterForm.getDeviceOperationParameter());
    }
}
