package com.desaysv.workserver.devices.bus.e2e;

import cantools.dbc.DbcReader;
import cantools.dbc.Message;
import cantools.dbc.Signal;
import cantools.dbc.SignalGroup;
import cantools.exceptions.DecodingFrameLengthException;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.devices.bus.DbcUtils;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import lombok.SneakyThrows;

import java.math.BigInteger;
import java.util.*;

public class GeelyE2EL946 implements CanMessageEventListener {

    private static final Map<String, Integer> counters = new HashMap<>();
    private final List<Message> messages;
    private final CanConfig canConfig;
    private final List<String> dbcPaths;
    private final int channel;

    public GeelyE2EL946(CanConfig canConfig, List<String> dbcPaths, int channel) {
        this.canConfig = canConfig;
        this.dbcPaths = dbcPaths;
        this.channel = channel;
        messages = new ArrayList<>();
        for (String dbcPath : dbcPaths) {
            DbcReader dbcReader = DbcUtils.getDbcReader(dbcPath);
            messages.addAll(dbcReader.getBus().getMessages());
        }
    }

    @SneakyThrows
    public byte[] calculateL946E2e(int canId, byte[] inData) {
        byte[] outData = new byte[inData.length];
        System.arraycopy(inData, 0, outData, 0, inData.length);
        String hexString = String.format("0x%02X", canId);
        Optional<Message> optionalMessage = messages.stream().filter(o -> o.getId().equals(hexString)).findFirst();
        if (optionalMessage.isPresent()) {
            Message dbcMessage = optionalMessage.get();
            Map<String, Object> decodedSignalMap = dbcMessage.decode(inData);

            List<SignalGroup> signalGroupsWithDataId = dbcMessage.getSignalGroupsWithDataId();
            for (SignalGroup signalGroup : signalGroupsWithDataId) {
                int size = 3 + calculateSize(signalGroup);//-ID和count
                byte[] data = new byte[size];
                int dataIndex = 3; // 用于填充data数组的索引
                String CHKSignalName = null;
                String counterSignalName = null;
                for (Signal signal : signalGroup.getSignals()) {
                    if ("CHK".equals(signal.getSigFuncType())) {
                        CHKSignalName = signal.getName();
                        int intSignalValue = Integer.parseInt(signalGroup.getDataId().substring(2), 16);
                        data[0] = (byte) (intSignalValue & 0xFF);
                        data[1] = (byte) ((intSignalValue >> 8) & 0xFF);
                    } else if ("CNTR".equals(signal.getSigFuncType())) {
                        counterSignalName = signal.getName();
                        int min = (int) signal.getValue().getMin();
                        int max = (int) signal.getValue().getMax();
                        data[2] = (byte) counterAddBySignalName(signal.getName(), min, max);
                    } else {
                        BigInteger signalValue = (BigInteger) decodedSignalMap.get(signal.getName());
                        int intSignalValue = signalValue.intValue();
                        int signalLength = signal.getLength();
                        int byteCount = (signalLength + 7) / 8; // 计算需要的字节数

                        for (int i = 0; i < byteCount; i++) {
                            data[dataIndex++] = (byte) (intSignalValue & 0xFF);
                            intSignalValue >>= 8;
                        }
                    }
                }
                byte checksum = crc8(data, data.length);
                decodedSignalMap.put(CHKSignalName, checksum);
                decodedSignalMap.put(counterSignalName, getCounterValueBySignalName(counterSignalName));
            }
            outData = dbcMessage.encode(decodedSignalMap);
            return outData;
        } else {
            throw new NoSuchElementException("No message found with ID: " + hexString + " in DBCPaths:" + dbcPaths.toString() + "channel:" + channel);
        }

    }

    private int calculateSize(SignalGroup signalGroup) {
        int totalSize = 0;
        for (Signal signal : signalGroup.getSignals()) {
            if ("CHK".equals(signal.getSigFuncType()) || "CNTR".equals(signal.getSigFuncType())) {
                continue;
            }
            int signalLength = signal.getLength(); // 获取信号的位长度
            int byteCount = (signalLength + 7) / 8;
            totalSize += byteCount;
        }
        return totalSize;
    }

    public static byte crc8(byte[] crcdata, int len) {
        int crc = 0x00;
        int polynomial = 0x1D;

        for (int i = 0; i < len; i++) {
            crc ^= crcdata[i] & 0xFF;

            for (int j = 0; j < 8; j++) {
                if ((crc & 0x80) != 0) {
                    crc = (crc << 1) ^ polynomial;
                } else {
                    crc = crc << 1;
                }
            }
        }

        return (byte) (crc & 0xFF);
    }


    public int counterAddBySignalName(String signalName, int minValue, int maxValue) {
        if (counters.get(signalName) != null) {
            int counter = counters.get(signalName);
            counter = (counter + 1) % maxValue;
            counters.put(signalName, counter);
        } else {
            counters.put(signalName, minValue);
        }
        return counters.get(signalName);
    }

    public static int getCounterValueBySignalName(String signalName) {
        if (counters.get(signalName) != null) {
            return counters.get(signalName);
        } else {
            counters.put(signalName, 0);
        }
        return counters.get(signalName);
    }

    @Override
    public void onMessageSend(CanMessage message) {
        if (message.getE2eType() == null) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            if ("GeelyL946".equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
        }
        if (!message.isE2eEnabled()) {
            return;
        }
        if (message.getE2eType() != null && message.getE2eType().equalsIgnoreCase("GeelyL946")) {
            message.setData(calculateL946E2e(message.getArbitrationId(), message.getData()));
        }
    }

    public static void main(String[] args) throws DecodingFrameLengthException {
        List<String> paths = new ArrayList<>();
        String path = "D:\\extstptv01\\Desktop\\数据库\\01-DBC\\SDB22435_L946_ZCU_CANFD1Cfg_240615_PNC.dbc";
        paths.add(path);
        GeelyE2EL946 geelyE2EL946 = new GeelyE2EL946(null, paths, 0);
        byte[] tmp = new byte[64];
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
        geelyE2EL946.calculateL946E2e(0x62, tmp);
    }
}
