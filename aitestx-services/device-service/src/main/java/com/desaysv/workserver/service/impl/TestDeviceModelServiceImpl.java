package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.mapper.TestDeviceModelMapper;
import com.desaysv.workserver.model.TestDeviceModel;
import com.desaysv.workserver.service.TestDeviceModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-9 15:13
 * @description :
 * @modified By :
 * @since : 2022-5-9
 */
@Service
@Slf4j
//no lazy
public class TestDeviceModelServiceImpl implements TestDeviceModelService {
    private TestDeviceModelMapper testDeviceModelMapper;

    @Autowired
    public void setTestDeviceModelMapper(TestDeviceModelMapper testDeviceModelMapper) {
        this.testDeviceModelMapper = testDeviceModelMapper;
    }

    @Async
    @Transactional
    @Override
    public void initDB() {
//        log.info("testDeviceModelMapper初始化db");
        try {
            //删除过时的设备型号
            List<String> deprecatedDeviceModelsList = DeviceModel.getAllDeprecatedDeviceModels();
            for (String deviceModel : deprecatedDeviceModelsList) {
                TestDeviceModel testDeviceModel = testDeviceModelMapper.selectByName(deviceModel);
                if (testDeviceModel != null) {
                    testDeviceModelMapper.deleteByPrimaryKey(testDeviceModel.getId());
                }
            }

            //重命名设备型号
            List<String> allDeviceModels = DeviceModel.getAllDeviceModels();
            List<DeviceModel.DeviceModelConverter> deviceModelConverters = DeviceModel.getAllDeviceModelConverterList();
            for (DeviceModel.DeviceModelConverter converter : deviceModelConverters) {
                String oldName = converter.getOldName();
                String newName = converter.getNewName();
                TestDeviceModel deviceModel = testDeviceModelMapper.selectByName(oldName);
                if (deviceModel != null) {
                    TestDeviceModel existingDeviceModel = testDeviceModelMapper.selectByName(newName);
                    if (existingDeviceModel != null && !existingDeviceModel.getId().equals(deviceModel.getId())) {
                        log.warn("设备型号名称 '{}' 已存在, 跳过更新", newName);
                        continue; // 或者抛出一个异常
                    }
                    log.info("更新设备型号：{}", deviceModel);
                    deviceModel.setName(newName);
                    testDeviceModelMapper.updateByPrimaryKey(deviceModel);
                }
            }
            //更新新增的设备型号
            for (String deviceModel : allDeviceModels) {
                TestDeviceModel testDeviceModel = testDeviceModelMapper.selectByName(deviceModel);
                if (testDeviceModel == null) {
                    log.info("新增设备型号：{}", deviceModel);
                    testDeviceModel = new TestDeviceModel();
                    testDeviceModel.setName(deviceModel);
                    testDeviceModelMapper.insert(testDeviceModel);
                }
            }
        } catch (IllegalAccessException e) {
            log.error("初始化设备型号数据库时发生错误", e);
        }
    }

    @Override
    public TestDeviceModel getDeviceModelById(Integer deviceModelId) {
        return testDeviceModelMapper.selectByPrimaryKey(deviceModelId);
    }

    @Override
    public TestDeviceModel getDeviceModelByName(String deviceModelName) {
        return testDeviceModelMapper.selectByName(deviceModelName);
    }

    @Override
    public List<TestDeviceModel> getAllDeviceModels() {
        return testDeviceModelMapper.selectAll();
    }
}
