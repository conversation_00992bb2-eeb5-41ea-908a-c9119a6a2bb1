package com.desaysv.workserver.finder;

import MvCameraControlWrapper.CameraControlException;
import MvCameraControlWrapper.MvCameraControlDefines;
import com.desaysv.workserver.constants.AppConstants;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.devices.camera.basler.BaslerCamera;
import com.desaysv.workserver.devices.camera.hik.HikCamera;
import com.desaysv.workserver.devices.camera.usb.USBCamera;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.stream.grabber.HikEnumDevices;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FrameGrabber;
import org.bytedeco.videoinput.videoInput;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static MvCameraControlWrapper.MvCameraControlDefines.MV_GIGE_DEVICE;
import static MvCameraControlWrapper.MvCameraControlDefines.MV_USB_DEVICE;

@Component
@Lazy
@Slf4j
public class CameraDeviceFinder {

    static {
        new Thread(FrameGrabber::init);
    }


    private boolean isBuiltInCamera(String deviceName, String uniqueCode) {
        // 检查设备名称中是否包含常见的内置相机关键词
        String lowerCaseName = deviceName.toLowerCase();
        if (lowerCaseName.contains("integrated") ||
                lowerCaseName.contains("built-in") ||
                lowerCaseName.contains("internal") ||
                lowerCaseName.contains("notebook") ||
                lowerCaseName.contains("laptop")) {
            return true;
        }

        // 可能还需要检查特定制造商的内置相机命名规则
        // 例如：HP, Dell, Lenovo等常见笔记本品牌的内置相机可能有特定命名
        if (lowerCaseName.contains("hp webcam") ||
                lowerCaseName.contains("lenovo easycamera") ||
                lowerCaseName.contains("dell webcam") ||
                lowerCaseName.contains("hp hd camera") ||
                lowerCaseName.contains("integrated_webcam")) {
            return true;
        }

        // 检查uniqueCode是否有特定模式
        // 这需要根据实际观察到的内置相机的uniqueCode来定义
        if (uniqueCode.toLowerCase().contains("@device:pnp") ||
                uniqueCode.toLowerCase().contains("@device:sw")) {
            return false;
        }
        return true;
    }

    public List<Device> findAllPhysicalCameras(boolean isLock) {
        log.info("查找所有相机");
        return findAllPhysicalCameras(null, isLock);
    }

    public List<Device> findAllPhysicalCameras(String deviceModel, boolean isLock) {
        if (deviceModel == null) {
            List<Device> cameraDevices = findAllPhysicalUSBCameras(isLock);
            cameraDevices.addAll(findAllHikCameras(isLock));
            return cameraDevices;
        }
        if (deviceModel.equals(DeviceModel.Camera.HIK_CAMERA)) {
            return findAllHikCameras(isLock);
        }
        return findAllPhysicalUSBCameras(isLock);
    }

    private List<Device> findAllPhysicalUSBCameras(boolean isLock) {
        List<Device> devices = new ArrayList<>();
        int count = videoInput.listDevices(true);

        String simpleCameraName;
        String uniqueCode;
        Pattern uuidPattern = Pattern.compile("\\{([^{}]*)}");
        Matcher matcher;
        String uuid;
        int deviceIndex = 0;
        CameraDevice device;
        for (int i = 0; i < count; ++i) {
            simpleCameraName = videoInput.getDeviceName(i).getString();
            uniqueCode = videoInput.getUniqueDeviceName(i).getString();
            log.info("查找到相机名:{}", simpleCameraName);
            // 获取设备唯一标识中的UUID部分
            uuid = "";
            matcher = uuidPattern.matcher(uniqueCode);
            if (matcher.find()) {
                uuid = matcher.group(1);
            }

            // 根据设备类型创建对应实例
            if (uniqueCode.startsWith(AppConstants.PNP_CAMERA)) {
                device = new USBCamera();
            } else if (uniqueCode.startsWith(AppConstants.SW_CAMERA)) {
                device = new BaslerCamera();
            } else {
                device = new USBCamera();
            }
            device.setDeviceName(simpleCameraName + "_" + uuid);
            //TODO: 简单的解决方法反转索引顺序，后续可能要改成FrameStreamGrabber同样的方式获取索引比较好
            device.setDevicePort(i);

            device.setAliasName("Camera#" + (deviceIndex + 1));
            device.setDeviceUniqueCode(uniqueCode);
            device.setDeviceOperationParameter(new DeviceOperationParameter());
            devices.add(device);
            deviceIndex++;
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), d.getDeviceModel()));
        }
        videoInput.free(null);
        return devices;
    }

    /**
     * 查找所有海康相机
     *
     * @return 所有海康相机
     */
    private List<Device> findAllHikCameras(boolean isLock) {
        List<Device> devices = new ArrayList<>();
        try {
            List<MvCameraControlDefines.MV_CC_DEVICE_INFO> deviceInfoArray = HikEnumDevices.enumDevices();
            CameraDevice device;
            String deviceName;
            String uuid;
            int index = 0;
            for (MvCameraControlDefines.MV_CC_DEVICE_INFO deviceInfo : deviceInfoArray) {
                if (deviceInfo.transportLayerType == MV_GIGE_DEVICE) {
                    deviceName = deviceInfo.gigEInfo.currentIp;
                    uuid = deviceInfo.gigEInfo.serialNumber;
                } else if (deviceInfo.transportLayerType == MV_USB_DEVICE) {
                    deviceName = deviceInfo.usb3VInfo.modelName + "_" + deviceInfo.usb3VInfo.deviceGUID;
                    uuid = deviceInfo.usb3VInfo.serialNumber;
                } else {
                    continue;
                }
                device = new HikCamera();
                device.setDevicePort(index);
                device.setDeviceName(deviceName);
                device.setAliasName("HikCamera#" + ++index);
//                device.setDeviceAliasName("HikCamera#" + ++index);
                device.setDeviceUniqueCode(uuid);
                device.setDeviceOperationParameter(new DeviceOperationParameter());
                devices.add(device);
            }
        } catch (CameraControlException e) {
            log.error(e.getMessage(), e);
        }
        if (isLock) {
            devices.removeIf(d -> Device.isLocked(d.getDeviceName(), d.getDeviceModel()));
        }
        return devices;
    }

    // 测试相机索引是否正常
    public static void main(String[] args) {
        // 测试设备发现功能
        System.out.println("===== 开始设备枚举测试 =====");
        List<Device> devices = new CameraDeviceFinder().findAllPhysicalUSBCameras(false);

        // 打印发现的所有设备信息
        System.out.println("\n===== 发现设备列表 =====");
        devices.forEach(d -> {
            System.out.printf("[设备 %d]%n类型: %s%n名称: %s%n端口: %d%n唯一码: %s%n别名: %s%n%n",
                    devices.indexOf(d),
                    d.getClass().getSimpleName(),
                    d.getDeviceName(),
                    d.getDevicePort(),
                    d.getDeviceUniqueCode(),
                    d.getAliasName());
        });

        // 设备初始化测试
        System.out.println("===== 开始设备初始化测试 =====");
        videoInput vi = new videoInput();

        devices.forEach(device -> {
            int port = device.getDevicePort();
            System.out.printf("\n--- 正在测试设备 %d (%s) ---%n", port, device.getDeviceName());

            // 带重试的初始化（最多3次）
            boolean initSuccess = false;
            for (int retry = 1; retry <= 3; retry++) {
                try {
                    System.out.printf("尝试初始化 (第%d次)...%n", retry);
                    if (vi.setupDevice(port)) {
                        initSuccess = true;
                        break;
                    }
                    Thread.sleep(500); // 等待500ms重试
                } catch (Exception e) {
                    System.out.println("初始化异常: " + e.getMessage());
                }
            }

            if (initSuccess) {
                System.out.println("✅ 设备初始化成功");
                System.out.println("支持的分辨率: " + vi.getWidth(port) + "x" + vi.getHeight(port));
                // 尝试捕获一帧测试
                if (vi.isFrameNew(port)) {
                    System.out.println("帧数据大小: " + vi.getSize(port) + " bytes");
                } else {
                    System.out.println("⚠️ 未收到新帧");
                }
            } else {
                System.out.println("❌ 设备初始化失败");
            }

            // 释放设备资源
            vi.stopDevice(port);
        });

        // 显示未识别的设备
        System.out.println("\n===== 底层设备列表对比 =====");
        int rawCount = videoInput.listDevices();
        System.out.println("底层库报告设备总数: " + rawCount);
        for (int i = 0; i < rawCount; i++) {
            System.out.printf("设备 %d: %s [%s]%n",
                    i,
                    videoInput.getDeviceName(i).getString(),
                    videoInput.getUniqueDeviceName(i).getString());
        }
    }


}
