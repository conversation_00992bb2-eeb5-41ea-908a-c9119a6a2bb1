package com.desaysv.workserver.manager;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.model.TestDevice;
import com.desaysv.workserver.model.roi.TemplateRoi;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class ImageFileManager extends ProjectFileManager {
    protected static final String IMAGE_SUFFIX_WITH_DOT = ".png";
    protected static final String IMAGE_SUFFIX = "png";

    protected static final class _ImageConstants {
        public File basePath;
        public File imageFileDBPath;
        public File originalImageFileDBPath;
        public File templateImageFileDBPath;
        public File failImageDBPath;
        public File roiImageDBPath;
        public File screenShotDBPath;
        public File roiConfigPath;
        public File roiConfigDbPath; // 新增SQLite数据库文件路径
    }

    protected _ImageConstants imageConstants;

    public ImageFileManager(String projectName) {
        super(projectName);
    }

    public ImageFileManager(String projectName, String dynamicFolderName) {
        super(projectName, dynamicFolderName);
    }

    protected abstract String getImageFolderName();

    private void initImagePaths(String dynamicFolderName) {
        imageConstants = new _ImageConstants();
        imageConstants.imageFileDBPath = createFolder(fileDbPath, "pictures");
        String imageBelongFolder = getImageFolderName();
        imageConstants.basePath = createFolder(imageConstants.imageFileDBPath, imageBelongFolder);
        if (dynamicFolderName != null) {
            imageConstants.basePath = createFolder(imageConstants.basePath, dynamicFolderName);
        }
        imageConstants.originalImageFileDBPath = createFolder(imageConstants.basePath, "originalPictures");
        //举例：D:\FlyTest\data\server\projects\自动化工具调试\database\fileDB\pictures\camera\Camera#1\templatePictures
        imageConstants.templateImageFileDBPath = createFolder(imageConstants.basePath, "templatePictures");
        imageConstants.failImageDBPath = createFolder(imageConstants.basePath, "failPictures");
        imageConstants.screenShotDBPath = createFolder(imageConstants.basePath, "screenShotPictures");
        imageConstants.roiImageDBPath = createFolder(imageConstants.basePath, "roiPictures");
        imageConstants.roiConfigPath = new File(imageConstants.templateImageFileDBPath, "roiConfig.json");
        imageConstants.roiConfigDbPath = new File(imageConstants.templateImageFileDBPath, "roiConfig.db"); // 初始化数据库文件路径
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        initImagePaths(dynamicFolderName);
    }

    private static ImageFileManager getImageFileManagerInner(String projectName, String deviceType, String deviceAliasName) {
        ImageFileManager imageFileManager;
        switch (deviceType) {
            case DeviceType.DEVICE_ANDROID:
                imageFileManager = ProjectFileManager.of(projectName, deviceAliasName, AndroidImageFileManager.class);
                break;
            case DeviceType.DEVICE_CAMERA:
                imageFileManager = ProjectFileManager.of(projectName, deviceAliasName, CameraImageFileManager.class);
                break;
            case DeviceType.DEVICE_VIDEO_CAPTURE:
                imageFileManager = ProjectFileManager.of(projectName, deviceAliasName, VideoCaptureImageFileManager.class);
                break;
            case DeviceType.DEVICE_QNX:
                imageFileManager = ProjectFileManager.of(projectName, deviceAliasName, QnxImageFileManager.class);
                break;
            default:
                throw new IllegalArgumentException(String.format("设备类型%s未知", deviceType));
        }
        return imageFileManager;
    }

    public static ImageFileManager getImageFileManager(TestDevice device, String deviceType, String projectName) {
        return getImageFileManagerInner(projectName, deviceType, device.getAliasName());
    }

    public static ImageFileManager getImageFileManager(Device device, String projectName) {
        String deviceType = device.getDeviceType();
        return getImageFileManagerInner(projectName, deviceType, device.getAliasName());
    }

    public Frame getTemplateImageFrame(String templateName) throws FileNotFoundException {
        File templateFile = getTemplateImageFile(templateName);
        Frame frame = null;
        if (templateFile.exists()) {
            try {
                frame = Java2DFrameUtils.toFrame(ImageIO.read(templateFile)); //新建Frame存储
//            frame = getImageConverter.getFrame(ImageIO.read(templateFile));
            } catch (IOException e) {
                log.warn(e.getMessage(), e);
            }
        } else {
            throw new FileNotFoundException(String.format("图像模板\"%s\"未找到", templateName));
        }
        return frame;
    }

    public File getTemplateImageFile(String templateName) {
        return new File(imageConstants.templateImageFileDBPath, templateName + IMAGE_SUFFIX_WITH_DOT);
    }

    public File saveOriginImageFile(String imageName, Frame frame) throws IOException {
        return saveImageFile(imageConstants.originalImageFileDBPath, imageName, frame);
    }

    public File saveTemplateImageFile(String imageName, Frame frame) throws IOException {
        return saveImageFile(imageConstants.templateImageFileDBPath, imageName, frame);
    }

    public File saveSnapshotImageFile(String imageName, Frame frame) throws IOException {
        return saveImageFile(imageConstants.templateImageFileDBPath, imageName, frame);
    }

    /**
     * 初始化SQLite数据库
     */
    private void initRoiDatabase() {
        String url = "jdbc:sqlite:" + imageConstants.roiConfigDbPath.getAbsolutePath();
        try (Connection conn = DriverManager.getConnection(url);
             Statement stmt = conn.createStatement()) {

            // 创建ROI表
            String sql = "CREATE TABLE IF NOT EXISTS roi_config (" +
                    "template_name TEXT PRIMARY KEY," +
                    "roi_data TEXT NOT NULL" +
                    ");";
            stmt.execute(sql);
            log.info("ROI数据库初始化成功：{}", imageConstants.roiConfigDbPath);
        } catch (SQLException e) {
            log.error("初始化ROI数据库失败", e);
        }
    }

    /**
     * 保存ROI到SQLite数据库
     */
    private void saveRoiToDatabase(String templateName, TemplateRoi templateRoi) {
        if (!imageConstants.roiConfigDbPath.exists()) {
            initRoiDatabase();
        }

        String url = "jdbc:sqlite:" + imageConstants.roiConfigDbPath.getAbsolutePath();
        String sql = "INSERT OR REPLACE INTO roi_config (template_name, roi_data) VALUES (?, ?)";

        try (Connection conn = DriverManager.getConnection(url);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, templateName);
            pstmt.setString(2, JSON.toJSONString(templateRoi));
            pstmt.executeUpdate();
            log.info("保存ROI到数据库成功：{}, 模板：{}", imageConstants.roiConfigDbPath, templateName);
        } catch (SQLException e) {
            log.error("保存ROI到数据库失败", e);
        }
    }

    /**
     * 从SQLite数据库读取ROI
     */
    private TemplateRoi getRoiFromDatabase(String templateName) {
        if (!imageConstants.roiConfigDbPath.exists()) {
            log.info("ROI数据库不存在：{}", imageConstants.roiConfigDbPath);
            return null;
        }

        String url = "jdbc:sqlite:" + imageConstants.roiConfigDbPath.getAbsolutePath();
        String sql = "SELECT roi_data FROM roi_config WHERE template_name = ?";

        try (Connection conn = DriverManager.getConnection(url);
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, templateName);
            ResultSet rs = pstmt.executeQuery();

            if (rs.next()) {
                String roiData = rs.getString("roi_data");
                log.info("从数据库成功读取ROI：{}, 模板：{}", imageConstants.roiConfigDbPath, templateName);
                return JSON.parseObject(roiData, TemplateRoi.class);
            }

        } catch (SQLException e) {
            log.error("从数据库读取ROI失败", e);
        }
        return null;
    }

    public void saveRoi(String templateName, TemplateRoi templateRoi) {
        // 保存到JSON文件
        File file = imageConstants.roiConfigPath;
        if (!file.exists()) {
            try {
                Files.createFile(file.toPath());
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        String content = ThreadSafeFileUtils.readFileToString(file);
        Map<String, TemplateRoi> map = JSON.parseObject(content, new TypeReference<Map<String, TemplateRoi>>() {
        });
        if (map == null) {
            map = new HashMap<>();
        }
        map.put(templateName, templateRoi);
        //写入到file
        ThreadSafeFileUtils.writeFileFromString(file, JSON.toJSONString(map));
        log.info("保存roi到文件:{}", file);

        // 同时保存到SQLite数据库
        saveRoiToDatabase(templateName, templateRoi);
    }

    public TemplateRoi getRoi(String templateName) {
        File file = imageConstants.roiConfigPath;
        if (file.exists()) {
            String content = ThreadSafeFileUtils.readFileToString(file);
            Map<String, TemplateRoi> map = JSON.parseObject(content, new TypeReference<Map<String, TemplateRoi>>() {
            });
            if (map == null) {
                map = new HashMap<>();
            }
            return map.get(templateName);
        } else {
            // JSON文件不存在时，尝试从数据库中获取
            log.info("roiConfig.json不存在，尝试从数据库获取ROI：{}", templateName);
            return getRoiFromDatabase(templateName);
        }
    }

    public File saveFailImageFile(String templateName, String imageName, Frame frame, boolean async) {
        File templateFolder = createFolder(imageConstants.failImageDBPath, templateName);
        Runnable saveTask = () -> {
            if (frame == null) {
                return;
            }
            try {
                saveImageFile(templateFolder, imageName, frame);
                SseUtils.pubMsg(SseConstants.PICTURE_SAVE, JSON.toJSONString("success"));
//                log.info("成功保存失败图片到文件:{}", templateFolder);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                SseUtils.pubMsg(SseConstants.PICTURE_SAVE, JSON.toJSONString("fail"));
            } finally {
                frame.close();
            }
        };

        if (async) {
            log.debug("异步保存图片:{}", imageName);
            CompletableFuture.runAsync(saveTask);
            return null;
        } else {
            saveTask.run();
            return new File(templateFolder, imageName + IMAGE_SUFFIX_WITH_DOT);
        }
    }

    public File screenShotFile(String imageName, Frame frame, boolean async) {
        File templateFolder = imageConstants.screenShotDBPath;
        Runnable saveTask = () -> {

            try {
                saveImageFile(templateFolder, imageName, frame);
                SseUtils.pubMsg(SseConstants.PICTURE_SAVE, JSON.toJSONString("success"));
                log.info("成功保存截图到文件:{}", templateFolder);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
                SseUtils.pubMsg(SseConstants.PICTURE_SAVE, JSON.toJSONString("fail"));
            }
        };

        if (async) {
            log.debug("异步保存图片:{}", imageName);
            CompletableFuture.runAsync(saveTask);
            return null;
        } else {
            saveTask.run();
            return new File(templateFolder, imageName + IMAGE_SUFFIX_WITH_DOT);
        }
    }

    public File getImageFile(File dstFolder, String imageName) {
        return new File(dstFolder, imageName + ImageFileManager.IMAGE_SUFFIX_WITH_DOT);
    }

    public File getImageFileOfFail(String templateName, String fileName) {
        File templateFolder = createFolder(imageConstants.failImageDBPath, templateName);
        return new File(templateFolder, fileName + ".png");
    }

    public File getImageFileOfRoi(String templateName, String fileName) {
        File templateFolder = createFolder(imageConstants.roiImageDBPath, templateName);
        return new File(templateFolder, fileName + ".png");
    }

    public String getImageFileOfFailPath(String templateName) {
        return createFolder(imageConstants.failImageDBPath, templateName).getAbsolutePath();
    }

    public String getImageFileOfRoiPath(String templateName) {
        return createFolder(imageConstants.roiImageDBPath, templateName).getAbsolutePath();
    }

    /**
     * 复制图片文件到指定位置
     *
     * @param sourcePath 原图片完整路径
     * @param destPath   目标图片完整路径
     * @throws IOException 如果复制过程中发生错误
     */
    public static void copyImage(String sourcePath, String destPath) throws IOException {
        Path source = Paths.get(sourcePath);
        Path destination = Paths.get(destPath);

        // 检查源文件是否为图片（可选）
        String sourceFile = source.getFileName().toString().toLowerCase();
        if (!sourceFile.matches(".*\\.(jpg|jpeg|png|gif|bmp)$")) {
            throw new IllegalArgumentException("源文件不是有效的图片格式: " + sourcePath);
        }
        // 创建目标目录（如果不存在）
        if (!Files.exists(destination.getParent())) {
            Files.createDirectories(destination.getParent());
        }
        // 执行复制操作
        Files.copy(source, destination);
    }


    public File saveImageFile(File dstFolder, String imageName, Frame frame) throws IOException {
        //TODO：后续考虑将图片转Base64编码存储到数据库中
        File file = getImageFile(dstFolder, imageName);
        // 可能的异常:java.lang.IllegalArgumentException:image==null!
        log.info("保存图片\"{}\"到: {}", imageName, file);
        long startMills1 = System.currentTimeMillis();
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = Java2DFrameUtils.toBufferedImage(frame);
            long startMills2 = System.currentTimeMillis();
            ImageIO.write(bufferedImage, ImageFileManager.IMAGE_SUFFIX, file);
            log.debug("图片\"{}\"转化耗时:{}s，写入耗时:{}s", imageName, (startMills2 - startMills1) / 1000.0, (System.currentTimeMillis() - startMills2) / 1000.0);
        } finally {
            // 释放BufferedImage资源
            if (bufferedImage != null) {
                bufferedImage.getGraphics().dispose();
                bufferedImage.flush();
            }
        }
        return file;
    }

}
