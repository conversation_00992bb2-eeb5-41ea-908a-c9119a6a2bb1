package com.desaysv.workserver.devices.electronic_load.base;

public class N68000ElectronicSCPICommands {
    public static final String SET_INPUT_STATE = "INPut:STATe %s";
    public static final String SET_INPUT_FUNCTION = "INPut:FUNCtion %s";

    /**-------------------------------------CC----------------------------------------------*/
    public static final String SET_CC_RANGE = "STATic:CC:RANGe %d";
    public static final String SET_CC_HIGH_LEVEL = "STATic:CC:HIGH:LEVel %f";
    public static final String SET_CC_LOW_LEVEL = "STATic:CC:LOW:LEVel %f";
    public static final String SET_CC_HIGH_SLEWRATE_RAISE = "STATic:CC:HIGH:SLEWRate:RAIse %f";
    public static final String SET_CC_HIGH_SLEWRATE_FALL = "STATic:CC:HIGH:SLEWRate:FALL %f";

    /**-------------------------------------CV----------------------------------------------*/
    public static final String SET_CV_RANGE = "STATic:CV:RANGe %d";
    public static final String SET_CV_HIGH_LEVEL = "STATic:CV:HIGH:LEVel %f";
    public static final String SET_CV_LOW_LEVEL = "STATic:CV:LOW:LEVel %f";
    public static final String SET_CV_RESPONSE = "STATic:CV: RESPonse %d";  //设置恒压响应速度:0-快速，1-中速，2-慢速
    public static final String SET_CV_PATTERN = "STATic:CV:PATTErn %d";  //设置恒压充放模式:0-放电，1-充电
    public static final String SET_CV_HIGH_SLEWRATE_RAISE = "STATic:CV:HIGH:SLEWRate:RAIse %f";
    public static final String SET_CV_HIGH_SLEWRATE_FALL = "STATic:CV:HIGH:SLEWRate:FALL %f";
    /**-------------------------------------CR----------------------------------------------*/
    public static final String SET_CR_RANGE = "STATic:CR:RANGe %d";
    public static final String SET_CR_LOW_LEVEL = "STATic:CR:LOW:LEVel %f";
    public static final String SET_CR_HIGH_SLEWRATE_RAISE = "STATic:CR:HIGH:SLEWRate:RAIse %f";
    public static final String SET_CR_HIGH_SLEWRATE_FALL = "STATic:CR:HIGH:SLEWRate:FALL %f";

    /**-------------------------------------CP----------------------------------------------*/
    public static final String SET_CP_LEVEL = "STATic:CP:level %f";
    public static final String SET_CP_RAISE = "STATic:CP:RAIse %f";
    public static final String SET_CP_FALL = "STATic:CP:FALL %f";

    /**-------------------------------------MEASure----------------------------------------------*/
    //读取负载的电流值
    public static final String MEASURE_CURRENT = "MEASure:CURRent?";
    //读取负载的电压值
    public static final String MEASURE_VOLTAGE = "MEASure:VOLTage?";
    //读取负载的功率值
    public static final String MEASURE_POWER = "MEASure:POWer?";
    //读取负载的电阻值
    public static final String MEASURE_RESISTANCE = "MEASure:RESistance?";
    //读取负载的温度值
    public static final String MEASURE_TEMPERATURE = "MEASure:TEMPerature?";

    // 用于格式化需要特定值的命令
    public static String formatCommand(String command, Object... values) {
        return String.format(command, values);
    }
}
