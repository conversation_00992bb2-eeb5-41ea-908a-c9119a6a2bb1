package com.desaysv.workserver.devices.robot.type.magician;

import com.desaysv.workserver.devices.robot.base.RobotPose;
import com.desaysv.workserver.devices.robot.type.magician.CPlusDll.DobotDll;
import com.desaysv.workserver.devices.robot.type.magician.CPlusDll.DobotDll.*;
import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

@Slf4j
public class DobotApi {
    private final List<String> commandList = Arrays.asList("X+", "X-", "Y+", "Y-", "Z+", "Z-", "R+", "R-");
    private final JOGCmd jogCmd = new JOGCmd();
    private final IntByReference ib = new IntByReference();

    private DobotResult connectDobot(String portName) {
        return DobotResult.values()[DobotDll.instance.ConnectDobot(portName, 115200, (char) 0, (char) 0)];
    }

    public boolean connect(String portName) throws DeviceOpenException {
        DobotResult ret = connectDobot(portName);
        // 开始连接
        if (ret == DobotResult.DobotConnect_NotFound) {
            throw new DeviceOpenException("Dobot魔术师机械臂端口未连接");
        } else if (ret == DobotResult.DobotConnect_Occupied) {
            throw new DeviceOpenException("Dobot魔术师机械臂端口被占用");
        } else {
            log.info("Dobot魔术师机械臂{}连接成功", portName);
        }
//        log.info("connect success code:" + ret.name());
        startDobot();
//        DobotDll.instance.SetCmdTimeout(3000);
//        DobotDll.instance.SetQueuedCmdClear();
//        DobotDll.instance.SetQueuedCmdStartExec();
//        startGetStatus();
        return true;
    }

    public void disconnect() {
        DobotDll.instance.SetQueuedCmdStopExec();
        DobotDll.instance.DisconnectDobot();
    }

    private void startDobot() {
        IntByReference ib = new IntByReference();
//        EndEffectorParams endEffectorParams = new EndEffectorParams();
//        endEffectorParams.xBias = 71.6f;
//        endEffectorParams.yBias = 0;
//        endEffectorParams.zBias = 0;
//        DobotDll.instance.SetEndEffectorParams(endEffectorParams, false, ib);
        JOGJointParams jogJointParams = new JOGJointParams();
        for (int i = 0; i < 4; i++) {
            jogJointParams.velocity[i] = 800;
            jogJointParams.acceleration[i] = 300;
        }
        DobotDll.instance.SetJOGJointParams(jogJointParams, false, ib);
        log.info("SetJOGJointParams->{}", jogJointParams);

        JOGCoordinateParams jogCoordinateParams = new JOGCoordinateParams();
        for (int i = 0; i < 4; i++) {
            jogCoordinateParams.velocity[i] = 800;
            jogCoordinateParams.acceleration[i] = 300;
        }
        DobotDll.instance.SetJOGCoordinateParams(jogCoordinateParams, false, ib);
        log.info("SetJOGCoordinateParams->{}", jogCoordinateParams);

        JOGCommonParams jogCommonParams = new JOGCommonParams();
        jogCommonParams.velocityRatio = 100;
        jogCommonParams.accelerationRatio = 100;
        DobotDll.instance.SetJOGCommonParams(jogCommonParams, false, ib);
        log.info("SetJOGCommonParams->{}", jogCommonParams);

        PTPJointParams ptpJointParams = new PTPJointParams();
        for (int i = 0; i < 4; i++) {
            ptpJointParams.velocity[i] = 800;
            ptpJointParams.acceleration[i] = 300;
        }
        DobotDll.instance.SetPTPJointParams(ptpJointParams, false, ib);
        log.info("SetPTPJointParams->{}", ptpJointParams);

        PTPCoordinateParams ptpCoordinateParams = new PTPCoordinateParams();
        ptpCoordinateParams.xyzVelocity = 800;
        ptpCoordinateParams.xyzAcceleration = 300;
        ptpCoordinateParams.rVelocity = 800;
        ptpCoordinateParams.rAcceleration = 300;
        DobotDll.instance.SetPTPCoordinateParams(ptpCoordinateParams, false, ib);
        log.info("SetPTPCoordinateParams->{}", ptpCoordinateParams);

        PTPJumpParams ptpJumpParams = new PTPJumpParams();
        ptpJumpParams.jumpHeight = 20;
        ptpJumpParams.zLimit = 180;
        DobotDll.instance.SetPTPJumpParams(ptpJumpParams, false, ib);
        log.info("SetPTPJumpParams->{}", ptpJumpParams);
        //设置零点
//        HOMEParams homeParams = new HOMEParams();
//        homeParams.x = 200;
//        homeParams.y = 200;
//        homeParams.z = 200;
//        homeParams.r = 200;
//        DobotDll.instance.SetHOMEParams(homeParams, false, ib);

        DobotDll.instance.SetCmdTimeout(3000);
        DobotDll.instance.SetQueuedCmdClear();
        DobotDll.instance.SetQueuedCmdStartExec();
    }

    private void startGetStatus() {
        //此函数调用GetPose用于定时获得dobot的实时位置
        Timer timerPos = new Timer();
        timerPos.schedule(new TimerTask() {
            public void run() {
                Pose pose = new Pose();
                DobotDll.instance.GetPose(pose);

                log.info("joint1Angle=" + pose.jointAngle[0] + "  " + "joint2Angle=" + pose.jointAngle[1] + "  "
                        + "joint3Angle=" + pose.jointAngle[2] + "  " + "joint4Angle=" + pose.jointAngle[3] + "  " + "x="
                        + pose.x + "  " + "y=" + pose.y + "  " + "z=" + pose.z + "  " + "r=" + pose.r + "  ");
            }
        }, 100, 500);
    }

    public void returnHome() {
        DobotDll.instance.SetHOMECmd(new HOMECmd(), true, ib);
        waitFinish(ib.getValue());
    }

    public void moveLine(MoveEntity moveEntity) {
        move(moveEntity, (byte) PTPMode.PTPMOVLXYZMode.getValue());
    }

    public void moveJoint(MoveEntity moveEntity) {
        move(moveEntity, (byte) PTPMode.PTPMOVJXYZMode.getValue());
    }

    public void jump(MoveEntity moveEntity) {
        move(moveEntity, (byte) PTPMode.PTPJUMPXYZMode.getValue());
    }

    public void move(MoveEntity moveEntity, byte mode) {
        IntByReference ib = new IntByReference();
        PTPCmd ptpCmd = new PTPCmd();
        ptpCmd.ptpMode = mode;
        ptpCmd.x = (float) moveEntity.getX();
        ptpCmd.y = (float) moveEntity.getY();
        ptpCmd.z = (float) moveEntity.getZ();
        ptpCmd.r = 0;
        DobotDll.instance.SetPTPCmd(ptpCmd, true, ib);
        waitFinish(ib.getValue());
    }

    private void waitFinish(int lastIndex) {
        // 如果还未完成指令队列则等待
        IntByReference intByReference = new IntByReference();
        do {
            try {
                DobotDll.instance.GetQueuedCmdCurrentIndex(intByReference);
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        } while (lastIndex > intByReference.getValue());
    }

    public void moveJog(String jogCommand) {
        byte cmd = (byte) (commandList.indexOf(jogCommand) + 1);
        jogCmd.isJoint = 0;
        jogCmd.cmd = cmd;
        DobotDll.instance.SetJOGCmd(jogCmd, false, ib);
    }

    public void stopMoveJog() {
        jogCmd.isJoint = 0;
        jogCmd.cmd = (byte) JOG.JogIdle.getValue();
        DobotDll.instance.SetJOGCmd(jogCmd, false, ib);
    }

    public RobotPose getPose() {
        Pose pose = new Pose();
        DobotDll.instance.GetPose(pose);
        RobotPose robotPose = new RobotPose();
        robotPose.setX((double) pose.x);
        robotPose.setY((double) pose.y);
        robotPose.setZ((double) pose.z);
        robotPose.setR((double) pose.r);
        return robotPose;
    }

}
