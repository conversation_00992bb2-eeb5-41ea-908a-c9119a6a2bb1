package com.desaysv.workserver.devices.robot.base;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.robot.config.RobotConfig;
import com.desaysv.workserver.devices.robot.interfaces.IDobot;
import com.desaysv.workserver.devices.robot.interfaces.IRobotDevice;
import com.desaysv.workserver.devices.robot.interfaces.IRobotWithDB;
import com.desaysv.workserver.devices.robot.interfaces.IVisionGuide;
import com.desaysv.workserver.devices.robot.type.mg400.entity.*;
import com.desaysv.workserver.devices.robot.vision.VisionGuideCalibrationData;
import com.desaysv.workserver.devices.robot.vision.VisionGuideConfig;
import com.desaysv.workserver.devices.robot.vision.VisionMatchFormula;
import com.desaysv.workserver.entity.Point2D;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.factory.DeviceFileManager;
import com.desaysv.workserver.model.RobotCoordinates;
import com.desaysv.workserver.model.SpeedCoordinates;
import com.desaysv.workserver.model.roi.CoordinatesRoi;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.SpringContextHolder;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
public abstract class DefaultDobotDevice extends DobotDevice
        implements IDobot, IRobotDevice, IRobotWithDB, IVisionGuide, TestProcessListener {

    private VisionGuideConfig visionGuideConfig;
    private ExecutorService poseMonitorExecutorService;
    private Lock lock;
    private Condition condition;
    private volatile boolean paused = false;
    private final static int MILLIMETER_PARAMETER = 10;

    public DefaultDobotDevice() {
        super();
    }

    public DefaultDobotDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        lock = new ReentrantLock(true);
        condition = lock.newCondition();
        poseMonitorExecutorService = Executors.newSingleThreadExecutor();
    }

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            TestProcessManager.addTestProcessListener(getDeviceName(), this);
            fetchVisionGuideConfig(); //FIXME：无法获取设备名字，使用回调
        }
    }

    @Override
    public boolean close() throws DeviceCloseException {
        poseMonitorExecutorService.shutdownNow();
        TestProcessManager.removeTestProcessListener(getDeviceName());
        return super.close();
    }

    @Override
    public RobotReply touch(MoveEntity moveEntity) {
        return touch(moveEntity, true, true);
    }

    public RobotReply touch(MoveEntity moveEntity, boolean autoUp) {
        return touch(moveEntity, autoUp, true);
    }

    @Override
    public RobotReply touch(MoveEntity moveEntity, boolean autoUp, boolean verbose) {
        log.info("{} {}->准备点击坐标:{}", getDeviceModel(), getDeviceName(), moveEntity);
        return smartTouch(moveEntity, autoUp, verbose);
    }

    @Override
    public RobotReply arc(ArcEntity arcEntity) {
        log.info("{} {}->画圆弧:{}", getDeviceModel(), getDeviceName(), arcEntity);
        return RobotReply.ok();
    }

    @Override
    public RobotReply swipe(List<MoveEntity> moveEntityList) {
        return swipe(moveEntityList, true);
    }

    @Override
    public RobotReply swipe(List<MoveEntity> moveEntityList, boolean autoUp) {
        log.info("{} {}->滑动坐标列表:{}", getDeviceModel(), getDeviceName(), moveEntityList);
        return smartSwipe(moveEntityList, autoUp);
    }

    @Override
    public RobotReply swipeByName(List<String> coordinateNameList, boolean noLift) {
        return swipeByName(coordinateNameList, noLift, getDeviceOperationParameter().getProject());
    }

    @Override
    public RobotReply swipeByName(List<String> coordinateNameList, boolean noLift, String projectName) {
        String message = String.format("%s %s->滑动坐标%s %s", getDeviceModel(), getDeviceName(), coordinateNameList, noLift ? "(不抬起)" : "");
        log.info(message);
        SwipeRobotCoordinates swipeRobotCoordinates = new SwipeRobotCoordinates();
        swipeRobotCoordinates.setCoordinateNameList(coordinateNameList);
        swipeRobotCoordinates.setDeviceName(getDeviceName());
        swipeRobotCoordinates.setNoLift(noLift);
        swipeRobotCoordinates.setProjectName(projectName);
        ResponseEntity<SwipeRobotCoordinates> entity = smartSwipe(swipeRobotCoordinates);
        if (entity.getStatusCode().equals(HttpStatus.OK)) {
            return RobotReply.ok(message);
        } else {
            message = String.format("%s %s->滑动失败,原因:%s", getDeviceModel(), getDeviceName(), entity);
            log.warn(message);
            return RobotReply.fail(message);
        }
    }

    @Override
    public RobotReply move(List<MoveEntity> moveEntityList) {
        log.info("{} {}->移动坐标列表:{}", getDeviceModel(), getDeviceName(), moveEntityList);
        MoveEntity startPoint = moveEntityList.get(0);
        log.info("准备移动到起点:{}", startPoint);
        RobotReply robotReply = moveLine(startPoint);
        if (robotReply.isOk()) {
            for (MoveEntity moveEntity : moveEntityList.subList(1, moveEntityList.size())) {
                robotReply = moveLine(moveEntity);
                if (!robotReply.isOk()) {
                    return robotReply;
                }
            }
        }
        return robotReply;
    }

    public RobotReply moveWithTime(List<RobotTrajectory> robotTrajectoryList) {
        log.info("动作还原开始");
        RobotReply robotReply = null;
        for (int i = 1; i < robotTrajectoryList.size(); i++) {
            RobotTrajectory prev = robotTrajectoryList.get(i - 1);
            RobotTrajectory curr = robotTrajectoryList.get(i);
            double timeDiff = getTimeDifference(prev.getTime(), curr.getTime());
            MoveEntity moveEntity = new MoveEntity();
            moveEntity.setX(curr.getX());
            moveEntity.setY(curr.getY());
            moveEntity.setZ(curr.getZ());
            moveEntity.setR(curr.getR());
            robotReply = moveLine(moveEntity);
            if (!robotReply.isOk()) {
                return robotReply;
            }
            try {
                Thread.sleep((long) (timeDiff * 1000));
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return robotReply;
    }

    void executeTrajectory(List<RobotTrajectory> path) throws InterruptedException {

    }

    double getTimeDifference(String time1, String time2) {
        return Double.parseDouble(time2.substring(1)) - Double.parseDouble(time1.substring(1));
    }

    @Override
    public RobotReply moveByName(List<String> coordinateNameList) {
        String message = String.format("%s %s->移动坐标%s", getDeviceModel(), getDeviceName(), coordinateNameList);
        log.info(message);
        List<MoveEntity> moveEntityList = new ArrayList<>();
        for (String coordinateName : coordinateNameList) {
            RobotCoordinates robotCoordinates = getCoordinatesByName(coordinateName);
            if (robotCoordinates == null) {
                return RobotReply.fail(String.format("%s %s->坐标%s不存在", getDeviceModel(), getDeviceName(), coordinateName));

            }
            moveEntityList.add(MoveEntity.fromRobotCoordinates(robotCoordinates));
        }
        return move(moveEntityList);
    }


    @Override
    public RobotReply randomTouchByName(List<String> coordinateNameList) {
        return performTouchByName(coordinateNameList, "Random");
    }

    @Override
    public RobotReply centerTouchByName(List<String> coordinateNameList) {
        return performTouchByName(coordinateNameList, "Center");
    }

    private RobotReply performTouchByName(List<String> coordinateNameList, String touchType) {
        String message = String.format("%s %s->%s点击范围%s", getDeviceModel(), getDeviceName(), touchType, coordinateNameList);
        log.info(message);
        RandomRobotCoordinates randomRobotCoordinates = new RandomRobotCoordinates();
        randomRobotCoordinates.setCoordinateNameList(coordinateNameList);
        randomRobotCoordinates.setDeviceName(getDeviceName());
        randomRobotCoordinates.setProjectName(getExecutionContext().get().getProjectName());
        ResponseEntity<RandomRobotCoordinates> entity = smartRandomTouch(randomRobotCoordinates, touchType);
        if (entity.getStatusCode().equals(HttpStatus.OK)) {
            return RobotReply.ok(message);
        } else {
            message = String.format("%s %s->%s点击失败,原因:%s", getDeviceModel(), getDeviceName(), touchType, entity);
            log.warn(message);
            return RobotReply.fail(message);
        }
    }

    /**
     * 检查安全点
     *
     * @param afterTouch 是否触摸之后
     */
    private void checkSafePoint(String projectName, CoordinatesRoi coordinatesRoi, boolean afterTouch) throws OperationFailNotification {
        if (getTouchEventMap().values().isEmpty()) {
            return;
        }
        RobotConfig robotConfig = getDeviceConfig();
        if (!robotConfig.getMonitorArea().isValid() && coordinatesRoi == null) {
            return;
        }
        log.info("机械臂移动到安全点:{}", getTouchEventMap());
        for (RobotTouchEvent touchEvent : getTouchEventMap().values()) {
            ScaledRoiRect scaledRoiRect = coordinatesRoi != null ? coordinatesRoi.getScaledRoiRect() : robotConfig.getMonitorArea().toScaledRoiRect();
            try {
                if (!afterTouch) {
                    executeSafePoint(getDeviceName(), projectName);
                    touchEvent.beforeTouch(this, scaledRoiRect);
                } else {
                    executeSafePoint(getDeviceName(), projectName);
                    OperationResult operationResult = touchEvent.afterTouch(this, scaledRoiRect);
                    if (operationResult.isFailed()) {
                        operationResult.setRetry(true);
                        operationResult.setRetryTimes(3);
                        throw new OperationFailNotification(operationResult);
                    }
                }
            } catch (FrameGrabberException e) {
                throw new OperationFailNotification(e);
            }
        }
    }

    private RobotCoordinates getCoordinates(String coordinateName, String projectName) {
        NamedRobotCoordinates namedRobotCoordinates = new NamedRobotCoordinates();
        namedRobotCoordinates.setDeviceUniqueCode(getDeviceUniqueCode());
        namedRobotCoordinates.setProjectName(projectName);
        namedRobotCoordinates.setCoordinateName(coordinateName);
        ResponseEntity<RobotCoordinates> entity = query(namedRobotCoordinates);
        return entity.getBody();
    }

    private RobotCoordinates getCoordinatesByName(String coordinateName) {
        return getCoordinates(coordinateName, getDeviceOperationParameter().getProject());
    }

    @Override
    public RobotReply touch(String coordinateName) throws OperationFailNotification {
        return touch(coordinateName, getDeviceOperationParameter().getProject());
    }

    @Override
    public RobotReply touch(String coordinateName, String projectName) throws OperationFailNotification {
        return touch(coordinateName, 0, 0, projectName);
    }

    @Override
    public RobotReply touch(SpeedCoordinates speedCoordinates) throws OperationFailNotification {
        return touch(speedCoordinates.getCoordinateName(), speedCoordinates.getSpeed(), speedCoordinates.getAcceleration(), getExecutionContext().get().getProjectName());
    }

    private RobotReply touch(String coordinateName, int speed, int acceleration, String projectName) throws OperationFailNotification {
        log.info("{} {}->准备点击坐标\"{}\"", getDeviceModel(), getDeviceName(), coordinateName);
        RobotCoordinates robotCoordinates = getCoordinates(coordinateName, projectName);
        if (robotCoordinates == null) {
            String message = String.format("%s %s->点击失败, 原因:%s", getDeviceModel(), getDeviceName(), coordinateName + "坐标名无法找到");
            log.warn(message);
            throw new OperationFailNotification(message);
        }
        CoordinatesRoi coordinatesRoi = robotCoordinates.getCoordinatesRoi();
        RobotConfig robotConfig = getDeviceConfig();
        if ((robotConfig != null
                && robotConfig.getMonitorArea().isValid() || coordinatesRoi != null)
                && getTouchEventMap().values().isEmpty()) {
            try {
                SpringContextHolder.getBeanAndExecute("imageOperationCommand", "addRobotCalibration", new Class[]{DobotDevice.class}, new Object[]{this});
            } catch (InvocationTargetException | IllegalAccessException e) {
                log.warn(e.getMessage(), e);
            }
        }
        checkSafePoint(projectName, coordinatesRoi, false);
        MoveEntity moveEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
        //如果speed和acceleration都不是0
        if (speed > 0 && acceleration > 0) {
            moveEntity.setSpeed(speed);
            moveEntity.setAcc(acceleration);
        }
        RobotReply robotReply = touch(moveEntity);
        checkSafePoint(projectName, coordinatesRoi, true);
        return robotReply;
    }

    @Override
    public RobotReply randomTouch(String coordinateName, int lowerTouchCount, int upperTouchCount) throws OperationFailNotification {
        int randomTouchCount = RandomUtil.randomInt(lowerTouchCount, upperTouchCount);
        log.info("执行随机点击坐标\"{}\"{}次", coordinateName, randomTouchCount);
        int i = 0;
        RobotReply robotReply = new RobotReply();
        while (i < randomTouchCount) {
            log.info("执行第{}次", ++i);
            robotReply = touch(coordinateName);
        }
        return robotReply;
    }

    @Override
    public RobotReply touchMore(List<String> coordinatesArray) throws OperationFailNotification {
        log.info("连续点击坐标:{}", coordinatesArray);
        for (String coordinateName : coordinatesArray) {
            touch(coordinateName);
        }
        return RobotReply.ok();
    }

    @Override
    public RobotReply circle(CirclePoint circlePoint) throws OperationFailNotification {
        return circle(circlePoint.getCoordinateName(), circlePoint.getRadius());
    }

    @Override
    public RobotReply circle(String coordinateName, Double radius) throws OperationFailNotification {
        log.info("画圆圆心{}半径{}", coordinateName, radius);
        double radiusInMm = radius * MILLIMETER_PARAMETER;
        RobotCoordinates robotCoordinates = getCoordinatesByName(coordinateName);
        if (robotCoordinates != null) {
            MoveEntity circleCenterEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
            MoveEntity circleStartEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
            circleStartEntity.setX(circleStartEntity.getX() + radiusInMm);
            MoveEntity upPoint = (MoveEntity) circleStartEntity.clone();
            upPoint.setZ(circleStartEntity.getZ() + AUTO_UP_HEIGHT);
            log.info("画圆起点: (" + circleStartEntity.getX() + ", " + circleStartEntity.getY() + ")");
            RobotReply robotReply;
            outputLog(String.format("准备移动到画圆上方坐标:%s", upPoint), false);
            robotReply = move(upPoint);
            if (robotReply.isOk()) {
                outputLog(String.format("准备移动到画圆坐标:%s", circleStartEntity), false);
                robotReply = move(circleStartEntity);
                if (robotReply.isOk()) {
                    List<MoveEntity> circleList = circleMoveListCalculate(circleCenterEntity, radiusInMm);
                    return circle(circleList);
                }
            }
            return robotReply;
        }
        throw new OperationFailNotification(String.format("数据库无法找到坐标\"%s\"", coordinateName));
    }

    public List<MoveEntity> circleMoveListCalculate(MoveEntity moveEntity, Double radius) {
        List<MoveEntity> moveEntityList = new ArrayList<>();
        MoveEntity circleMidMoveEntity = (MoveEntity) moveEntity.clone();
        circleMidMoveEntity.setY(circleMidMoveEntity.getY() + radius);
        moveEntityList.add(circleMidMoveEntity);
        MoveEntity circleEndMoveEntity = (MoveEntity) moveEntity.clone();
        circleEndMoveEntity.setY(circleMidMoveEntity.getY() - radius);
        moveEntityList.add(circleEndMoveEntity);
        return moveEntityList;
    }

    @Override
    public RobotReply quickSwipe(SwipeQuicklyPoint swipeQuicklyPoint) throws OperationFailNotification {
        return quickSwipe(swipeQuicklyPoint.getCoordinateName(), swipeQuicklyPoint.getDirection(), swipeQuicklyPoint.getDynamics());
    }

    @Override
    public RobotReply quickSwipe(String coordinateName, Integer direction, Integer dynamics) throws OperationFailNotification {

        log.info("快速滑动{}方向{}", coordinateName, direction);
        RobotCoordinates robotCoordinates = getCoordinatesByName(coordinateName);
        if (robotCoordinates != null) {
            MoveEntity swipeStartEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
            swipeStartEntity.setCp(100);//平滑过渡为100
            MoveEntity upPoint = (MoveEntity) swipeStartEntity.clone();
            upPoint.setZ(swipeStartEntity.getZ() + AUTO_UP_HEIGHT);
            log.info("滑动起点: (" + swipeStartEntity.getX() + ", " + swipeStartEntity.getY() + ")");
            RobotReply robotReply;
            outputLog(String.format("准备移动到上方坐标:%s", upPoint), false);
            robotReply = move(upPoint);
            if (robotReply.isOk()) {
                List<MoveEntity> quickSwipeList = quickSwipeMoveListCalculate(swipeStartEntity, direction, dynamics);
                return moveLineQuickly(quickSwipeList);
            }
            return robotReply;
        }
        throw new OperationFailNotification(String.format("数据库无法找到坐标\"%s\"", coordinateName));
    }

    public List<MoveEntity> quickSwipeMoveListCalculate(MoveEntity moveEntity, Integer direction, Integer dynamics) {
        List<MoveEntity> moveEntityList = new ArrayList<>();
        moveEntityList.add(moveEntity);
        java.awt.geom.Point2D.Double point2D = calculateQuickSwipePosition(moveEntity.getX(), moveEntity.getY(), direction, dynamics);
        log.info("滑动终点: (" + point2D.x + ", " + point2D.y + ")");
        MoveEntity swipeEndPoint = (MoveEntity) moveEntity.clone();
        swipeEndPoint.setX(point2D.x);
        swipeEndPoint.setY(point2D.y);
        moveEntityList.add(swipeEndPoint);
        MoveEntity endPoint = (MoveEntity) swipeEndPoint.clone();
        endPoint.setZ(endPoint.getZ() + AUTO_UP_HEIGHT);
        log.info("移动到滑动终点上方:{}", endPoint);
        moveEntityList.add(endPoint);
        return moveEntityList;
    }

    public static java.awt.geom.Point2D.Double calculateQuickSwipePosition(double x, double y, int direction, int effectRatio) {
        int swipeLength = 10 * effectRatio;
        double offsetX = ((direction - 1) % 3 - 1) * swipeLength;
        double offsetY = (direction <= 3) ? swipeLength : (direction <= 6) ? 0 : -swipeLength;
        double newX = x + offsetX;
        double newY = y + offsetY;
        return new java.awt.geom.Point2D.Double(newX, newY);
    }

    @Override
    public RobotReply longTouch(LongTouchRobotCoordinate longTouchRobotCoordinate) throws OperationFailNotification {
        return longTouch(longTouchRobotCoordinate.getCoordinateName(), longTouchRobotCoordinate.getTime());
    }

    @Override
    public RobotReply longTouch(String coordinateName, String timeWithUnit) throws OperationFailNotification {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(timeWithUnit);
        if (seconds != null) {
            if (seconds > 0) {
                return longTouch(coordinateName, seconds * 1000);
            } else {
                return RobotReply.ok(String.format("长按时间为0:%s", timeWithUnit));
            }
        }
        throw new OperationFailNotification(String.format("时间单位解析错误:%s", timeWithUnit));
    }

    private RobotReply handleTouch(String coordinateName, String projectName, boolean isPress) throws OperationFailNotification {
        log.info("{} {}->准备{}坐标\"{}\"", getDeviceModel(), getDeviceName(), isPress ? "按住" : "抬起", coordinateName);
        RobotCoordinates robotCoordinates = getCoordinates(coordinateName, projectName);
        if (robotCoordinates == null) {
            String message = String.format("%s %s->点击失败, 原因:%s", getDeviceModel(), getDeviceName(), coordinateName + "坐标名无法找到");
            log.warn(message);
            throw new OperationFailNotification(message);
        }
        MoveEntity moveEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
        return isPress ? touch(moveEntity, false, true) : releaseRobot(moveEntity);
    }

    @Override
    public RobotReply pressTouch(String coordinateName) throws OperationFailNotification {
        return handleTouch(coordinateName, getExecutionContext().get().getProjectName(), true);
    }

    @Override
    public RobotReply releaseTouch(String coordinateName) throws OperationFailNotification {
        return handleTouch(coordinateName, getExecutionContext().get().getProjectName(), false);
    }

    @Override
    public RobotReply pressTouch(String coordinateName, String projectName) throws OperationFailNotification {
        return handleTouch(coordinateName, projectName, true);
    }

    @Override
    public RobotReply releaseTouch(String coordinateName, String projectName) throws OperationFailNotification {
        return handleTouch(coordinateName, projectName, false);
    }

    private RobotReply releaseRobot(MoveEntity moveEntity) {
        MoveEntity upPoint = (MoveEntity) moveEntity.clone();
        upPoint.setZ(moveEntity.getZ() + AUTO_UP_HEIGHT);
        RobotReply robotReply;
        //移动到上方
        outputLog(String.format("准备移动到上方坐标:%s", upPoint), true);
        robotReply = move(upPoint);
        return robotReply;
    }

    @Override
    public RobotReply longTouch(String coordinateName, float milliseconds) throws OperationFailNotification {
        log.info("长按坐标{} {}毫秒", coordinateName, milliseconds);
        RobotCoordinates robotCoordinates = getCoordinatesByName(coordinateName);
        if (robotCoordinates != null) {
            MoveEntity moveEntity = MoveEntity.fromRobotCoordinates(robotCoordinates);
            return smartTouch(moveEntity, milliseconds, true, true);
        }
        throw new OperationFailNotification(String.format("数据库无法找到坐标\"%s\"", coordinateName));
    }


    @Override
    public RobotReply slideMore(List<String> coordinatesArray) throws OperationFailNotification {
        for (String coordinateName : coordinatesArray) {
            RobotCoordinates coordinates = getCoordinatesByName(coordinateName);
            if (coordinates == null) {
                throw new OperationFailNotification(String.format("数据库无法找到坐标\"%s\"", coordinateName));
            }
        }
        return swipeByName(coordinatesArray);
    }

    @Override
    public RobotReply slideMoreWithoutLift(List<String> coordinatesArray) throws OperationFailNotification {
        for (String coordinateName : coordinatesArray) {
            RobotCoordinates coordinates = getCoordinatesByName(coordinateName);
            if (coordinates == null) {
                throw new OperationFailNotification(String.format("数据库无法找到坐标\"%s\"", coordinateName));
            }
        }
        return swipeByName(coordinatesArray, true);
    }

    @Override
    public boolean visionTouch(PointInt pixelPoint) {
        if (visionGuideConfig == null) {
            log.warn("机械臂视觉引导校准数据缺失，请先导入");
            return false;
        }
        Point2D robotPoint = VisionMatchFormula.transferPixelPointToRobot(pixelPoint, visionGuideConfig);
        log.info("{} {}->视觉引导点击{}->{}，视觉平面高度:{}", getDeviceModel(), getDeviceName(), pixelPoint,
                robotPoint, visionGuideConfig.getVisionGuideZ());
        MoveEntity moveEntity = new MoveEntity();
        moveEntity.setX(robotPoint.getX());
        moveEntity.setY(robotPoint.getY());
        moveEntity.setZ(visionGuideConfig.getVisionGuideZ());
        touch(moveEntity);
        return true;
    }

    @Override
    public boolean setVisionGuideCalibrationData(VisionGuideCalibrationData visionGuideCalibrationData) {
        visionGuideConfig = DeviceFileManager.getInstance().writeVisionGuideCalibrationData(visionGuideCalibrationData);
        log.info("{} {}->设置机械臂视觉引导校准数据:{}", getDeviceModel(), getDeviceName(), visionGuideConfig);
        return true;
    }

    @Override
    public boolean setVisionGuideZ(double visionGuideZ) {
        visionGuideConfig = DeviceFileManager.getInstance().writeVisionGuideZ(visionGuideZ);
        log.info("{} {}->设置机械臂视觉引导校准数据:{}", getDeviceModel(), getDeviceName(), visionGuideConfig);
        return true;
    }

    @Override
    public VisionGuideConfig fetchVisionGuideConfig() {
        visionGuideConfig = DeviceFileManager.getInstance().readVisionGuideData();
        log.info("{} {}->导入机械臂视觉引导校准数据:{}", getDeviceModel(), getDeviceName(), visionGuideConfig);
        return visionGuideConfig;
    }

    public void enablePoseMonitor() {
        poseMonitorExecutorService.submit(() -> {
            while (!poseMonitorExecutorService.isShutdown()) {
                lock.lock(); // 获取锁
                try {
                    // 如果 paused 为 true，则等待
                    while (paused) {
                        condition.await(); // 等待
                    }
                    RobotPose fetchFeedbackData = fetchFeedbackData();
                    SseUtils.pubMsg(getDeviceName(), JSON.toJSONString(fetchFeedbackData));
//                    System.out.println("位姿打印:" + fetchFeedbackData);

                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException ignored) {

                } finally {
                    lock.unlock();
                }
            }
        });
    }


    // 暂停循环
    public void pausePoseMonitor() {
        lock.lock(); // 获取锁
        try {
            paused = true; // 标记为暂停
        } finally {
            lock.unlock(); // 释放锁
        }
    }

    // 恢复循环
    public void resumePoseMonitor() {
        lock.lock(); // 获取锁
        try {
            paused = false; // 标记为恢复
            condition.signal(); // 唤醒等待线程
        } finally {
            lock.unlock(); // 释放锁
        }
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        removeTouchEvent(DobotDevice.smartTouchKey);
    }

}
