package com.desaysv.workserver.devices.robot.interfaces;

import com.desaysv.workserver.devices.robot.vision.VisionGuideCalibrationData;
import com.desaysv.workserver.devices.robot.vision.VisionGuideConfig;
import com.desaysv.workserver.entity.PointInt;

/**
 * 机械臂视觉引导接口
 */
public interface IVisionGuide {

    /**
     * 通过视觉引导机械臂点击
     *
     * @param pixelPoint 像素点坐标
     * @return
     */
    boolean visionTouch(PointInt pixelPoint);

    /**
     * 设置机械臂视觉校准数据
     *
     * @param visionCalibrationData 机械臂视觉校准数据
     * @return
     */
    boolean setVisionGuideCalibrationData(VisionGuideCalibrationData visionCalibrationData);

    boolean setVisionGuideZ(double visionGuideZ);

    VisionGuideConfig fetchVisionGuideConfig();

}
