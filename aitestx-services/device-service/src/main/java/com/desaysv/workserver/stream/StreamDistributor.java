package com.desaysv.workserver.stream;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-15 10:09
 * @description :
 * @modified By :
 * @since : 2022-4-15
 */
@Slf4j
@Component
@Lazy
public class StreamDistributor {
    private final Map<String, StreamProducer> streamProducerMap = new ConcurrentHashMap<>();

    public boolean isDistributed(String key) {
        return streamProducerMap.containsKey(key);
    }

    public StreamProducer getStreamProducer(String key) {
        return streamProducerMap.get(key);
    }

    synchronized public void register(String key, StreamProducer streamProducer) {
        streamProducerMap.put(key, streamProducer);
    }

    synchronized public void unregister(String key) {
        streamProducerMap.remove(key);
    }

    public void unregisterAll() {
        streamProducerMap.clear();
    }
}
