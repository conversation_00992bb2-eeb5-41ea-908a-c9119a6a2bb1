package com.desaysv.workserver.finder;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.art_daq.ArtDaqApi;
import com.desaysv.workserver.devices.daq.art_daq.USB3200N;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * DAQ设备查找器
 */
@Component
@Slf4j
@Lazy
public class DaqDeviceFinder {

    /**
     * 查找所有DAQ设备(只包括USB3200N)
     *
     * @param deviceModel 设备型号
     * @return 所有DAQ设备
     */
    public List<Device> findAllDaqDevices(String deviceModel) {
        List<Device> devices = new ArrayList<>();

        if (!DeviceModel.Daq.USB3200N.equals(deviceModel)) {
            log.warn("不支持的设备型号: {}", deviceModel);
            return devices;
        }

        try {
            // 直接使用 ArtDaqApi 获取设备名称，避免依赖未初始化的 USB3200N 实例
            ArtDaqApi api = new ArtDaqApi();
            String deviceNamesStr = api.getDeviceName();
            log.info("获取到的设备名称字符串: '{}'", deviceNamesStr);

            if (deviceNamesStr == null || deviceNamesStr.isEmpty()) {
                log.warn("未找到任何 USB3200N 设备");
                return devices;
            }

            // 分割设备名称并过滤空值
            String[] deviceNames = deviceNamesStr.split(",");
            for (int i = 0; i < deviceNames.length; i++) {
                String name = deviceNames[i].trim();
                if (!name.isEmpty()) {
                    Device device = new USB3200N(new DeviceOperationParameter());
                    device.setDeviceName(name);
                    device.setAliasName(DeviceModel.Daq.USB3200N + "#" + (i + 1));
                    device.setDeviceUniqueCode(name);
                    device.setDeviceModel(DeviceModel.Daq.USB3200N);
                    devices.add(device);
                    log.debug("找到设备: {}", name);
                }
            }
        } catch (Exception e) {
            log.error("查找设备时发生异常: ", e);
        }

        log.info("共找到 {} 个 USB3200N 设备", devices.size());
        return devices;
    }
}