package com.desaysv.workserver.common.port;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.config.DeviceConfig;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.exceptions.serial.SerialExceptions;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.StrUtils;
import com.fazecast.jSerialComm.SerialPort;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 16:26
 * @description :
 * @modified By :
 * @since : 2022-5-6
 */
@Getter
@Setter
@Slf4j
public abstract class PortDevice<T extends DeviceConfig> extends ConfigurableDevice<T> {

    private static final Set<String> occupiedPorts = new HashSet<>();
    private static Integer DEFAULT_INTERVAL = 500;

    @Getter
    @JSONField(serialize = false)
    private SerialPort serialPort;

    public static boolean isOccupied(String portNumber) {
        return occupiedPorts.contains(portNumber);
    }

    public PortDevice() {
        this(new DeviceOperationParameter());
    }

    public PortDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        String portName = getDevicePortName();
        try {
            if (isOccupied(portName)) {
                log.info("串口已打开");
                throw new DeviceOpenRepeatException();
            } else {
                DeviceOperationParameter operationParameter = getDeviceOperationParameter();
                PortConfig portConfig = new PortConfig(getBaudRate());
                portConfig.setParity(operationParameter.getParity());
                serialPort = SerialUtils.openPort(portName, portConfig);
                log.info("串口打开成功:{}", serialPort.getSystemPortName());
                occupiedPorts.add(getDeviceName());
            }
            return true;
        } catch (SerialExceptions.NotSerialPort | SerialExceptions.PortOpenFail e) {
            throw new DeviceOpenException(e.toString());
        }
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        SerialUtils.closePort(serialPort);
        occupiedPorts.remove(getDeviceName());
        log.info("串口关闭成功:{}", getDeviceName());
        return true;
    }

    public String sendAndReceiveString(String command) throws DeviceSendException {
        try {
            byte[] rawData = query((command + "\n").getBytes(), false);
            return new String(rawData).trim();
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

    /**
     * 发送消息
     *
     * @param message 消息
     * @param isHex   是否为十六进制
     * @return
     * @throws DeviceSendException 设备发送异常
     */
    public boolean send(String message, boolean isHex) throws DeviceSendException {
        if (isHex) {
            return send(SerialUtils.hexStringToByteArray(message));
        } else {
            return send(message);
        }
    }

    /**
     * 以字符串形式发送
     *
     * @param message 字符串形式消息
     * @return
     * @throws DeviceSendException 设备发送异常
     */
    public boolean send(String message) throws DeviceSendException {
        if (isSimulated()) {
            log.info("{}模拟发送:{}", getDeviceName(), message.trim());
        } else {
            log.info("{}发送:{}", serialPort.getSystemPortName(), message.trim());
        }
        return send(message.getBytes(), true);
    }

    public boolean send(byte[] message) throws DeviceSendException {
        if (isSimulated()) {
            log.info("{}模拟发送:{}", getDeviceName(), StrUtils.getHexStringWithBlank(message));
        } else {
            log.info("{}发送:{}", serialPort.getSystemPortName(), StrUtils.getHexStringWithBlank(message));
        }
        return send(message, true);
    }

    public boolean send(byte[] message, boolean terminator) throws DeviceSendException {
        try {
            boolean isOk;
            if (isSimulated()) {
                isOk = true;
            } else {
                if (serialPort == null) {
                    log.warn("串口未初始化，无法发送");
                    throw new DeviceSendException("串口未初始化");
                }

                if (terminator) {
                    byte[] newBytes = new byte[message.length + 1];
                    System.arraycopy(message, 0, newBytes, 0, message.length);
                    newBytes[message.length] = 10;
                    message = newBytes;
                }
//            if (fromString) {
//                log.info("{}发送:{}({})", serialPort.getSystemPortName(), new String(message).trim(), StrUtils.getHexStringWithBlank(message));
//            } else {
//                log.info("{}发送:{}", serialPort.getSystemPortName(), StrUtils.getHexStringWithBlank(message));
//            }
                isOk = SerialUtils.sendToPort(serialPort, message);
            }
            try {
                Thread.sleep(DEFAULT_INTERVAL);
            } catch (InterruptedException e) {
                throw new DeviceSendException(e);
            }
            return isOk;
        } catch (SerialExceptions.SendDataToSerialPortFailure e) {
            throw new DeviceSendException(e);
        } finally {
            sendCompleted(message);
        }
    }


    public byte[] query(byte[] command, boolean terminator) throws DeviceSendException {
        try {
            // 1. 发送查询指令
            boolean sendSuccess = send(command, terminator);
            if (!sendSuccess) {
                throw new DeviceSendException("指令发送失败");
            }

            // 2. 初始化读取参数
            InputStream inputStream = serialPort.getInputStream();
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            byte[] readBuffer = new byte[1024];
            long startTime = System.currentTimeMillis();
            int timeout = 3000; // 3秒超时

            // 3. 循环读取响应数据
            while (System.currentTimeMillis() - startTime < timeout) {
                int available = inputStream.available();
                if (available > 0) {
                    int bytesRead = inputStream.read(readBuffer, 0, Math.min(available, readBuffer.length));
                    buffer.write(readBuffer, 0, bytesRead);

                    // 根据终止符判断是否结束（例如遇到换行符）
                    if (terminator && buffer.toString().endsWith("\n")) {
                        break;
                    }
                }
                Thread.sleep(50); // 适当间隔减少CPU占用
            }

            // 4. 处理超时情况
            if (buffer.size() == 0) {
                throw new DeviceSendException("设备响应超时");
            }

            return buffer.toByteArray();
        } catch (Exception e) {
            throw new DeviceSendException("数据接收失败");
        }
    }

    public boolean sendWithInterval(String message, boolean isHex, int interval) throws DeviceSendException {
        boolean isOk = send(message, isHex);
        try {
            Thread.sleep(interval);
        } catch (InterruptedException e) {
            throw new DeviceSendException(e);
        }
        return isOk;
    }

    //TODO:考虑加注解标识它是客户端的调用方法
    public OperationResult send(MessageText messageText) {
        OperationResult operationResult = new OperationResult();
        try {
            send(messageText.getSendText(), messageText.isHex());
            return operationResult.ok();
        } catch (DeviceSendException e) {
            return operationResult.fail(e.getMessage());
        }
    }

    public void sendCompleted(byte[] message) {

    }

    public OperationResult batchSend(PackageMessages packageMessages) {
        List<MessageText> messageTextList = packageMessages.getMessageTexts();
        DEFAULT_INTERVAL = packageMessages.getInterval();
        OperationResult operationResult = new OperationResult();
        for (MessageText messageText : messageTextList) {
            try {
                send(messageText.getSendText(), messageText.isHex());
            } catch (DeviceSendException e) {
                return operationResult.fail(e.getMessage());
            }
        }
        return operationResult.ok();
    }
}
