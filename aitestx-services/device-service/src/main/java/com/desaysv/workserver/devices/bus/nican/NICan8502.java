package com.desaysv.workserver.devices.bus.nican;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public class NICan8502 extends NICan {
    public NICan8502() {
        this(new DeviceOperationParameter());
    }

    public NICan8502(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public int getMaxChannelCount() {
        return 2;
    }

    @Override
    public long getLatestCanTimestamp(Integer deviceChannel) {
        return 0;
    }

    @Override
    public Object getReceiveBuffer() {
        return null;
    }

    @Override
    public Object readFromTail(Object buffer, int index) {
        return null;
    }

    @Override
    public int getDataFrameId(Object data) {
        return 0;
    }

    @Override
    public long getTimestamp(Object data) {
        return 0;
    }

    @Override
    public byte[] getDataBytes(Object data) {
        return new byte[0];
    }

    @Override
    public boolean isBufferEmpty(Object buffer) {
        return false;
    }

    @Override
    public int getBufferSize(Object buffer) {
        return 0;
    }

    @Override
    public void sendFlowControlFrame(CanMessage canMessage, byte[] flowControlFrame) throws BusError {

    }

    @Override
    protected boolean isValidFirstFrame(Object data) {
        return false;
    }

    @Override
    protected boolean isValidFlowControlFrame(Object data, int receiveId, Object msgData) {
        return false;
    }

    @Override
    protected boolean isContinueFrame(Object data) {
        return false;
    }

    @Override
    protected boolean isAbortFrame(Object data) {
        return false;
    }

    @Override
    public int getChannelFromData(Object data) {
        return 0;
    }

    @Override
    public String checkReplyData(Integer deviceChannel, String messageId, String byteData) {
        return "";
    }

    @Override
    public String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError {
        return "";
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.NI_CAN_8502;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }
}
