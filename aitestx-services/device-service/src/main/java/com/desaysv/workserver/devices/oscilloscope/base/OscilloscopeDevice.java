package com.desaysv.workserver.devices.oscilloscope.base;

import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.Getter;

/**
 * 示波器设备
 */
public abstract class OscilloscopeDevice extends Device {
    @Getter
    private final String deviceType = DeviceType.DEVICE_OSCILLOSCOPE;

    public OscilloscopeDevice() {
    }

    public OscilloscopeDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return true;
    }

}
