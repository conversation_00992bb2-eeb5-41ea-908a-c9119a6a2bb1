package com.desaysv.workserver.common.utils.dump;

import org.usb4java.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 18:41
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
public class UsbUtils {

    public static void dumpConfigurationDescriptors(final Device device,
                                                    final int numConfigurations) {
        for (byte i = 0; i < numConfigurations; i += 1) {
            final ConfigDescriptor descriptor = new ConfigDescriptor();
            final int result = LibUsb.getConfigDescriptor(device, i, descriptor);
            if (result < 0) {
                throw new LibUsbException("Unable to read config descriptor",
                        result);
            }
            try {
                System.out.println(descriptor.dump().replaceAll("(?m)^",
                        "  "));
            } finally {
                // Ensure that the config descriptor is freed
                LibUsb.freeConfigDescriptor(descriptor);
            }
        }
    }


    public static void dumpDevice(final Device device) {
        // Dump device address and bus number
        final int address = LibUsb.getDeviceAddress(device);
        final int busNumber = LibUsb.getBusNumber(device);
        System.out.printf("Device %03d/%03d%n", busNumber, address);

        // Dump port number if available
        final int portNumber = LibUsb.getPortNumber(device);
        if (portNumber != 0)
            System.out.println("Connected to port: " + portNumber);

        // Dump parent device if available
        final Device parent = LibUsb.getParent(device);
        if (parent != null) {
            final int parentAddress = LibUsb.getDeviceAddress(parent);
            final int parentBusNumber = LibUsb.getBusNumber(parent);
            System.out.printf("Parent: %03d/%03d%n",
                    parentBusNumber, parentAddress);
        }

        // Dump the device speed
        System.out.println("Speed: "
                + DescriptorUtils.getSpeedName(LibUsb.getDeviceSpeed(device)));

        // Read the device descriptor
        final DeviceDescriptor descriptor = new DeviceDescriptor();
        int result = LibUsb.getDeviceDescriptor(device, descriptor);
        if (result < 0) {
            throw new LibUsbException("Unable to read device descriptor",
                    result);
        }

        // Try to open the device. This may fail because user has no
        // permission to communicate with the device. This is not
        // important for the dumps, we are just not able to resolve string
        // descriptor numbers to strings in the descriptor dumps.
        DeviceHandle handle = new DeviceHandle();
        result = LibUsb.open(device, handle);
        if (result < 0) {
            System.out.printf("Unable to open device: %s. "
                            + "Continuing without device handle.%n",
                    LibUsb.strError(result));
            handle = null;
        }

        // Dump the device descriptor
        System.out.print(descriptor.dump(handle));

        // Dump all configuration descriptors
        dumpConfigurationDescriptors(device, descriptor.bNumConfigurations());

        // Close the device if it was opened
        if (handle != null) {
            LibUsb.close(handle);
        }

    }

    public static DeviceList listUsbDevice() {
        Context context = new Context();
        int result = LibUsb.init(context);
        if (result < 0) {
            throw new LibUsbException("Unable to initialize libusb", result);
        }
        DeviceList deviceList = new DeviceList();
        result = LibUsb.getDeviceList(context, deviceList);
        if (result < 0) {
            throw new LibUsbException("Unable to get device list", result);
        }
        try {
            for (Device device : deviceList) {
                int address = LibUsb.getDeviceAddress(device);
                int portNumber = LibUsb.getPortNumber(device);
                System.out.println("portNumber:" + portNumber);
                int busNumber = LibUsb.getBusNumber(device);
                DeviceDescriptor descriptor = new DeviceDescriptor();
                result = LibUsb.getDeviceDescriptor(device, descriptor);
                if (result < 0) {
                    throw new LibUsbException(
                            "Unable to read device descriptor", result);
                }
                System.out.format(
                        "Bus %03d, Device %03d: Vendor %04x, Product %04x%n",
                        busNumber, address, descriptor.idVendor(),
                        descriptor.idProduct());
                System.out.format(
                        "Bus %03d, Device %03d: Vendor %d, Product %d%n",
                        busNumber, address, descriptor.idVendor(),
                        descriptor.idProduct());
//                dumpDevice(device);
            }
        } finally {
            // Ensure the allocated device deviceList is freed
            LibUsb.freeDeviceList(deviceList, true);
        }
        LibUsb.exit(context);
        return deviceList;
    }

    public static void main(String[] args) {
        UsbUtils.listUsbDevice();
    }
}
