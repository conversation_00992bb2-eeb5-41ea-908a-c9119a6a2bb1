package com.desaysv.workserver.common.utils;

import com.desaysv.workserver.exceptions.device.DeviceSendException;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 线程安全的队列命令执行器
 */
@Slf4j
public class QueueCommandRunner {

    private final CommandRunner commandRunner;
    private final BlockingQueue<String> commandQueue = new LinkedBlockingQueue<>(1);
    private final ReentrantLock executionLock = new ReentrantLock();

    public QueueCommandRunner(CommandRunner commandRunner) {
        this.commandRunner = commandRunner;
    }

    /**
     * 获取当前待执行的命令
     *
     * @return 待执行的命令，如果没有则返回null
     */
    public String remain() {
        return commandQueue.peek();
    }

    /**
     * 发送命令并等待执行完成
     *
     * @param command 要执行的命令
     * @param receive 是否需要接收返回值
     * @return 命令执行结果
     * @throws DeviceSendException 发送异常
     */
    public Object sendCommand(String command, boolean receive) throws DeviceSendException {
        if (command == null) {
            throw new IllegalArgumentException("菊水电源命令不能为空");
        }

        try {
            //等待上一条运行完成，阻塞
            log.info("发送菊水电源队列命令: {}", command);
            commandQueue.put(command);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("发送菊水电源队列命令被中断: {}", command, e);
            throw new DeviceSendException("菊水电源等待命令队列时被中断");
        }

        executionLock.lock();
        try {
            // 确保当前执行的是我们的命令
            if (!command.equals(commandQueue.peek())) {
                throw new DeviceSendException("菊水电源队列命令同步错误");
            }

//            log.info("正在执行菊水电源队列命令: {}", command);
            Object result = null;
            try {
                if (receive) {
                    result = commandRunner.sendAndReceive(command);
                } else {
                    commandRunner.send(command);
                }
                return result;
            } finally {
                String removedCommand = commandQueue.poll();
                if (removedCommand != null) {
                    log.info("完成菊水电源队列命令: {}", removedCommand);
                }
            }
        } finally {
            executionLock.unlock();
        }
    }
}