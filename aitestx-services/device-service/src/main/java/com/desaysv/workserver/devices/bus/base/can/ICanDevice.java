package com.desaysv.workserver.devices.bus.base.can;

import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.interfaces.IBusDevice;

public interface ICanDevice extends IBusDevice {

    OperationResult sendPtsCanMessage(Integer deviceChannel, CanPtsMessage message) throws BusError;

    OperationResult comparisonCanMessage(Integer deviceChannel, CanComparison canComparison) throws BusError;

    OperationResult inspectCanMessage(Integer deviceChannel, CanMessageInspectorRequest canMessageInspectorRequest) throws BusError;

    OperationResult inspectCanSignal(Integer deviceChannel, CanSignalInspectorRequest canSignalInspectorRequest) throws BusError;

    byte[]  readDataByIdHex(int deviceChannel, int targetCanId, boolean isCanFd, long timeoutMilliseconds) throws TSCanException, InterruptedException;

}
