package com.desaysv.workserver.finder;

import Automation.BDaq.DeviceInformation;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.soundcard.AuxInSoundDevice;
import com.desaysv.workserver.devices.soundcard.USB4704;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.sound.sampled.*;
import java.util.ArrayList;
import java.util.List;

@Component
@Lazy
@Slf4j
public class SoundDeviceFinder {
    public List<Device> findAllSoundOutputDevices() {
        List<Device> devices = new ArrayList<>();
        log.info("查找所有声音输出设备");

        // 获取所有的音频设备信息
        Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
        int deviceIndex = 0;
        // 遍历所有音频设备，查找输出设备（扬声器、耳机等）
        for (Mixer.Info mixerInfo : mixerInfos) {
            Mixer mixer = AudioSystem.getMixer(mixerInfo);
            Line.Info[] sourceLineInfos = mixer.getSourceLineInfo();

            // 如果存在输出设备
            if (sourceLineInfos.length > 0) {
                try {
                    // 遍历输出设备的每个Line，查找支持SourceDataLine的设备
                    for (Line.Info lineInfo : sourceLineInfos) {
                        if (lineInfo instanceof DataLine.Info) {
                            DataLine.Info dataLineInfo = (DataLine.Info) lineInfo;
                            if (SourceDataLine.class.isAssignableFrom(dataLineInfo.getLineClass())) {
                                String simpleSpeakerName = mixerInfo.getName();
                                log.info("查找到声音输出设备: {}", simpleSpeakerName);
                                // 只保留名称中包含 "Audio" 的设备
                                if (!simpleSpeakerName.contains("Audio")) {
                                    continue;
                                }
                                // 只提取包含括号的部分，并截取括号后面的内容
                                int bracketIndex = simpleSpeakerName.indexOf('(');
                                if (bracketIndex != -1) {
                                    simpleSpeakerName = simpleSpeakerName.substring(bracketIndex + 1).replaceAll("\\)", "");
                                }

                                AuxInSoundDevice device = new AuxInSoundDevice();
                                // 设置设备的相关属性
                                device.setDeviceName(simpleSpeakerName);
                                deviceIndex++;
                                device.setAliasName("Speaker#" + deviceIndex);
                                device.setDeviceUniqueCode("" + deviceIndex);
                                device.setDeviceOperationParameter(new DeviceOperationParameter());
                                devices.add(device);
                            }
                        }
                    }
                } catch (SecurityException se) {
                    log.warn("访问设备被拒绝: {}", mixerInfo.getName(), se);
                } catch (Exception e) {
                    log.warn("访问设备时出现未知错误: {}", mixerInfo.getName(), e);
                }
            }
        }

        return devices;
    }

    public List<Device> findAllSoundInputDevices() {
        List<Device> devices = new ArrayList<>();
        log.info("查找所有麦克风设备");

        // 获取所有的音频设备信息
        Mixer.Info[] mixerInfos = AudioSystem.getMixerInfo();
        int deviceIndex = 0;
        // 遍历所有音频设备，查找输入设备（麦克风）
        for (Mixer.Info mixerInfo : mixerInfos) {
            Mixer mixer = AudioSystem.getMixer(mixerInfo);
            Line.Info[] targetLineInfos = mixer.getTargetLineInfo();

            // 如果存在输入设备
            if (targetLineInfos.length > 0) {
                try {
                    // 遍历输入设备的每个Line，查找支持TargetDataLine的设备
                    for (Line.Info lineInfo : targetLineInfos) {
                        if (lineInfo instanceof DataLine.Info) {

                            DataLine.Info dataLineInfo = (DataLine.Info) lineInfo;
                            if (TargetDataLine.class.isAssignableFrom(dataLineInfo.getLineClass())) {
                                String simpleMicrophoneName = mixerInfo.getName();
                                log.info("查找到麦克风设备: {}", simpleMicrophoneName);
                                // 只保留名称中包含 "KT USB Audio" 的设备
                                if (!simpleMicrophoneName.contains("Audio")) {
                                    continue;
                                }
                                // 只提取包含括号的部分，并截取括号后面的内容
                                int bracketIndex = simpleMicrophoneName.indexOf('(');
                                if (bracketIndex != -1) {
                                    simpleMicrophoneName = simpleMicrophoneName.substring(bracketIndex + 1).replaceAll("\\)", "");
                                }

                                AuxInSoundDevice device = new AuxInSoundDevice();
                                // 设置设备的相关属性
                                device.setDeviceName(simpleMicrophoneName);
                                deviceIndex++;
                                device.setAliasName("Microphone#" + deviceIndex);
                                device.setDeviceUniqueCode("" + deviceIndex);
                                device.setDeviceOperationParameter(new DeviceOperationParameter());
                                devices.add(device);
                            }
                        }
                    }
                } catch (SecurityException se) {
                    log.warn("访问设备被拒绝: {}", mixerInfo.getName(), se);
                } catch (Exception e) {
                    log.warn("访问设备时出现未知错误: {}", mixerInfo.getName(), e);
                }
            }
        }

        return devices;
    }

    public List<Device> findUSB4704Devices() {
        List<Device> devices = new ArrayList<>();
        log.info("开始查找USB-4704设备");
        USB4704 usb4704 = new USB4704(new DeviceOperationParameter());
        try {
            // 使用研华SDK枚举设备
            List<DeviceInformation> advDevices = usb4704.listSupportedDevices();

            int deviceIndex = 0;
            for (DeviceInformation devInfo : advDevices) {
                // 筛选USB-4704设备
                if (devInfo.toString().contains("USB-4704")) {
                    USB4704 device = new USB4704(new DeviceOperationParameter());

                    // 配置设备参数
                    device.setDeviceName(devInfo.toString());
                    device.setAliasName("USB4704#" + (++deviceIndex));
                    device.setDeviceUniqueCode(String.valueOf(devInfo.DeviceNumber));
                    device.setDeviceModel(DeviceModel.SoundCard.USB4704_DEVICE);

                    devices.add(device);
                    log.info("找到USB-4704设备: {}", devInfo);
                }
            }
        } catch (Exception e) {
            log.error("USB4704设备检测失败，请确认：", e);
            log.error("1. 研华驱动是否安装");
            log.error("2. Automation.BDaq.dll是否在java.library.path中");
            log.error("3. 设备是否通过USB正确连接");
        }

        return devices;
    }


    public static void main(String[] args) {
        SoundDeviceFinder soundDeviceFinder = new SoundDeviceFinder();
//        List<Device> allSoundInputDevices = soundDeviceFinder.findAllSoundInputDevices();
//        System.out.println(allSoundInputDevices);
        //findUSB4704Devices();
        List<Device> allUsb4704Devices = soundDeviceFinder.findUSB4704Devices();
        System.out.println(allUsb4704Devices);
    }


}
