package com.desaysv.workserver.listeners;


import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import com.fazecast.jSerialComm.SerialPortEvent;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 19:07
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Slf4j
public class PortEventListener implements SerialPortDataListener {

//    @Override
//    public void serialEvent(SerialPortEvent serialPortEvent) {
//        switch (serialPortEvent.getEventType()) {
//            case SerialPortEvent.BI: // 10 通讯中断
////                log.warn("串口通讯中断");
//                break;
//            case SerialPortEvent.OE: // 7 溢位（溢出）错误
//                log.warn("串口溢位错误");
//                break;
//            case SerialPortEvent.FE: // 9 帧错误
//                //TODO: 让相同的log打印只出现一次
////                log.warn("串口帧错误");
//                break;
//            case SerialPortEvent.PE: // 8 奇偶校验错误
//                log.warn("串口奇偶校验错误");
//                break;
//            case SerialPortEvent.CD: // 6 载波检测
//                log.info("串口载波检测");
//                break;
//            case SerialPortEvent.CTS: // 3 清除待发送数据
//                log.info("串口清除待发送数据");
//                break;
//            case SerialPortEvent.DSR: // 4 待发送数据准备好了
//                log.info("串口待发送数据准备好了");
//                break;
//            case SerialPortEvent.RI: // 5 振铃指示
//                log.info("串口振铃指示");
//                break;
//            case SerialPortEvent.OUTPUT_BUFFER_EMPTY: // 2 输出缓冲区已清空
//                log.info("串口输出缓冲区已清空");
//                break;
//        }
//    }

    @Override
    public int getListeningEvents() {
        return SerialPort.LISTENING_EVENT_DATA_AVAILABLE | SerialPort.LISTENING_EVENT_DATA_WRITTEN | SerialPort.LISTENING_EVENT_PORT_DISCONNECTED;
    }

    @Override
    public void serialEvent(SerialPortEvent serialPortEvent) {
        switch (serialPortEvent.getEventType()) {
            case SerialPort.LISTENING_EVENT_BREAK_INTERRUPT: //BI 通讯中断
                log.warn("串口通讯中断");
                break;
            case SerialPort.LISTENING_EVENT_SOFTWARE_OVERRUN_ERROR:  //OE 溢位（溢出）错误
            case SerialPort.LISTENING_EVENT_FIRMWARE_OVERRUN_ERROR:
                log.warn("串口溢位错误");
                break;
            case SerialPort.LISTENING_EVENT_FRAMING_ERROR: //FE 帧错误
                log.warn("串口帧错误");
                break;
            case SerialPort.LISTENING_EVENT_PARITY_ERROR:  //PE 奇偶校验错误
                log.warn("串口奇偶校验错误");
                break;
            case SerialPort.LISTENING_EVENT_CARRIER_DETECT:  //CD 载波检测
                log.info("串口载波检测");
                break;
            case SerialPort.LISTENING_EVENT_CTS: // 清除待发送数据
                log.info("串口清除待发送数据");
                break;
            case SerialPort.LISTENING_EVENT_DSR: // 待发送数据准备好了
                log.info("串口待发送数据准备好了");
                break;
            case SerialPort.LISTENING_EVENT_RING_INDICATOR: //RI 振铃指示
                log.info("串口振铃指示");
                break;
            case SerialPort.LISTENING_EVENT_DATA_WRITTEN: // 输出缓冲区已清空
                log.info("数据已发送完毕，输出缓冲区清空");
                break;
            case SerialPort.LISTENING_EVENT_PORT_DISCONNECTED:
//                log.info("串口断开");
                break;
            default:
                log.debug("未知状态");
        }
    }
}
