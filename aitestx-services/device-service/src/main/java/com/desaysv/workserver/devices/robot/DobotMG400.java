package com.desaysv.workserver.devices.robot;

import com.desaysv.workserver.GlobalConfigHolder;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.devices.robot.base.CoordinateSystem;
import com.desaysv.workserver.devices.robot.base.DobotDeviceWithTouchPoint;
import com.desaysv.workserver.devices.robot.base.JogMode;
import com.desaysv.workserver.devices.robot.base.RobotPose;
import com.desaysv.workserver.devices.robot.config.RobotConfig;
import com.desaysv.workserver.devices.robot.type.mg400.IDobotMG400;
import com.desaysv.workserver.devices.robot.type.mg400.entity.*;
import com.desaysv.workserver.devices.robot.type.mg400.sockets.Dashboard;
import com.desaysv.workserver.devices.robot.type.mg400.sockets.DobotMove;
import com.desaysv.workserver.devices.robot.type.mg400.sockets.Feedback;
import com.desaysv.workserver.devices.robot.type.mg400.utils.ErrorInfoHelper;
import com.desaysv.workserver.exceptions.ReflectMethodsNotFoundException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ReflectUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * MG400控制程序
 * 其中29999，30003端口只能连接一次
 */
@Slf4j
public class DobotMG400 extends DobotDeviceWithTouchPoint implements IDobotMG400 {

    @Getter
    private final String deviceModel = DeviceModel.Robot.DOBOT_MG400;

    private final DobotMove dobotMove;

    private final Dashboard dashboard;

    private final Feedback feedback;

    private boolean isEnableRobot;

    private boolean isModeSwitched;

    private final CoordinateSystem coordinateSystem;


    public DobotMG400() {
        this(new DeviceOperationParameter());
    }

    public DobotMG400(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        dobotMove = new DobotMove();
        dashboard = new Dashboard();
        feedback = new Feedback(dashboard);
        isEnableRobot = false;
        isModeSwitched = false;
        coordinateSystem = new CoordinateSystem();
    }

    private String getIp() {
        return getDeviceUniqueCode() == null ? DeviceParams.defaultMG400IP : getDeviceUniqueCode();
    }

    /*
    以下是回调方法
     */
    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        super.testSuiteStart(executionContext);
        //清空用户运动队列
        enableRobot();
    }

    @Override
    public void openCompleted(boolean isOpenSuccess) {
        if (isOpenSuccess) {
            config();
            clearError();
        }
        super.openCompleted(isOpenSuccess);
    }

    /*
    以下是设备连接和关闭方法
     */

    private boolean connectDashboard() {
        String ip = getIp();
        boolean isOk = dashboard.connect(ip);
        if (isOk) {
            dashboard.setDobotEventHandler(this::enableRobot);
            log.info("MG400机械臂Dashboard连接成功");
        } else {
            log.warn("MG400机械臂Dashboard连接失败");
        }
        return isOk;
    }

    private boolean connectDobotMove() {
        String ip = getIp();
        boolean isOk = dobotMove.connect(ip);
        if (isOk) {
            dobotMove.setDobotEventHandler(this::enableRobot);
            log.info("MG400机械臂DobotMove连接成功");
        } else {
            log.warn("MG400机械臂DobotMove连接失败");
        }
        return isOk;
    }

    private boolean connectFeedback() {
        String ip = getIp();
        boolean isOk = feedback.connect(ip);
        if (isOk) {
            log.info("MG400机械臂Feedback连接成功");
        } else {
            log.warn("MG400机械臂Feedback连接失败");
        }
        return isOk;
    }

    //    @MockReturnBoolean(returnValue = true) //TODO：改成springboot容器管理，才能用AOP，并且使用动态配置，不用注释这条
    @Override
    public synchronized boolean open() {
        if (isSimulated()) {
            return true;
        }
        return connectDobotMove() && connectDashboard() && connectFeedback();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        super.close();
        if (isSimulated()) {
            return true;
        }
        disableRobot();
        boolean isMOk = dobotMove.disconnect();
        boolean isFOk = feedback.disconnect();
        boolean isDOk = dashboard.disconnect();
        boolean isOk = isMOk && isFOk && isDOk;
        if (isOk) {
            log.info("MG400机械臂已断开连接");
        }
        return isOk;
    }

    /*
    以下是设备配置方法
     */

    public void config() {
        //重新设置用户坐标系及运动参数
        RobotConfig robotConfig = getDeviceConfig();
        setUserCoordinate(robotConfig != null ? robotConfig.getParameters().getUserCoordinate() : 0);
        setSpeedFactor(robotConfig != null ? robotConfig.getParameters().getSpeedFactor() : 50);
//        setPayLoad(robotConfig != null ? robotConfig.getParameters().getPayload() : 0);
    }

    /*
    以下是点动方法
     */

    @Override
    public RobotReply moveJog(String command) {
        RobotConfig robotConfig = getDeviceConfig();

        checkRobotMode();
        if (robotConfig.getParameters().getJogMode() == JogMode.FINE_TUNING.getValue()) {
            log.info("MG400 {}->微调:{}", getDeviceName(), command);
            return moveMinimumLineDistance(command);
        } else {
            log.info("MG400 {}->点动:{}", getDeviceName(), command);
            isModeSwitched = true;
            return dobotMove.moveJog(command);
        }
    }

    @Override
    public RobotReply stopMoveJog() {
        RobotConfig robotConfig = getDeviceConfig();
        if (robotConfig.getParameters().getJogMode() == JogMode.JOG.getValue()) {
            return dobotMove.stopMoveJog();
        }
        return RobotReply.ok();
    }

    /*
    以下是运动方法
     */

    @Override
    public RobotReply moveJoint(MoveEntity moveEntity) {
        checkRobotMode();
        checkModeSwitched();
        double slideRailDistance = moveEntity.getSlideRail();
        moveSlideRail(slideRailDistance);
        log.info("MG400 {}->关节移动到坐标:{}(user={})", getDeviceName(), moveEntity, moveEntity.getUser());
        RobotReply robotReply = dobotMove.movJ(moveEntity);
        if (robotReply.isOk()) {
            return dobotMove.sync();
        }
        return robotReply;

    }

    @Override
    public RobotReply moveJointExt(MovJExtEntity movJExtEntity) {
        checkRobotMode();
        checkModeSwitched();
        log.info("MG400 {}->滑轨移动到{}", getDeviceName(), movJExtEntity);
        RobotReply robotReply = dobotMove.movJExt(movJExtEntity);
        if (robotReply.isOk()) {
            return dobotMove.syncAll();
        }
        return robotReply;

    }

    @Override
    public RobotReply moveSlideRail(double slideRailDistance) {
        RobotConfig robotConfig = getDeviceConfig();
        if (robotConfig.isEnableSlideRail()) {
            if (slideRailDistance >= 0) {
                MovJExtEntity entity = new MovJExtEntity();
                entity.setDistanceOrAngle(slideRailDistance);
                entity.setSync(true);
                return moveJointExt(entity);
            }
            return RobotReply.fail("滑动距离小于0");
        }
        return RobotReply.ok();
    }


    @Override
    public RobotReply moveLine(MoveEntity moveEntity, boolean moveDurationCalc) {
        checkRobotMode();
        checkModeSwitched();
        double slideRailDistance = moveEntity.getSlideRail();
        moveSlideRail(slideRailDistance);
        log.info("MG400 {}->直线移动到坐标:{}(user={})", getDeviceName(), moveEntity, moveEntity.getUser());
        RobotReply robotReplyWithMotionDuration = new RobotReply();
        if (moveDurationCalc) {
            //统计移动时间
            feedback.monitorMotionDuration(moveEntity, coordinateSystem,
                    robotReplyWithMotionDuration::setMotionDuration);
        }
        RobotReply robotReply = dobotMove.movL(moveEntity);
        if (robotReply.isOk()) {
            robotReply = dobotMove.sync();
        }
        robotReplyWithMotionDuration.setOk(robotReply.isOk());
        robotReplyWithMotionDuration.setMessage(robotReply.getMessage());
        robotReplyWithMotionDuration.setReplyDuration(robotReply.getReplyDuration());
        return robotReplyWithMotionDuration;
    }

    @Override
    public RobotReply moveLineQuickly(List<MoveEntity> moveEntityList) {
        checkRobotMode();
        checkModeSwitched();
        RobotReply robotReplyWithMotionDuration = new RobotReply();

        RobotReply robotReply = dobotMove.movLQuickly(moveEntityList);
        if (robotReply.isOk()) {
            robotReply = dobotMove.sync();
        }
        robotReplyWithMotionDuration.setOk(robotReply.isOk());
        robotReplyWithMotionDuration.setMessage(robotReply.getMessage());
        robotReplyWithMotionDuration.setReplyDuration(robotReply.getReplyDuration());

        return robotReplyWithMotionDuration;
    }

    /**
     * 移动最小直线距离
     */
    @Override
    public RobotReply moveLineDistance(String direction, float distance) {
        RobotPose robotPose = fetchPose(coordinateSystem);
        MoveEntity moveEntity = robotPose.toMoveEntity();
        switch (direction) {
            case "X+":
                moveEntity.setX(moveEntity.getX() + distance);
                break;
            case "X-":
                moveEntity.setX(moveEntity.getX() - distance);
                break;
            case "Y+":
                moveEntity.setY(moveEntity.getY() + distance);
                break;
            case "Y-":
                moveEntity.setY(moveEntity.getY() - distance);
                break;
            case "Z+":
                moveEntity.setZ(moveEntity.getZ() + distance);
                break;
            case "Z-":
                moveEntity.setZ(moveEntity.getZ() - distance);
                break;
        }
        return move(moveEntity);
    }

    @Override
    public RobotReply moveMinimumLineDistance(String direction) {
        float minimumMoveDistance = 0.2f;
        return moveLineDistance(direction, minimumMoveDistance);
    }


    @Override
    public RobotReply touch(MoveEntity moveEntity, boolean autoUp, boolean verbose) {
        log.info("{} {}->准备点击坐标:{}(user={})", getDeviceModel(), getDeviceName(), moveEntity, moveEntity.getUser());
        checkModeSwitched();
        return smartTouch(moveEntity, autoUp, verbose);
    }

    @Override
    public RobotReply swipe(List<MoveEntity> moveEntityList) {
        checkRobotMode();
        checkModeSwitched();
        return super.swipe(moveEntityList);
    }


    /**
     * @param moveEntityList:坐标列表
     * @return RobotReply
     * <AUTHOR>
     * @description 机械臂画圆
     * @date 2024/6/18 13:58
     */

    @Override
    public RobotReply circle(List<MoveEntity> moveEntityList) {
        checkRobotMode();
        checkModeSwitched();
        RobotReply robotReplyWithMotionDuration = new RobotReply();
        RobotReply robotReply = dobotMove.circle(moveEntityList);
        if (robotReply.isOk()) {
            robotReply = dobotMove.sync();
        }
        robotReplyWithMotionDuration.setOk(robotReply.isOk());
        robotReplyWithMotionDuration.setMessage(robotReply.getMessage());
        robotReplyWithMotionDuration.setReplyDuration(robotReply.getReplyDuration());
        return robotReplyWithMotionDuration;
    }

    @Override
    public RobotReply air(boolean pressure) throws InterruptedException {
        log.info("MG400 {}->气压控制:{}", getDeviceName(), pressure);
        checkRobotMode();
        checkModeSwitched();
        if (pressure) {
            return dashboard.setDO(2, 1);
        } else {
            List<IOEntity> initialList = Arrays.asList(new IOEntity(1, 1), new IOEntity(2, 0));
            RobotReply robotReply = dashboard.setDOGroup(initialList);
            Thread.sleep(100);
            if (robotReply.isOk()) {
                List<IOEntity> closedList = Arrays.asList(new IOEntity(1, 0), new IOEntity(2, 0));
                return dashboard.setDOGroup(closedList);
            }
            return robotReply;
        }
    }

    @Override
    public RobotReply arc(ArcEntity arcEntity) {
        checkRobotMode();
        checkModeSwitched();
        super.arc(arcEntity);
        return dobotMove.arc(arcEntity);
    }


    /*
    以下dashboard控制方法
     */

    private void checkModeSwitched() {
        //TODO：获取碰撞状态，增加拖曳检测
        if (isModeSwitched) {
            log.info("点动切换后，enableRobot");
            enableRobot();
            isModeSwitched = false;
        } else if (feedback.RunQueuedCmd == 0) {
            log.info("未在队列状态，enableRobot");
            enableRobot();
        }
    }

    private RobotReply stopDrag() {
        log.info("退出拖曳");
        return dashboard.stopDrag();
    }

    private String getError() {
        RobotReply robotReply = dashboard.getErrorID();
        if (robotReply.isOk()) {
            RobotReply reply = ErrorInfoHelper.getInstance().parseResult(robotReply.getMessage());
            return reply.getMessage();
        }
        return "";
    }

    private void checkRobotMode() {
        if (feedback.RobotMode == 6) {
            //拖曳状态
            log.info("当前处于拖曳状态，退出拖曳并使能机械臂");
            stopDrag();
            enableRobot();
        }
    }

    @Override
    public RobotReply resetRobot() {
        log.info("MG400 {}->停止机器人并清空指令队列", getDeviceName());
        return dashboard.resetRobot();
    }

    @Override
    public RobotReply clearError() {
        log.info("MG400 {}->清除错误", getDeviceName());
        // enableRobot();
        RobotReply robotReply = dashboard.clearError();
        if (robotReply.isOk()) {
            return enableRobot();
        }
        return robotReply;
    }

    @Override
    public RobotReply searchError() {
        log.info("MG400 {}->查询错误与解决方法", getDeviceName());
        return RobotReply.ok(getError());
    }

    @Override
    public RobotReply setPayLoad(int weight) {
        log.info("MG400 {}->设置负载{}g", getDeviceName(), weight);
        return dashboard.setPayLoad(weight / 1000.0f);
    }

    @Override
    public RobotReply setSpeedFactor(int factor) {
        log.info("MG400 {}->设置全局速度比例:{}", getDeviceName(), factor);
        return dashboard.setSpeedFactor(factor);
    }

    @Override
    public RobotReply setUserCoordinate(int userIndex) {
        userIndex = GlobalConfigHolder.getGlobalConfig().getUserCoordinates() == 0 ?
                userIndex : GlobalConfigHolder.getGlobalConfig().getUserCoordinates();
        log.info("MG400 {}->设置全局用户坐标系:{}", getDeviceName(), userIndex);
        dashboard.setUserCoordinate(userIndex + 1);//临时方案，防止User不生效
        RobotReply robotReply = dashboard.setUserCoordinate(userIndex);
        if (robotReply.isOk()) {
            coordinateSystem.setUser(userIndex);
        }
        return robotReply;
    }

    @Override
    public RobotReply enableRobot() {
        //TODO:点击前，判断错误
        //上使能，会清空运动学参数和坐标系参数
        log.info("MG400 {}->启用使能", getDeviceName());
        isEnableRobot = true;
        RobotReply robotReply = dashboard.enableRobot();
        if (robotReply.isOk()) {
            config();
        }
        return robotReply;
    }

    @Override
    public RobotReply disableRobot() {
        log.info("MG400 {}->关闭使能", getDeviceName());
        isEnableRobot = false;
        return dashboard.disableRobot();
    }

    @Override
    public RobotPose fetchPose(CoordinateSystem coordinateSystem) {
        return dashboard.getPose(coordinateSystem);
    }


    @Override
    public FeedbackData fetchFeedbackData() {
        //TODO: 改成ws连接
//        log.info("fetchFeedback:{}", Thread.currentThread());
        FeedbackData feedbackData = new FeedbackData();
        if (isSimulated()) {
            RobotConfig robotConfig = getDeviceConfig();
            Random random = new Random();
            feedbackData.setX(random.nextDouble() * 10 + 100);
            feedbackData.setY(random.nextDouble() * 20 + 10);
            feedbackData.setZ(random.nextDouble() * 30 + 10);
            feedbackData.setR(random.nextDouble());
            feedbackData.setSlideRail((double) 0);
            feedbackData.setUser(robotConfig.getParameters().getUserCoordinate());
            feedbackData.setMode("模拟");
        } else {
            RobotPose robotPose = fetchPose(coordinateSystem);
            feedbackData.setX(robotPose.getX());
            feedbackData.setY(robotPose.getY());
            feedbackData.setZ(robotPose.getZ());
            feedbackData.setR(robotPose.getR());
            feedbackData.setUser(coordinateSystem.getUser());

            feedbackData.setJ1(feedback.QActual[0]);
            feedbackData.setJ2(feedback.QActual[1]);
            feedbackData.setJ3(feedback.QActual[2]);
            feedbackData.setJ4(feedback.QActual[3]);

//            feedbackData.setX(feedback.ToolVectorActual[0]);
//            feedbackData.setY(feedback.ToolVectorActual[1]);
//            feedbackData.setZ(feedback.ToolVectorActual[2]);
//            feedbackData.setR(feedback.ToolVectorActual[3]);
            //TODO:接上滑轨，调试这个feedback.ToolVectorActual[4]，看是否是滑轨的位置
            feedbackData.setSlideRail(feedback.ToolVectorActual[4]);
            feedbackData.setXyzSpeedFactor((int) feedback.SpeedScaling);
            feedbackData.setXyzAccelerationSpeedFactor((int) feedback.XYZAccelerationRatio);
            feedbackData.setModeCode(feedback.RobotMode);
            feedbackData.setMode(feedback.convertRobotModeFriendly());
            feedbackData.setDigitalInputs(Long.toBinaryString(feedback.DigitalInputs));
            feedbackData.setDigitalOutputs(Long.toBinaryString(feedback.DigitalOutputs));
//            if (feedback.ErrorStatus != 0) {
//                //TODO：状态变化时后触发
//                feedbackData.setErrorMessage(getError());
//            }
        }
        return feedbackData;
    }


    public static void main(String[] args) {
        DobotMG400 dobotMG400 = new DobotMG400(new DeviceOperationParameter());
        dobotMG400.setSimulated(true);
        dobotMG400.open();
        try {
            ReflectUtils.invokeMethod(dobotMG400, "swipeByName", new Class[]{List.class}, new Object[]{Arrays.asList("a", "b")});
        } catch (ReflectMethodsNotFoundException e) {
            log.error(e.getMessage(), e);
        }
        //        dobotMG400.enableRobot();
//        dobotMG400.moveMinimumLineDistance("X+");
        try {
            dobotMG400.close();
        } catch (DeviceCloseException e) {
            log.error(e.getMessage(), e);
        }
    }
}
