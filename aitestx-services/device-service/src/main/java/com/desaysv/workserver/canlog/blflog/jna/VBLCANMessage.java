package com.desaysv.workserver.canlog.blflog.jna;

import com.sun.jna.Structure;

import java.util.Arrays;
import java.util.List;

public class VBLCANMessage extends Structure {
    public VBLObjectHeader mHeader;
    public short mChannel;
    public byte mFlags;
    public byte mDLC;
    public int mID;
    public byte[] mData;

    public VBLCANMessage() {
        this.mData = new byte[8];
    }

    @Override
    protected List<String> getFieldOrder() {
        return Arrays.asList("mHeader", "mChannel", "mFlags", "mDLC", "mID", "mData");
    }
}