package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.port.PortDevice;

/**
 * N6700电源协议
 */
public class N6700Protocol extends PowerSCPIProtocol {

    public N6700Protocol(PortDevice device) {
        super(device);
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return sendCommand(String.format("OUTP %s , (@%d)", isOutput ? "ON" : "OFF", channel));
    }


    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        return sendCommand(String.format("VOLT %f,  (@%d)", voltage, channel));
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        return sendCommand(String.format("CURR %f, (@%d)", current, channel));
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return sendCommand(String.format("Meas:VOLT? (@%d)", channel));
    }

    @Override
    public boolean readCurrent(Integer channel) {
        return sendCommand(String.format("Meas:CURR? (@%d)", channel));
    }
}
