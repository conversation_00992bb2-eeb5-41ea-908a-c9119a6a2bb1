package com.desaysv.workserver.finder;

import com.desaysv.workserver.devices.autoclicker.AutoClickerDevice;
import com.desaysv.workserver.devices.autoclicker.ConcreteAutoClickerDevice;
import com.desaysv.workserver.devices.autoclicker.UsbHidClicker;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * clicker设备查找
 */
@Component
@Slf4j
@Lazy
public class AutoClickerDeviceFinder {

    private static final List<Integer> simulateAutoClickerDevicePorts = Collections.singletonList(0);

    /**
     * 查找所有Clicker设备
     *
     * @param deviceModel 设备型号
     * @return 所有Clicker设备
     */

    public List<Device> findAllAutoClickerDevice(String deviceModel) {
        List<Device> devices = new ArrayList<>();
        AutoClickerDevice autoClickerDevice;
        UsbHidClicker usbHidClicker = new UsbHidClicker();
        if (usbHidClicker.searchDevices() != null) {
            int index = 1;
            for (int port : simulateAutoClickerDevicePorts) {
                autoClickerDevice = new ConcreteAutoClickerDevice();
                autoClickerDevice.setAliasName(deviceModel + "#" + index++);
//                autoClickerDevice.setDeviceAliasName(deviceModel);
                autoClickerDevice.setDeviceOperationParameter(new DeviceOperationParameter());
                String deviceName = String.format("AutoClicker_%d", port);
                autoClickerDevice.setDeviceName(deviceName + usbHidClicker.searchDevices().serial_number);
                autoClickerDevice.setDeviceUniqueCode(deviceName);
                devices.add(autoClickerDevice);
            }
        }
        return devices;
    }

}
