package com.desaysv.workserver.devices.power.kikusui;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任意递增或者递减电压复位脉冲
 * start->end +0.1 or -0.1
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ArbitraryVoltageResetPulse extends WavePulse {

    private double startVoltage;
    private double endVoltage;
    private double changeableVoltageDuration;

    private double constantVoltage;
    private double constantVoltageDuration;

}
