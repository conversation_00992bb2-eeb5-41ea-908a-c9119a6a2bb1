package com.desaysv.workserver.devices.testbox.light;

import com.desaysv.workserver.devices.testbox.PWMEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class ResponseParser {
    // 优化点1：预定义错误码映射表
//    private static final Map<String, String> ERROR_CODE_MAP = new HashMap<String, String>() {{
//        put("01", "不存在通道");
//        put("02", "参数错误");
//        put("03", "其他错误");
//        put("F0", "不存在");
//    }};
//
//    // 优化点2：统一预处理方法
//    private static String preprocessResponse(String response) {
//        return (response != null) ? response.replace(" ", "") : null;
//    }
//
//    public static boolean checkExecutionWriteBroadcastResult(String response) {
//        // 优化点3：先处理空值再校验长度（网页3）
//        String processed = preprocessResponse(response);
//        if (processed == null || processed.length() < 18) {
//            log.error("ERR_FORMAT：{}", "响应格式无效");
//            return false;
//        }
//
//        // 优化点4：字符直接比对替代子字符串
//        if (processed.charAt(4) == 'F' && processed.charAt(5) == 'F') {
//            log.error("ERR_CARD_MISSING: 板卡不存在");
//            return false;
//        }
//
//        // 优化点5：使用Map替代switch-case
//        String resultCode = processed.substring(8, 10);
//        if (!"00".equals(resultCode)) {
//            String errorDesc = ERROR_CODE_MAP.getOrDefault(resultCode, "未知错误");
//            log.error("ERR_CODE_{}: 失败原因={}", resultCode, errorDesc); // 优化日志格式（网页2）
//            return false;
//        }
//        return true;
//    }
//
//    public static ParseResult checkExecutionAcquisitionBroadcastResult(String response) {
//        String processed = preprocessResponse(response);
//        if (processed == null || processed.length() < 10) {
//            log.error("ERR_LENGTH: 响应长度不足");
//            return null;
//        }
//
//        // 优化点6：防御性数据分割
//        String params = processed.substring(0, 8);
//        String dataPart = processed.length() > 8 ? processed.substring(8) : "";
//
//        // 优化点7：枚举替代魔法值（网页6）
//        CardType cardType = CardType.fromCode(params.substring(4, 6));
//        ParseResult result = new ParseResult();
//
//        // 优化点8：策略模式处理不同板卡
//        if (cardType == CardType.LEVEL) {
//            result.setVoltages(parseLevelData(dataPart, params.substring(6, 8)));
//        } else if (cardType == CardType.PWM) {
//            result.setPwmDataList(parsePWMData(dataPart, params.substring(6, 8)));
//        }
//        return result;
//    }
//
//    // 优化点9：板卡类型枚举
//    enum CardType {
//        LEVEL("01"), PWM("03"), UNKNOWN("");
//
//        private static final Map<String, CardType> CODE_MAP = new HashMap<>();
//        static {
//            for (CardType type : values()) {
//                CODE_MAP.put(type.code, type);
//            }
//        }
//
//        private final String code;
//
//        CardType(String code) {
//            this.code = code;
//        }
//
//        public static CardType fromCode(String code) {
//            return CODE_MAP.getOrDefault(code, UNKNOWN);
//        }
//    }
//
//    private static List<Float> parseLevelData(String dataPart, String channel) {
//        final boolean isAllChannels = "FF".equals(channel);
//        final int expectedLength = isAllChannels ? 120 : 2; // 字符长度=数值个数×2
//
//        if (dataPart.length() != expectedLength) {
//            log.error("电平采集卡数据长度错误");
//            return null;
//        }
//
//        final int size = isAllChannels ? 60 : 1;
//        List<Float> voltages = new ArrayList<>(size);
//        final char[] chars = dataPart.toCharArray();
//
//        for (int i = 0; i < size * 2; i += 2) {
//            int high = Character.digit(chars[i], 16);
//            int low = Character.digit(chars[i+1], 16);
//            voltages.add((high << 4 | low) * 0.1f);
//        }
//        return voltages;
//    }
//
//    private static List<PWMEntity> parsePWMData(String dataPart, String channel) {
//        final boolean isAllChannels = "FF".equals(channel);
//        final int expectedGroups = isAllChannels ? 10 : 1;
//        final int requiredLength = expectedGroups * 6; // 每组3参数×2字符=6字符
//
//        if (dataPart.length() != requiredLength) {
//            log.error("PWM采集卡数据长度错误");
//            return null;
//        }
//
//        List<PWMEntity> pwmList = new ArrayList<>(expectedGroups);
//        final char[] chars = dataPart.toCharArray();
//
//        for (int i = 0; i < requiredLength; i += 6) {
//            // 解析Y0 (高位在前)
//            int y0 = (Character.digit(chars[i], 16) << 4)
//                    | Character.digit(chars[i+1], 16);
//
//            // 解析Y1
//            int y1 = (Character.digit(chars[i+2], 16) << 4)
//                    | Character.digit(chars[i+3], 16);
//
//            // 解析Y2
//            int y2 = (Character.digit(chars[i+4], 16) << 4)
//                    | Character.digit(chars[i+5], 16);
//
//            pwmList.add(new PWMEntity(
//                    (y0 << 8) | y1, // 频率
//                    y2 // 占空比
//            ));
//        }
//        return pwmList;
//    }

    public static boolean checkExecutionWriteBroadcastResult(String response) {
        if (response == null) {
            log.error("ERR_FORMAT：{}", "响应字符串格式无效");
            return false;
        }
        response = response.substring(4);   //先处理去掉帧头和有效数据位
        // 提取关键参数
        String boardCardType = response.substring(4, 6);  // 设备类型编码（第5-6位）
        String channelNumber = response.substring(6, 8);  // 通道号（第7-8位）
        String resCode = response.substring(8, 10); // 执行结果（第9-10位）
        // 检查参数3（电平采集卡）
        if ("FF".equals(boardCardType)) {
            log.error("ERR_CARD_MISSING", "板卡不存在，执行终止");
            return false;
        }
        // 检查参数4（通道号）
        if ("F0".equals(channelNumber)) {
            log.error("ERR_CHANNEL_INVALID", "通道[" + channelNumber + "]不存在");
            return false;
        }
        // 检查参数5（执行结果）
        if (!"00".equals(resCode)) {
            String errorType;
            switch (resCode) {
                case "01":
                    errorType = "不存在通道";
                    break;
                case "02":
                    errorType = "参数错误";
                    break;
                case "03":
                    errorType = "其他错误";
                    break;
                case "F0":
                    errorType = "不存在";
                    break;
                default:
                    errorType = "未知错误";
                    break;
            }
            log.error("ERR_CODE_{}, 板卡执行失败原因:{}", resCode, errorType);
            return false;
        }
        return true;
    }

    public static ParseResult checkSingleChannelAcquisitionBroadCardResult(String response) {
        if (response == null) {
            log.error("无效的响应字符串NULL");
            return null;
        }
        if (response.length() < 10) {
            log.error("长度小于10，无效的响应字符串");
            return null;
        }
        //有效数据长度
        String header = response.substring(0, 2); //帧头
        int dataByteLength = Integer.parseInt(response.substring(2, 4), 16);  //有效数据长度
        String chassisNumber = response.substring(4, 6); //机箱号
        String slotNumber = response.substring(6, 8); //卡槽号
        String broadCardType = response.substring(8, 10); // 板卡类型
        String channelNumber = response.substring(10, 12);  // 通道值（如05或FF:05表示单个通道， FF表示所有的通道）
        String dataPart = response.substring(12, 12 + (dataByteLength-4) * 2);   //板卡数据
        ParseResult result = new ParseResult();
        // 判断板卡类型
        if ("01".equals(broadCardType)) {
            result.setVoltages(parseLevelData(dataPart, channelNumber));
        } else if ("03".equals(broadCardType)) {
            result.setPwmDataList(parsePWMData(dataPart, channelNumber));
        } else {
            log.info("未知的板卡类型");
        }
        return result;
    }

    private static List<Float> parseLevelData(String dataPart, String channel) {
        // 检查通道是否为FF（采集所有通道）
        boolean isAllChannels = "FF".equals(channel);
        int expectedLength = isAllChannels ? 60 * 2 : 1 * 2; // 每个数据占2位十六进制
        if (dataPart.length() != expectedLength) {
            log.error("单通道电平采集卡数据长度错误:{}", expectedLength);
            return null;
        }
        List<Float> voltages = new ArrayList<>();
        int dataCount = isAllChannels ? 60 : 1;
        for (int i = 0; i < dataCount; i++) {
            String hex = dataPart.substring(i * 2, i * 2 + 2);
            int value = Integer.parseInt(hex, 16);
            voltages.add((float) (value * 0.1));  // 电压 = 值 × 0.1V
        }
        return voltages;
    }

    private static List<PWMEntity> parsePWMData(String dataPart, String channel) {
        // 检查通道是否为FF（采集所有通道）
        boolean isAllChannels = "FF".equals(channel);
        int expectedGroups = isAllChannels ? 10 : 1;
        int expectedLength = expectedGroups * 3 * 2; // 每组3个数据，每个占2位十六进制

        if (dataPart.length() != expectedLength) {
            log.error("PWM采集卡数据长度错误");
            return null;
        }

        List<PWMEntity> pwmList = new ArrayList<>();
        // 解析数据
        for (int group = 0; group < expectedGroups; group++) {
            int base = group * 6;  // 每组3个参数 × 2字符
            String hexY0 = dataPart.substring(base, base + 2);
            String hexY1 = dataPart.substring(base + 2, base + 4);
            String hexY2 = dataPart.substring(base + 4, base + 6);
            int y0 = Integer.parseInt(hexY0, 16);
            int y1 = Integer.parseInt(hexY1, 16);
            int frequency = (y0 << 8) + y1;  // 频率 = (y0×256 + y1) Hz
            float dutyCycle = Integer.parseInt(hexY2, 16);  // 占空比 = y2 %
            pwmList.add(new PWMEntity(frequency, dutyCycle));
        }

        return pwmList;
    }

    public static void main(String[] args) {
//        String S = "01 01 01 1A 00";
        String S = "EB 05 01 01 01 01 00 F4";
        String res=  S.replaceAll("\\s+", "");
        ParseResult result = checkSingleChannelAcquisitionBroadCardResult(res); // 输出：电压值: 1.0V
        System.out.println(result);
//        // 示例调用（假设数据部分为十六进制字符串）
//        String levelResponse = "010301050000000000" + "0A"; // 电平采集卡，通道05，电压=0x0A*0.1=1.0V
//        String levelResponse = "010301050A"; // 电平采集卡，通道05，电压=0x0A*0.1=1.0V
//        checkExecutionAcquisitionBroadcastResult(levelResponse); // 输出：电压值: 1.0V
//
//
////
//        String pwmResponse = "01030305" + "00127B"; // PWM采集卡，频率=0x00 * 256+0x12=18Hz，占空比=0x7B=123%
////        String pwmResponse = "01030305" + "00127B00"; // PWM采集卡，频率=0x00 * 256+0x12=18Hz，占空比=0x7B=123%
//       checkExecutionAcquisitionBroadcastResult(pwmResponse); // 输出：频率: 18Hz, 占空比: 123.0%
        String command = "EB 08 01 03 05 01 00 ";
       boolean res2 = checkExecutionWriteBroadcastResult(command.replaceAll("\\s+", "")); // 输出：频率: 18Hz, 占空比: 123.0%
        System.out.println(res2);
//
//        String allChannelsLevel = "010301FF0000000000" + "0A0B0C".repeat(20); // 60组数据示例（简写）
//        parseResponse(allChannelsLevel); // 输出60个通道的电压值
//
//        String allChannelsPWM = "010303FF0000000000" + "00127B".repeat(10); // 10组数据示例
//        parseResponse(allChannelsPWM); // 输出10个通道的频率和占空比
//    }
    }

}