package com.desaysv.workserver.config.lin;

import com.desaysv.workserver.config.DeviceConfig;
import com.desaysv.workserver.config.can.PtsConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class LinConfig extends DeviceConfig {

    private PtsConfig ptsConfig = new PtsConfig();

    private Map<String, LinConfigParameter> configParameters = new HashMap<>();

    private Map<String, NetLinConfigParameter> configNetParameters = new HashMap<>();

    private Map<String, LdfConfig> ldfConfigs = new HashMap<>(); //key-》通道channel
}
