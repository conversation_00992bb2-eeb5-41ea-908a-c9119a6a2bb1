package com.desaysv.workserver.devices.robot.base;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-20 11:21
 * @description :
 * @modified By :
 * @since : 2022-7-20
 */
@Deprecated
public class DobotDeviceDecorator extends DobotDevice {

    private final DobotDevice device;

    public DobotDeviceDecorator(DobotDevice device) {
        super(device.getDeviceOperationParameter());
        this.device = device;
    }

    @Override
    public String getDeviceModel() {
        return device.getDeviceModel();
    }

    @Override
    public Device getDevice() {
        return device;
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return device.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        return device.close();
    }

}
