package com.desaysv.workserver.devices.bus.canoe;

import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.fdx.FdxSocket;
import com.desaysv.workserver.devices.bus.interfaces.IBusDevice;
import com.desaysv.workserver.devices.bus.interfaces.IEthernetSequence;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;

public class VectorEthernetFDX extends Device implements IEthernetSequence, IBusDevice {
    private FdxSocket socket;

    public VectorEthernetFDX(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        socket = FdxSocket.getInstance();
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_ETHERNET;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.VECTOR_ETHERNET;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        try {
            if (!socket.isCANoeRunning()) {
                FdxSocket.setInstanceNULL();
                socket = FdxSocket.getInstance();
                return socket.isCANoeRunning();
            }
        } catch (BusError e) {
            throw new DeviceOpenException(e);
        }
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }

    @Override
    public boolean setEthDoIPFun(String address, String ethDoIPCaseIdString) throws BusError {
        log.info("---------setEthDoIPFun:{}-------", ethDoIPCaseIdString);
        long sTime = System.currentTimeMillis();
        if (!socket.setAddress(address.equals("Fun") ? 2 : 1)) {
            ActionSequencesLoggerUtil.info(SET_DOIP_FAIL, address, ethDoIPCaseIdString, FAIL, SET_ADDRESS_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.getUDSFinishFlag()) {
            ActionSequencesLoggerUtil.info(GET_FINISH_FLAG_FAIL, FAIL, GET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        if (!socket.sendUdsData(ethDoIPCaseIdString)) {
            ActionSequencesLoggerUtil.info(SET_DOIP_FAIL,
                    address, ethDoIPCaseIdString, FAIL, VectorUtils.SET_DATA_FAIL, System.currentTimeMillis() - sTime);
            return false;
        }
        return socket.getUDSFinishFlag();
    }


    @Override
    public byte[] fetchEthDoIPFun() throws BusError {
        return socket.getUDSFinishFlag() ? socket.getUDSRecUdsData() : null;

    }

    @Override
    public String fetchEthDoIPExplainFun() throws BusError {
        return socket.getUDSFinishFlag() ? socket.getUDSRecUdsDataDescribe22() : null;
    }

    @Override
    public boolean fetchEthDoIPUdp(String doIPUdpCaseIdString) throws BusError {
        return socket.getTestResult();
    }

    @Override
    public boolean fetchEthOTA(String otaCaseIdString) throws BusError {
        return socket.getTestResult();
    }

    @Override
    public boolean setEthUdsLog(int commandId) throws BusError {
        log.info("---------setEthUdsLog: commandId {}--------", commandId);
        if (commandId == 0) {
            socket.setUDSLogName();
        }
        return commandId == 0 ? socket.setUSDStartLogging() : socket.setUDSStopLogging();
    }


    @Override
    public boolean setEthDoIPUdp(String doIPUdpEthCaseId) throws BusError {
        return socket.executeTestCase(doIPUdpEthCaseId) && socket.getTestResult();
    }

    @Override
    public boolean setEthOTA(String ethOTACaseIdString) throws BusError {
        return socket.executeTestCase(ethOTACaseIdString) && socket.getTestResult();
    }

}
