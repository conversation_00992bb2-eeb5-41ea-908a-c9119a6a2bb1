package com.desaysv.workserver.screen;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.common.port.CheckPointInfo;
import com.desaysv.workserver.screen.events.TouchEvent;
import com.desaysv.workserver.screen.result.TouchPointSummary;
import com.desaysv.workserver.screen.result.mist.PointInfo;

import java.util.List;

/**
 * 触摸报点收集监听器
 */
public interface TouchDataListener {

    void notifyTouchDataListenerStart();

    List<PointInfo> notifyTouchDataListenerEnd();

    OperationResult notifyTouchDataListenerEnd(TouchEvent touchEvent);

    OperationResult notifyTouchDataListenerEnd(CheckPointInfo checkPointInfo);

    /**
     * 开始报点统计
     */
    void notifyTouchDataReportStart();

    /**
     * 结束报点统计
     */
    TouchPointSummary notifyTouchDataReportEnd();
}
