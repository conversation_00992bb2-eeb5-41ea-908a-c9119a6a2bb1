package com.desaysv.workserver.devices.camera.base;

import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.model.roi.ScaledRoiRect;
import lombok.Data;

@Data
public class VisionRecognizeRequest {
    private String project;
    private String templateName;
    private String algorithm;
    private String matchedText; //识别文字
    private RectSize imageSize;
    private ScaledRoiRect roi;
    private float threshold;
    private double recognizedDuration; //识别持续时间
    private double timeout; //检测超时时间
    private double preWaitTime; //前置等待时间
    private boolean mustExist = true;
    private boolean colorMatchEnabled; //是否启用颜色匹配
    private boolean onlyTestSimilarity;
    private boolean videoEnabled;
    private float targetSimilarity;
    private Float roiEnlargePercent;

}
