package com.desaysv.workserver.devices.testbox;

import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 继电器配置管理类
 */
@Component
@Lazy
@Log4j2
public class RelayConfigManager {
    private static final Map<String, Map<String, String>> deviceDelayConfigs = new ConcurrentHashMap<>();
    /**
     * 获取配置值（线程安全）
     * @param deviceType 设备类型
     * @param configKey 配置键
     * @return 配置值，不存在返回null
     */
    public static String getConfigValue(String deviceType, String configKey) {
        Map<String, String> deviceConfig = deviceDelayConfigs.get(deviceType);
        return deviceConfig != null ? deviceConfig.get(configKey) : "0";
    }

    /**
     * 整体替换配置
     */
    public static synchronized void setDeviceConfigs(Map<String, Map<String, String>> newConfigs) {
        deviceDelayConfigs.clear();
        deviceDelayConfigs.putAll(newConfigs);
    }

    /**
     * 等待延时时间
     */
    public static void waitForDelay(Integer delayTime) {
        if (delayTime > 0) {
            try {
                TimeUnit.SECONDS.sleep(delayTime);
            } catch (InterruptedException e) {
                log.error(e.getMessage());
            }
        }
    }
}
