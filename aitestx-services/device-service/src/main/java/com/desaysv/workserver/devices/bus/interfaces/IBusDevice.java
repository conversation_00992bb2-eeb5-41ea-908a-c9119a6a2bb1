package com.desaysv.workserver.devices.bus.interfaces;

import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.config.can.NetCanConfigParameter;
import com.desaysv.workserver.config.lin.LinConfigParameter;
import com.desaysv.workserver.devices.bus.base.can.CanLogRealTimeSaveParameter;
import com.desaysv.workserver.config.lin.NetLinConfigParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessageRealTimeSave;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;

public interface IBusDevice {

    //预留
    default void closeOpenButNotConfigDevice() {

    }

    default boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return false;
    }

    default boolean openChannel(NetCanConfigParameter netCanConfigParameter) throws DeviceOpenException {
        return false;
    }

    default boolean openChannel(int channel) throws DeviceOpenException {
        return false;
    }

    default boolean closeChannel(int channel) throws DeviceCloseException {
        return false;
    }

    default boolean openLinChannel(LinConfigParameter linConfigParameter) throws DeviceOpenException {
        return false;
    }

    default boolean openLinChannel(NetLinConfigParameter netLinConfigParameter) throws DeviceOpenException {
        return false;
    }

    default boolean closeLinChannel(int channel) throws DeviceCloseException {
        return false;
    }

    /**
     * dbc接收
     *
     * @param deviceChannel
     * @param dbcConfig
     * @throws Exception
     */
    default void startDbcReceiver(Integer deviceChannel, DbcConfig dbcConfig) throws Exception {

    }

    /**
     * 帧接收
     *
     * @param deviceChannel
     * @throws Exception
     */
    default void startFrameReceiver(Integer deviceChannel) throws Exception {

    }

    default void stopFrameReceiver(Integer deviceChannel) throws Exception {

    }

    default void stopDbcReceiver(Integer deviceChannel) throws Exception {

    }

    default void startRealTimeData(Integer deviceChannel, CanMessageRealTimeSave canMessageRealTimeSave) {
    }

    default void stopRealTimeData() {
    }
    default void startCaptureFrameCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter) {
    }

    default void stopCaptureFrameCanLog(Integer deviceChannel) {
    }

    default void startCaptureDbcCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter) {
    }

    default void stopCaptureDbcCanLog(Integer deviceChannel) {
    }

    default void saveLog(CanMessageRealTimeSave canMessageRealTimeSave) throws BlfException {
    }


    default void clearBuffer(int deviceChannel) {

    }


}
