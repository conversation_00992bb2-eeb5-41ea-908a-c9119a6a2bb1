package com.desaysv.workserver.devices.serial;

import com.desaysv.workserver.config.DeviceConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class SerialConfig extends DeviceConfig {

    private boolean sendOrReceiveWithHex; //是否hex发送接收

//    private boolean enableTouchDataTransfer; //启用报点传输

    private String commandsConfigPath; //指令集合配置路径

    private boolean saveLogFlag;//是否开启自动保存日志

    private int logSaveType;//log保存方式 0-日期 1-大小

    private String logSaveTimePeriod;//日志保存时间周期(日期) hour day

    private String logMaxFileSize;//日志最大保存量(大小) 10MB

    private Map<Integer, SerialMessage> textMapSendWhenOpen; //连接发送map

    private boolean useCustomLogPath; // 是否启用自定义日志路径

    private String customLogPath; // 自定义日志文件路径

    private float receiveTimeout; //序列全局等待接收时间

    public SerialConfig() {
        textMapSendWhenOpen = new HashMap<>();
        saveLogFlag = false;
        logMaxFileSize = "50MB";
        logSaveType = 0;
        logSaveTimePeriod = "day";
        useCustomLogPath = false;
        customLogPath = "";
        receiveTimeout = 1;
    }

}
