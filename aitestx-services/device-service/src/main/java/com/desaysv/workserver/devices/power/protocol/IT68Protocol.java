package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.devices.power.base.BundlePowerData;
import com.desaysv.workserver.devices.power.base.PowerDataDecoder;
import com.desaysv.workserver.devices.power.base.PowerDevice;
import com.desaysv.workserver.exceptions.device.DeviceDataDecodeException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.utils.ArrayUtils;
import com.desaysv.workserver.utils.StrUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 10:09
 * @description : 适用于IT6821/IT6822/IT6823/IT6831/IT6832/IT6833/IT6834/IT6832A/IT6833A/IT6861A/IT6862A/IT6863A/IT6872A/IT6873A/IT6874A
 * @modified By :
 * @since : 2022-6-29
 */
@Slf4j
public class IT68Protocol extends PowerBytesProtocol {
    private final PowerDevice device;

    public IT68Protocol(PowerDevice device) {
        this.device = device;
    }

    public boolean sendBytes(byte[] bytes) {
        checksum();
        try {
            return device.send(bytes);
        } catch (DeviceSendException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void decodeData(byte[] data, PowerDataDecoder powerDataDecoder) throws DeviceDataDecodeException {
        if (data.length != DATA_LENGTH) {
            throw new DeviceDataDecodeException(String.format("字节长度为%d,不符合%d个字节",
                    data.length, DATA_LENGTH));
        }
        if (data[CHECK_SUM] != getCheckSum(data)) {
            throw new DeviceDataDecodeException("校验码错误:" + StrUtils.getHexString(data));
        }

        if (data[SYNC_HEAD] == (byte) HEADER && data[POWER_ADDR] == (byte) ADDR && data[CMD_BYTE] == (byte) 0x26) {
            byte[] iByte = new byte[4];
            iByte[0] = 0x00;
            iByte[1] = 0x00;
            iByte[2] = data[4];
            iByte[3] = data[3];
            int current = ArrayUtils.byteArrayToIntLowFirst(iByte);

            byte[] vByte = new byte[4];
            vByte[0] = data[8];
            vByte[1] = data[7];
            vByte[2] = data[6];
            vByte[3] = data[5];

            int voltage = ArrayUtils.byteArrayToIntLowFirst(vByte);

            BundlePowerData bundlePowerData = new BundlePowerData();
            bundlePowerData.setVoltage((float) (voltage / 1000.0));
            bundlePowerData.setCurrent((float) (current / 1000.0));
            powerDataDecoder.decodeData(bundlePowerData);
        }

    }

    @Override
    public boolean setRemoteMode(boolean isRemote) {
        byte[] bytes = getBytes();
        bytes[CMD_BYTE] = (byte) 0x20;
        bytes[MSG_START_BYTE] = (byte) (isRemote ? 0x01 : 0x00);
        return sendBytes(bytes);
    }

    @Override
    public boolean fetchOutput() {
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        byte[] bytes = getBytes();
        bytes[CMD_BYTE] = (byte) 0x21;
        bytes[MSG_START_BYTE] = (byte) (isOutput ? 0x01 : 0x00);
        return sendBytes(bytes);
    }

    private boolean sendVoltage(byte cmdByte, float voltage) {
        byte[] bytes = getBytes();
        bytes[CMD_BYTE] = cmdByte;
        int vInt = (int) (1000 * voltage);
        byte[] vByte = ArrayUtils.intToByteArrayLowFirst(vInt);
        bytes[MSG_START_BYTE] = vByte[3];
        bytes[MSG_START_BYTE + 1] = vByte[2];
        bytes[MSG_START_BYTE + 2] = vByte[1];
        bytes[MSG_START_BYTE + 3] = vByte[0];
        return sendBytes(bytes);
    }

    @Override
    public boolean setVoltageMax(Integer channel, float voltage) {
        return sendVoltage((byte) 0x22, voltage);
    }

    @Override
    public boolean setVoltage(Integer channel, float voltage) {
        return sendVoltage((byte) 0x23, voltage);
    }

    @Override
    public boolean setCurrent(Integer channel, float current) {
        byte[] bytes = getBytes();
        bytes[CMD_BYTE] = (byte) 0x24;
        int iInt = (int) (1000 * current);
        byte[] iByte = ArrayUtils.intToByteArrayLowFirst(iInt);
        bytes[MSG_START_BYTE] = iByte[3];
        bytes[MSG_START_BYTE + 1] = iByte[2];
        return sendBytes(bytes);
    }

    @Override
    public boolean readData() {
        byte[] bytes = getBytes();
        bytes[CMD_BYTE] = (byte) 0x26;
        return sendBytes(bytes);
    }

    @Override
    public boolean readVoltage(Integer channel) {
        return readData();
    }

    @Override
    public boolean readCurrent(Integer channel) {
        return readData();
    }
}
