package com.desaysv.workserver.devices.device_udp.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.device_udp.DIDInfo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.python.bouncycastle.util.encoders.Hex;

import java.util.Map;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.*;
import static com.desaysv.workserver.devices.device_udp.CommandUdpClient.isNetworkReachable;
import static com.desaysv.workserver.devices.device_udp.XcpCommander.bytesToHex;
import static com.desaysv.workserver.devices.device_udp.XcpCommander.convertLittleEndianToInt;
import static com.desaysv.workserver.utils.ByteUtils.*;

public interface IDeviceUDP {
    Logger log = LogManager.getLogger(IDeviceUDP.class.getSimpleName());

    Map<String, DIDInfo> getDIDInfo(String items);

    boolean setDIDInfo(String items, String command);

    boolean sendCommandAndReceiveData(String variableName, byte[] command);

    byte[] getReceiveData(String variableName);

    boolean reconnectUdpDevice(int tryTimes);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SEND_UDP_COMMAND"})
    default ActualExpectedResult sendUdpDeviceCommand(Integer deviceChannel, String variableName, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = sendCommandAndReceiveData(variableName, StringToHexByteArr(command));
        actualExpectedResult.put("sendSimpleCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).READ_UDP_DATA"})
    default ActualExpectedResult readUdpDeviceData(Integer deviceChannel, String variableName, String expectedData) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);
        boolean pass = compare(data, expectedData);
        actualExpectedResult.put("readSimpleData", pass, bytesToHex(data));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_RESISTANCE_RANGE_DATA"})
    default ActualExpectedResult compareResistanceRange(Integer deviceChannel, String variableName, float resistanceBaseValue, float resistanceRange) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);
        float getOhm = getOhms(convertLittleEndianToInt(data), "ohm");  //单位处理成k欧姆
        boolean pass = getOhm >= ((resistanceBaseValue - resistanceRange) * 1000) && getOhm <= ((resistanceBaseValue + resistanceRange) * 1000); //电阻值都处理成统一单位后再比较
        actualExpectedResult.put("compareVoltageRange", pass, String.format("%sΩ", getOhm));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    /**
     * 采集电压高低电平：
     * 格式：1.UDP-getVoltage-观测量变量名-0/1
     */

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_VOLTAGE_HL_LEVEL"})
    default ActualExpectedResult compareVoltageLevel(Integer deviceChannel, String variableName, int levelValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);  //电压返回值0或者1，0代表低电平，1代表高电平
        int getVoltage = convertLittleEndianToInt(data);  //数据转换
        log.info("UDP目标返回:{}--表示{}", getVoltage, getVoltage == 0 ? "低电平" : "高电平");
        boolean pass = levelValue == getVoltage;
        actualExpectedResult.put("compareVoltageLevel", pass, getVoltage);
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    /**
     * 采集电压值在某个范围内：
     * 格式：1.UDP-getVoltage-观测量变量名-Random-11v-13v
     */

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_VOLTAGE_RANDOM_DATA"})
    default ActualExpectedResult compareVoltageRandom(Integer deviceChannel, String variableName, String lowerVoltageWithUnit,
                                                      String upperVoltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);   //电压返回值单位是mv
        String unit = getUnit(lowerVoltageWithUnit, "V");
        float getVoltage = valueUnitConvert(convertLittleEndianToInt(data), "mv", unit);  //单位处理成v
        float voltageLowerLimit = getVoltage(lowerVoltageWithUnit, unit);
        float voltageUpperLimit = getVoltage(upperVoltageWithUnit, unit);
        boolean pass = getVoltage >= voltageLowerLimit && getVoltage <= voltageUpperLimit;   //电压值都处理成统一单位后再比较
        actualExpectedResult.put("compareVoltageRandom", pass, String.format("%s%s", getVoltage, unit));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_VOLTAGE_RANGE_DATA"})
    default ActualExpectedResult compareVoltageRange(Integer deviceChannel, String variableName, String voltageBaseValueWithUnit,
                                                     String voltageRangeWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);
        String unit = getUnit(voltageBaseValueWithUnit, "V");
        float getVoltage = valueUnitConvert(convertLittleEndianToInt(data), "mv", unit);  //单位处理成v
        float voltageBaseValue = getVoltage(voltageBaseValueWithUnit, unit);
        float voltageRange = getVoltage(voltageRangeWithUnit, unit);
        boolean pass = getVoltage >= voltageBaseValue - voltageRange && getVoltage <= voltageBaseValue + voltageRange; //电压值都处理成统一单位后再比较
        actualExpectedResult.put("compareVoltageRange", pass, String.format("%s%s", getVoltage, unit));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_CURRENT_RANDOM_DATA"})
    default ActualExpectedResult compareCurrentRandom(Integer deviceChannel, String variableName, String lowerVoltageWithUnit,
                                                      String upperVoltageWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);
        String unit = getUnit(lowerVoltageWithUnit, "A");
        float getCurrent = valueUnitConvert(convertLittleEndianToInt(data), "mA", unit);  //单位处理成A
        float lowerVoltage = parseCurrent(lowerVoltageWithUnit, unit);
        float upperVoltage = parseCurrent(upperVoltageWithUnit, unit);
        boolean pass = getCurrent >= lowerVoltage && getCurrent <= upperVoltage;
        actualExpectedResult.put("compareCurrentRandom", pass, String.format("%s%s", getCurrent,unit));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_CURRENT_RANGE_DATA"})
    default ActualExpectedResult compareCurrentRange(Integer deviceChannel, String variableName, String currentBaseValueWithUnit,
                                                     String currentRangeWithUnit) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] data = getReceiveData(variableName);
        String unit = getUnit(currentBaseValueWithUnit, "A");
        float getCurrent = valueUnitConvert(convertLittleEndianToInt(data), "mA", unit);  //单位处理成A
        float currentBaseValue = parseCurrent(currentBaseValueWithUnit);
        float currentRange = parseCurrent(currentRangeWithUnit);
        boolean pass = getCurrent >= currentBaseValue - currentRange && getCurrent <= currentBaseValue + currentRange;
        actualExpectedResult.put("compareCurrentRange", pass, String.format("%s%s", getCurrent, unit));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_PWM_RANGE_DATA"})
    default ActualExpectedResult comparePWMRange(Integer deviceChannel, String freqVariableName, String frequencyBaseValueWithUnit,
                                                 String frequencyRangeWithUnit, String dutyVariableName, float dutyBaseValue, float dutyRange) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        byte[] freqData = getReceiveData(freqVariableName);
        byte[] dutyData = getReceiveData(dutyVariableName);
        float getFrequency = convertFrequency(convertLittleEndianToInt(freqData), "hz");  //单位处理成v
        float getDuty = convertLittleEndianToInt(dutyData);
        if (getDuty > 100) {
            getDuty = getDuty / 10;
        }
        float frequencyBaseValue = parseFrequency(frequencyBaseValueWithUnit);
        float frequencyRange = parseFrequency(frequencyRangeWithUnit);
        boolean frequencyPass = getFrequency >= frequencyBaseValue - frequencyRange && getFrequency <= frequencyBaseValue + frequencyRange;
        boolean dutyPass = getDuty >= dutyBaseValue - dutyRange && getDuty <= dutyBaseValue + dutyRange;
        boolean pass = frequencyPass && dutyPass;
        actualExpectedResult.put("comparePWMRange", pass, String.format("频率：%sHz,占空比：%s%%", getFrequency, getDuty));
        log.info("读取UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    //该接口在测试中暂时不用
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SET_PWM_DATA"})
    default ActualExpectedResult controlPinPWMCommand(Integer deviceChannel, String freqVariableName, String frequencyValueWithUnit, String dutyVariableName, float dutyValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        float frequencyValue = parseFrequency(frequencyValueWithUnit);
        int duty = (int) (dutyValue * 10);
//        boolean frequencyPass = sendCommandAndReceiveData(freqVariableName, hexStringToByteArr(String.format("%02X", frequencyValue)));
        boolean dutyPass = sendCommandAndReceiveData(dutyVariableName, decimalToLittleEndianBytes(duty, 2));
//        boolean pass = frequencyPass && dutyPass;
        boolean pass = dutyPass;
        actualExpectedResult.put("sendSimpleCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SET_DUTY_DATA"})
    default ActualExpectedResult sendDutyCommand(Integer deviceChannel, String variableName, float dutyValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        int dutyCommand = dutyValue == 50 ? 2 : dutyValue == 80 ? 3 : 1;
        boolean pass = sendCommandAndReceiveData(variableName, hexStringToByteArr(String.format("%02X", dutyCommand)));
        actualExpectedResult.put("sendDutyCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SET_DUTY_VALUE"})
    default ActualExpectedResult sendDutyValueCommand(Integer deviceChannel, String variableName, float dutyValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = sendCommandAndReceiveData(variableName, decimalToLittleEndianBytes((int) (dutyValue * 10), 2));
        actualExpectedResult.put("sendDutyCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SEND_RECONNECT_TIMES_COMMAND"})
    default ActualExpectedResult sendReconnectCommand(Integer deviceChannel, int tryTimes) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = reconnectUdpDevice(tryTimes);
        actualExpectedResult.put("sendPingCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    //暂时不需要该序列测试，因为已经能与样机连接，所以不需要测试ping功能
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SEND_UDP_PING_COMMAND"})
    default ActualExpectedResult sendPingCommand(Integer deviceChannel, String ipAddress, String port) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = isNetworkReachable(ipAddress, Integer.parseInt(port), 10000, 3, 100);
        actualExpectedResult.put("sendPingCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).SET_DID_INFO"})
    default ActualExpectedResult sendDIDCommand(Integer deviceChannel, String items, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setDIDInfo(items, command);
        actualExpectedResult.put("sendSimpleCommand", pass, "");
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.UdpDeviceRegexRule).GET_DID_INFO"})
    default ActualExpectedResult compareDIDCommand(Integer deviceChannel, String items, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        Map<String, DIDInfo> didMap = getDIDInfo(items);
        DIDInfo didInfo = didMap.get(items);
        boolean pass = command.equals(didInfo.getParseData());
        actualExpectedResult.put("compareDIDCommand", pass, String.format("UDP指令返回：%s, 转换成字符：%s", Hex.toHexString(didInfo.getReceiveDataBytes()), didInfo.getParseData().replaceAll("_", "-")));
        log.info("发送指令到UDP目标响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }
}
