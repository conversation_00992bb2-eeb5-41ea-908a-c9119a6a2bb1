package com.desaysv.workserver.devices.electric_relay.service.impl;

import com.desaysv.workserver.common.constant.RelayConstants;
import com.desaysv.workserver.devices.electric_relay.ElectricRelayConfig;
import com.desaysv.workserver.devices.electric_relay.service.ElectricRelayService;
import com.desaysv.workserver.devices.testbox.RelayConfigManager;
import com.desaysv.workserver.mapper.ElectricRelayMapper;
import com.desaysv.workserver.model.relay.ElectricRelayEntity;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ElectricRelayServiceImpl 类实现了 ElectricRelayService 接口，用于提供继电器相关的服务功能。
 */
@Component
public class ElectricRelayServiceImpl implements ElectricRelayService {
    @Autowired
    private ElectricRelayMapper electricRelayMapper;

    /**
     * 将继电器延迟时间配置信息传递给中继配置管理器进行设置。
     */
    @Override
    public void forwardRelayConfig(Map<String, Map<String, String>> deviceDelayConfigs) {
        RelayConfigManager.setDeviceConfigs(deviceDelayConfigs);
    }

    /**
     * 处理电继电器配置的保存或更新逻辑。
     */
    @Override
    public void updateRelayTextConfig(ElectricRelayConfig electricRelayConfig) {
        // 没有通道 为刚连接状态 判断数据是否已经保存
        if (electricRelayConfig.getChannel() == null) {
            // 处理新继电器连接初始化
            handleNewRelayConnection(electricRelayConfig);
        } else {
            // 存在通道时执行更新操作
            ElectricRelayEntity entity = getElectricRelayEntity(electricRelayConfig);
            electricRelayMapper.updateText(entity);
        }
    }

    /**
     * 继电器修改或新增文本框数据。
     */
    @Override
    public List<ElectricRelayConfig> getRelayTextConfig(Map<String, String> paramsMap) {
        List<ElectricRelayEntity> relayList = electricRelayMapper.selectByAliasAndProject(paramsMap);
        // 组装实体对象返回前端
        return getElectricRelayConfigs(relayList);
    }

    /**
     * 更新继电器状态配置
     */
    @Override
    public void updateRelayStatusConfig(ElectricRelayConfig config) {
        // 创建实体对象并拷贝配置属性
        ElectricRelayEntity entity = new ElectricRelayEntity();
        BeanUtils.copyProperties(config, entity);
        entity.setProject(config.getProject());

        // 执行数据库更新操作
        electricRelayMapper.updateStatus(entity);
    }


    /**
     * 更新连接开关状态
     */
    @Override
    public void updateConnectSwitchStatus(ElectricRelayConfig config) {
        // 创建实体对象并拷贝配置属性
        ElectricRelayEntity entity = new ElectricRelayEntity();
        BeanUtils.copyProperties(config, entity);
        entity.setProject(config.getProject());

        // 执行数据库更新操作
        electricRelayMapper.updateConnectSwitchStatus(entity);
    }

    /**
     * 处理新继电器连接初始化
     * 当新继电器设备连接时，检查系统中是否已存在相同别名和项目的配置
     * 若不存在则初始化16个通道的默认配置并保存至数据库
     */
    private void handleNewRelayConnection(ElectricRelayConfig electricRelayConfig) {
        // 构建查询参数：按别名和项目查询现有记录
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("aliasName", electricRelayConfig.getAliasName());
        paramsMap.put("project", electricRelayConfig.getProject());
        List<ElectricRelayEntity> relayList = electricRelayMapper.selectByAliasAndProject(paramsMap);

        // 没有相关数据 新增操作
        if (relayList == null || relayList.isEmpty()) {
            // 创建并初始化entityList对象
            List<ElectricRelayEntity> entityList = getElectricRelayEntityList(electricRelayConfig);

            // 新增电继电器实体中的文本信息
            for (ElectricRelayEntity electricRelayEntity : entityList) {
                // 设置默认值
                electricRelayEntity.setSwitchOn(RelayConstants.SWITCH_OFF);
                electricRelayEntity.setConnectedStatus(RelayConstants.CONNECTED_STATUS_1);
                electricRelayMapper.insertText(electricRelayEntity);
            }
        }
    }

    /**
     * 获取电继电器配置列表
     * 将电继电器实体列表按照别名分组，并转换为电继电器配置对象列表。
     * 每个配置对象包含对应分组的所有通道文本信息，以及从第一个实体中复制的公共属性。
     *
     * @param relayList 电继电器实体列表，包含需要转换的电继电器数据
     * @return List<ElectricRelayConfig> 转换后的电继电器配置列表，按别名分组后的配置集合
     */
    private List<ElectricRelayConfig> getElectricRelayConfigs(List<ElectricRelayEntity> relayList) {
        // 按照别名分组电继电器实体
        Map<String, List<ElectricRelayEntity>> entityMapList = relayList.stream().collect(Collectors.groupingBy(ElectricRelayEntity::getAliasName));
        List<ElectricRelayConfig> relayConfigList = new ArrayList<>();

        // 处理每个分组
        for (Map.Entry<String, List<ElectricRelayEntity>> entry : entityMapList.entrySet()) {
            List<ElectricRelayEntity> entityList = entry.getValue();
            ElectricRelayConfig config = new ElectricRelayConfig();

            // 创建并初始化phyText列表
            List<String> phyText = new ArrayList<>(Collections.nCopies(16, ""));
            List<String> switchOnList = new ArrayList<>(Collections.nCopies(16, RelayConstants.SWITCH_OFF));

            // 设置每个通道对应的继电器文本（注意：channel-1作为索引）
            for (ElectricRelayEntity entity : entityList) {
                phyText.set(entity.getChannel() - 1, entity.getRelayText());
                switchOnList.set(entity.getChannel() - 1, entity.getSwitchOn());
            }

            // 复制公共属性并构建配置对象
            ElectricRelayEntity relayEntity = entityList.get(0);
            BeanUtils.copyProperties(relayEntity, config);
            config.setProject(relayEntity.getProject());
            config.setPhyText(phyText);
            config.setSwitchOnList(switchOnList);

            // 添加配置对象到列表中
            relayConfigList.add(config);
        }
        return relayConfigList;
    }

    /**
     * 根据ElectricRelayConfig配置创建并初始化ElectricRelayEntity对象
     *
     * @param electricRelayConfig 继电器配置对象，包含项目信息、别名和物理文本列表
     */
    private ElectricRelayEntity getElectricRelayEntity(ElectricRelayConfig electricRelayConfig) {
        // 创建新的继电器实体对象
        ElectricRelayEntity electricRelayEntity = new ElectricRelayEntity();

        // 前端通道 文本标签
        List<String> phyText = electricRelayConfig.getPhyText();

        // 设置基础属性 将electricRelayConfig对象的属性值复制到electricRelayEntity对象中
        BeanUtils.copyProperties(electricRelayConfig, electricRelayEntity);
        electricRelayEntity.setProject(electricRelayConfig.getProject());

        // 设置16个继电器列属性，phyText列表必须包含至少16个元素
        if (electricRelayConfig.getChannel() != null) {
            electricRelayEntity.setRelayText(phyText.get(electricRelayConfig.getChannel() - 1));
        }

        return electricRelayEntity;
    }

    /**
     * 根据ElectricRelayConfig配置创建并初始化ElectricRelayEntityList对象 用于第一次新增操作
     *
     * @param electricRelayConfig 继电器配置对象，包含项目信息、别名和物理文本列表
     */
    private List<ElectricRelayEntity> getElectricRelayEntityList(ElectricRelayConfig electricRelayConfig) {
        // 创建并初始化entityList对象
        List<ElectricRelayEntity> entityList = new ArrayList<>();

        // 前端通道 文本标签
        List<String> phyText = electricRelayConfig.getPhyText();
        for (int i = 0; i < phyText.size(); i++) {
            // 创建新的继电器实体对象
            ElectricRelayEntity electricRelayEntity = new ElectricRelayEntity();

            // 设置基础属性 将electricRelayConfig对象的属性值复制到electricRelayEntity对象中
            BeanUtils.copyProperties(electricRelayConfig, electricRelayEntity);
            electricRelayEntity.setProject(electricRelayConfig.getProject());

            // 设置16个继电器列属性，phyText列表必须包含至少16个元素
            electricRelayEntity.setRelayText(phyText.get(i));
            electricRelayEntity.setChannel(i + 1);
            entityList.add(electricRelayEntity);
        }
        return entityList;
    }
}