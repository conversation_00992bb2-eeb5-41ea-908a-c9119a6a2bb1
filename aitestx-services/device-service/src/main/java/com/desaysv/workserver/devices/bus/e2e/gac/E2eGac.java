package com.desaysv.workserver.devices.bus.e2e.gac;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/6/24 18:11
 * @Version: 1.0
 * @Desc : 描述信息
 */
@Slf4j
public class E2eGac implements CanMessageEventListener {

    private static final Map<Integer, Integer> counters = new ConcurrentHashMap<>();
    // 初始计数器值
    private int initialCounterValue = 0;
    private final CanConfig canConfig;
    private final int channel;
    private static final int COUNTER_BYTE = 2;
    private final int MIN_COUNTER_LENGTH = 0;
    private final int MAX_COUNTER_LENGTH = 255;



    public E2eGac(CanConfig canConfig, int channel) {
        this.canConfig = canConfig;
        this.channel = channel;
    }

    public byte[] e2e_calculate_gac(int canId, byte[] canData) {
        try {
            // 获取 E2eCheryE0XTableManager 的单例实例
            E2eGacTableManager tableManager = E2eGacTableManager.getInstance();
            if (tableManager == null) {
                log.info("E2eGacTableManager instance is null");
                return canData;
            }
            // 获取 e2eMap
            Map<Integer, Integer> e2eMap = tableManager.e2eMap;
            if (e2eMap == null) {
                log.info("e2eMap is null");
                return canData;
            }

            // 获取 dataId
            Integer dataId = e2eMap.get(canId);
            if (dataId != null && dataId != 0) {
                if (!counters.containsKey(canId)) {
                    counters.put(canId, initialCounterValue);
                }
                canData = e2eProfile5(canId, canData, dataId);
            }
        } catch (Exception e) {
            log.info("E2E Calculation Exception: {}", e.getMessage());
        }

        return canData;
    }

    private byte[] e2eProfile5(int canId, byte[] data, int dataID) {
        try {
            data[COUNTER_BYTE] = e2eCounter(canId, data[COUNTER_BYTE]);
            byte[] dataId = new byte[2];
            dataId[0] = (byte) ((dataID & 0xff00) >> 8);
            dataId[1] = (byte) (dataID & 0x00ff);
            int[] crcResult = getCRC1021(data,dataId,data.length);
            data[0] = (byte) crcResult[1]; // 低位字节
            data[1] = (byte) crcResult[0]; // 高位字节     此处是因为要求低位字节在前，高位字节在后是官方要求
        } catch (Exception e) {
            log.info("e2eProfile5Exception:", e);
        }
        return data;
    }

    public byte e2eCounter(int canId, byte data) {
        int counterValue = counters.get(canId);
        counterValue++;
        if (counterValue >= MAX_COUNTER_LENGTH) {
            counterValue = MIN_COUNTER_LENGTH;
        }
        counters.put(canId, counterValue);
        data = (byte)counterValue;
        return data;
    }




    /**
     * 直接计算CRC1021
     * CRC1021校验
     *
     * @return
     */
    public int[] getCRC1021(byte[] data_arr, byte[] dataId, int data_len) {
        int crc16 = 0xFFFF;  // 初始值应为0xFFFF
        
        // 如果长度为64，则保护范围是2-55；否则保护从2到data_len的所有字节
        int endIdx = (data_len == 64) ? 56 : data_len;

        for (int i = 2; i < endIdx; i++) {
            crc16 ^= (data_arr[i] & 0xFF) << 8;

            for (int j = 0; j < 8; j++) {
                if ((crc16 & 0x8000) != 0) {
                    crc16 = (crc16 << 1) ^ 0x1021;
                } else {
                    crc16 <<= 1;
                }
            }
        }

        // 最后两个字节用dataId替换
        crc16 ^= (dataId[1] & 0xFF) << 8;
        for (int j = 0; j < 8; j++) {
            if ((crc16 & 0x8000) != 0) {
                crc16 = (crc16 << 1) ^ 0x1021;
            } else {
                crc16 <<= 1;
            }
        }

        crc16 ^= (dataId[0] & 0xFF) << 8;
        for (int j = 0; j < 8; j++) {
            if ((crc16 & 0x8000) != 0) {
                crc16 = (crc16 << 1) ^ 0x1021;
            } else {
                crc16 <<= 1;
            }
        }

        int[] result = new int[2];
        result[0] = (crc16 >> 8) & 0xFF; // 高位字节
        result[1] = crc16 & 0xFF;        // 低位字节
        return result;
    }

    @Override
    public void onMessageSend(CanMessage message) {
        if (message.getE2eType() == null) {
            message.setE2eType(canConfig.getE2eTypes().get(message.getChannel()));
            if ("GAC".equals(canConfig.getE2eTypes().get(message.getChannel()))) {
                message.setE2eEnabled(true);
            }
        }
        if (!message.isE2eEnabled()) {
            return;
        }
        if (message.getE2eType() != null && message.getE2eType().equalsIgnoreCase("GAC")) {
            message.setData(e2e_calculate_gac(message.getArbitrationId(), message.getData()));
        }
    }

    public static void main(String[] args) {
        CanConfig canConfig = new CanConfig();
        // 假设 CAN 配置中已经配置了 E2E 类型为 "CheryE01"
        canConfig.getE2eTypes().put(1, "GAC");
        E2eGac e2eGac = new E2eGac(canConfig, 1);

        for (int i = 0; i < 100; i++) {
            // 测试数据
            int canId = 0x188; // 示例 CAN ID
            byte[] canData = new byte[]{0x00,0x00,0x00,0x01, (byte) 0xff, (byte) 0xe0,0x00,0x00, (byte) 0xff,0x00,0x00,0x3e, (byte) 0xdb, (byte) 0x88,0x03, (byte) 0xf4,0x00,0x0a, (byte) 0xa8,0x02,0x72, (byte) 0xaa,0x28, (byte) 0x83,0x03,0x1e,0x00,0x00,0x00,0x00,0x00,0x00, (byte) 0xff, (byte) 0xff,0x00,0x00,0x00,0x00,0x68, (byte) 0x81,0x0a,0x00,0x3f, (byte) 0xc2,0x44,0x00,0x11, (byte) 0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00};

            byte[] processedData = e2eGac.e2e_calculate_gac(canId, canData);

            System.out.println("Processed CAN Data:");
            for (byte b : processedData) {
                System.out.printf("%02X ", b);
            }
        }
    }

    public static String idChange(int id, boolean isExtend) {
        if (!isExtend && id > 0x7FF) {
            throw new IllegalArgumentException("ID 超出标准帧范围，请检查输入");
        }
        if (id >= 0x1FFFFFFF) {
            throw new IllegalArgumentException("ID 超出最大允许值 0x1FFFFFFF");
        }

        String hexId = Integer.toHexString(id).toUpperCase();
        if (isExtend) {
            hexId += "x";
        }
        return hexId;
    }


}
