package com.desaysv.workserver.screen.entity;

import lombok.Data;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

@Data
public class ColorCellCollection {

    private String text;
    private String color; // Represented as hex string, e.g., "#FFA500"
    private List<Cell> cells;

    public Cell firstCell() {
        return cells.get(0);
    }

    public ColorCellCollection() {
        cells = new ArrayList<>();
    }

}