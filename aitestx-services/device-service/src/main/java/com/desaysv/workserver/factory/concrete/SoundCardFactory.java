package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.soundcard.AuxInSoundDevice;
import com.desaysv.workserver.devices.soundcard.USB4704;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractSoundCardFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lpf
 * @date : Created in 2024-10-15
 * @description :
 * @modified By :
 * @since : 2024-10-15
 */
@Component
@Lazy
public class SoundCardFactory implements AbstractSoundCardFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        return createSoundCardDevice(deviceRegisterForm);
    }

    @Override
    public Device createSoundCardDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if  (deviceModel.equals(DeviceModel.SoundCard.USB4704_DEVICE)) {
            return new USB4704(deviceRegisterForm.getDeviceOperationParameter());
        }else if (deviceModel.equals(DeviceModel.SoundCard.AUXIN_SOUND_DEVICE)) {
            return new AuxInSoundDevice(deviceRegisterForm.getDeviceOperationParameter());
        }
        return null;
    }
}
