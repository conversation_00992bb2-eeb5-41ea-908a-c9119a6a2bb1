package com.desaysv.workserver.devices.signal_generator;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.oscilloscope.RigolSCPICommands;
import com.desaysv.workserver.devices.signal_generator.interfaces.ISignalGenerator;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.google.common.base.Preconditions;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

@Slf4j
public class TKSignalGenerator extends SignalGeneratorDevice implements ISignalGenerator {
    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;
    @JSONField(serialize = false)
    private JVisaInstrument instrument;

    public TKSignalGenerator() {
        this(new DeviceOperationParameter());
    }

    public TKSignalGenerator(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.SignalGenerator.TK_AFG_1022;
    }

    public String sendAndReceiveString(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        return instrument.queryString(command);
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (visaResourceManager == null) {
            try {
                visaResourceManager = new JVisaResourceManager();
            } catch (JVisaException e) {
                log.warn(e.getMessage(), e);
            }
        }
        try {
            instrument = visaResourceManager.openInstrument(getDeviceName());
            initializeTKAFG1022();
        } catch (JVisaException e) {
            log.error(e.getMessage(), e);
        }
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (instrument != null) {
            try {
                instrument.write(RigolSCPICommands.CLEAR_SCREEN);
                instrument.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (visaResourceManager != null) {
            try {
                visaResourceManager.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        return true;
    }

    private void checkInstrument() {
        Preconditions.checkNotNull(instrument, "仪器JVisaInstrument实例不存在");
    }

    /**
     * 初始化信号发生器到默认状态。
     */
    public void initializeTKAFG1022() throws JVisaException {
        resetTKAFG1022();
        setWaveformCmd(1, 4);
        setOUTPut(1, true);   //初始化把输出通道打开
//        setWaveformFrequencyCmd(1,300);
//        setWaveformAmplitudeCmd(1, 5);
//        setWaveformDCYCleCmd(1,80);
    }

    private void resetTKAFG1022() throws JVisaException {
        sendCommand(TKSCPICommands.RESET_DEVICE);
    }

    private void setWaveformCmd(int channel, int waveformCode) throws JVisaException {
        String waveform = getWaveformByCode(waveformCode);
        setWaveformCmd(channel, waveform);
    }

    private String getWaveformByCode(int waveformCode) {
        String waveform;
        switch (waveformCode) {
            case 1:
                waveform = "SIN";
                break;
            case 2:
                waveform = "SQU";
                break;
            case 3:
                waveform = "RAMP";
                break;
            case 5:
                waveform = "NOISe";
                break;
            case 6:
                waveform = "DC";
                break;
            case 7:
                waveform = "USER";
                break;
            default:
                waveform = "PULSe";
                break;
        }
        return waveform;
    }

    private void setWaveformCmd(int channel, String waveform) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_FUNCTION_SHAPE, channel, waveform));
    }

    private void setWaveformFrequencyCmd(int channel, float frequencyValue) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_FREQUENCY_FIXED, channel, frequencyValue));
    }

    private void setWaveformAmplitudeCmd(int channel, int amplitudeValue) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_AMPLITUDE, channel, amplitudeValue));
    }

    private void setWaveformDCYCleCmd(int channel, double dutyCycleValue) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_PULSE_DUTY, channel, String.format("%.2f%%", dutyCycleValue)));
    }

    private void setOUTPut(int channel, boolean open) throws JVisaException {
//        sendCommand("OUTPUT1:STATE ON");
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SET_OUTPUT_STATE, channel, open ? "ON" : "OFF"));
    }

    private void setWaveFormPulseWidth(int channel, String value) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_PULSE_WIDTH, channel, value));
    }


    private void setWaveFormVoltageOffset(int channel, String value) throws JVisaException {
        sendCommand(TKSCPICommands.formatCommand(TKSCPICommands.SOURCE_VOLTAGE_OFFSET, channel, value));
    }

    private void sendCommand(String command) throws JVisaException {
        Preconditions.checkNotNull(instrument, "仪器实例为空");
        instrument.write(command);
    }


    @Override
    public boolean setSignalInput(Integer deviceChannel, int waveFormCode, int amplitudeValue, float frequencyValue, float dutyValue) {
        try {
            setWaveformCmd(deviceChannel, waveFormCode);
            setWaveformFrequencyCmd(deviceChannel, frequencyValue);
            setWaveformAmplitudeCmd(deviceChannel, amplitudeValue);
            setWaveformDCYCleCmd(deviceChannel, dutyValue);
        } catch (JVisaException e) {
            log.error("设置信号发生器失败", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean setSignalInput(Integer deviceChannel, int amplitudeValue, float frequencyValue, float dutyValue) {
        try {
            setWaveformCmd(deviceChannel, 4); //默认波形为PULSe
            setWaveformAmplitudeCmd(deviceChannel, amplitudeValue);
            setWaveformFrequencyCmd(deviceChannel, frequencyValue);
            setWaveformDCYCleCmd(deviceChannel, dutyValue);
//            setOUTPutOn(deviceChannel, true);
        } catch (JVisaException e) {
            log.error("设置信号发生器失败", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean setWaveForm(Integer deviceChannel, int waveFormCode) {
        try {
            setWaveformCmd(deviceChannel, waveFormCode);
        } catch (JVisaException e) {
            log.error("设置波形失败", e.getMessage());
            return false;
        }
        return false;
    }

    @Override
    public boolean setFrequency(Integer deviceChannel, float frequencyValue) {
        try {
            setWaveformFrequencyCmd(deviceChannel, frequencyValue);
        } catch (JVisaException e) {
            log.error("设置频率失败", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean setAmplitude(Integer deviceChannel, int amplitudeValue) {
        try {
            setWaveformAmplitudeCmd(deviceChannel, amplitudeValue);
        } catch (JVisaException e) {
            log.error("设置幅值失败", e.getMessage());
            return false;
        }
        return true;
    }

    @Override
    public boolean setDutyCycle(Integer deviceChannel, float dutyCycleValue) {
        try {
            setWaveformDCYCleCmd(deviceChannel, dutyCycleValue);
        } catch (JVisaException e) {
            log.error("设置占空比失败", e.getMessage());
            return false;
        }
        return true;
    }

    public static void main(String[] args) {
        TKSignalGenerator signalGenerator = new TKSignalGenerator(new DeviceOperationParameter());
        signalGenerator.setDeviceName("USB0::0x0699::0x0353::2238338::INSTR");
        try {
            signalGenerator.open();
//            signalGenerator.setOUTPutOn(1, false);
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            signalGenerator.close();
        } catch (DeviceOpenRepeatException e) {
            throw new RuntimeException(e);
        } catch (DeviceOpenException e) {
            throw new RuntimeException(e);
        } catch (DeviceCloseException e) {
            throw new RuntimeException(e);
        }
    }
}
