package com.desaysv.workserver.factory;

import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.autoclicker.AutoClickerDevice;
import com.desaysv.workserver.devices.tcpserver.TcpServerDevice;
import com.desaysv.workserver.devices.usbplug.UsbPlugDevice;
import com.desaysv.workserver.devices.usbswtich.UsbSwitchDevice;
import com.desaysv.workserver.devices.videocapture.VideoCaptureDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.concrete.*;
import com.desaysv.workserver.factory.interfaces.AbstractDeviceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 18:49
 * @description : 设备工厂
 * @modified By : lwj
 * @since : 2022-4-12
 */
@Component
@Lazy
public class DeviceFactory implements AbstractDeviceFactory {

    @Autowired
    private AndroidFactory androidFactory;

    @Autowired
    private QnxDeviceFactory qnxDeviceFactory;

    @Autowired
    private PowerFactory powerFactory;

    @Autowired
    private RobotFactory robotFactory;

    @Autowired
    private CameraFactory cameraFactory;

    @Autowired
    private SerialFactory serialFactory;

    @Autowired
    private DaqFactory daqFactory;

    @Autowired
    private CanFactory canFactory;

    @Autowired
    private LinFactory linFactory;

    @Autowired
    private EthernetFactory ethernetFactory;

    @Autowired
    private TestBoxFactory testBoxFactory;

    @Autowired
    private ResistanceFactory resistanceFactory;
    @Autowired
    private SoundCardFactory soundCardFactory;
    @Autowired
    private SpeakerFactory speakerFactory;
    @Autowired
    private OscilloscopeFactory oscilloscopeFactory;
    @Autowired
    private SignalGeneratorFactory signalGeneratorFactory;
    @Autowired
    private ElectronicLoadFactory electronicLoadFactory;
    @Autowired
    private ElectricRelayFactory electricRelayFactory;
    @Autowired
    private UdpDeviceFactory udpDeviceFactory;
    @Autowired
    private TcpClientClientDeviceFactory tcpClientDeviceFactory;
    @Autowired
    private DcCollectorDeviceFactory dcCollectorDeviceFactory;

    private Device configDevice(Device device, DeviceRegisterForm deviceRegisterForm) {
        if (device != null) {
            device.setDeviceIndex(deviceRegisterForm.getDeviceIndex());
            device.setDeviceName(deviceRegisterForm.getDeviceName());
            device.setDeviceUniqueCode(deviceRegisterForm.getDeviceUniqueCode());
            if (deviceRegisterForm.getNumberChannels() > 0) {
                device.setNumberChannels(deviceRegisterForm.getNumberChannels());
            }
//            device.setDeviceAliasName(deviceRegisterForm.getDeviceAliasName());
            device.setAliasName(deviceRegisterForm.getAliasName());
            device.setDeviceOperationParameter(deviceRegisterForm.getDeviceOperationParameter());
            device.setDevicePort(deviceRegisterForm.getDevicePort());
            device.setChannel(deviceRegisterForm.getChannel());
            device.setBaudRate(deviceRegisterForm.getBaudRate());
            device.setSampleRate(deviceRegisterForm.getSampleRate());
            device.setSimulated(device.isSimulated() || deviceRegisterForm.isSimulated());
        }
        return device;
    }

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceType = deviceRegisterForm.getDeviceType();
        Device device = null;
//        AbstractDeviceCreator abstractDeviceCreator;
        //初始化设备
        //TODO：使用策略模式改进switch，利用abstract方法将创建对象的过程交给子类化实现，而不需要使用if
        switch (deviceType) {
            case DeviceType.DEVICE_POWER:
                device = createPowerDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_CAMERA:
                device = createCameraDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_SERIAL:
                device = createSerialDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_ROBOT:
                device = createRobotDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_DAQ:
                device = createDaqDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_LIN:
                device = createLinDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_CAN:
                device = createCanDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_ETHERNET:
                device = createEthernetDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_ANDROID:
                device = createAndroidDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_QNX:
                device = createQnxDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_USB_SWITCH:
                device = createUsbSwtichDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_TEST_BOX:
                device = createTestBoxDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_AUTO_CLICKER:
                device = createAutoClickerDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_PLUG:
                device = createPlugDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_ELECTRIC_RELAY:
                device = createElectricRelayDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_RESISTANCE:
                device = createResistanceDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_TCP_SERVER:
                device = createTcpServerDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_SOUND_CARD:
                device = createSoundCardDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_SPEAKER:
                device = createSpeakerDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_VIDEO_CAPTURE:
                device = createVideoCaptureDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_OSCILLOSCOPE:
                device = createOscilloscopeDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_SIGNAL_GENERATOR:
                device = createSignalGeneratorDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_ELECTRONIC_LOAD:
                device = createElectronicLoadDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_UDP:
                device = createUdpDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_TCP_CLIENT:
                device = createTcpClientDevice(deviceRegisterForm);
                break;
            case DeviceType.DEVICE_DC_COLLECTOR:
                device = createDcCollectorDevice(deviceRegisterForm);
                break;
        }
//        abstractDeviceCreator.createDevice(deviceRegisterForm);
        //配置设备
        return configDevice(device, deviceRegisterForm);
    }

    private Device createTcpClientDevice(DeviceRegisterForm deviceRegisterForm) {
        return tcpClientDeviceFactory.createDevice(deviceRegisterForm);
    }

    private Device createDcCollectorDevice(DeviceRegisterForm deviceRegisterForm) {
        return dcCollectorDeviceFactory.createDevice(deviceRegisterForm);
    }

    private Device createUdpDevice(DeviceRegisterForm deviceRegisterForm) {
        return udpDeviceFactory.createDevice(deviceRegisterForm);
    }

    private Device createVideoCaptureDevice(DeviceRegisterForm deviceRegisterForm) {
        return new VideoCaptureDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    private Device createLinDevice(DeviceRegisterForm deviceRegisterForm) {
        return linFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createAndroidDevice(DeviceRegisterForm deviceRegisterForm) {
        return androidFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createQnxDevice(DeviceRegisterForm deviceRegisterForm) {
        return qnxDeviceFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createPowerDevice(DeviceRegisterForm deviceRegisterForm) {
        return powerFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createUsbSwtichDevice(DeviceRegisterForm deviceRegisterForm) {
        return new UsbSwitchDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createCameraDevice(DeviceRegisterForm deviceRegisterForm) {
        return cameraFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createSerialDevice(DeviceRegisterForm deviceRegisterForm) {
        return serialFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createRobotDevice(DeviceRegisterForm deviceRegisterForm) {
        return robotFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createDaqDevice(DeviceRegisterForm deviceRegisterForm) {
        return daqFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createCanDevice(DeviceRegisterForm deviceRegisterForm) {
        return canFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createEthernetDevice(DeviceRegisterForm deviceRegisterForm) {
        return ethernetFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createTestBoxDevice(DeviceRegisterForm deviceRegisterForm) {
        return testBoxFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createAutoClickerDevice(DeviceRegisterForm deviceRegisterForm) {
        return new AutoClickerDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createPlugDevice(DeviceRegisterForm deviceRegisterForm) {
        return new UsbPlugDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createElectricRelayDevice(DeviceRegisterForm deviceRegisterForm) {
        return electricRelayFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createResistanceDevice(DeviceRegisterForm deviceRegisterForm) {
        return resistanceFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createTcpServerDevice(DeviceRegisterForm deviceRegisterForm) {
        return new TcpServerDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createSoundCardDevice(DeviceRegisterForm deviceRegisterForm) {
        return soundCardFactory.createSoundCardDevice(deviceRegisterForm);
    }

    @Override
    public Device createSpeakerDevice(DeviceRegisterForm deviceRegisterForm) {
        return speakerFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createOscilloscopeDevice(DeviceRegisterForm deviceRegisterForm) {
        return oscilloscopeFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createSignalGeneratorDevice(DeviceRegisterForm deviceRegisterForm) {
        return signalGeneratorFactory.createDevice(deviceRegisterForm);
    }

    @Override
    public Device createElectronicLoadDevice(DeviceRegisterForm deviceRegisterForm) {
        return electronicLoadFactory.createDevice(deviceRegisterForm);
    }

}
