package com.desaysv.workserver.cicd;

import lombok.Data;


import java.util.List;

@Data
public class BuildInfoResponse {
    private List<Case> cases;//执行用例列表
    private CiBuildInfo ciBuildInfo;//构建信息
    private boolean cicd;//是否是cicd任务
    private int errorRetry;//错误重试次数
    private int executionId;//任务执行ID
    private List<Storage> storages;//用例存储位置数组
    private int taskCaseTotal;//用例执行次数（SUM(cases.loop) * task_loop）
    private int taskLoop;//任务执行循环次数
    private String taskName;//任务名称
    private boolean updateFirmware;//是否升级最新软件版本
}
