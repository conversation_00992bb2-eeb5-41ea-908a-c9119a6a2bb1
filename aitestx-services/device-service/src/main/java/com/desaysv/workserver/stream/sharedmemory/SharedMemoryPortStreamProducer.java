package com.desaysv.workserver.stream.sharedmemory;

import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.stream.PortStreamProducerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.Buffer;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.StandardOpenOption;

/**
 * 共享内存视频流生产者
 */
@Slf4j
public class SharedMemoryPortStreamProducer extends PortStreamProducerAdapter {

    private final File tmpDir = new File("D:\\FlyTest\\bin");
    private File sharedMemoryFile;
    private MappedByteBuffer mappedByteBuffer;
    private FileChannel fileChannel;

    public SharedMemoryPortStreamProducer(int w, int h) {
        super(w, h);
    }


    private void createSharedMemoryFile(String deviceModel, int devicePort) throws IOException {
        String tempFileName = String.format("%s_%d", deviceModel, devicePort);
        sharedMemoryFile = new File(tmpDir, tempFileName);

        if (sharedMemoryFile.exists() && !sharedMemoryFile.delete()) {
            throw new IOException("删除已存在的共享内存文件失败: " + sharedMemoryFile.getAbsolutePath());
        }

        if (!sharedMemoryFile.createNewFile()) {
            throw new IOException("创建共享内存文件失败: " + sharedMemoryFile.getAbsolutePath());
        }

        log.info("已创建共享内存文件: {}", sharedMemoryFile.getAbsolutePath());
        sharedMemoryFile.deleteOnExit();
    }


    /**
     * 设置共享内存
     */
    private void setupShareMemory(String deviceModel, int devicePort) {
        try {
            createSharedMemoryFile(deviceModel, devicePort);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 获取唯一MappedByteBuffer
     *
     * @param length buffer长度
     * @return MappedByteBuffer
     * @throws IOException IO异常
     */
    private MappedByteBuffer getOrCreateMappedBuffer(int length) throws IOException {
        if (mappedByteBuffer != null && mappedByteBuffer.capacity() >= length) {
            mappedByteBuffer.clear();
            return mappedByteBuffer;
        }

        // 关闭现有资源
        unmapAndCloseResources();

        // 创建新缓冲区
        fileChannel = FileChannel.open(sharedMemoryFile.toPath(),
                StandardOpenOption.READ, StandardOpenOption.WRITE, StandardOpenOption.CREATE);
        mappedByteBuffer = fileChannel.map(FileChannel.MapMode.READ_WRITE, 0, length);
        return mappedByteBuffer;
    }


    private void unmapAndCloseResources() {
        // 释放映射缓冲区
        if (mappedByteBuffer != null) {
            try {
                Method cleanerMethod = mappedByteBuffer.getClass().getMethod("cleaner");
                cleanerMethod.setAccessible(true);
                Object cleaner = cleanerMethod.invoke(mappedByteBuffer);
                Method cleanMethod = cleaner.getClass().getMethod("clean");
                cleanMethod.invoke(cleaner);
                mappedByteBuffer = null;
            } catch (Exception e) {
                log.error("释放映射缓冲区失败", e);
            }
        }

        // 关闭文件通道
        if (fileChannel != null) {
            try {
                fileChannel.close();
                fileChannel = null;
            } catch (IOException e) {
                log.error("关闭文件通道失败", e);
            }
        }
    }

    /**
     * 分发视频流
     *
     * @param imageBuffer 图像缓存
     */
    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {
        Frame frame = imageBuffer.getFrame();
        Buffer buffer = frame.image[0];

        if (buffer instanceof ByteBuffer) {
            ByteBuffer byteBuffer = (ByteBuffer) buffer;
//            System.out.println("distributeStream->frame：" + System.identityHashCode(imageBuffer.getFrame()));
//            System.out.println("distributeStream->data：" + System.identityHashCode(byteBuffer));
            try {
                MappedByteBuffer mappedByteBuffer = getOrCreateMappedBuffer(byteBuffer.remaining());
                // 直接从ByteBuffer写入MappedByteBuffer，避免创建byte[]
                mappedByteBuffer.put(byteBuffer);
                byteBuffer.rewind();
            } catch (IOException e) {
                log.warn(e.getMessage(), e);
            }
        }
    }

    /**
     * 开始流传输
     *
     * @param deviceModel 设备型号
     * @param devicePort  设备端口
     * @throws Exception 异常
     */
    @Override
    protected void startStream(String deviceModel, int devicePort) throws Exception {
        setupShareMemory(deviceModel, devicePort);
        startGrabber(deviceModel);
    }

    @Override
    public void closeStream() {
        try {
            // 先调用父类方法
            super.closeStream();

            // 确保资源释放的顺序和完整性
            if (mappedByteBuffer != null) {
                unmapAndCloseResources();
                mappedByteBuffer = null;
            }

            if (fileChannel != null) {
                fileChannel.close();
                fileChannel = null;
            }

            // 删除共享内存文件
            if (sharedMemoryFile != null && sharedMemoryFile.exists()) {
                if (!sharedMemoryFile.delete()) {
                    log.error("删除共享内存文件失败: {}", sharedMemoryFile.getAbsolutePath());
                }
                sharedMemoryFile = null;
            }
        } catch (Exception e) {
            log.error("关闭流失败", e);
        }
    }
}