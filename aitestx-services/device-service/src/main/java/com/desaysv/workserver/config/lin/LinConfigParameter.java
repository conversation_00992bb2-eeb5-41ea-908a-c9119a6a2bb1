package com.desaysv.workserver.config.lin;

import lombok.Data;

import java.io.Serializable;

@Data
public class LinConfigParameter implements Serializable {
    private int channel = 1; //通道1开始
    private int linMode;//LIN 工作模式，从站为 0，主站为 1。
    private int linBaud;//LIN 波特率，取值 1000~20000
    public int chkSumMode;  //校验方式，1-经典校验 2-增强校验 3-自动(即经典校验跟增强校验都会进行轮询)
    private boolean terminalResistanceEnabled;//终端电阻 "使能"/"禁能"

}

