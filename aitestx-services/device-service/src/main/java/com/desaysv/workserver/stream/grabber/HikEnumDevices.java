package com.desaysv.workserver.stream.grabber;

import MvCameraControlWrapper.CameraControlException;
import MvCameraControlWrapper.MvCameraControl;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static MvCameraControlWrapper.MvCameraControlDefines.*;

@Slf4j
public class HikEnumDevices {

    /**
     * 枚举所有设备
     *
     * @return
     * @throws CameraControlException
     */
    public static List<MV_CC_DEVICE_INFO> enumDevices() throws CameraControlException {
        try {
            List<MV_CC_DEVICE_INFO> stDeviceList = MvCameraControl.MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE);
            if (!stDeviceList.isEmpty()) {
                for (MV_CC_DEVICE_INFO stDeviceInfo : stDeviceList) {
                    if (stDeviceInfo == null || !MvCameraControl.MV_CC_IsDeviceAccessible(stDeviceInfo, MV_ACCESS_Monitor)) {
                        continue;
                    }
                    log.info("找到海康相机:\n{}", getDeviceInfo(stDeviceInfo));
                }
            }
            return stDeviceList;
        } catch (Error e) {
            log.error(e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    private static String getDeviceInfo(MV_CC_DEVICE_INFO stDeviceInfo) {
        StringBuilder res = new StringBuilder();
        if (null == stDeviceInfo) {
            res.append("无设备信息");
        } else {
            if (stDeviceInfo.transportLayerType == MV_GIGE_DEVICE) {
                res.append(" CurrentIp:").append(stDeviceInfo.gigEInfo.currentIp).append("\n").
                        append(" Model:").append(stDeviceInfo.gigEInfo.modelName).append("\n").
                        append(" UserDefinedName:").append(stDeviceInfo.gigEInfo.userDefinedName).append("\n");

            } else if (stDeviceInfo.transportLayerType == MV_USB_DEVICE) {
                res.append(" UserDefinedName:").append(stDeviceInfo.usb3VInfo.userDefinedName).append("\n").
                        append(" Serial Number:").append(stDeviceInfo.usb3VInfo.serialNumber).append("\n").
                        append(" Device Number:").append(stDeviceInfo.usb3VInfo.deviceNumber).append("\n");
            } else {
                res.append("不支持该设备!");
            }
            res.append(" Accessible:").append(MvCameraControl.MV_CC_IsDeviceAccessible(stDeviceInfo, MV_ACCESS_Exclusive));
        }
        return res.toString();
    }

}
