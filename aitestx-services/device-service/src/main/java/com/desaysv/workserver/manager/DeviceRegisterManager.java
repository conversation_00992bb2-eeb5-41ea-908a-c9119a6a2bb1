package com.desaysv.workserver.manager;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.factory.DeviceRegisterFactory;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.model.TestDevice;
import com.desaysv.workserver.model.TestDeviceModel;
import com.desaysv.workserver.model.TestDeviceType;
import com.desaysv.workserver.service.TestDeviceModelService;
import com.desaysv.workserver.service.TestDeviceService;
import com.desaysv.workserver.service.TestDeviceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-6 11:39
 * @description :
 * @modified By :
 * @since : 2022-5-6
 */
@Component
@Slf4j
@Lazy
public class DeviceRegisterManager {

    @Autowired
    private DeviceRegisterFactory deviceRegisterFactory;

    @Autowired
    private TestDeviceService testDeviceService;

    @Autowired
    private TestDeviceTypeService testDeviceTypeService;

    @Autowired
    private TestDeviceModelService testDeviceModelService;


    private void updateDevice(TestDevice testDevice, DeviceRegisterForm deviceRegisterForm) {
        testDevice.setName(deviceRegisterForm.getDeviceName());
        testDevice.setPort(deviceRegisterForm.getDevicePort());
        testDevice.setBaudRate(deviceRegisterForm.getBaudRate());
        testDevice.setSampleRate(deviceRegisterForm.getSampleRate());
        testDevice.setAliasName(deviceRegisterForm.getAliasName());
        testDevice.setOperationParameter(JSONObject.toJSONString(deviceRegisterForm.getDeviceOperationParameter()));
    }

    private TestDevice addToDB(DeviceRegisterForm deviceRegisterForm) {
        //保存数据库
        TestDevice testDevice = testDeviceService.getDeviceByUniqueCode(deviceRegisterForm.getDeviceUniqueCode());
        if (testDevice != null) {
            //原本存在
            updateDevice(testDevice, deviceRegisterForm);
            testDeviceService.updateDevice(testDevice);
            log.info("已存在数据库记录:{}", testDevice);
        } else {
            testDevice = new TestDevice();

            TestDeviceType deviceType = testDeviceTypeService.getDeviceTypeByName(deviceRegisterForm.getDeviceType());
            testDevice.setTypeId(deviceType.getId());
            TestDeviceModel deviceModel = testDeviceModelService.getDeviceModelByName(deviceRegisterForm.getDeviceModel());
            testDevice.setModelId(deviceModel.getId());
            testDevice.setUniqueCode(deviceRegisterForm.getDeviceUniqueCode());

            updateDevice(testDevice, deviceRegisterForm);
            log.info("保存到数据库:{}", testDevice);
            testDeviceService.addDevice(testDevice);
        }
        return testDevice;
    }

    public Device registerDevice(DeviceRegisterForm deviceRegisterForm) {
        log.info("注册设备:{}", deviceRegisterForm.getDeviceName());
        Device device = deviceRegisterFactory.registerDevice(deviceRegisterForm);
        if (device != null) {
            device.addDeviceMapping();
            log.info("注册成功:{}", device);
            TestDevice testDevice = addToDB(deviceRegisterForm);
            log.info("DB设备:{}", testDevice);
            device.setId(testDevice.getId());
        } else {
            log.warn("注册失败");
        }
        return device;
    }

    public boolean isRegistered(String deviceName) {
        return deviceRegisterFactory.isRegistered(deviceName);
    }

    public Device getDevice(String deviceName) {
        return deviceRegisterFactory.getDevice(deviceName);
    }

    public List<Device> getAllDevices() {
        return deviceRegisterFactory.getAllDevices();
    }

    public List<Device> getDevicesByType(String deviceType) {
        return getAllDevices().stream().filter(device -> device.getDeviceType().equals(deviceType)).collect(Collectors.toList());
    }

    public boolean closeDevice(String deviceName) throws DeviceCloseException {
        return deviceRegisterFactory.closeDevice(deviceName);
    }

    public boolean removeDevice(String deviceName) {
        return deviceRegisterFactory.removeDevice(deviceName);
    }

    public boolean unregisterAndCloseDevice(String deviceName) throws DeviceCloseException {
        return deviceRegisterFactory.unregisterAndCloseDevice(deviceName);
    }

    public boolean unregisterAndCloseAllDevices() {
        return deviceRegisterFactory.unregisterAndCloseAllDevices();
    }

}
