package com.desaysv.workserver.devices.bus.zlg;

import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.Structure;
import com.sun.jna.Union;
import com.sun.jna.win32.StdCallLibrary;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 周立功Can JNA接口
 *
 * @link <a href="https://manual.zlg.cn/web/#/152/5336">...</a>
 * 结构体里的嵌套Struct成员变量默认使用ByValue，可省略
 * 函数变量中的Struct参数默认使用ByReference，可省略
 * 结构体里面使用Public声明属性
 */
public interface ZlgCanLib extends StdCallLibrary {

    @Getter
    enum ZCAN_CHANNEL_TYPE {
        CAN(0), CAN_FD(1);

        private final int value;

        ZCAN_CHANNEL_TYPE(int value) {
            this.value = value;
        }
    }

    int CAN_MAX_DLEN = 8;
    int CANFD_MAX_DLEN = 64;

    //zlgcan.dll和kerneldlls文件夹和jar包保持同级目录
    String filePath = "D:\\FlyTest\\bin\\lib\\zlgcan\\zlgcan";
    ZlgCanLib Instance = Native.load(filePath, ZlgCanLib.class);

//    /**
//     * 设备句柄
//     */
//    class DEVICE_HANDLE extends Pointer {
//
//        public DEVICE_HANDLE(long peer) {
//            super(peer);
//        }
//    }
//
//    /**
//     * 通道句柄
//     */
//    class CHANNEL_HANDLE extends Pointer {
//
//        public CHANNEL_HANDLE(long peer) {
//            super(peer);
//        }
//    }

    /**
     * 该结构体包含设备的一些基本信息，在函数ZCAN_GetDeviceInf中被填充。
     */
    class ZCAN_DEVICE_INFO extends Structure {
        public short hwVersion; // 硬件版本号，16进制，比如0x0100表示V1.00。
        public short fwVersion; // 固件版本号，16进制。
        public short drVersion; // 驱动程序版本号，16进制。
        public short inVersion; // 接口库版本号，16进制。
        public short irqNum;    // 板卡所使用的中断号。
        public byte canNum;     // 表示有几路通道。
        public byte[] strSerialNum = new byte[20]; // 此板卡的序列号，比如” USBCAN V1.00”（注意：包括字符串结束符’\0’）。
        public byte[] strHwType = new byte[40]; // 硬件类型
        public short[] reserved = new short[4]; // 仅作保留，不设置。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("hwVersion", "fwVersion", "drVersion", "inVersion",
                    "irqNum", "canNum", "strSerialNum", "strHwType", "reserved");
        }
    }

    /**
     * CAN设备
     * 注意：当设备类型为PCI-5010-U、PCI-5020-U、USBCAN-E-U、 USBCAN-2E-U、USBCAN-4E-U、CANDTU时，
     * 帧过滤（acc_code和acc_mask忽略）采用GetIProperty设置
     */
    class Can extends Structure {

        public int accCode;  // SJA1000的帧过滤验收码，对经过屏蔽码过滤为“有关位”进行匹配，全部匹配成功后，此报文可以被接收，否则不接收。推荐设置为0。
        public int accMask;  // SJA1000的帧过滤屏蔽码，对接收的CAN帧ID进行过滤，位为0的是“有关位”，位为1的是“无关位”。推荐设置为0xFFFFFFFF，即全部接收。
        public int reserved; // 仅作保留，不设置。
        public byte filter;  // 滤波方式，=1表示单滤波，=0表示双滤波。
        public byte timing0; // 忽略，不设置。
        public byte timing1; // 忽略，不设置。
        public byte mode;    // 工作模式，=0表示正常模式（相当于正常节点），=1表示只听模式（只接收，不影响总线）。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("accCode", "accMask", "reserved", "filter", "timing0", "timing1", "mode");
        }

    }

    /**
     * CANFD设备
     * 注意：当设备类型为USBCANFD-100U、USBCANFD-200U、USBCANFD-MINI时，帧过滤(acc_code和acc_mask忽略)采用GetIProperty设置。
     */
    class CanFd extends Structure {
        public int accMode; // 验收码，同CAN设备。
        public int accMask; // 屏蔽码，同CAN设备。
        public int abitTiming; // 忽略，不设置。
        public int dbitTiming; // 忽略，不设置。
        public int brp;  // 波特率预分频因子，设置为0。
        public byte filter; // 滤波方式，同CAN设备。
        public byte mode; // 模式，同CAN设备。
        public short pad; // 数据对齐，不设置。
        public int reserved; // 仅作保留，不设置。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("accMode", "accMask", "abitTiming", "dbitTiming", "brp", "filter", "mode", "pad", "reserved");
        }

    }

    class _ZCAN_CHANNEL_INIT_CONFIG extends Union {
        public Can can = new Can();  // CAN设备
        public CanFd canFd = new CanFd(); //CANFD设备

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("can", "canFd");
        }
    }

    /**
     * 该结构体定义了初始化配置的参数，调用ZCAN_InitCAN之前，要先初始化该结构体。
     */
    class ZCAN_CHANNEL_INIT_CONFIG extends Structure {
        public int canType; // 设备类型， =0表示CAN设备，=1表示CANFD设备。
        public Can can = new Can();  // CAN设备
        public CanFd canFd = new CanFd(); //CANFD设备

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("canType", "can", "canFd");
        }
    }

    /**
     * 该结构体包含总线错误信息，在函数ZCAN_ReadChannelErrInfo中被填充。
     */
    class ZCAN_CHANNEL_ERROR_INFO extends Structure {
        public int errorCode; // 错误码。
        public byte[] passiveErrorData = new byte[3]; // 当产生的错误中有消极错误时表示为消极错误的错误标识数据。
        public byte arLostErrorData; // 当产生的错误中有仲裁丢失错误时表示为仲裁丢失错误的错误标识数据。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("errorCode", "passiveErrorData", "arLostErrorData");
        }
    }

    /**
     * 该结构体包含控制器状态信息，在函数ZCAN_ReadChannelStatus中被填充。
     */
    class ZCAN_CHANNEL_STATUS extends Structure {
        public byte errInterrupt; // 中断记录，读操作会清除中断。
        public byte regMode;  // CAN控制器模式寄存器值。
        public byte regStatus; // CAN控制器状态寄存器值。
        public byte regALCapture; // CAN控制器仲裁丢失寄存器值。
        public byte regECCapture; // CAN控制器错误寄存器值。
        public byte regEWLimit; // CAN控制器错误警告限制寄存器值。默认为96。
        public byte regRECounter; // CAN控制器接收错误寄存器值。为0-127时，为错误主动状态；为128-254时，为错误被动状态；为255时，为总线关闭状态。
        public byte regTECounter; // CAN控制器发送错误寄存器值。为0-127时，为错误主动状态；为128-254时，为错误被动状态；为255时，为总线关闭状态。
        public int reserved; // 仅作保留，不设置。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("errInterrupt", "regMode", "regStatus", "regALCapture", "regECCapture", "regEWLimit",
                    "regRECounter", "regTECounter", "reserved");
        }
    }

    /**
     * 该结构体包含了CAN报文信息。
     */
    class CanFrame extends Structure {
        /**
         * 帧ID，32位，高3位属于标志位，标志位含义如下：
         * 第31位(最高位)代表扩展帧标志，=0表示标准帧，=1代表扩展帧，宏IS_EFF可获取该标志；
         * 第30位代表远程帧标志，=0表示数据帧，=1表示远程帧，宏IS_RTR可获取该标志；
         * 第29位代表错误帧标准，=0表示CAN帧，=1表示错误帧，目前只能设置为0；
         * 其余位代表实际帧ID值，使用宏MAKE_CAN_ID构造ID，使用宏GET_ID获取ID。
         */
        public int canId;
        // public int err;
        // public int rtr;
        // public int eff;
        public byte canDlc; // 数据长度。
        public byte __pad;  // 对齐，忽略。
        public byte __res0; // 仅作保留，不设置。
        public byte __res1; // 仅作保留，不设置。
        public byte[] data = new byte[CAN_MAX_DLEN]; // 报文数据，有效长度为can_dlc。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("canId", "canDlc", "__pad", "__res0", "__res1", "data");
//            return Arrays.asList("canId", "err", "rtr", "eff", "canDlc", "__pad", "__res0", "__res1", "data");
        }
    }

    /**
     * 该结构体包含发送的CAN报文信息，在函数ZCAN_Transmit中使用。
     */
    class ZCAN_Transmit_Data extends Structure {
        public CanFrame frame = new CanFrame(); // 报文数据信息，参见can_frame。
        /**
         * 发送方式，0=正常发送，1=单次发送，2=自发自收，3=单次自发自收。
         * 发送方式说明如下：
         * 正常发送：在ID仲裁丢失或发送出现错误时，CAN控制器会自动重发，直到发送成功，或发送超时，或总线关闭。
         * 单次发送：在一些应用中，允许部分数据丢失，但不能出现传输延迟时，自动重发就没有意义了。在这些应用中，
         * 一般会以固定的时间间隔发送数据，自动重发会导致后面的数据无法发送，出现传输延迟。使用单次发送，仲裁丢失或发送错误，CAN控制器不会重发报文。
         * 自发自收：产生一次带自接收特性的正常发送，在发送完成后，可以从接收缓冲区中读到已发送的报文。
         * 单次自发自收：产生一次带自接收特性的单次发送，在发送出错或仲裁丢失不会执行重发。在发送完成后，可以从接收缓冲区中读到已发送的报文。
         */
        public int transmitType;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("frame", "transmitType");
        }
    }

    /**
     * 该结构体包含了CANFD报文信息。
     */
    class CanFdFrame extends Structure {
        /**
         * 帧ID，32位，高3位属于标志位，标志位含义如下：
         * 第31位(最高位)代表扩展帧标志，=0表示标准帧，=1代表扩展帧，宏IS_EFF可获取该标志；
         * 第30位代表远程帧标志，=0表示数据帧，=1表示远程帧，宏IS_RTR可获取该标志；
         * 第29位代表错误帧标准，=0表示CAN帧，=1表示错误帧，目前只能设置为0；
         * 其余位代表实际帧ID值，使用宏MAKE_CAN_ID构造ID，使用宏GET_ID获取ID。
         */
        public int canId;
        // public int err;
        // public int rtr;
        // public int eff;
        // public byte brs;// 额外标志，比如使用CANFD加速，则设置为宏CANFD_BRS。
        // public byte esi;
        // public byte __res;
        public byte len; // 数据长度。
        public byte flags;
        public byte __res0; // 仅作保留，不设置。
        public byte __res1; // 仅作保留，不设置。
        public byte[] data = new byte[CANFD_MAX_DLEN]; // 报文数据，有效长度为len。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("canId", "len", "flags", "__res0", "__res1", "data");
//            return Arrays.asList("canId", "err", "rtr", "eff", "len", "brs", "esi", "__res", "__res0", "__res1", "data");
        }
    }

    /**
     * 该结构体包含发送的CANFD报文信息，在函数ZCAN_TransmitFD中使用。
     */
    class ZCAN_TransmitFD_Data extends Structure {
        public CanFdFrame frame = new CanFdFrame(); // 报文数据信息，参见canfd_frame。
        /**
         * 发送方式，0=正常发送，1=单次发送，2=自发自收，3=单次自发自收。
         * 发送方式说明如下：
         * 正常发送：在ID仲裁丢失或发送出现错误时，CAN控制器会自动重发，直到发送成功，或发送超时，或总线关闭。
         * 单次发送：在一些应用中，允许部分数据丢失，但不能出现传输延迟时，自动重发就没有意义了。在这些应用中，
         * 一般会以固定的时间间隔发送数据，自动重发会导致后面的数据无法发送，出现传输延迟。使用单次发送，仲裁丢失或发送错误，CAN控制器不会重发报文。
         * 自发自收：产生一次带自接收特性的正常发送，在发送完成后，可以从接收缓冲区中读到已发送的报文。
         * 单次自发自收：产生一次带自接收特性的单次发送，在发送出错或仲裁丢失不会执行重发。在发送完成后，可以从接收缓冲区中读到已发送的报文。
         */
        public int transmitType;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("frame", "transmitType");
        }

    }

    class UnionVal extends Structure {
        public short reserved; // 保留

        @Override
        protected List<String> getFieldOrder() {
            return Collections.singletonList("reserved");
        }
    }

//    /**
//     * 数据标志，暂未使用
//     */
//    class CANDataObjFlag extends Union {
//        public CANFDUnionVal unionVal =new CANFDUnionVal();
//        public short rawVal;
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("unionVal", "rawVal");
//        }
//    }
//
//    class ZCANCANFDData extends Structure {
//        public long timeStamp;
//        public GPSFlagUnion flag = new GPSFlagUnion();
//        public byte[] extraData;
//        public CanFdFrame frame =  new CanFdFrame();
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("timeStamp", "flag", "extraData", "frame");
//        }
//    }
//
//    class FlagUnion extends Union {
//        public CANFDUnionVal unionVal =  new CANFDUnionVal();
//        public int rawVal;
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("unionVal", "rawVal");
//        }
//
//    }
//
//
//    class CANFDUnionVal extends Structure {
//        public short[] reserved = new short[16];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("reserved");
//        }
//    }
//
//    class ZCANErrorData extends Structure {
//        public long timeStamp;
//        public byte errType;
//        public byte errSubType;
//        public byte nodeState;
//        public byte rxErrCount;
//        public byte txErrCount;
//        public byte errData;
//        public byte[] reserved = new byte[2];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("timeStamp", "errType", "errSubType", "nodeState", "rxErrCount", "txErrCount", "errData", "reserved");
//        }
//
//    }
//
//    class ZCANGPSData extends Structure {
//        public TimeStruct time;
//        public GPSFlagUnion flag;
//        public double latitude;
//        public double longitude;
//        public double altitude;
//        public double speed;
//        public double courseAngle;
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("time", "flag", "latitude", "longitude", "altitude", "speed", "courseAngle");
//        }
//    }
//
//    class TimeStruct extends Structure{
//        public short year;
//        public short mon;
//        public short day;
//        public short hour;
//        public short min;
//        public short sec;
//        public short milsec;
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("year", "mon", "day", "hour", "min", "sec", "milsec");
//        }
//    }
//
//
//    class GPSFlagUnion extends Union {
//        public GPSUnionVal unionVal = new GPSUnionVal();
//        public short rawVal;
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("unionVal", "rawVal");
//        }
//    }
//
//
//    class GPSUnionVal extends Structure{
//        public short timeValid;
//        public short latlongValid;
//        public short altitudeValid;
//        public short speedValid;
//        public short courseAngleValid;
//        public short[] reserved = new short[13];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("timeValid", "latlongValid", "altitudeValid", "speedValid", "courseAngleValid", "reserved");
//        }
//    }
//
//    class ZCANLINData extends Structure {
//        public PIDUnion PID; // LIN协议PID
//        public RxDataStruct RxData; // LIN协议接收数据
//        public byte[] reserved = new byte[7];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("PID", "RxData", "reserved");
//        }
//    }
//
//    /**
//     * 成员定义为联合体，包含具体数据结构
//     */
//    class CANDataObjData extends Union {
//        public ZCANCANFDData canfdData;
//        public ZCANErrorData canErrorData;
//        public ZCANGPSData canGpsData;
//        public ZCANLINData canLinData;
//        public ZCANLINErrData zcanLINErrData;
//        public ZCANLINExData  zcanLINExData;     // LIN��չ����
//        public ZCANLINEventData zcanLINEventData;
//        public BusUsage busUsage;
//        public byte[] raw = new byte[92];
//
//
//        public CANDataObjData() {
//            super();
//            this.canGpsData = new ZCANGPSData();
//            this.canLinData = new ZCANLINData();
//            this.zcanLINErrData = new ZCANLINErrData();
//            this.zcanLINExData = new ZCANLINExData();
//            this.zcanLINEventData = new ZCANLINEventData();
//            this.busUsage = new BusUsage();
//            this.raw = new byte[92];
//        }
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("canGpsData", "canLinData", "zcanLINErrData", "zcanLINExData", "zcanLINEventData", "busUsage", "raw");
//        }
//
//
//
//    }
//
//
//    class ZCANLINExData extends Structure {
//        public PIDUnion PID;
//        public byte[] reserved = new byte[7];
//        public LINExRxDataStruct RxData;
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("PID", "reserved", "RxData");
//        }
//    }
//
//    class LINExRxDataStruct extends Structure {
//        public long timeStamp;
//        public byte dataLen;
//        public byte dir;
//        public byte chkSum;
//        public byte[] reserved = new byte[5];
//        public byte[] data = new byte[64];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("timeStamp", "dataLen", "dir", "chkSum", "reserved", "data");
//        }
//    }
//
//
//    class ZCANLINEventData extends Structure {
//        public long timeStamp;
//        public byte type;
//        public byte[] reserved = new byte[7];
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("timeStamp", "type", "reserved");
//        }
//
//    }
//
//
//
//    class BusUsage extends Structure {
//        public long nTimeStampBegin; // 采集开始时间，单位为微秒
//        public long nTimeStampEnd; // 采集结束时间，单位为微秒
//        public byte nChnl; // 采集通道
//        public byte nReserved; // 保留
//        public short nBusUsage; // 采集总线使用率，单位为百分比，取值范围为0~10000，表示80.50%时，值为8050
//        public int nFrameCount; // 采集帧数
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("nTimeStampBegin", "nTimeStampEnd", "nChnl", "nReserved", "nBusUsage", "nFrameCount");
//        }
//    }
//
//
//    /**
//     * 结构体详情见程序清单，该结构作为合并接收使用的各种数据的载体，支持 CAN，CANFD，LIN，GPS，错误数据等各种不同类型的数据
//     */
//    class ZCANDataObj extends Structure {
//        public byte dataType; // 数据类型，指示当前结构的数据类型。数据类型含义
//        public byte chnl; // 数据通道。数据类型表示CAN/CANFD/错误数据时，通道表示的是CAN通道。数据类型表示的是 LIN 数据时，通道表示的是设备的 LIN 通道。
//        public CANDataObjFlag flag = new CANDataObjFlag(); // 数据标志，暂未使用。
//        public byte[] extraData = new byte[4]; // 额外数据，暂未使用。
//        public CANDataObjData data = new CANDataObjData(); // data成员定义为联合体，包含具体数据结构。
//
//        @Override
//        protected List<String> getFieldOrder() {
//            return Arrays.asList("dataType", "chnl", "flag", "extraData", "data");
//        }
//
//        public static class ByReference extends ZCANDataObj implements Structure.ByReference {
//        }
//
//
//        public ZCANDataObj() {
//            super();
//            this.extraData = new byte[4]; // 显式初始化数组字段
//        }
//    }
//

    class ZCANDataObj extends Structure {
        public byte dataType;
        public byte chnl;
        public FlagUnion flag;
        public byte[] extraData;
        public DataUnion data;

        public static class ByReference extends ZCANDataObj implements Structure.ByReference {
        }

        public static class FlagUnion extends Union {
            public UnionVal unionVal;
            public short rawVal;

            public static class UnionVal extends Structure {
                public short reserved;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("reserved");
                }
            }

            public FlagUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }


        }

        public static class DataUnion extends Union {
            public ZCANCANFDData zcanCANFDData;
            public ZCANErrorData zcanErrData;
            public ZCANGPSData zcanGPSData;
            public ZCANLINData zcanLINData;
            public ZCANLINErrData zcanLINErrData;
            public ZCANLINExData zcanLINExData;
            public ZCANLINEventData zcanLINEventData;
            public BusUsage busUsage;
            public byte[] raw;

            public DataUnion() {
                this.zcanCANFDData = new ZCANCANFDData();
                this.zcanErrData = new ZCANErrorData();
                this.zcanGPSData = new ZCANGPSData();
                this.zcanLINData = new ZCANLINData();
                this.zcanLINErrData = new ZCANLINErrData();
                this.zcanLINExData = new ZCANLINExData();
                this.zcanLINEventData = new ZCANLINEventData();
                this.busUsage = new BusUsage();
                this.raw = new byte[92];
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("zcanCANFDData", "zcanErrData", "zcanGPSData", "zcanLINData", "zcanLINErrData", "zcanLINExData", "zcanLINEventData", "busUsage", "raw");
            }
        }

        public ZCANDataObj() {
            this.flag = new FlagUnion();
            this.extraData = new byte[4];
            this.data = new DataUnion();
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("dataType", "chnl", "flag", "extraData", "data");
        }
    }

    // ZCANCANFDData
    class ZCANCANFDData extends Structure {
        public long timeStamp;
        public FlagUnion flag;
        public byte[] extraData;
        public CanFdFrameStructure frame;

        public static class FlagUnion extends Union {
            public UnionVal unionVal;
            public int rawVal;

            public static class UnionVal extends Structure {
                public int frameType;
                public int txDelay;
                public int transmitType;
                public int txEchoRequest;
                public int txEchoed;
                public int reserved;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("frameType", "txDelay", "transmitType", "txEchoRequest", "txEchoed", "reserved");
                }
            }

            public FlagUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public ZCANCANFDData() {
            this.flag = new FlagUnion();
            this.extraData = new byte[4];
            this.frame = new CanFdFrameStructure();
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "flag", "extraData", "frame");
        }
    }

    // ZCANErrorData
    class ZCANErrorData extends Structure {
        public long timeStamp;
        public byte errType;
        public byte errSubType;
        public byte nodeState;
        public byte rxErrCount;
        public byte txErrCount;
        public byte errData;
        public byte[] reserved;

        public ZCANErrorData() {
            this.reserved = new byte[2];
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "errType", "errSubType", "nodeState", "rxErrCount", "txErrCount", "errData", "reserved");
        }
    }

    // ZCANGPSData
    class ZCANGPSData extends Structure {
        public TimeStruct time;
        public FlagUnion flag;
        public double latitude;
        public double longitude;
        public double altitude;
        public double speed;
        public double courseAngle;

        public static class TimeStruct extends Structure {
            public short year;
            public short mon;
            public short day;
            public short hour;
            public short min;
            public short sec;
            public short milsec;

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("year", "mon", "day", "hour", "min", "sec", "milsec");
            }
        }

        public static class FlagUnion extends Union {
            public UnionVal unionVal;
            public short rawVal;

            public static class UnionVal extends Structure {
                public short timeValid;
                public short latlongValid;
                public short altitudeValid;
                public short speedValid;
                public short courseAngleValid;
                public short reserved;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("timeValid", "latlongValid", "altitudeValid", "speedValid", "courseAngleValid", "reserved");
                }
            }

            public FlagUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public ZCANGPSData() {
            this.time = new TimeStruct();
            this.flag = new FlagUnion();
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("time", "flag", "latitude", "longitude", "altitude", "speed", "courseAngle");
        }
    }

    // ZCANLINData
    class ZCANLINData extends Structure {
        //        public PIDUnion PID;
        public byte PID;
        public RxDataStruct RxData;
        public byte[] reserved;


        @Override
        protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
            return Structure.ALIGN_NONE; // 设置1字节对齐
        }


        public static class PIDUnion extends Union {
            public UnionVal unionVal;
            public byte rawVal;

            public static class UnionVal extends Structure {
                public byte ID;
                public byte Parity;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("ID", "Parity");
                }
            }

            public PIDUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public static class RxDataStruct extends Structure {
            public long timeStamp;
            public byte dataLen;
            public byte dir;
            public byte chkSum;
            public byte[] reserved;
            public byte[] data;

            public RxDataStruct() {
                this.reserved = new byte[13];
                this.data = new byte[8];
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("timeStamp", "dataLen", "dir", "chkSum", "reserved", "data");
            }
        }

        public ZCANLINData() {
//            this.PID = new PIDUnion();
            this.RxData = new RxDataStruct();
            this.reserved = new byte[7];
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("PID", "RxData", "reserved");
        }
    }

    // ZCANLINErrData
    class ZCANLINErrData2 extends Structure {
        public long timeStamp;
        public PIDUnion PID;
        public byte dataLen;
        public byte[] data;
        public ErrDataUnion errData;
        public byte dir;
        public byte chkSum;
        public byte[] reserved;

        public static class PIDUnion extends Union {
            public UnionVal unionVal;
            public byte rawVal;

            public static class UnionVal extends Structure {
                public byte ID;
                public byte Parity;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("ID", "Parity");
                }
            }

            public PIDUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public static class ErrDataUnion extends Union {
            public UnionVal unionVal;
            public short rawVal;

            public static class UnionVal extends Structure {
                public short errStage;
                public short errReason;
                public short reserved;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("errStage", "errReason", "reserved");
                }
            }

            public ErrDataUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public ZCANLINErrData2() {
            this.PID = new PIDUnion();
            this.data = new byte[8];
            this.errData = new ErrDataUnion();
            this.reserved = new byte[10];
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "PID", "dataLen", "data", "errData", "dir", "chkSum", "reserved");
        }
    }

    // ZCANLINExData
    class ZCANLINExData extends Structure {
        public PIDUnion PID;
        public byte[] reserved;
        public RxDataStruct RxData;

        public static class PIDUnion extends Union {
            public UnionVal unionVal;
            public byte rawVal;

            public static class UnionVal extends Structure {
                public byte ID;
                public byte Parity;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("ID", "Parity");
                }
            }

            public PIDUnion() {
                this.unionVal = new UnionVal();
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("unionVal", "rawVal");
            }
        }

        public static class RxDataStruct extends Structure {
            public long timeStamp;
            public byte dataLen;
            public byte dir;
            public byte chkSum;
            public byte[] reserved;
            public byte[] data;

            public RxDataStruct() {
                this.reserved = new byte[5];
                this.data = new byte[64];
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("timeStamp", "dataLen", "dir", "chkSum", "reserved", "data");
            }
        }

        public ZCANLINExData() {
            this.PID = new PIDUnion();
            this.reserved = new byte[7];
            this.RxData = new RxDataStruct();
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("PID", "reserved", "RxData");
        }
    }

    // ZCANLINEventData
    class ZCANLINEventData extends Structure {
        public long timeStamp;
        public byte type;
        public byte[] reserved;

        public ZCANLINEventData() {
            this.reserved = new byte[7];
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "type", "reserved");
        }
    }

    // BusUsage
    class BusUsage extends Structure {
        public long nTimeStampBegin;
        public long nTimeStampEnd;
        public byte nChnl;
        public byte nReserved;
        public short nBusUsage;
        public int nFrameCount;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("nTimeStampBegin", "nTimeStampEnd", "nChnl", "nReserved", "nBusUsage", "nFrameCount");
        }
    }

    // CanFdFrame
    class CanFdFrameStructure extends Structure {
        public int canId;
        public byte len;
        public byte flags;
        public byte __res0;
        public byte __res1;
        public byte[] data;

        public CanFdFrameStructure() {
            this.data = new byte[64];
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("canId", "len", "flags", "__res0", "__res1", "data");
        }
    }


    class ZCAN_LIN_MSG extends Structure {
        public byte chnl;  // 通道
        public byte dataType;  // 数据类型：0-LIN数据 1-LIN错误数据 2-LIN事件数据
        public Data data;
        public ZCAN_LIN_MSG() {
            super();
            this.data = new Data();
        }
        protected ZCAN_LIN_MSG(int align) {
            super(align); // 使用指定的对齐值构造函数
            this.data = new Data(align);
        }
        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("chnl", "dataType", "data");
        }
        // 联合体部分
        public static class Data extends Structure {
            public ZCANLINData zcanlinData;
            //            public ZCANLINErrData zcanlinErrData;
//            public ZCANLINEventData zcanlinEventData;
            public byte[] raw = new byte[46];  // RAW数据
            // 默认构造函数
            public Data() {
                super();
            }

            // 带有对齐参数的构造函数
            protected Data(int align) {
                super(align); // 使用指定的对齐值构造函数
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("zcanlinData","raw");
            }
        }


        // 嵌套的结构体 ZCANLINData
        public static class ZCANLINData extends Structure {
            public byte PID;
            public RxData RxData;
            public byte[] reserved = new byte[7];
            public ZCANLINData() {
                super();
            }

            // 带有对齐参数的构造函数
            protected ZCANLINData(int align) {
                super(align); // 使用指定的对齐值构造函数
            }

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("PID", "RxData", "reserved");
            }
            public static class RxData extends Structure{
                public long timestamp;
                public byte dataLen;
                public byte dir;
                public byte chkSum;
                public byte[] reserved = new byte[13];
                public byte[] data = new byte[8];
                public RxData() {
                    super();
                }

                // 带有对齐参数的构造函数
                protected RxData(int align) {
                    super(align); // 使用指定的对齐值构造函数
                }
                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("timestamp", "dataLen", "dir", "chkSum", "reserved", "data");
                }
            }
        }


        // 嵌套的结构体 ZCANLINErrData
        public static class ZCANLINErrData extends Structure {
            public long timeStamp;  // 时间戳，单位微秒(us)
            public byte PID;
            public byte dataLen;
            public byte[] data = new byte[8];
            public ErrData errData;
            public byte dir;  // 传输方向
            public byte chkSum;  // 校验和，如果设备不支持校验和的获取，则为0
            public byte[] reserved = new byte[10];  // 保留

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("timeStamp", "PID", "dataLen", "data", "errData", "dir", "chkSum", "reserved");
            }

            @Override
            protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
                return Structure.ALIGN_NONE; // 设置1字节对齐
            }

            // 嵌套的结构体 ErrData
            public static class ErrData extends Union {
                public static class ErrDataStruct extends Structure {
                    public byte[] errStage = new byte[4];  // 错误阶段
                    public byte[] errReason = new byte[4];  // 错误原因
                    public byte[] reserved = new byte[8];  // 保留

                    @Override
                    protected List<String> getFieldOrder() {
                        return Arrays.asList("errStage", "errReason", "reserved");
                    }

                    @Override
                    protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
                        return Structure.ALIGN_NONE; // 设置1字节对齐
                    }
                }

                public ErrDataStruct unionErrData;
                public short rawVal;

                @Override
                protected List<String> getFieldOrder() {
                    return Arrays.asList("unionErrData", "rawVal");
                }

                @Override
                protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
                    return Structure.ALIGN_NONE; // 设置1字节对齐
                }
            }
        }

        // 嵌套的结构体 ZCANLINEventData
        public static class ZCANLINEventData extends Structure {
            public long timeStamp;  // 时间戳，单位微秒(us)
            public byte type;  // 事件类型
            public byte[] reserved = new byte[7];  // 保留

            @Override
            protected List<String> getFieldOrder() {
                return Arrays.asList("timeStamp", "type", "reserved");
            }

            @Override
            protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
                return Structure.ALIGN_NONE; // 设置1字节对齐
            }
        }
    }


    /**
     * 该结构体包含接收的CAN报文信息，在函数ZCAN_Receive中使用。
     */
    class ZCAN_Receive_Data extends Structure {
        public CanFrame frame; // 报文数据信息，详见can_frame结构体。
        public long timestamp; // 时间戳，单位微秒，基于设备启动时间。（如果为云设备，则基于1970年1月1日0时0分0秒）。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("frame", "timestamp");
        }
    }

    class ZCAN_ReceiveFD_Data extends Structure {
        public CanFdFrame frame; // 报文数据信息，详见canfd_frame结构体。
        public long timestamp;  // 时间戳，单位微秒，基于设备启动时间。（如果为云设备，则基于1970年1月1日0时0分0秒）。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("frame", "timestamp");
        }
    }

    /**
     * 设置设备属性值
     */
    interface SetValueFunc extends StdCallCallback {
        int setValueFunc(String path, String value);
    }

    /**
     * 获取属性值
     */
    interface GetValueFunc extends StdCallCallback {

        String getValueFunc(String path);

    }

    /**
     * 节点mata的可选项。
     */
    class Options extends Structure {
        public String type; // 可选项的数据类型
        public String value; // 可选项的值
        public String desc; // 可选项的描述信息

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("type", "value", "desc");
        }
    }

    /**
     * 节点mata信息。
     */
    class Meta extends Structure {
        public String type; // 配置项的数据类型
        public String desc; // 配置项的说明性信息
        public int readOnly; // 配置项是否是只读的，缺省为可读写
        public String format; // 配置项输入格式的提示
        public double minValue;// 对于数值类型的配置项来说是最小值，对字符串的配置项来说是最小长度（字节数）。
        public double maxValue; // 对于数值类型的配置项来说是最大值，对字符串的配置项来说是最大长度（字节数）。
        public String unit; // 配置项的单位
        public double delta; // 通过旋钮/滚轮等方式修改配置项时的增量
        public String visible; // 配置项是否可见, true可见，false不可见，也可以绑定表达式（表达式使用参考demo3），缺省可见
        public String enable; // 该配置项是否使能, true使能，false不使能，也可以绑定表达式（表达式使用参考demo3）。缺省使能
        public int editable; // 配置项的可选值，仅但『type』为间接类型时有效
        public Options options; // 配置项的可选值，仅但『type』为间接类型时有效，以NULL结束

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("type", "desc", "readOnly", "format", "minValue", "maxValue", "unit", "delta", "visible",
                    "enable", "editable", "options");
        }
    }

    /**
     * 属性的KeyValue对。
     */
    class Pair extends Structure {
        public String key;
        public String value;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("key", "value");
        }
    }

    /**
     * 配置节点
     */
    class ConfigNode extends Structure {
        public String name; // 节点的名字
        public String value; // 节点的值 同样可以绑定表达式
        public String bindingValue; // 节点值的表达式，当有该表达式时，value由此表达式计算而来
        public String path; // 该节点的路径
        public Meta metaInfo = new Meta(); // 配置项信息
        public ConfigNode children = new ConfigNode(); // 该节点的子节点, 以NULL结束
        public Pair attributes = new Pair(); // 该节点的属性, 以NULL结束

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("name", "value", "bindingValue", "path", "metaInfo", "children", "attributes");
        }
    }

    /**
     * 用于返回设备包含的所有属性。
     */
    interface GetPropertiesFunc extends StdCallCallback {

        ConfigNode getPropertiesFunc();
    }

    /**
     * 该结构体用于获取/设置设备参数信息。
     */
    class IProperty extends Structure {
        public SetValueFunc SetValue;
        public GetValueFunc GetValue;
        public GetPropertiesFunc GetProperties;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("SetValue", "GetValue", "GetProperties");
        }
    }

    class ZCAN_AUTO_TRANSMIT_OBJ extends Structure {
        public short enable;
        public short index;
        public int interval;
        public ZCAN_Transmit_Data obj;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("enable", "index", "interval", "obj");
        }
    }

    class ZCANFD_AUTO_TRANSMIT_OBJ extends Structure {
        public short enable;
        public short index;
        public int interval;
        public ZCAN_TransmitFD_Data obj;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("enable", "index", "interval", "obj");
        }
    }

    class ZCAN_AUTO_TRANSMIT_OBJ_PARAM extends Structure {
        @Override
        protected List<String> getFieldOrder() {
            return new ArrayList<>();
        }
    }

    /**
     * 该函数用于打开设备。一个设备只能被打开一次。
     *
     * @param deviceType  　　设备类型，详见头文件zlgcan.h中的宏定义。
     * @param deviceIndex 　　设备索引号，比如当只有一个USBCANFD-200U时，索引号为0，这时再插入一个USBCANFD-200U，那么后面插入的这个设备索引号就是1，以此类推。
     * @param reserved    　　仅作保留。
     * @return 　为INVALID_DEVICE_HANDLE表示操作失败，否则表示操作成功，返回设备句柄值，请保存该句柄值，往后的操作需要使用。
     */
    Pointer ZCAN_OpenDevice(int deviceType, int deviceIndex, int reserved);

    default Pointer ZCAN_OpenDevice(int deviceType, int deviceIndex) {
        return ZCAN_OpenDevice(deviceType, deviceIndex, 0);
    }

    /**
     * 该函数用于关闭设备，关闭设备和打开设备一一对应。
     *
     * @param deviceHandle 　　需要关闭的设备的句柄值，即ZCAN_OpenDevice成功返回的值。
     * @return 　　STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_CloseDevice(Pointer deviceHandle);

    /**
     * 该函数用于获取设备信息。
     *
     * @param deviceHandle 　　设备句柄值。
     * @param deviceInfo   　　设备信息结构体，详见ZCAN_DEVICE_INFO结构体。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_GetDeviceInf(Pointer deviceHandle, ZCAN_DEVICE_INFO deviceInfo);

    /**
     * 该函数用于检测设备是否在线，仅支持USB系列设备。
     *
     * @param deviceHandle 设备句柄值。
     * @return 设备在线=STATUS_ONLINE，不在线=STATUS_OFFLINE。
     */
    int ZCAN_IsDeviceOnLine(Pointer deviceHandle);

    /**
     * 该函数用于初始化CAN。
     *
     * @param deviceHandle 设备句柄值。
     * @param channel      　　通道索引号，通道0的索引号为0，通道1的索引号为1，以此类推。
     * @param initConfig   　　初始化结构，详见ZCAN_CHANNEL_INIT_CONFIG结构体。
     * @return 为INVALID_CHANNEL_HANDLE表示操作失败，否则表示操作成功，返回通道句柄值，请保存该句柄值，往后的操作需要使用。
     */
    Pointer ZCAN_InitCAN(Pointer deviceHandle, int channel, ZCAN_CHANNEL_INIT_CONFIG initConfig);

    /**
     * 该函数用于启动CAN通道。
     *
     * @param channelHandle 通道句柄值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_StartCAN(Pointer channelHandle);

    /**
     * 该函数用于复位CAN通道，可通过ZCAN_StartCAN恢复。
     *
     * @param channelHandle 通道句柄值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_ResetCAN(Pointer channelHandle);

    /**
     * 该函数用于清除库接收缓冲区。
     *
     * @param channelHandle 通道句柄值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_ClearBuffer(Pointer channelHandle);

    /**
     * 该函数用于读取通道的错误信息。
     *
     * @param channelHandle 通道句柄值。
     * @param errorInfo     错误信息结构，详见 ZCAN_CHANNEL_ERROR_INFO 结构体。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_ReadChannelErrInfo(Pointer channelHandle, ZCAN_CHANNEL_ERROR_INFO errorInfo);

    /**
     * 该函数用于读取通道的状态信息。
     *
     * @param channelHandle 通道句柄值。
     * @param canStatus     状态信息结构，详见 ZCAN_CHANNEL_STATUS 结构体。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_ReadChannelStatus(Pointer channelHandle, ZCAN_CHANNEL_STATUS canStatus);

    /**
     * 该函数用于发送CAN报文。
     *
     * @param channelHandle 通道句柄值。
     * @param transmitData  结构体ZCAN_Transmit_Data数组的首指针。
     * @param len           报文数目。
     * @return 返回实际发送成功的报文数目。
     */
    int ZCAN_Transmit(Pointer channelHandle, ZCAN_Transmit_Data transmitData, int len);

    /**
     * 该函数用于发送CANFD报文。
     *
     * @param channelHandle  通道句柄值。
     * @param transmitFDData 结构体ZCAN_TransmitFD_Data数组的首指针。
     * @param len            报文数目。
     * @return 返回实际发送成功的报文数目。
     */
    int ZCAN_TransmitFD(Pointer channelHandle, ZCAN_TransmitFD_Data transmitFDData, int len);

    /**
     * 该函数用于发送CAN/CANFD报文
     *
     * @param deviceHandle 设备句柄值。
     * @param dataObj      结构体 ZCANDataObj 数组的首指针。
     * @param len          报文数目
     * @return 返回实际发送成功的报文数目。
     */
    int ZCAN_TransmitData(Pointer deviceHandle, ZCANDataObj.ByReference dataObj, int len);

    /**
     * 获取缓冲区中CAN或CANFD报文数目。
     *
     * @param channelHandle 通道句柄值。
     * @param type          获取CAN或CANFD报文，0=CAN，1=CANFD。
     * @return 返回报文数目。
     */
    int ZCAN_GetReceiveNum(Pointer channelHandle, byte type);


    /**
     * 该函数用于接收CAN报文，建议使用ZCAN_GetReceiveNum确保缓冲区有数据再使用。
     *
     * @param channelHandle 通道句柄值。
     * @param receiveDatas  结构体ZCAN_Receive_Data数组的首指针。
     * @param len           数组长度（本次接收的最大报文数目，实际返回值小于等于这个值）。
     * @param waitTime      缓冲区无数据，函数阻塞等待时间，单位毫秒。若为-1则表示无超时，一直等待，默认值为-1。
     * @return 返回实际接收的报文数目。
     */
    int ZCAN_Receive(Pointer channelHandle, ZCAN_Receive_Data[] receiveDatas, int len, int waitTime);

    default int ZCAN_Receive(Pointer channelHandle, ZCAN_Receive_Data[] receiveDatas, int len) {
        return ZCAN_Receive(channelHandle, receiveDatas, len, -1);
    }

    /**
     * 该函数用于接收CANFD数据，建议使用ZCAN_GetReceiveNum确保缓冲区有数据再使用。
     *
     * @param channelHandle  通道句柄值。
     * @param receiveFDDatas 结构体ZCAN_ReceiveFD_Data数组的首指针。
     * @param len            数组长度（本次接收的最大报文数目，实际返回值小于等于这个值）。
     * @param waitTime       缓冲区无数据，函数阻塞等待时间，单位毫秒。若为-1则表示无超时，一直等待，默认值为-1。
     * @return 返回实际接收的报文数目。
     */
    int ZCAN_ReceiveFD(Pointer channelHandle, ZCAN_ReceiveFD_Data[] receiveFDDatas, int len, int waitTime);

    default int ZCAN_ReceiveFD(Pointer channelHandle, ZCAN_ReceiveFD_Data[] receiveFDDatas, int len) {
        return ZCAN_ReceiveFD(channelHandle, receiveFDDatas, len, -1);
    }

    /**
     * 该函数用于接收 CAN、CANFD、LIN、GPS、错误数据等各种类型的数据，建议使用ZCAN_GetReceiveNum 确保缓冲区有数据再使用
     * 注：ZCAN_ReceiveData 接口只有在设备支持合并接收的情况下，开启合并接收功能后才可以正常的接收到各种数据，
     * 设备不支持合并接收或者设备支持合并接收但是未开启合并接收时，请使用ZCAN_Receive/ZCAN_ReceiveFD 等接口获取设备数据
     *
     * @param deviceHandle 设备句柄值。
     * @param canDataObj   结构体 ZCANDataObj 数组的首指针。
     * @param len          数组长度（本次接收的最大报文数目，实际返回值小于等于这个值）。
     * @param waitTime     缓冲区无数据，函数阻塞等待时间，单位毫秒。若为-1 则表示无超时，一直等待，默认值为-1。
     * @return 返回实际接收的报文数目。
     */
    int ZCAN_ReceiveData(Pointer deviceHandle, ZCANDataObj.ByReference canDataObj, int len, int waitTime);

    default int ZCAN_ReceiveData(Pointer deviceHandle, ZCANDataObj.ByReference canDataObj, int len) {
        return ZCAN_ReceiveData(deviceHandle, canDataObj, len, -1);
    }

    /**
     * 该函数返回属性配置接口。
     *
     * @param deviceHandle 设备句柄值。
     * @return 返回属性配置接口指针，详见IProperty结构体，返回空则表示操作失败。
     */
    IProperty GetIProperty(Pointer deviceHandle);

    /**
     * 释放属性接口，与GetIProperty结对使用。
     *
     * @param iProperty GetIProperty的返回值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ReleaseIProperty(IProperty iProperty);


    /**------------------------------ZLG LIN--------------------------------**/

    /**
     * 该结构体表示配置 LIN 的信息，在函数 ZCAN_InitLIN 函数中调用。
     * 用于设置设备 LIN 的工作模式、波特率，是否使用增强校验等信息。
     */

    class ZCAN_LIN_INIT_CONFIG extends Structure {
        public byte linMode;   //LIN 工作模式，从站为 0，主站为 1。
        public byte chkSumMode;  //校验方式，1-经典校验 2-增强校验 3-自动(即经典校验跟增强校验都会进行轮询)
        public byte maxLength;  //校验方式，1-经典校验 2-增强校验 3-自动(即经典校验跟增强校验都会进行轮询)
        public byte reserved; //保留位。
        public int linBaud;  //LIN 波特率，取值 1000~20000

        public ZCAN_LIN_INIT_CONFIG(byte linMode, byte chkSumMode, int linBaud) {
            this.linMode = linMode;
            this.chkSumMode = chkSumMode;
            this.maxLength = 8;
            this.reserved = 0;
            this.linBaud = linBaud;
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("linMode", "chkSumMode", "maxLength", "reserved", "linBaud");
        }
    }


    class LINDataUnion extends Union {
        public ZCANLINData zcanLINData;
        public ZCANLINErrData zcanLINErrData;
        public ZCANLINEventData zcanLINEventData;
        public byte[] raw = new byte[46];


        @Override
        protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
            return Structure.ALIGN_NONE; // 设置1字节对齐
        }

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("zcanLINData", "zcanLINErrData", "zcanLINEventData", "raw");
        }
    }


    /**
     * 该结构体表示 LIN 错误数据结构，目前仅作为ZCANDataObj 结构的成员使用
     */
    class ZCANLINErrData extends Structure {
        public long timeStamp;  //时间戳，单位微秒(us)，表示数据帧接收时间。
        public PIDUnion PID; // LIN协议PID
        public byte dataLen;  //数据长度
        public byte[] data = new byte[8];  //数据
        public ErrDataUnion errData = new ErrDataUnion();
        public byte dir;  //传输方向，0-接收 1-发送
        public byte chkSum;  //数据校验, 部分设备不支持校验数据的获取
        public byte[] reserved = new byte[10];  //保留。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "PID", "dataLen", "data", "errData", "dir", "chkSum", "reserved");
        }
    }


    class ErrDataUnion extends Structure {
        public short errStage;
        public short errReason;
        public short reserved;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("errStage", "errReason", "reserved");
        }
    }

    class PIDUnion extends Union {
        public LINUnionVal unionVal;
        public byte rawVal;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("unionVal", "rawVal");
        }
    }

    class LINUnionVal extends Structure {
        public byte ID;
        public byte Parity;

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("ID", "Parity");
        }
    }

    class RxDataStruct extends Structure {
        public long timeStamp;  //时间戳，单位微秒(us)，表示数据帧接收时间。
        public byte dataLen;  //数据长度
        public byte dir;  //传输方向，0-接收 1-发送
        public byte chkSum;  //数据校验, 部分设备不支持校验数据的获取
        public byte[] reserved = new byte[13];  //保留。
        public byte[] data = new byte[8];  //数据

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("timeStamp", "dataLen", "dir", "chkSum", "reserved", "data");
        }
    }

    /**
     * 该结构体表示 LIN 订阅数据结构。
     */
    class ZCAN_LIN_SUBSCIBE_CFG extends Structure {
        public byte ID;  //受保护的 ID（ID 取值范围为 0-63）。
        public byte dataLen;  //数据长度，范围为 1-8 当为 255（0xff）则表示设备自动识别报文长度
        public byte chkSumMode;  //校验方式， 0- 默 认 ， 启 动 时 配 置 1- 经 典 校 验 2- 增强校验 3- 自 动 ( 对 应eZLINChkSumMode 的模式)
        public byte[] reserved = new byte[5];  //保留。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("ID", "dataLen", "chkSumMode", "reserved");
        }
    }

    class ZCAN_LIN_PUBLISH_CFG extends Structure {
        public byte ID;  //受保护的 ID（ID 取值范围为 0-63）。
        public byte dataLen;  //数据长度，范围为 1-8
        public byte[] data = new byte[8];  //数据
        public byte chkSumMode;  //校验方式，0-默认，启动时配置 1-经典校验 2-增强校验 (对应 eZLINChkSumMode 的模式)
        public byte[] reserved = new byte[5];  //保留。

        @Override
        protected List<String> getFieldOrder() {
            return Arrays.asList("ID", "dataLen", "data", "chkSumMode", "reserved");
        }

    }

    /**
     * 该函数用于对 LIN 进行初始化，指定设备工作模式，采用经典校验方式还是增强校验
     * 等参数，如果是主站模式，需要指定 LIN 工作的波特率。
     *
     * @param deviceHandle  设备句柄值。
     * @param channel       　 通道索引号，通道0的索引号为0，通道1的索引号为1，以此类推。
     * @param linInitConfig 初始化结构，详见 ZCAN_LIN_INIT_CONFIG 结构说明。
     */
    Pointer ZCAN_InitLIN(Pointer deviceHandle, int channel, ZCAN_LIN_INIT_CONFIG linInitConfig);

    /**
     * 该函数用于启动LIN通道。
     *
     * @param channelHandle 通道句柄值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_StartLIN(Pointer channelHandle);

    /**
     * 该函数用来控制 LIN 发送 LIN 消息，只有 LIN 处于主站模式下才可以使用此函数进行
     * 数据发送。
     *
     * @param channelHandle 通道句柄值。
     * @param transmitData  结构体 ZCAN_LIN_MSG 数组的首指针。
     * @param len           报文数目。
     * @return 返回实际发送成功的报文数目。
     */
//    long ZCAN_TransmitLIN(Pointer channelHandle, ZCAN_LIN_MSG transmitData, long len);
    long ZCAN_TransmitLIN(Pointer channelHandle, ZCAN_LIN_MSG transmitData, long len);

    long ZCAN_TransmitLIN(Pointer channelHandle, ZCAN_LIN_MSG[] transmitData, long len);

    /**
     * 该函数用来接收 LIN 消息，不论 LIN 处于主站还是从站模式，都可以使用该函数获取
     * 总线上的数据信息。
     *
     * @param chn_handle   通道句柄值。
     * @param msg 结构体 ZCAN_LIN_MSG 数组的首指针。
     * @param len             数组长度（本次接收的最大报文数目，实际返回值小于等于这个值）。
     * @return 返回实际接收的报文数目。
     */
    int ZCAN_ReceiveLIN(Pointer chn_handle, ZCAN_LIN_MSG msg, long len, int waittime);


    /**
     * 该函数用于复位对应的 LIN 通道，即停止此通道的数据发送和接收。复位之后如果需
     * 要继续接收或者发送数据，需要重新调用 VCI_StartLIN 来启动 LIN 通道。
     *
     * @param channelHandle 通道句柄值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_ResetLIN(Pointer channelHandle);


    /**
     * 该函数用于获取指定通道已经接收到的 LIN 消息数量。
     *
     * @param channelHandle 通道句柄值。
     * @return 返回报文数目。
     */
    int ZCAN_GetLINReceiveNum(Pointer channelHandle);

    /**
     * 该函数用来设置 LIN 作为从机任务时候的发布数据，设置发布数据后，从机任务收到
     * 对应 ID 的请求时候会将预定义的数据发送出去作为响应。（注：主站跟从站都有从机任务）
     *
     * @param channelHandle 通道句柄值。
     * @param pSend         结构体 ZCAN_LIN_PUBLISH_CFG 数组的首指针。
     * @param publishCount  数组长度。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */
    int ZCAN_SetLINPublish(Pointer channelHandle, ZCAN_LIN_PUBLISH_CFG pSend, int publishCount);

    /**
     * 设置LIN通道的订阅配置。不需要响应某个ID的数据了
     * 该函数用于为指定的LIN通道设置订阅配置，允许用户订阅特定的LIN消息。
     *
     * @param channelHandle    指向LIN通道的句柄，用于标识要配置的LIN通道。
     * @param pSend            ZCAN_LIN_SUBSCIBE_CFG类型的指针，指向LIN订阅配置结构体，包含订阅的详细信息。
     * @param nSubscribeCount  要订阅的LIN消息数量，表示用户希望订阅的LIN消息的个数。
     *
     * @return;
     */
    int ZCAN_SetLINSubscribe(Pointer channelHandle, ZCAN_LIN_SUBSCIBE_CFG pSend, int nSubscribeCount);

    /**
     * 该函数设置设备属性。使用方法可以参考 IProperty 属性的 SetValue 函数
     *
     * @param channelHandle 通道句柄值。
     * @param path          设备属性路径。
     * @param value         要设置的属性值。
     * @return STATUS_OK表示操作成功，STATUS_ERR表示操作失败。
     */

    int ZCAN_SetValue(Pointer channelHandle, String path, String value);


    int ZCAN_SetValue(Pointer channelHandle, String path, Pointer value);

    /**
     * 该函数获取设备属性。用方法可以参考 IProperty 属性的 GetValue 函数
     *
     * @param channelHandle 通道句柄值。
     * @param path          设备属性路径。
     * @return 返回的指针值非空表示操作成功，为空(NULL)则表示操作失败。
     */
    int ZCAN_GetValue(Pointer channelHandle, String path);
}
