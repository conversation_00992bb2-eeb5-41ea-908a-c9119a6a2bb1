package com.desaysv.workserver.devices.testbox;

import cn.hutool.core.util.ArrayUtil;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.utils.DataUtils;
import com.fazecast.jSerialComm.SerialPort;
import com.serotonin.modbus4j.ModbusFactory;
import com.serotonin.modbus4j.ModbusMaster;
import com.serotonin.modbus4j.exception.ModbusInitException;
import com.serotonin.modbus4j.serial.SerialPortWrapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.desaysv.workserver.devices.testbox.OperateRegister.readHoldingRegisters;
import static com.desaysv.workserver.devices.testbox.OperateRegister.writeHoldingRegisters;
import static com.desaysv.workserver.utils.DataUtils.*;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/26 15:29
 * @description :
 * @modified By :
 * @since : 2023/5/26
 **/
@Slf4j
public class RtuMasterTest {

    public static void main(String[] args) throws BoardCardTransportException {
        String commPortId = "COM1";

        List<SerialPort> portList = SerialUtils.findPorts();
        for (SerialPort serialPort : portList) {
            commPortId = serialPort.getSystemPortName();
        }
        System.out.println("commPortId====" + commPortId);
        int baudRate = 115200;
        int dataBits = 8;
        int stopBits = 2;
        int parity = 0;
        int flowControlIn = 0;
        int flowControlOut = 0;

        SerialPortWrapper serialPortWrapper = new SerialPortWrapperImpl(
                commPortId, baudRate, dataBits, stopBits, parity, flowControlIn, flowControlOut);
        ModbusFactory modbusFactory = new ModbusFactory();
        ModbusMaster master = modbusFactory.createRtuMaster(serialPortWrapper);

        try {
            master.init();//初始化主站，只有init后才可以读写

//            //电阻板卡测试----------------start////////////////////////
////            int slaveId = 6;
////            readHoldingRegistersTest(master, slaveId, 5, 8);
////            readHoldingRegistersTest(master, 7, 5, 8);
////
////            byte[] bytes = intArrToByteArrBigEndian(new int[]{777, 333, 555, 888});
////            short[] shorts = convertToShorts(bytes);
////            System.out.println("new int[]{100, 200, 300, 400} bytes---"+Arrays.toString(bytes));
////            System.out.println("new int[]{100, 200, 300, 400} shorts---"+Arrays.toString(shorts));
////            writeRegistersTest(master, slaveId, 5, shorts);
////
////            byte[] bytes2 = intArrToByteArrBigEndian(new int[]{600, 700, 800, 900});
////            short[] shorts2 = convertToShorts(bytes2);
////            System.out.println("new int[]{600, 700, 800, 900} bytes---"+Arrays.toString(bytes2));
////            System.out.println("new int[]{600, 700, 800, 900} shorts---"+Arrays.toString(shorts2));
////            writeRegistersTest(master, 7, 5, shorts2);
////
////            readHoldingRegistersTest(master, slaveId, 5, 8);
////            readHoldingRegistersTest(master, 7, 5, 8);
//            //电阻板卡测试 ----------------end////////////////////////
//
//
//            //模拟量采集板卡测试 ----------------start*************//
////            int slaveId = 1;
//            //            readHoldingRegistersTest(master, slaveId, 77, 64);
//            //模拟量采集板卡测试 ----------------end*************//
//
            //三态输出板卡测试 ----------------start*************//
            int slaveId = 5;

            readTriStateOutputBoardCard(master, slaveId, 45, 64);

            byte[] bytes2 = int16ArrToByteArrBigEndian(new int[]{1});
            System.out.println("bytes2=" + ArrayUtil.toString(bytes2));
            short[] shorts2 = convertToShorts(bytes2);
            System.out.println("shorts2=" + ArrayUtil.toString(shorts2));
            writeHoldingRegisters(master, slaveId, 46, shorts2);

            readTriStateOutputBoardCard(master, slaveId, 45, 64);

            //三态输出板卡测试 ----------------end*************//
//
//
//            //PWM输出板卡测试================start===================//
////            int slaveId = 5;
////            readHoldingRegistersTest(master, slaveId, 5, 20);
//////            byte[] bytes2 = intArrToByteArrBigEndian(new int[]{600,70});
////            byte[] bytes2 = floatToBytes(new float[]{609,88},true);
////            short[] shorts2 = convertToShorts(bytes2);
////            writeRegistersTest(master, slaveId, 9, shorts2);
////            readHoldingRegistersTest(master, slaveId, 5, 20);

//            OperateRegister operateRegister = new OperateRegister();
//            operateRegister.writePWMOutputBoardCardData(master, 1, new float[]{609,88});

//            //PWM输出板卡测试================end===================//
//
//
//            //PWM输入板卡测试---------------start----------------------//
////            int slaveId = 5;
////            readHoldingRegistersTest(master, slaveId, 256, 20);
//
//            //PWM输入板卡测试---------------end----------------------//
//
//
//
//            //继电器板卡测试---------------start----------------------//
////            int slaveId = 4;
////            readHoldingRegisters(master, slaveId, 5, 1);
////
////            byte[] bytes2 = int16ArrToByteArrBigEndian(new int[]{67});
////            short[] shorts2 = convertToShorts(bytes2);
////            int count = bytes2.length / 2;
////            for (int i = 0; i < count; i++) {
////                byte[] bytes = DataUtils.subByte(bytes2, i * 2, 2);
////                String value = convertBigEndianTo16Bit(bytes);
//////                    int value = byteToInt16(bytes);//测试三态输出板卡返回
//////                    System.out.println("chanel"+(i+1)+", read value=="+df.format(value));
////                System.out.println("value=="+value);
////            }
////            writeRegistersTest(master,slaveId,5,shorts2);
////            readHoldingRegisters(master, slaveId, 5, 1);
//
//            //继电器板卡测试---------------end----------------------//
//
////-------------for test
//
//
        } catch (ModbusInitException e) {
            log.error(e.getMessage(), e);
        } finally {
            master.destroy();
        }
    }

    //    public void readResistanceBoardCard(ModbusMaster master, int slaveId, int startAddress, int len){
//        ReadHoldingRegistersResponse response = readHoldingRegisters(master, slaveId, startAddress, len);
//        if (response != null) {
//            if (response.isException()){
//                log.error("Exception response: message=" + response.getExceptionMessage());
//            } else {
//                int count = response.getData().length / 4;
//                for (int i = 0; i < count; i++) {
//                    byte[] bytes = DataUtils.subByte(response.getData(), i * 4, 4);
//                    int value = DataUtils.bytesToInt(bytes);    //测试电阻板卡返回
//                    log.info("chanel"+(i+1)+", read value=="+value);
//                }
//            }
//        }
//    }
//
//
//
//    public void readCollectionBoardCard(ModbusMaster master, int slaveId, int startAddress, int len) {
////        ReadHoldingRegistersResponse response = readHoldingRegisters(master, slaveId, startAddress, len);
////        if (response != null) {
////            if (response.isException()){
////                log.error("Exception response: message=" + response.getExceptionMessage());
////            } else {
////                DecimalFormat df = new DecimalFormat("#.00");
////                int count = response.getData().length / 4;
////                for (int i = 0; i < count; i++) {
////                    byte[] bytes = DataUtils.subByte(response.getData(), i * 4, 4);
////                    float value = DataUtils.bytesToFloat(bytes);  //测试模拟量采集板卡和PWM输出板卡返回和PWM输入板卡
////                    System.out.println("chanel"+(i+1)+", read value=="+df.format(value));
////                }
////            }
////        }
//    }
//
//
//
//    public void readRelayBoardCard(ModbusMaster master, int slaveId, int startAddress, int len) {
//        ReadHoldingRegistersResponse response = readHoldingRegisters(master, slaveId, startAddress, len);
//        if (response != null) {
//            if (response.isException()){
//                log.error("Exception response: message=" + response.getExceptionMessage());
//            } else {
//                int count = response.getData().length / 2;
//                for (int i = 0; i < count; i++) {
//                    byte[] bytes = DataUtils.subByte(response.getData(), i * 2, 2);
//                    String value = convertBigEndianTo16Bit(bytes); //测试继电器板卡返回
//                }
//            }
//        }
//    }
//
//
//
    public static void readTriStateOutputBoardCard(ModbusMaster master, int slaveId, int startAddress, int len) throws BoardCardTransportException {
        byte[] response = readHoldingRegisters(master, slaveId, startAddress, len);
        int count = response.length / 2;
        for (int i = 0; i < count; i++) {
            byte[] bytes = DataUtils.subByte(response, i * 2, 2);
            int value = byteToInt16(bytes);//测试三态输出板卡
            System.out.println("readTriStateOutputBoardCard---" + value);
        }
    }

}
