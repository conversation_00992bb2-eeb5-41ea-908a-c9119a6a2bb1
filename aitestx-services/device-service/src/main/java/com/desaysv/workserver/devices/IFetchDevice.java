package com.desaysv.workserver.devices;

import com.desaysv.workserver.exceptions.device.DeviceReadException;

public interface IFetchDevice {

    /**
     * 获取电压
     *
     * @return 电压
     */
//    @JsonIgnore
    float fetchVoltage(Integer deviceChannel, String currentDirection) throws Device<PERSON>eadException;

    default float fetchVoltage(Integer deviceChannel) throws DeviceReadException {
        return fetchVoltage(deviceChannel, null);
    }

    default float fetchVoltage() throws DeviceReadException {
        return fetchVoltage(null, null);
    }

    /**
     * 获取电流
     *
     * @return 电流
     */
//    @JsonIgnore
    float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException;

    default float fetchCurrent(Integer deviceChannel) throws DeviceReadException {
        return fetchCurrent(deviceChannel, null);
    }

    default float fetchCurrent() throws DeviceReadException {
        return fetchCurrent(null, null);
    }

}
