package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.testbox.LightTestBox;
import com.desaysv.workserver.devices.testbox.RZCUTestBox;
import com.desaysv.workserver.devices.testbox.decorated.DefaultDecoratedTestBox;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractTestBoxFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/6/6 10:42
 * @description :
 * @modified By :
 * @since : 2023/6/6
 **/
@Component
@Lazy
public class TestBoxFactory implements AbstractTestBoxFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.TestBox.TEST_BOX:
                return createTestBoxDevice(deviceRegisterForm);
            case DeviceModel.TestBox.LIGHT_TEST_BOX:
                return createLightTestBoxDevice(deviceRegisterForm);
            case DeviceModel.TestBox.RZCU_TEST_BOX:
                return createrRZCUTestBoxDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createTestBoxDevice(DeviceRegisterForm deviceRegisterForm) {
        return new DefaultDecoratedTestBox(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createLightTestBoxDevice(DeviceRegisterForm deviceRegisterForm) {
        return new LightTestBox(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createrRZCUTestBoxDevice(DeviceRegisterForm deviceRegisterForm) {
        return new RZCUTestBox(deviceRegisterForm.getDeviceOperationParameter());
    }
}
