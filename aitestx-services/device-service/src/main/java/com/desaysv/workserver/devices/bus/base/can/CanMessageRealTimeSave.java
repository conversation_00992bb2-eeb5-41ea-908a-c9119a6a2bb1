package com.desaysv.workserver.devices.bus.base.can;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode
public class CanMessageRealTimeSave {
    private String filePath;
    private String filterType;
    private String filterText;
    private String saveType;         // 保存方式（如"SIZE_BASED"/"COUNT_BASED"）
    private Integer fileSizeMB = 300;      // 文件大小限制(MB)
    private Integer recordCount = 200000;  // 记录条数限制

    public enum SaveType {
        SIZE_BASED, COUNT_BASED
    }

    public CanMessageRealTimeSave(String filePath){
        this(filePath, "", "");
    }
    public CanMessageRealTimeSave(String filePath, String filterType, String filterText) {
        this.filePath = filePath;
        this.filterType = filterType;
        this.filterText = filterText;
    }
    public enum DbcFilterType {
        Id, Chn, Name, Dir;
    }
}
