package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.AppConstants;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.camera.basler.BaslerCamera;
import com.desaysv.workserver.devices.camera.hik.HikCamera;
import com.desaysv.workserver.devices.camera.usb.USBCamera;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractCameraFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-12 14:19
 * @description : 相机工厂
 * @modified By :
 * @since : 2022-4-12
 */
@Component
@Lazy
public class CameraFactory implements AbstractCameraFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Camera.HIK_CAMERA)) {
            return createHikCamera(deviceRegisterForm);
        } else {
            String deviceUniqueCode = deviceRegisterForm.getDeviceUniqueCode();
            if (deviceUniqueCode.contains(AppConstants.SW_CAMERA) || deviceUniqueCode.contains("Basler")) {
                return createBaslerCamera(deviceRegisterForm);
            } else {
                return createUSBCamera(deviceRegisterForm);
            }
        }
    }

    @Override
    public Device createUSBCamera(DeviceRegisterForm deviceRegisterForm) {
        return new USBCamera(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createBaslerCamera(DeviceRegisterForm deviceRegisterForm) {
        return new BaslerCamera(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createHikCamera(DeviceRegisterForm deviceRegisterForm) {
        return new HikCamera(deviceRegisterForm.getDeviceOperationParameter());
    }
}
