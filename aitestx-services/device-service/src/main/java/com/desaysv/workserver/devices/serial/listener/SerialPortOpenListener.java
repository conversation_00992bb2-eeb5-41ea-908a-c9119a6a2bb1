package com.desaysv.workserver.devices.serial.listener;

import com.desaysv.workserver.devices.serial.SerialPortDevice;

/**
 * Interface for listeners that want to be notified when a SerialPortDevice is opened.
 */
public interface SerialPortOpenListener {

    /**
     * Called when a SerialPortDevice has been successfully opened.
     *
     * @param device The SerialPortDevice that was opened.
     */
    void onSerialPortOpened(SerialPortDevice device);

    /**
     * Called when a SerialPortDevice is about to be closed or has been closed.
     * This can be used to clear references to the device.
     *
     * @param device The SerialPortDevice that was closed.
     */
    void onSerialPortClosed(SerialPortDevice device);
} 