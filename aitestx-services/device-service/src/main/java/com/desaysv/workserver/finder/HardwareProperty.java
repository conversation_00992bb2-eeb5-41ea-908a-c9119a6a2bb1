package com.desaysv.workserver.finder;

import lombok.Data;

import java.util.Objects;

@Data
public class HardwareProperty {
    private String vid;
    private String pid;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HardwareProperty that = (HardwareProperty) o;
        return Objects.equals(vid, that.vid) && Objects.equals(pid, that.pid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(vid, pid);
    }

}