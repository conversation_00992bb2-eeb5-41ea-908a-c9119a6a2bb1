package com.desaysv.workserver.devices.camera.hik;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.camera.base.CameraDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * 海康相机
 */
@Slf4j
public class HikCamera extends CameraDevice {

    public HikCamera() {
        this(new DeviceOperationParameter());
    }

    public HikCamera(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Camera.HIK_CAMERA;
    }

}
