package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.qnx.QnxInstrument;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractQnxDeviceFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class QnxDeviceFactory implements AbstractQnxDeviceFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Qnx.QNX_INSTRUMENT:
                return createQnxDeviceDevice(deviceRegisterForm);
        }
        return null;
    }


    @Override
    public Device createQnxDeviceDevice(DeviceRegisterForm deviceRegisterForm) {
        return new QnxInstrument(deviceRegisterForm.getDeviceOperationParameter());
    }
}
