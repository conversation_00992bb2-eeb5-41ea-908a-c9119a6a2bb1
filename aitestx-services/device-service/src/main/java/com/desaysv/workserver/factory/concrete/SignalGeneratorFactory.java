package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.signal_generator.TKSignalGenerator;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractSignalGeneratorFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class SignalGeneratorFactory implements AbstractSignalGeneratorFactory {
    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.SignalGenerator.TK_AFG_1022:
                return createTKDevice(deviceRegisterForm);
        }
        return null;
    }

    @Override
    public Device createTKDevice(DeviceRegisterForm deviceRegisterForm) {
        return new TKSignalGenerator(deviceRegisterForm.getDeviceOperationParameter());
    }
}
