package com.desaysv.workserver.factory.interfaces;

import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-17 10:24
 * @description : 抽象电源工厂
 * @modified By :
 * @since : 2022-3-17
 */
public interface AbstractPowerFactory extends AbstractDeviceCreator {

    Device createIT68xx(DeviceRegisterForm deviceRegisterForm);

    Device createIT6322(DeviceRegisterForm deviceRegisterForm);

    Device createIT63xx(DeviceRegisterForm deviceRegisterForm);

    Device createIT69xx(DeviceRegisterForm deviceRegisterForm);

    Device createIT65xx(DeviceRegisterForm deviceRegisterForm);

    Device createIT67xx(DeviceRegisterForm deviceRegisterForm);

    Device createPowerBox(DeviceRegisterForm deviceRegisterForm);

    Device createKikusui(DeviceRegisterForm deviceRegisterForm);

    Device createN6700(DeviceRegisterForm deviceRegisterForm);

    Device createMPS3610H(DeviceRegisterForm deviceRegisterForm);

    Device createPVD8V50E(DeviceRegisterForm deviceRegisterForm);
}
