package com.desaysv.workserver.canlog.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.blflog.service.BlfService;
import com.desaysv.workserver.canlog.blflog.service.impl.BlfServiceImpl;
import com.desaysv.workserver.canlog.queue.FixedSizeQueue;
import com.desaysv.workserver.canlog.service.CanLogService;
import com.desaysv.workserver.common.constant.CanConstants;
import com.desaysv.workserver.devices.bus.base.can.CanLogRealTimeSaveParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessageRealTimeSave;
import com.desaysv.workserver.devices.bus.nican.CanMessageVo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class CanLogServiceImpl implements CanLogService {

    private File currentSaveFile;
    private List<CanMessageVo> realTimeSaveCanMessagesForBlf;
    @Getter
    private boolean realTimeSaveEnabled = false; // 实时保存是否启用 默认不启用
    private final Lock fileLock = new ReentrantLock(); // 文件操作锁
    private ExecutorService executor = Executors.newSingleThreadExecutor(); // 异步执行服务
    private long currentFileSize = 0; // 当前文件大小
    private String filePath;
    private String fileType;
    private String filterType;
    private String filterText;
    private String saveType;         // 保存方式（如"SIZE_BASED"/"COUNT_BASED"）
    private Integer fileSizeMB = CanConstants.DEFAULT_FILE_SIZE_MB;    // 文件大小限制(MB)
    private Integer recordCount = CanConstants.DEFAULT_RECORD_COUNT;  // 记录条数限制
    private final AtomicInteger counter = new AtomicInteger(0);

    private final BlfService blfService = new BlfServiceImpl();
    private static final int BATCH_SIZE = 200000;
    private int filePartCounter = 1;
    public CanLogServiceImpl() {
        // 无参构造
    }

    public CanLogServiceImpl(CanMessageRealTimeSave canMessageRealTimeSave) {
        this.filePath = canMessageRealTimeSave.getFilePath();
        this.filterType = canMessageRealTimeSave.getFilterType();
        this.filterText = canMessageRealTimeSave.getFilterText();
        this.saveType = canMessageRealTimeSave.getSaveType();
        this.fileSizeMB = canMessageRealTimeSave.getFileSizeMB();
        this.recordCount = canMessageRealTimeSave.getRecordCount();
    }

    public CanLogServiceImpl(CanLogRealTimeSaveParameter canLogRealTimeSaveParameter) {
        this.filePath = canLogRealTimeSaveParameter.getFilePath();
        this.fileType = canLogRealTimeSaveParameter.getFileType();
        this.filterType = canLogRealTimeSaveParameter.getFilterType();
        this.filterText = canLogRealTimeSaveParameter.getFilterText();
    }

    public void startRealTimeData() {
        if (!realTimeSaveEnabled) {
            currentSaveFile = new File(filePath);
            if (currentSaveFile.getAbsolutePath().endsWith(".blf")) {
                realTimeSaveCanMessagesForBlf = Collections.synchronizedList(new ArrayList<>());
            }
            if (currentSaveFile.getAbsolutePath().endsWith(".asc")) {
                String header = generateAscHeader();  // 在数据前加上Header
                //保存头部信息
                saveCanLogCreateAsyn(currentSaveFile, header);
            }
            realTimeSaveEnabled = !realTimeSaveEnabled;
            // 重置文件大小计数器
            currentFileSize = 0;
        }
    }

    public void startCaptureCanLog(){
        if (!realTimeSaveEnabled) {
            currentSaveFile = new File(filePath+"/"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())+fileType);
            if (currentSaveFile.getAbsolutePath().endsWith(".blf")) {
                realTimeSaveCanMessagesForBlf = Collections.synchronizedList(new ArrayList<>());
            }
            if (currentSaveFile.getAbsolutePath().endsWith(".asc")) {
                String header = generateAscHeader();  // 在数据前加上Header
                //保存头部信息
                saveCanLogCreateAsyn(currentSaveFile, header);
            }
            realTimeSaveEnabled = !realTimeSaveEnabled;
            // 重置文件大小计数器
            currentFileSize = 0;
        }
    }

    public void stopCaptureCanLog() {
        stopRealTimeData();
    }

    public void stopRealTimeData() {
        if (realTimeSaveEnabled) {
            // 当从启用切换到禁用时，在文件末尾添加"End TriggerBlock\n"
            if (currentSaveFile != null && currentSaveFile.getAbsolutePath().endsWith(".asc")) {
                String footer = "End TriggerBlock\n";
                // 根据保存方式决定是否创建新文件
                if (CanMessageRealTimeSave.SaveType.COUNT_BASED.toString().equals(saveType)) {
                    // 根据条数 创建文件
                    saveCanLogCreateAppendAsyn(currentSaveFile, footer, 0);
                    // 重置计数器
                    counter.set(0);
                } else {
                    saveCanLogCreateAppendAsyn(currentSaveFile, footer, true);
                }
            } else if (currentSaveFile != null && currentSaveFile.getAbsolutePath().endsWith(".blf")) {
                if (!realTimeSaveCanMessagesForBlf.isEmpty()) {
                    saveCurrentBatch();
                }
            }
            realTimeSaveEnabled = !realTimeSaveEnabled;
        }
    }

    /**
     * 异步保存日志信息到文件，并根据文件大小决定是否创建新文件。
     *
     * @param fileToSave    要保存日志信息的文件。
     * @param allMessages   要写入文件的所有日志信息。
     * @param checkFileSize 当为true时，会检查文件大小是否超过300MB并在必要时创建新文件；当为false时，即使超过300MB也继续在原文件上追加。
     */
    private void saveCanLogCreateAppendAsyn(File fileToSave, String allMessages, boolean checkFileSize) {
        File[] fileContainer = new File[]{fileToSave}; // 使用数组作为容器来持有fileToSave
        executor.submit(() -> {
            fileLock.lock();
            try {
                // 检查当前文件大小是否超过300MB，如果是，则根据checkFileSize决定是否创建新文件
                long fileSizeInBytes = fileContainer[0].length();
                long messageSizeInBytes = allMessages.getBytes().length;
                if (checkFileSize && fileSizeInBytes + messageSizeInBytes > fileSizeMB * 1024 * 1024) {
                    fileContainer[0] = createNewFileForSaving(fileContainer[0]);
                    currentSaveFile = fileContainer[0];
                }
                Files.write(fileContainer[0].toPath(), allMessages.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.APPEND);
                currentFileSize += messageSizeInBytes; // 更新当前文件大小计数器
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                fileLock.unlock();
            }
        });
    }

    /**
     * 异步保存日志信息到文件，并根据记录条数决定是否创建新文件
     * @param fileToSave    要保存日志信息的文件
     * @param allMessages   要写入文件的所有日志信息
     * @param messageCount  当前消息条数
     */
    private void saveCanLogCreateAppendAsyn(File fileToSave, String allMessages, int messageCount) {
        File[] fileContainer = new File[]{fileToSave};
        // 使用AtomicInteger作为计数器
        counter.addAndGet(messageCount);
        executor.submit(() -> {
            fileLock.lock();
            try {
                // 检查当前记录条数是否超过20万
                if (counter.get() >= recordCount) { // recordCount默认为200000
                    fileContainer[0] = createNewFileForSaving(fileContainer[0]);
                    currentSaveFile = fileContainer[0];
                    // 重置计数器
                    counter.set(0);
                }
                Files.write(fileContainer[0].toPath(), allMessages.getBytes(), StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                fileLock.unlock();
            }
        });
    }

    /**
     * 创建新文件用于保存，自动增加后缀
     *
     * @param currentFile 当前文件
     */
    private File createNewFileForSaving(File currentFile) {
        String fileName = currentFile.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String extension = fileName.substring(fileName.lastIndexOf('.'));
        int index = 1;

        // 检查文件名中是否已经包含了索引，如果包含，从当前索引继续
        Pattern pattern = Pattern.compile("(.*)_(\\d+)$");
        Matcher matcher = pattern.matcher(baseName);
        if (matcher.find()) {
            baseName = matcher.group(1);
            index = Integer.parseInt(matcher.group(2)) + 1;
        }

        File newFile;
        do {
            newFile = new File(currentFile.getParent(), baseName + "_" + index + extension);
            index++;
        } while (newFile.exists());

        return newFile;
    }

    private String generateAscHeader() {
        StringBuilder header = new StringBuilder();

        // 添加日期和时间信息
        DateFormat dateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss.SSS a yyyy", Locale.ENGLISH);
        String dateStr = dateFormat.format(new Date());
        header.append("date ").append(dateStr).append("\n");

        // 添加基数和时间戳类型
        header.append("base hex timestamps absolute\n");

        // 添加内部事件记录信息，这里假设总是记录内部事件
        header.append("internal events logged\n");

        // 添加版本号
        header.append("// version 7.0.0\n");

        return header.toString();
    }

    private void saveCanLogCreateAsyn(File fileToSave, String allMessages) {
        executor.submit(() -> {
            fileLock.lock();
            try {
                Files.write(fileToSave.toPath(), allMessages.getBytes(), StandardOpenOption.CREATE);
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                fileLock.unlock();
            }
        });
    }

    /**
     * 实时保存数据方法，需要在数据更新处调用
     *
     * @param canMessages
     */
    public void realTimeSaveData(List<CanMessageVo> canMessages) {
        if (!realTimeSaveEnabled) {
            return;
        }
        String fileName = currentSaveFile.getName().toLowerCase();
        canMessages = filterData(canMessages);
        if (fileName.endsWith(".asc")) {
            String allMessages = initAscData(canMessages);
            // 根据保存方式决定是否创建新文件
            if (CanMessageRealTimeSave.SaveType.COUNT_BASED.toString().equals(saveType)) {
                // 根据条数 创建文件
                saveCanLogCreateAppendAsyn(currentSaveFile, allMessages, canMessages.size());
            } else {
                // 根据大小 创建文件
                saveCanLogCreateAppendAsyn(currentSaveFile, allMessages, true);
                currentFileSize += allMessages.getBytes().length;
            }
        } else if (fileName.endsWith(".blf")) {
            int count;
            if (CanMessageRealTimeSave.SaveType.SIZE_BASED.toString().equals(saveType)) {
                // 根据大小 创建文件 1MB约等于10万条数据
                count = fileSizeMB * 100000;
            } else {
                count = recordCount;
            }
            realTimeSaveCanMessagesForBlf.addAll(canMessages);
            if (realTimeSaveCanMessagesForBlf.size() >= count) {
                saveCurrentBatch();
            }
        }
    }

    private List<CanMessageVo> filterData(List<CanMessageVo> canMessages) {
        if (StringUtils.isEmpty(filterText)) {
            return canMessages;
        }
        // 判断filterType并根据filterText筛选数据
        if (CanMessageRealTimeSave.DbcFilterType.Id.toString().equals(filterType)) {
            // 处理范围筛选
            if (filterText.contains("-")) {
                String[] range = filterText.split("-");
                int start = parseId(range[0].trim());
                int end = parseId(range[1].trim());
                return canMessages.stream()
                        .filter(message -> {
                            int messageId = parseId(message.getId().trim());
                            return messageId >= start && messageId <= end;
                        })
                        .collect(Collectors.toList());
            } else { // 处理单一值的情况
                int id = parseId(filterText.trim());
                return canMessages.stream()
                        .filter(message -> parseId(message.getId().trim()) == id)
                        .collect(Collectors.toList());
            }
        } else if (CanMessageRealTimeSave.DbcFilterType.Chn.toString().equals(filterType)) {
            return canMessages.stream()
                    .filter(message -> filterText.equals(message.getChn()))
                    .collect(Collectors.toList());
        } else if (CanMessageRealTimeSave.DbcFilterType.Name.toString().equals(filterType)) {
            return canMessages.stream()
                    .filter(message -> filterText.equals(message.getName()))
                    .collect(Collectors.toList());
        } else if (CanMessageRealTimeSave.DbcFilterType.Dir.toString().equals(filterType)) {
            return canMessages.stream()
                    .filter(message -> filterText.equals(message.getDir()))
                    .collect(Collectors.toList());
        }

        return canMessages;
    }

    private int parseId(String id) throws NumberFormatException {
        if (id.startsWith("0x") || id.startsWith("0X")) {
            // 如果是以"0x"开头，则按十六进制处理
            return Integer.parseInt(id.replace("0x", "").replace("0X", ""), 16);
        } else {
            // 否则按十进制处理
            return Integer.parseInt(id);
        }
    }

    private void saveCurrentBatch() {
        String baseFileName = currentSaveFile.getAbsolutePath().replace(".blf", "");
        String newFileName = baseFileName + "_" + filePartCounter + ".blf";
        filePartCounter++;

        File partFile = new File(newFileName);
        List<CanMessageVo> messagesToWrite = new ArrayList<>(realTimeSaveCanMessagesForBlf);
        executor.submit(() -> {
            try {
                blfService.write(partFile.getAbsolutePath(), messagesToWrite);
                log.info("can blflog保存结束文件:{} log条数:{}", partFile.getAbsolutePath(), messagesToWrite.size());
            } catch (BlfException e) {
                throw new RuntimeException(e);
            }
        });
        realTimeSaveCanMessagesForBlf.clear();
    }

    private String initAscData(List<CanMessageVo> canMessages) {
        StringBuilder allMessages = new StringBuilder();
        for (CanMessageVo canMessage : canMessages) {
            double timeInSeconds = canMessage.getTime();
            String messageIdHex = canMessage.getId().replace("0x", "").toLowerCase();
            String direction = canMessage.getDir();
            int length = canMessage.getData().length;
            String dataBytesHex = bytesToHexString(canMessage.getData());
            String eventType = canMessage.getEventType();
            // CAN类型区分格式
            if ("CANFD".equals(eventType) || "CANFD加速".equals(eventType)) {
                // 统一使用CANFD格式，加速状态用参数区分
                int acceleratedFlag = "CANFD加速".equals(eventType) ? 1 : 0;
                // 构建标准CANFD日志格式：时间 协议 通道 ID 方向 是否加速 错误状态标志 CANFD报文数据帧 DLC 数据
                allMessages.append(String.format("%.6f CANFD %s %s %s %d 0 d %d %d %s%n",
                        timeInSeconds, canMessage.getChn(), messageIdHex, direction,
                        acceleratedFlag, canMessage.getDlc(), length, dataBytesHex));
            } else {
                // 保持与CANFD相同的字段数量，填充默认值
                allMessages.append(String.format("%.6f %s %s %s d %d %s%n",
                        timeInSeconds, canMessage.getChn(), messageIdHex, direction,
                        canMessage.getDlc(), dataBytesHex));
            }
        }
        return allMessages.toString();
    }

    private String bytesToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x ", b));
        }
        return sb.toString().trim();
    }

    public void saveLog(FixedSizeQueue<CanMessageVo> fixedQueue) throws BlfException {
        currentSaveFile = new File(filePath);
        String extension;
        if (currentSaveFile.getName().toLowerCase().endsWith(".asc")) {
            extension = ".asc";
            if (!currentSaveFile.getAbsolutePath().endsWith(extension)) {
                currentSaveFile = new File(currentSaveFile.getAbsolutePath() + extension);
            }
            List<CanMessageVo> canMessages = filterData(fixedQueue.toList());
            String header = generateAscHeader();  // 生成头部
            String body = initAscData(canMessages);  // 生成消息体
//            String footer = "End TriggerBlock\n";  // 生成结尾
            String allMessages = header + body;

            // 异步保存数据逻辑
            saveCanLogCreateAsyn(currentSaveFile, allMessages);
        } else if (currentSaveFile.getName().toLowerCase().endsWith(".blf")) {
            extension = ".blf";
            if (!currentSaveFile.getAbsolutePath().endsWith(extension)) {
                currentSaveFile = new File(currentSaveFile.getAbsolutePath() + extension);
            }
            blfService.write(currentSaveFile.getAbsolutePath(), filterData(fixedQueue.toList()));
        } else {
            log.error("CAN日志保存失败，文件类型未知");
        }
    }
}
