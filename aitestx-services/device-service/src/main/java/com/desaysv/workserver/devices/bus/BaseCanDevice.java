package com.desaysv.workserver.devices.bus;

import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequence;
import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public abstract class BaseCanDevice extends ConfigurableDevice<CanConfig> implements ICanSequence {

    public BaseCanDevice() {
        this(new DeviceOperationParameter());
    }

    public BaseCanDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_CAN;
    }

    @Override
    public Class<CanConfig> getDeviceConfigClass() {
        return CanConfig.class;
    }

    @Override
    public void updateConfig(CanConfig canConfig) {
        super.updateConfig(canConfig);
    }

}
