package com.desaysv.workserver.service;

import com.desaysv.workserver.model.RobotCoordinates;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 13:51
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
public interface RobotCoordinatesService {

    Integer deleteCoordinatesByUUID(String  coordinatesUUID);

    Integer deleteCoordinatesByCondition(RobotCoordinates coordinates);

    Integer addCoordinates(RobotCoordinates coordinates);

    RobotCoordinates getCoordinatesByCondition(RobotCoordinates coordinates);

    Integer updateCoordinatesByUUID(RobotCoordinates coordinates);

    List<RobotCoordinates> getAllCoordinates();

    List<RobotCoordinates> getAllCoordinates(Integer projectId, Integer deviceId);

    Integer clearAllCoordinates(Integer projectId, Integer deviceId);

}
