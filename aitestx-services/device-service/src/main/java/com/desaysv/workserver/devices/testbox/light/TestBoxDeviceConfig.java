package com.desaysv.workserver.devices.testbox.light;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TestBoxDeviceConfig {
    // 核心标识字段
    @ExcelProperty("序列化的关键字")
    private String serialKey;
    
    @ExcelProperty("参数关键字") 
    private String paramKey;

    // 数值参数
    @ExcelProperty("参数容错率")
    private Double faultTolerance;

    // 硬件配置
    @ExcelProperty("MCU的PIN脚号")
    private String mcuPin;
    
    @ExcelProperty("机箱号")
    private String chassisNumber;
    
    @ExcelProperty("卡槽号")
    private String slotNumber;
    
    @ExcelProperty("通道号")
    private String channelNumber;

    // 电气参数
    @ExcelProperty("默认上拉下拉状态")
    private String pullUpDownState;
    
    @ExcelProperty("参数值")
    private String paramValue;
    
    @ExcelProperty("单位")
    private String unit;

    // 设备类型
    @ExcelProperty("板卡类型")
    private String boardType;
}