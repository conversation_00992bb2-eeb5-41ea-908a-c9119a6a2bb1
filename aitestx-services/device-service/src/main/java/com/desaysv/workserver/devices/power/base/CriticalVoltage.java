package com.desaysv.workserver.devices.power.base;

import com.desaysv.workserver.base.operation.invoker.OperationResultFormatter;
import lombok.Data;

@Data
public class CriticalVoltage implements OperationResultFormatter {

    private Float minimumLowerVoltage; //最小电压下限

    private Float minimumUpperVoltage; //最小电压上限

    @Override
    public String format() {
        return String.format("最小:%.2fV\n最大:%.2fV", minimumLowerVoltage, minimumUpperVoltage);
    }
}
