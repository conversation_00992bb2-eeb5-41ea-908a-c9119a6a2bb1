package com.desaysv.workserver.devices.autoclicker;


import com.sun.jna.Pointer;
import com.sun.jna.WString;

public class HidApi {
    private static final int WSTR_LEN = 512;
    private static final String DEVICE_NULL = "Device not initialised";
    private static final int DEVICE_ERROR = -2;
    private static final HidApiLibrary hidApiLibrary;
    /**
     * @deprecated
     */
    @Deprecated
    public static boolean dropReportIdZero;

    public HidApi() {
    }

    public static HidDeviceStructure open(int vendor, int product, String serialNumber) {
        Pointer p = hidApiLibrary.hid_open((short) vendor, (short) product, serialNumber == null ? null : new WString(serialNumber));
        return p != null ? new HidDeviceStructure(p) : null;
    }

    public static void init() {
        hidApiLibrary.hid_init();
    }

    public static void exit() {
        hidApiLibrary.hid_exit();
    }

    public static HidDeviceStructure open(String path) {
        Pointer p = hidApiLibrary.hid_open_path(path);
        return p == null ? null : new HidDeviceStructure(p);
    }

    public static void close(HidDeviceStructure device) {
        if (device != null) {
            hidApiLibrary.hid_close(device.ptr());
        }

    }

    public static HidDeviceInfoStructure enumerateDevices(int vendor, int product) {
        return hidApiLibrary.hid_enumerate((short) vendor, (short) product);
    }

    public static void freeEnumeration(HidDeviceInfoStructure list) {
        hidApiLibrary.hid_free_enumeration(list.getPointer());
    }

    public static String getLastErrorMessage(HidDeviceStructure device) {
        if (device == null) {
            return "Device not initialised";
        } else {
            Pointer p = hidApiLibrary.hid_error(device.ptr());
            return p == null ? null : (new WideStringBuffer(p.getByteArray(0L, WSTR_LEN))).toString();
        }
    }

    public static String getManufacturer(HidDeviceStructure device) {
        if (device == null) {
            return "Device not initialised";
        } else {
            WideStringBuffer wStr = new WideStringBuffer(WSTR_LEN);
            hidApiLibrary.hid_get_manufacturer_string(device.ptr(), wStr, WSTR_LEN);
            return wStr.toString();
        }
    }

    public static String getProductId(HidDeviceStructure device) {
        if (device == null) {
            return "Device not initialised";
        } else {
            WideStringBuffer wBuffer = new WideStringBuffer(WSTR_LEN);
            hidApiLibrary.hid_get_product_string(device.ptr(), wBuffer, WSTR_LEN);
            return wBuffer.toString();
        }
    }

    public static String getSerialNumber(HidDeviceStructure device) {
        if (device == null) {
            return "Device not initialised";
        } else {
            WideStringBuffer wBuffer = new WideStringBuffer(WSTR_LEN);
            hidApiLibrary.hid_get_serial_number_string(device.ptr(), wBuffer, WSTR_LEN);
            return wBuffer.toString();
        }
    }

    public static boolean setNonBlocking(HidDeviceStructure device, boolean nonBlocking) {
        return device != null && 0 == hidApiLibrary.hid_set_nonblocking(device.ptr(), nonBlocking ? 1 : 0);
    }

    public static int read(HidDeviceStructure device, byte[] buffer) {
        if (device != null && buffer != null) {
            WideStringBuffer wBuffer = new WideStringBuffer(buffer);
            return hidApiLibrary.hid_read(device.ptr(), wBuffer, wBuffer.buffer.length);
        } else {
            return -2;
        }
    }

    public static int read(HidDeviceStructure device, byte[] buffer, int timeoutMillis) {
        if (device != null && buffer != null) {
            WideStringBuffer wBuffer = new WideStringBuffer(buffer);
            return hidApiLibrary.hid_read_timeout(device.ptr(), wBuffer, buffer.length, timeoutMillis);
        } else {
            return -2;
        }
    }

    public static int getFeatureReport(HidDeviceStructure device, byte[] data, byte reportId) {
        if (device != null && data != null) {
            WideStringBuffer report = new WideStringBuffer(WSTR_LEN);
            report.buffer[0] = reportId;
            int res = hidApiLibrary.hid_get_feature_report(device.ptr(), report, data.length + 1);
            if (res == -1) {
                return res;
            } else {
                System.arraycopy(report.buffer, 1, data, 0, Math.min(res, data.length));
                return res;
            }
        } else {
            return -2;
        }
    }

    public static int sendFeatureReport(HidDeviceStructure device, byte[] data, byte reportId) {
        if (device != null && data != null) {
            WideStringBuffer report = new WideStringBuffer(data.length + 1);
            report.buffer[0] = reportId;
            System.arraycopy(data, 0, report.buffer, 1, data.length);
            return hidApiLibrary.hid_send_feature_report(device.ptr(), report, report.buffer.length);
        } else {
            return -2;
        }
    }

    public static int write(HidDeviceStructure device, byte[] data, int len, byte reportId) {
        if (device != null && data != null) {
            if (data.length < len) {
                len = data.length;
            }

            WideStringBuffer report = new WideStringBuffer(len + 1);
            report.buffer[0] = reportId;
            if (len >= 1) {
                System.arraycopy(data, 0, report.buffer, 1, len);
            }

            return hidApiLibrary.hid_write(device.ptr(), report, report.buffer.length);
        } else {
            System.out.println("null");
            return -2;
        }
    }

    public static String getIndexedString(HidDeviceStructure device, int idx) {
        if (device == null) {
            return "Device not initialised";
        } else {
            WideStringBuffer wStr = new WideStringBuffer(WSTR_LEN);
            int res = hidApiLibrary.hid_get_indexed_string(device.ptr(), idx, wStr, WSTR_LEN);
            return res == -1 ? null : wStr.toString();
        }
    }

    static {
        hidApiLibrary = HidApiLibrary.INSTANCE;
        dropReportIdZero = false;
    }
}
