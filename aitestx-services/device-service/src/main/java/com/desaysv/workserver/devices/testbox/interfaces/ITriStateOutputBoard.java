package com.desaysv.workserver.devices.testbox.interfaces;

import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ITriStateOutputBoard {
    Logger log = LogManager.getLogger(ITriStateOutputBoard.class.getSimpleName());

    /**
     * 三态高低悬空电平输出（64个通道）
     *
     * @param deviceChannel 板卡通道
     * @param state         0:悬空，1：高电平，2：低电平
     */
    boolean writeTriStateOutputBoardCard(Integer deviceChannel, int state) throws BoardCardTransportException;

    int fetchTriStateOutputByChannel(int deviceChannel) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.TestBoxRegexRule).CHANGE_TRI_STATE_OUTPUT_VALUE"})
    default void writeTriStateOutputBoardCard(Integer deviceChannel, int state, String physicalMeaning) throws OperationFailNotification {
        log.info("三态输出板卡通道{}{}设置{}", deviceChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                state == 0 ? "浮空" : (state == 1 ? "高电平" : "低电平"));
        try {
            boolean isWriteOk = writeTriStateOutputBoardCard(deviceChannel, state);
            log.info("三态输出通道{}{}设置{}--{}", deviceChannel,
                    StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                    state == 0 ? "浮空" : (state == 1 ? "高电平" : "低电平"), isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("三态输出通道%s%s--%s", deviceChannel, state == 0 ? "浮空" : (state == 1 ? "高电平" : "低电平"), isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("三态输出板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("三态输出板卡写入失败:" + e.getMessage());
        }
    }
}
