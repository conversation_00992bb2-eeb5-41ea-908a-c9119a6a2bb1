package com.desaysv.workserver.devices.testbox.interfaces;

import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.action_sequence.ActionSequenceLock;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.action_sequence.IControllableAction;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface IResistorBoard extends IControllableAction {
    Logger log = LogManager.getLogger(IResistorBoard.class.getSimpleName());

    /**
     * 改变电阻值（44个通道）
     *
     * @param deviceChannel 板卡通道
     * @param resistance    电阻值（欧姆）
     */
    //TODO:考虑用异常代替boolean返回值失败场景
    boolean writeResistanceBoardCard(Integer deviceChannel, int resistance) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE_WITH_MEANING"})
    default void changeResistanceWithMeaning(Integer deviceChannel, int resistance, String physicalMeaning) throws OperationFailNotification {
        log.info("电阻板卡通道{}{}设置阻值{}Ω", deviceChannel,
                StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                resistance);
        try {
            boolean isWriteOk = writeResistanceBoardCard(deviceChannel, resistance);
            log.info("电阻板卡通道{}{}设置阻值{}Ω--{}", deviceChannel,
                    StrUtils.isEmpty(physicalMeaning) ? "" : String.format("(%s)", physicalMeaning),
                    resistance, isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("电阻板卡通道%s设置阻值%sΩ--%s", deviceChannel, resistance, isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("电阻板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("电阻板卡写入失败:" + e.getMessage());
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE_WITH_MEANING_NEW"})
    default void changeResistanceWithMeaningNew(Integer deviceChannel, int resistance, String physicalMeaning) throws OperationFailNotification {
        changeResistanceWithMeaning(deviceChannel, resistance, physicalMeaning);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE_WITH_CHANNEL"})
    default void changeResistance(Integer deviceChannel, int resistance) throws OperationFailNotification {
        changeResistanceWithMeaning(deviceChannel, resistance, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE_NEW"})
    default void changeResistanceNew(Integer deviceChannel, int resistance) throws OperationFailNotification {
        changeResistanceWithMeaning(deviceChannel, resistance, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).CHANGE_VALUE"})
    default void changeResistance(Integer deviceChannel) throws OperationFailNotification {
        //只包含值，不包含通道
        int resistance = deviceChannel;
        changeResistanceWithMeaning(1, resistance, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).STEP_RANGE_WITH_TIME"})
    default boolean writeResistanceBoardCardStepRangeWithTime(Integer deviceChannel, int startResistance, int endResistance, int step, String duration) throws OperationFailNotification {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(duration);  //不带单位，代表ms
        long milliseconds = (long) (seconds * 1000);
        log.info("电阻板卡通道{}设置步进阻值{}~{}Ω(步进{}Ω，持续时间:{}s)", deviceChannel, startResistance, endResistance, step, seconds);
        boolean ok = false;
        float stepInterval = duration == null ? 0.1f : seconds / ((float) Math.abs(endResistance - startResistance) / step);
        long startMills = System.currentTimeMillis();
        try {
            if (startResistance <= endResistance) {
                for (int resistance = startResistance; resistance <= endResistance; resistance += step) {
                    while (isPause()) {
                        log.info("电阻步进已暂停");
                        try {
                            synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                                ActionSequenceLock.getInstance().getPauseLock().wait();
                            }
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                    ok = writeResistanceBoardCard(deviceChannel, resistance);
                    try {
                        Thread.sleep(milliseconds);
                    } catch (InterruptedException e) {
                        break;
                    }
                }
            } else {
                for (int resistance = startResistance; resistance >= endResistance; resistance -= step) {
                    while (isPause()) {
                        log.info("电阻步进已暂停");
                        try {
                            synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                                ActionSequenceLock.getInstance().getPauseLock().wait();
                            }
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                    ok = writeResistanceBoardCard(deviceChannel, resistance);
                    try {
                        Thread.sleep(milliseconds);
                    } catch (InterruptedException e) {
                        break;
                    }
                }
            }
            writeResistanceBoardCard(deviceChannel, endResistance);
            log.info("电阻步进耗时:{}s", (System.currentTimeMillis() - startMills) / 1000.0f);
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification(e);
        }
        return ok;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).STEP_RANGE"})
    default boolean writeResistanceBoardCardStepRange(Integer deviceChannel, int startResistance, int endResistance, int step) throws OperationFailNotification {
        return writeResistanceBoardCardStepRangeWithTime(deviceChannel, startResistance, endResistance, step, null);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CommonRegexRule).RANDOM_RANGE"})
    default boolean writeResistanceBoardCardRandomRange(Integer deviceChannel, int startResistance, int endResistance) throws OperationFailNotification {
        try {
            return writeResistanceBoardCard(deviceChannel, RandomUtil.randomInt(startResistance, endResistance));
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification(e);
        }
    }


//    boolean writeResistanceBoardCard(Integer slotNumber, Integer deviceChannel, int resistance) throws BoardCardTransportException;

}
