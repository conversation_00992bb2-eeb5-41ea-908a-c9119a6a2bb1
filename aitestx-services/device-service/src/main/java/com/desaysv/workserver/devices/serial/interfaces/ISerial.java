package com.desaysv.workserver.devices.serial.interfaces;

import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.common.port.MessageText;
import com.desaysv.workserver.entity.DeviceContextInfo;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.utils.ByteUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 串口操作接口
 */
public interface ISerial {
    Logger log = LogManager.getLogger(ISerial.class.getSimpleName());

    // 判断字符串是否是十六进制格式
    default boolean isHexadecimal(String message) {
        // 移除所有空格
        String cleanedMessage = message.replaceAll("\\s+", "");
        // 十六进制的正则表达式：必须是偶数长度的字符串，只包含0-9, a-f, A-F的字符
        String hexPattern = "^[0-9A-Fa-f]+$";
        // 检查是否符合正则表达式，并且长度是偶数
        return cleanedMessage.matches(hexPattern) && (cleanedMessage.length() % 2 == 0);
    }

    boolean send(String message, boolean isHex) throws DeviceSendException;

    default boolean sendByActionSequence(String message, boolean isHex, String checkedContext) throws DeviceSendException {
        return send(message, isHex);
    }


    /**
     * 串口发送数据
     *
     * @param text 数据带有括号的值
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_PARENTHESIS"})
    default void send(String text, String parenthesisContext, String checkedContext) throws DeviceSendException {
        sendAnything(parenthesisContext, checkedContext, String.valueOf(50));
    }

    /**
     * 串口发送数据
     *
     * @param text 数据
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND"})
    default void send(String text, String checkedContext) throws DeviceSendException {
        sendAnything(text, checkedContext, String.valueOf(50));
    }
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_BY_INTERPRETATION"})
    default boolean sendByInterpretation(String name, String checkedContext) throws DeviceSendException {
        return false;
    }

    default void sendAnything(String text, String checkedContext, String interval) throws DeviceSendException {
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        List<String> contexts = expandContextIfNecessary(checkedContext);

        if (text != null && text.length() >= 3 && text.charAt(text.length() - 3) == '~') {
            String[] parts = text.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("起始值不能大于结束值");
            }

            boolean useSameContext = contexts.size() == 1;

            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                String message = prefix + hexValue;
                String dynamicContext = useSameContext ? contexts.get(0) : (i - start < contexts.size() ? contexts.get(i - start) : null);
                sendByActionSequence(message, isHexadecimal(message), dynamicContext);
                try {
                    TimeUnit.SECONDS.sleep(seconds.longValue());
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } else if (text.contains("~")) {
            throw new IllegalArgumentException("格式错误: ~ 必须位于倒数第三位");
        } else {
            // 单条消息发送
            if (contexts.size() != 1) {
                throw new IllegalArgumentException("单条消息发送时，上下文不能为范围");
            }
            sendByActionSequence(text, isHexadecimal(text), contexts.get(0));
        }
    }

    default List<String> expandContextIfNecessary(String context) {
        if (context != null && context.length() >= 3 && context.charAt(context.length() - 3) == '~') {
            String[] parts = context.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("上下文格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("上下文格式错误: 前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("上下文格式错误: 起始值不能大于结束值");
            }

            List<String> contexts = new ArrayList<>();
            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                contexts.add(prefix + hexValue);
            }
            return contexts;
        } else if (context != null && context.contains("~")) {
            throw new IllegalArgumentException("上下文格式错误: ~ 必须位于倒数第三位");
        } else {
            return Collections.singletonList(context);
        }
    }

    /**
     * 获取串口反馈
     *
     * @param isHex               是否为16进制
     * @param compareMessage      比较的消息
     * @param timeoutMilliseconds 接收超时时间
     * @return 反馈内容
     */
    String readPortFeedback(boolean isHex, String compareMessage, int timeoutMilliseconds);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_PARENTHESIS"})
    default ActualExpectedResult compareSerialFeedback(String text, String parenthesisContext) throws DeviceSendException {
        return compareFeedback(parenthesisContext);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV"})
    default ActualExpectedResult compareFeedback(String message) {
        return compareFeedbackWithTimeout(message, "2000");
    }

    /**
     * 串口反馈比对
     *
     * @return 是否匹配
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_WAIT"})
    default ActualExpectedResult compareFeedbackWithTimeout(String message, String timeoutMilliseconds) {
        Float timeout = BaseRegexRule.getSecondsOfDefaultMills(timeoutMilliseconds);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("读取串口反馈");

        boolean isHex = isHexadecimal(message);
        String recvMessage;

        if (message != null && message.length() >= 3 && message.charAt(message.length() - 3) == '~') {
            String[] parts = message.split("~");
            if (parts.length != 2) {
                throw new IllegalArgumentException("接收消息格式错误: 必须为 XXXXXXXX00~FF 格式");
            }

            String prefixWithStart = parts[0];
            String endStr = parts[1];

            if (prefixWithStart.length() < 2) {
                throw new IllegalArgumentException("接收消息格式错误: 前缀部分长度不足");
            }

            String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
            int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
            int end = Integer.parseInt(endStr, 16);

            if (start > end) {
                throw new IllegalArgumentException("接收消息格式错误: 起始值不能大于结束值");
            }

            Set<String> expectedMessages = new HashSet<>();
            for (int i = start; i <= end; i++) {
                String hexValue = String.format("%02X", i);
                expectedMessages.add(prefix + hexValue);
            }

            recvMessage = readPortFeedback(isHex, message, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);

            String[] receivedLines = recvMessage.split("\n"); // 假设每条消息以换行为分隔
            boolean allMatched = true;

            for (String line : receivedLines) {
                String trimmedLine = line.trim();
                if (!expectedMessages.contains(trimmedLine)) {
                    allMatched = false;
                    break;
                }
            }
            actualExpectedResult.put("compareFeedback", allMatched, recvMessage);

        } else if (message.contains("~")) {
            throw new IllegalArgumentException("接收消息格式错误: ~ 必须位于倒数第三位");
        } else {
            recvMessage = readPortFeedback(isHex, message, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);
            boolean pass = recvMessage.toLowerCase().contains(message.toLowerCase());
            actualExpectedResult.put("compareFeedback", pass, recvMessage);
        }

        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_CHECK_SUM"})
    default void sendWithCheckSum(String text, String checkType, String checkedContext) throws DeviceSendException {
        sendAnything(text, checkType, String.valueOf(50), checkedContext);
    }

    /**
     * 串口发送数据
     *
     * @param text 数据
     * @return
     * @throws DeviceSendException 发送失败
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SEND_CHECK_INTERNAL"})
    default void sendAnything(String text, String checkType, String interval, String checkedContext) throws DeviceSendException {
        List<String> contexts = expandContextIfNecessary(checkedContext);
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        if (text != null && hasValidRangeFormat(text)) {
            RangeInfo range = parseRange(text);
            boolean useSameContext = contexts.size() == 1;

            for (int i = range.start; i <= range.end; i++) {
                String hexValue = String.format("%02X", i);
                String message = range.prefix + hexValue;
                String finalMessage = applyChecksumIfNecessary(message, checkType);
                String dynamicContext = useSameContext ? contexts.get(0) : getDynamicContext(contexts, i - range.start);
                sendByActionSequence(finalMessage, isHexadecimal(finalMessage), dynamicContext);
                try {
                    TimeUnit.SECONDS.sleep(seconds.longValue());
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } else if (text.contains("~")) {
            throw new IllegalArgumentException("格式错误: ~ 必须位于倒数第三位");
        } else {
            String finalMessage = applyChecksumIfNecessary(text, checkType);
            if (contexts.size() != 1) {
                throw new IllegalArgumentException("单条消息发送时，上下文不能为范围");
            }
            sendByActionSequence(finalMessage, isHexadecimal(finalMessage), contexts.get(0));
        }
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_CHECK"})
    default ActualExpectedResult compareFeedback(String message, String checkType) {
        return compareFeedback(message, checkType, "2000");

    }

    /**
     * 串口反馈比对
     *
     * @return 是否匹配
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).RECV_CHECK_WAIT"})
    default ActualExpectedResult compareFeedback(String message, String checkType, String TimeoutMilliseconds) {
        Float timeout = BaseRegexRule.getSecondsOfDefaultMills(TimeoutMilliseconds);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        log.info("读取串口反馈");

        boolean isHex = isHexadecimal(message);
        String recvMessage;

        if (message != null && hasValidRangeFormat(message)) {
            RangeInfo range = parseRange(message);
            Set<String> expectedMessages = new HashSet<>();

            for (int i = range.start; i <= range.end; i++) {
                String hexValue = String.format("%02X", i);
                String expectedMessage = range.prefix + hexValue;
                expectedMessage = applyChecksumIfNecessary(expectedMessage, checkType);
                expectedMessages.add(expectedMessage);
            }

            recvMessage = readPortFeedback(isHex, message, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);

            String[] receivedLines = recvMessage.split("\n");
            boolean allMatched = true;

            for (String line : receivedLines) {
                String trimmedLine = line.trim();
                if (!expectedMessages.contains(trimmedLine)) {
                    allMatched = false;
                    break;
                }
            }

            actualExpectedResult.put("compareFeedback", allMatched, recvMessage);

        } else if (message.contains("~")) {
            throw new IllegalArgumentException("接收消息格式错误: ~ 必须位于倒数第三位");
        } else {
            String expectedMessage = applyChecksumIfNecessary(message, checkType);
            recvMessage = readPortFeedback(isHex, expectedMessage, Math.round(timeout * 1000));
            log.info("接收串口消息:{}", recvMessage);
            boolean pass = recvMessage.toLowerCase().contains(expectedMessage.toLowerCase());
            actualExpectedResult.put("compareFeedback", pass, recvMessage);
        }
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.SerialRegexRule).SERIAL_CHECK_MSG"})
    default ActualExpectedResult serialCheckMsg(String messageName) {
        String msgData = DeviceContextInfo.getInstance().getMessageDataMap().get(messageName);
        if(msgData == null){
            return compareFeedback(msgData);
        }else {
            ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
            actualExpectedResult.put("compareFeedback", false, "未获取到需要对比的报文数据");
            return actualExpectedResult;
        }
    }


    /**
     * 串口发送数据并匹配反馈
     *
     * @param messageText 数据
     * @return 是否匹配
     * @throws OperationFailNotification
     */
    boolean sendAndMatch(MessageText messageText) throws OperationFailNotification;

    default boolean hasValidRangeFormat(String text) {
        if (text == null) return false;
        String cleanedMessage = text.replaceAll("\\s+$", ""); // 只去除末尾空格
        return cleanedMessage.length() >= 3 && cleanedMessage.charAt(cleanedMessage.length() - 3) == '~';
    }

    default RangeInfo parseRange(String text) {
        String[] parts = text.split("~");
        if (parts.length != 2) {
            throw new IllegalArgumentException("格式错误: 必须为 XXXXXXXX00~FF 格式");
        }

        String prefixWithStart = parts[0];
        String endStr = parts[1];

        if (prefixWithStart.length() < 2) {
            throw new IllegalArgumentException("前缀部分长度不足");
        }

        String prefix = prefixWithStart.substring(0, prefixWithStart.length() - 2);
        int start = Integer.parseInt(prefixWithStart.substring(prefixWithStart.length() - 2), 16);
        int end = Integer.parseInt(endStr, 16);

        if (start > end) {
            throw new IllegalArgumentException("起始值不能大于结束值");
        }

        return new RangeInfo(prefix, start, end);
    }

    default String getDynamicContext(List<String> contexts, int index) {
        return index < contexts.size() ? contexts.get(index) : null;
    }

    default String applyChecksumIfNecessary(String message, String checkType) {
        if (checkType == null) return message;

        switch (checkType.toUpperCase()) {
            case "ADD8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateAdd8);
            case "XOR8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateXor8);
            case "CRC8":
                return ByteUtils.addChecksum(message, ByteUtils::calculateCrc8);
            default:
                return message;
        }
    }

    class RangeInfo {
        final String prefix;
        final int start;
        final int end;

        RangeInfo(String prefix, int start, int end) {
            this.prefix = prefix;
            this.start = start;
            this.end = end;
        }
    }
}
