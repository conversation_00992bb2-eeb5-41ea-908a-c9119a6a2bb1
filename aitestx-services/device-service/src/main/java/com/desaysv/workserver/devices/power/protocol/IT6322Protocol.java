package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.devices.power.base.PowerDevice;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-11 10:56
 * @description : 只适用于IT6322电源
 * @modified By :
 * @since : 2022-7-11
 */
public class IT6322Protocol extends PowerSCPIProtocol {

    public IT6322Protocol(PowerDevice device) {
        super(device);
    }

    @Override
    public boolean selectChannel(Integer channel) {
        if (channel != null) {
            return sendCommand(ScpiCommandConstants.selectChannel(channel, false));
        }
        return true;
    }

    @Override
    public boolean setOutput(Integer channel, boolean isOutput) {
        return selectChannel(channel) &&
                sendCommand(isOutput ? ScpiCommandConstants.OUTPUT_ON : ScpiCommandConstants.OUTPUT_OFF);
    }
}
