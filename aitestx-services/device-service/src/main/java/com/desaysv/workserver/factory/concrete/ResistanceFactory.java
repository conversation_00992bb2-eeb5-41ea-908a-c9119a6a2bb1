package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.resistance.RemoteResistanceDevice;
import com.desaysv.workserver.devices.resistance.ResistanceDevice;
import com.desaysv.workserver.devices.resistance.ResistanceQRDevice;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractResistanceFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 11:21
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
@Component
@Lazy
public class ResistanceFactory implements AbstractResistanceFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        if (deviceModel.equals(DeviceModel.Resistance.REMOTE_RESISTANCE)) {
            return createRemoteResistanceDevice(deviceRegisterForm);
        } else if (deviceModel.equals(DeviceModel.Resistance.QR10X)) {
            return createResistanceQRDevice(deviceRegisterForm);
        } else {
            return createResistanceDevice(deviceRegisterForm);
        }
    }

    @Override
    public Device createResistanceDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ResistanceDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createRemoteResistanceDevice(DeviceRegisterForm deviceRegisterForm) {
        return new RemoteResistanceDevice(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createResistanceQRDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ResistanceQRDevice(deviceRegisterForm.getDeviceOperationParameter());
    }
}
