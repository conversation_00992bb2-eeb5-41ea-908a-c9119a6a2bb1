package com.desaysv.workserver.devices.tcpserver;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: TCP服务器
 * @date: 2024/6/24 21:24
 */

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.BindException;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Pattern;

@Slf4j
public class TCPServerControl {
    private static TCPServerControl instance;
    private final String ipAddress;
    private final int port;
    private ServerSocket serverSocket;
    private volatile boolean isRunning;
    private volatile boolean isCollectingData;
    private final List<Socket> clientSockets;
    private final List<String> collectedData;
    private static final String TCP_SERVER_ADDRESS = "127.0.0.1";
    private static final int TCP_SERVER_PORT = 8082;
    private final Lock fileLock = new ReentrantLock(); // 文件操作锁
    private final ExecutorService executor = Executors.newSingleThreadExecutor(); // 异步执行服务
    private boolean appendFlag = false; // 标志位
    private boolean dataUpdateFlag = false; // 标志位
    private String filePath;
    private String tcpLine;
    private String splitFlag = "OS"; // 分隔标志
    private final List<String> recentDataHistory = new ArrayList<>();
    private final List<Long> dataTimestamps = new ArrayList<>();
    private final Lock recentDataLock = new ReentrantLock(); // 锁用于保护 recentDataWithinFourSeconds 和 dataTimestamps
    private ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final long HISTORY_SECONDS = 5000;


    private TCPServerControl(String ipAddress, int port) {
        this.ipAddress = ipAddress;
        this.port = port;
        this.isRunning = false;
        this.isCollectingData = false;
        this.clientSockets = new ArrayList<>();
        this.collectedData = new ArrayList<>();
    }

    public TCPServerControl() {
        this.ipAddress = "127.0.0.1";
        this.port = 8083;
        this.isRunning = false;
        this.isCollectingData = false;
        this.clientSockets = new ArrayList<>();
        this.collectedData = new ArrayList<>();
    }

    public static synchronized TCPServerControl getInstance() {

        return getInstance(TCP_SERVER_ADDRESS, TCP_SERVER_PORT);
    }

    public static TCPServerControl newInstance(String ipAddress, int port) {
        return new TCPServerControl(ipAddress, port);
    }

    public static synchronized TCPServerControl getInstance(String ipAddress, int port) {
        if (instance == null) {
            instance = new TCPServerControl(ipAddress, port);
        }
        return instance;
    }

    public void startServer() {
        try {
            serverSocket = new ServerSocket(port);
            this.isRunning = true;
            log.info("TCP/IP服务端口启动:{}:{}", ipAddress, port);
        } catch (BindException e) {
            log.error("端口绑定异常: {}", e.getMessage());
            stopServer();
        } catch (IOException e) {
            log.error("IO异常: {}", e.getMessage());
            stopServer();
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage());
            stopServer();
        }
    }

    public void startCollectingData() {
        this.isCollectingData = true;
        collectedData.clear();
    }

    public void clearCollectingData() {
        collectedData.clear();
    }

    public void stopCollectingData() {
        this.isCollectingData = false;
    }

    public void receiveData() {
        new Thread(() -> {
            try {
                while (isRunning) {
                    Socket clientSocket = serverSocket.accept();
                    clientSockets.add(clientSocket);
                    BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
                    StringBuilder dataBuffer = new StringBuilder();
                    char[] buffer = new char[1024];
                    int bytesRead;

                    while (isRunning && !clientSocket.isClosed() && (bytesRead = in.read(buffer)) != -1) {
                        dataBuffer.append(buffer, 0, bytesRead);

                        String rawData = dataBuffer.toString();
                        List<String> messages = new ArrayList<>();

                        if (splitFlag != null && !splitFlag.isEmpty()) {
                            // 使用 splitFlag 分割数据（保留起始标志）
                            String[] parts = rawData.split("(?=" + Pattern.quote(splitFlag) + ")");
                            for (String part : parts) {
                                if (!part.isEmpty()) {
                                    messages.add(part);
                                }
                            }
                        } else {
                            // 没有设置分隔符时，整条作为一个消息
                            if (!rawData.isEmpty()) {
                                messages.add(rawData);
                            }
                        }
                        for (String message : messages) {
                            long currentTime = System.currentTimeMillis();
                            recentDataLock.lock();
                            try {
                                recentDataHistory.add(message);
                                dataTimestamps.add(currentTime);
                            } finally {
                                recentDataLock.unlock();
                            }

                            dataUpdateFlag = true;
                            tcpLine = message;
                            log.info("接收数据: " + message);

                            if (isCollectingData) {
                                collectedData.add(message);
                            }

                            TCPShowMessages tcpShowMessages = TCPShowMessages.builder().message(message).isSend(false).build();
                            SseUtils.pubMsg(SseConstants.TCP_MSG, JSON.toJSONString(tcpShowMessages));
                        }

                        dataBuffer.setLength(0);
                        Thread.yield();
                    }
                }
            } catch (SocketException e) {
                log.info("Socket关闭，停止数据接收。");
            } catch (IOException e) {
                log.error("IO异常: {}", e.getMessage());
            } catch (Exception e) {
                log.error("未知异常: {}", e.getMessage());
            } finally {
                scheduler.scheduleAtFixedRate(this::cleanOldData, 0, 1, TimeUnit.SECONDS);
            }
        }).start();
    }

    private void cleanOldData() {
        long fourSecondsAgo = System.currentTimeMillis() - HISTORY_SECONDS;
        recentDataLock.lock();
        try {
            while (!dataTimestamps.isEmpty() && dataTimestamps.get(0) < fourSecondsAgo) {
                recentDataHistory.remove(0);
                dataTimestamps.remove(0);
            }
        } finally {
            recentDataLock.unlock();
        }
    }

    public List<String> getRecentDataHistory() {
        recentDataLock.lock();
        try {
            return new ArrayList<>(recentDataHistory);
        } finally {
            recentDataLock.unlock();
        }
    }

    public boolean sendData(String data) {
        if (!isRunning) {
            log.error("服务器未运行，无法发送数据");
            return false;
        }
        if (clientSockets.isEmpty()) {
            log.error("没有客户端连接，无法发送数据");
            return false;
        }
        boolean success = false;
        for (Socket clientSocket : clientSockets) {
            if (clientSocket != null && !clientSocket.isClosed()) {
                try {
                    clientSocket.getOutputStream().write(data.getBytes());
                    clientSocket.getOutputStream().flush();
                    TCPShowMessages tcpShowMessages = TCPShowMessages.builder().message(data).isSend(true).build();
                    SseUtils.pubMsg(SseConstants.TCP_MSG, JSON.toJSONString(tcpShowMessages));
                    log.info("发送数据: " + data);
                    success = true;
                } catch (IOException e) {
                    log.error("发送数据时发生IO异常: {}", e.getMessage());
                }
            }
        }
        return success;
    }

    public void stopServer() {
        try {
            this.isRunning = false;
            if (serverSocket != null && !serverSocket.isClosed()) {
                for (Socket clientSocket : clientSockets) {
                    if (clientSocket != null && !clientSocket.isClosed()) {
                        clientSocket.close();
                    }
                }
                serverSocket.close();
                log.info("websocket服务器关闭");
            }
        } catch (IOException e) {
            log.error("IO异常: {}", e.getMessage());
        } catch (Exception e) {
            log.error("未知异常: {}", e.getMessage());
        } finally {
            executor.shutdown();
        }
    }

    public void setSplitFlag(String splitFlag) {
        this.splitFlag = splitFlag;
    }

    public boolean isServerRunning() {
        return this.isRunning;
    }

    public List<String> getCollectedData() {
        return new ArrayList<>(collectedData);
    }

    public void saveLog(String LogName) {
        this.filePath = LogName;
    }

    public void startSaveData(String LogName) {
        if (appendFlag) {
            System.out.println("文件写入任务已经在运行中！");
            return;
        }
        appendFlag = true;
        executor.submit(this::appendData);
    }

    private void appendData() {
        try (FileWriter writer = new FileWriter(filePath, true)) {
            try {
                fileLock.lock();
                while (appendFlag) {
                    if (dataUpdateFlag) {
                        dataUpdateFlag = false;
                        System.out.println("appendData 写入数据: " + tcpLine);
                        writer.write(tcpLine + "\n");
                        writer.flush();
                    }
                }
            } finally {
                fileLock.unlock();
            }
        } catch (IOException e) {
            log.error("IO异常: {}", e.getMessage());
        } finally {
            appendFlag = false;
        }
    }

    public void stopSaveData() {
        if (!appendFlag) {
            System.out.println("文件写入任务没有运行！");
            return;
        }
        appendFlag = false;
    }

}