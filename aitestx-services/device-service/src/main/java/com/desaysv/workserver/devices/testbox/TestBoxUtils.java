package com.desaysv.workserver.devices.testbox;

import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;

@Slf4j
public class TestBoxUtils {
    public static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();
    public static final String HEADER = "53";
    public static final String WRITE_CODE = "03";
    public static final String READ_CODE = "04";
    private static final String PREFIX_WRITE = "5303"; // "5303"
    private static final String PREFIX_READ = "5304"; // "5304"
    public static final String PWM_INPUT_DATA = "01";
    public static final String PADDING_DATA = "01";
    public static final String VOLT_ACQUISITION_PADDING_DATA = "000000";
    // 常量定义
    public static final String SWITCH_ON_HEX = "01000000";
    public static final String SWITCH_OFF_HEX = "00000000";
    public static final String STATE_0_HEX = "00000000";
    public static final String STATE_1_HEX = "01000000";
    public static final String STATE_2_HEX = "02000000";
    // 新增预处理常量（类级别定义）
    private static final String CMD_ON = "ON".toUpperCase(Locale.ROOT);
    private static final String CMD_OFF = "OFF".toUpperCase(Locale.ROOT);
    private static final Set<String> VALID_COMMANDS;

    static {
        Set<String> commands = new HashSet<>(2);
        commands.add(CMD_ON);
        commands.add(CMD_OFF);
        VALID_COMMANDS = Collections.unmodifiableSet(commands);
    }


    public static String buildRelaySwitchCommand(int chassisNumber, int slotNumber, int channelNumber, String switchHexString) {
        return String.format("%s%02X%s%02X%02X%s",
                HEADER,
                chassisNumber,
                WRITE_CODE,
                slotNumber,
                channelNumber,
                switchHexString);
    }

    public static String buildPWMInputCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber, String data) {
        return String.format("%s%02X%s%02X%02X%s",
                HEADER,
                chassisNumber,
                READ_CODE,
                slotNumber,
                channelNumber,
                data);
    }

    //构建获取电压命令
    public static String buildGetVoltageCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber) {
        return String.format("%s%02X%s%02X%02X%s",
                HEADER,
                chassisNumber,
                READ_CODE, // READ_CODE 是读取操作的命令码
                slotNumber,
                channelNumber,
                PADDING_DATA);
    }

    public static String buildPWMOutCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, String frequencyHexString, String dutyCycleHexString) {
        return String.format("%s%02X%s%02X%02X%02X%s%s",
                HEADER,
                chassisNumber,
                WRITE_CODE,
                slotNumber,
                channelNumber,
                pullUpNumber,
                frequencyHexString,
                dutyCycleHexString);
    }

    public static String buildResistanceCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber, Integer pullUpNumber, String resistanceHexString) {
        return String.format("%s%02X%s%02X%02X%02X%s",
                HEADER,
                chassisNumber,
                WRITE_CODE,
                slotNumber,
                channelNumber,
                pullUpNumber,
                resistanceHexString);
    }

    // 构建设置电平命令
    public static String buildTriStateOutputCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber, String stateHexString) {
        return String.format("%s%02X%s%02X%02X%s",
                HEADER,
                chassisNumber,
                WRITE_CODE,
                slotNumber,
                channelNumber,
                stateHexString);
    }

    // 构建设置电平采集板卡命令
    public static String buildVoltAcquisitionCommand(Integer chassisNumber, Integer slotNumber, Integer channelNumber, String stateHexString) {
        return String.format("%s%02X%s%02X%02X%s%s",
                HEADER,
                chassisNumber,
                WRITE_CODE,
                slotNumber,
                channelNumber,
                stateHexString,
                VOLT_ACQUISITION_PADDING_DATA);
    }

    // 校验命令是否有效
    public static boolean isValidCommand(String command) {
        return command != null && (command.equalsIgnoreCase("ON") || command.equalsIgnoreCase("OFF"));
    }

    // 获取开关状态的十六进制值
    public static String getSwitchHexValue(String command) {
        return command.equalsIgnoreCase("ON") ? SWITCH_ON_HEX : SWITCH_OFF_HEX;
    }


    // 获取开关状态的十六进制值
    // 合并校验与转换逻辑（示例）
    public static String validateAndConvertCommand(String command) {
        if (command == null) return null;
        final String upperCmd = command.toUpperCase(Locale.ROOT);
        return VALID_COMMANDS.contains(upperCmd) ?
                (CMD_ON.equals(upperCmd) ? SWITCH_ON_HEX : SWITCH_OFF_HEX)
                : SWITCH_OFF_HEX;
    }

    // 校验状态值是否有效
    public static boolean isValidState(int state) {
        return state == 0 || state == 1 || state == 2;
    }

    // 获取状态对应的十六进制值
    public static String getStateHexValue(int state) {
        switch (state) {
            case 0:
                return STATE_0_HEX;
            case 1:
                return STATE_1_HEX;
            case 2:
                return STATE_2_HEX;
            default:
                throw new IllegalArgumentException("无效的状态值: " + state);
        }
    }


    public static boolean judgeResistanceMinValue(Integer pullUpNumber, int resistance) {
        switch (pullUpNumber) {
            case 0:
            case 3:
                if (resistance < 0) {
                    ActionSequencesLoggerUtil.warn("悬空/下拉到地状态：电阻值不能小于0Ω，当前值: {}", resistance);
                    return false;
                }
                break;
            case 1:
//                if (resistance < 60) {  ////第一次变更
                if (resistance < 112) {  ////第一次变更
                    ActionSequencesLoggerUtil.warn("上拉到5V状态：电阻值不能小于112Ω，当前值: {}", resistance);
                    return false;
                }
                break;
            case 2:
//                if (resistance < 130) {  //第一次变更
                if (resistance < 640) {   //第二次变更
                    ActionSequencesLoggerUtil.warn("上拉到12V状态：电阻值不能小于640Ω，当前值: {}", resistance);
                    return false;
                }
                break;
            default:
                ActionSequencesLoggerUtil.warn("无效的电阻上下拉状态: {}", pullUpNumber);
                return false;
        }
        return true;
    }
}
