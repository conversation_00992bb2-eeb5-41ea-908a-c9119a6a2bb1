package com.desaysv.workserver.common.utils;

public class Hex<PERSON><PERSON><PERSON> {

    private static void calXPos() {
        byte[] b = new byte[]{(byte) 0x51, (byte) 0x0A};
        int[] arr = new int[b.length];
        for (int i = 0; i < b.length; i++) {
            arr[i] = b[i] & 0xff;
        }
//        System.out.println("b[0]:" + Integer.toBinaryString(arr[0]));
//        System.out.println("b[1]:" + Integer.toBinaryString(arr[1]));
//
//        System.out.println();
//        int cc = arr[0] << 8 | arr[1];
//        System.out.println(arr[0] << 8);
//        System.out.println(arr[1]);
//        System.out.println(cc);
//        System.out.println("arr[0]<<8:" + Integer.toBinaryString(arr[0] << 8));
//        System.out.println("arr[1]:" + Integer.toBinaryString(arr[1]));
//        System.out.println("value:" + Integer.toBinaryString(cc));

//        int ccc = cc >> 6;
//        System.out.println(Integer.toBinaryString(ccc));
//        System.out.println(ccc);

        int ddd = arr[0] << 2 | (arr[1] >> 6);
        System.out.println(ddd);
        System.out.println("------");
    }

    private static void calYPos() {
        byte[] b = new byte[]{(byte) 0x0A, (byte) 0x20};
        int[] arr = new int[b.length];
        for (int i = 0; i < b.length; i++) {
            arr[i] = b[i] & 0xff;
        }
//        int dd = arr[0] << 4 | (arr[1] >> 4);
//        int ddd = dd & (0xfff >> 2);
//        System.out.println(">>4:" + Integer.toBinaryString(arr[1] >> 4));
//        System.out.println(Integer.toBinaryString(dd));
//        System.out.println(Integer.toBinaryString(ddd));
//        System.out.println(ddd);

//        int k = (arr[0] & (0xff >> 2)) << 4 | (arr[1] >> 4);
//        System.out.println();
//        System.out.println(Integer.toBinaryString(k));
//        System.out.println(k);

        int kk = ((arr[0] & 0x3F) << 4) | arr[1] >> 4;
        System.out.println(kk);

//        System.out.println(Integer.toBinaryString((arr[0] & 0x3F)));
    }

    public static void main(String[] args) {
        HexCombinator.calXPos();
        HexCombinator.calYPos();
    }

}
