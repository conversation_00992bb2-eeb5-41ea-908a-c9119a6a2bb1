package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.entity.PointInt;
import com.desaysv.workserver.model.roi.RoiRectInfo;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.SpringContextHolder;

import java.io.IOException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-21 11:05
 * @description :
 * @modified By :
 * @since : 2022-5-21
 */
public class WifiAndroid extends AndroidDevice {

    public WifiAndroid() {
        this(new DeviceOperationParameter());
    }

    public WifiAndroid(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Android.WIFI_ANDROID;
    }

    @Override
    public boolean close() {
        return true;
    }

    @Override
    public String screenshot() {
        return null;
    }

    @Override
    public boolean click(PointInt pointInt) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean swipe(PointInt startPoint, PointInt endPoint) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean testSimilarity() {
        SpringContextHolder.getBean("imageOperationCommand");
        return false;
    }

    @Override
    public boolean randomClick(RoiRectInfo roiRectInfo) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean randomSwipe(RoiRectInfo roiRectInfo) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean executeADBCommand(String command) throws OperationFailNotification {
        return false;
    }

    @Override
    public boolean multiFingerSwipe(RoiRectInfo roiRectInfo, int fingerCount, String direction) throws OperationFailNotification, IOException {
        return false;
    }
}
