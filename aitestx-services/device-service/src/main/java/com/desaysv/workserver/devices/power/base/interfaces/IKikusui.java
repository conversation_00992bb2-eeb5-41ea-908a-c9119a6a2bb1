package com.desaysv.workserver.devices.power.base.interfaces;

import com.desaysv.workserver.devices.power.kikusui.*;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import xyz.froud.jvisa.JVisaException;

public interface IKikusui {

    /**
     * 加载脉冲
     *
     * @param memory 脉冲内存位置
     * @return 是否加载成功
     */
    boolean loadWavePulse(int memory) throws DeviceSendException;


    /**
     * 暂停脉冲
     *
     * @throws DeviceSendException
     */
    void pauseWavePulse() throws DeviceSendException;

    /**
     * 继续脉冲
     *
     * @throws DeviceSendException
     */
    void continueWavePulse() throws DeviceSendException;

    /**
     * 停止脉冲
     *
     * @throws DeviceSendException
     */
    void stopWavePulse() throws DeviceSendException;

    /**
     * 加载恒压
     */
    void loadConstantVoltage();

    /**
     * 加载自定义脉冲波形
     */
    boolean writeCustomizeWavePulse(CustomizedWavePulse customizedWavePulse) throws DeviceSendException, JVisaException;

    /**
     * 加载启动脉冲
     */
    boolean writeStartWavePulse(StartPulse startPulse) throws DeviceSendException, JVisaException;

    /**
     * 加载时间级复位脉冲
     */
    boolean writeTimeLevelResetPulse(TimeLevelResetPulse timeLevelResetPulse) throws DeviceSendException, JVisaException;

    /**
     * 加载电压级复位脉冲
     */
    boolean writeVoltageLevelResetPulse(VoltageLevelResetPulse voltageLevelResetPulse) throws DeviceSendException, JVisaException;

    /**
     * 加载任意电压级复位脉冲
     */
    boolean writeArbitraryVoltageResetPulse(ArbitraryVoltageResetPulse arbitraryVoltageResetPulse) throws DeviceSendException, JVisaException;

    /**
     * 加载电源短暂中断脉冲
     */
    boolean writePowerTemporarilyInterruptPulse(PowerTemporarilyInterruptPulse powerTemporarilyInterruptPulse) throws DeviceSendException, JVisaException;
    /**
     * 电压触发动作执行
     */
    boolean kikusuiTriggerAction(VoltageTrigger voltageTrigger) throws DeviceSendException;


}