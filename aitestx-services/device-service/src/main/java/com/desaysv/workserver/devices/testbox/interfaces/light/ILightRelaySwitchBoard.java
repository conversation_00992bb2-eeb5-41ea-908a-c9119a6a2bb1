package com.desaysv.workserver.devices.testbox.interfaces.light;

import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.exceptions.boardcard.BoardCardTransportException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ILightRelaySwitchBoard {
    Logger log = LogManager.getLogger(ILightRelaySwitchBoard.class.getSimpleName());

    /**
     * 改变继电器状态（6个通道）
     *
     * @param command ON：打开， OFF：关闭
     */
    boolean writeRelaySwitchBoardCard(int chassisNumber, int slotNumber, int channelNumber, String command) throws BoardCardTransportException;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.LightTestBoxRegexRule).SWITCH_ON_OFF"})
    default void writeRelaySwitchBoardCardInfo(Integer deviceChannel, int slotNumber, int channelNumber, String command) throws OperationFailNotification {
        boolean isOpen = command.equalsIgnoreCase("ON");
        log.info("继电器板卡通道{}-{}-{}，设置{}", deviceChannel, slotNumber, channelNumber, isOpen ? "打开" : "关闭");
        try {
            boolean isWriteOk = writeRelaySwitchBoardCard(deviceChannel, slotNumber, channelNumber, command);
            log.info("继电器板卡通道设置:{}", isWriteOk ? "成功" : "失败");
            ActionSequencesLoggerUtil.info(String.format("继电器板卡通道设置:%s", isWriteOk ? "成功" : "失败"));
            if (!isWriteOk) {
                throw new OperationFailNotification("继电器板卡写入和反馈不一致");
            }
        } catch (BoardCardTransportException e) {
            throw new OperationFailNotification("继电器板卡写入失败:" + e.getMessage());
        }
    }

}
