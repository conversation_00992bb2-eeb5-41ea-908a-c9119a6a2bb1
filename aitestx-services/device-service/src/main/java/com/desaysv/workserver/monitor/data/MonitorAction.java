package com.desaysv.workserver.monitor.data;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Data
public class MonitorAction {
    private final static Map<String, MonitorAction> INSTANCES = new HashMap<>();

    //    private String clientAddr;
    private String deviceAliasName;
    private String monitorType;
//    private String command = DataMonitor.START;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MonitorAction that = (MonitorAction) o;
        return deviceAliasName.equals(that.deviceAliasName) && monitorType.equals(that.monitorType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deviceAliasName, monitorType);
    }


    public static MonitorAction of(String deviceAliasName, String monitorType) {
        String key = String.format("%s_%s", deviceAliasName, monitorType);
        if (!INSTANCES.contains<PERSON>ey(key)) {
            MonitorAction monitorAction = new MonitorAction();
            monitorAction.setDeviceAliasName(deviceAliasName);
            monitorAction.setMonitorType(monitorType);
            INSTANCES.put(key, monitorAction);
        }
        return INSTANCES.get(key);
    }

}