package com.desaysv.workserver.devices.bus.e2e.cheryE01;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ICI2 奇瑞E01项目 E2E
 *
 * <AUTHOR>
 */
public class E2eCheryE0XTableManager {

    @Getter
    private static final E2eCheryE0XTableManager instance = new E2eCheryE0XTableManager();
    public Map<Integer, Integer> e2eMap = new ConcurrentHashMap<>();
    public E2eCheryE0XTableManager(){
        e2eMap.clear();
        e2eMap.put(Integer.parseInt("0x49C".substring(2), 16), Integer.parseInt("0x00AD".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x30F".substring(2), 16), Integer.parseInt("0x000F".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x314".substring(2), 16), Integer.parseInt("0x0010".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x31A".substring(2), 16), Integer.parseInt("0x0011".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4C1".substring(2), 16), Integer.parseInt("0x0012".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x50B".substring(2), 16), Integer.parseInt("0x0075".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x50F".substring(2), 16), Integer.parseInt("0x0076".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x264".substring(2), 16), Integer.parseInt("0x002B".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4BE".substring(2), 16), Integer.parseInt("0x0000".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x122".substring(2), 16), Integer.parseInt("0x002E".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x477".substring(2), 16), Integer.parseInt("0x0028".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4EC".substring(2), 16), Integer.parseInt("0x0080".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x49D".substring(2), 16), Integer.parseInt("0x009C".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x52C".substring(2), 16), Integer.parseInt("0x0082".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4E8".substring(2), 16), Integer.parseInt("0x0081".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4EA".substring(2), 16), Integer.parseInt("0x0083".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x498".substring(2), 16), Integer.parseInt("0x0048".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x47E".substring(2), 16), Integer.parseInt("0x009E".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x485".substring(2), 16), Integer.parseInt("0x009D".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4DA".substring(2), 16), Integer.parseInt("0x0000".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x48A".substring(2), 16), Integer.parseInt("0x0056".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4A9".substring(2), 16), Integer.parseInt("0x007A".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x470".substring(2), 16), Integer.parseInt("0x0070".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4B4".substring(2), 16), Integer.parseInt("0x005C".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4B9".substring(2), 16), Integer.parseInt("0x005D".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x476".substring(2), 16), Integer.parseInt("0x00A8".substring(2), 16));
        e2eMap.put(Integer.parseInt("0x4F2".substring(2), 16), Integer.parseInt("0x0000".substring(2), 16));
    }

}
