package com.desaysv.workserver.devices.bus.fdx;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
public class FdxTimeoutUtils {

    public static <T> T executeWithTimeout(Supplier<T> supplier, long timeout, TimeUnit unit, Supplier<Boolean> isResultConsistent) {
        long startMills = System.currentTimeMillis();
        T result = null;
        while (true) {
            try {
                result = supplier.get();
                if (isResultConsistent.get()) {
                    log.info("读取响应事件完毕，结果一致，共耗时:{}ms", System.currentTimeMillis() - startMills);
                    break;
                }
                log.info("--------------------------------");
            } catch (Exception e) {
                // 处理异常，例如重试或记录日志
                log.info("Exception occurred: {}", e.getMessage());
            }
            if ((System.currentTimeMillis() - startMills) > unit.toMillis(timeout)) {
                log.info("读取响应事件超时:{}ms, 结果不一致", System.currentTimeMillis() - startMills);
                break;
            }
        }
        return result;
    }
}