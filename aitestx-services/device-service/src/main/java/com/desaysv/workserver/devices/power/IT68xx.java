package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.IT68Protocol;
import com.desaysv.workserver.devices.power.protocol.IT69Protocol;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * IT68系列电源
 */
@Slf4j
//@JsonIgnoreProperties({"voltage", "current"})
public class IT68xx extends DefaultPower {

    public IT68xx() {
        this(new DeviceOperationParameter());
    }

    public IT68xx(DeviceOperationParameter deviceOperationParameter) {
//        super(deviceOperationParameter);
//        PowerBytesProtocol powerProtocol = new IT68Protocol(this);
//        setPowerProtocol(powerProtocol);

        super(deviceOperationParameter);
        String commProtocol = String.valueOf(deviceOperationParameter.get("protocol"));
        commProtocol = commProtocol == null ? CommProtocol.USB : commProtocol;
        setCommProtocol(commProtocol); //TODO:setCommProtocol尝试合并到setPowerProtocol
        if (CommProtocol.USB.equals(commProtocol)){setPowerProtocol(new IT69Protocol(this));}
        else{setPowerProtocol(new IT68Protocol(this));}

    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT68xx;
    }
}
