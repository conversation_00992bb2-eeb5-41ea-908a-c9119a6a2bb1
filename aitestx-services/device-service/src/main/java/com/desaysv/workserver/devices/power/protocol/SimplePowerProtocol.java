package com.desaysv.workserver.devices.power.protocol;

import com.desaysv.workserver.devices.power.base.PowerDataDecoder;
import com.desaysv.workserver.exceptions.device.DeviceDataDecodeException;

public interface SimplePowerProtocol extends PowerProtocol {


    default int getDataLength() {
        return 0;
    }


    default void decodeData(byte[] data, PowerDataDecoder powerDataDecoder) throws DeviceDataDecodeException {

    }

    default boolean setRemoteMode(boolean isRemote) {
        return true;
    }

    @Override
    boolean setOutput(Integer channel, boolean isOutput);


    default boolean setVoltageMax(Integer channel, float voltage) {
        return true;
    }


    default boolean setVoltage(Integer channel, float voltage) {
        return true;
    }


    default boolean setCurrent(Integer channel, float current) {
        return true;
    }


    default boolean readData() {
        return true;
    }


    default boolean readVoltage(Integer channel) {
        return true;
    }


    default boolean readCurrent(Integer channel) {
        return true;
    }

    @Override
    default boolean fetchOutput() {
        return true;
    }
}
