package com.desaysv.workserver.cicd;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CiBuildInfo {
    @JsonProperty("CTS_path")
    private String ctsPath;
    @JsonProperty("Line_pkg_path")
    private String linePkgPath;
    @JsonProperty("Log_path")
    private String logPath;
    @JsonProperty("Material_pkg_path")
    private String materialPkgPath;
    private String autoTime;
    @JsonProperty("baidu_disk_path")
    private String baiduDiskPath;
    private String bugAtPath;
    private String chipVersion;
    @JsonProperty("comm_matrix_path")
    private String commMatrixPath;
    @JsonProperty("diagnostic_path")
    private String diagnosticPath;
    @JsonProperty("dsv_inner_path")
    private String dsvInnerPath;//放安装包的公司路径
    private String emailList;
    @JsonProperty("gac_version")
    private String gacVersion;
    @JsonProperty("jenkins_job_url")
    private String jenkinsJobUrl;
    private String log;
    @JsonProperty("mcu_key")
    private String mcuKey;
    @JsonProperty("mcu_version")
    private String mcuVersion;
    @JsonProperty("module_ID")
    private String moduleId;
    private String name;//安装包名字
    private String officialReport;
    @JsonProperty("project_ID")
    private String projectId;//用以建文件夹
    @JsonProperty("qnx_version")
    private String qnxVersion;
    @JsonProperty("release_note")
    private String releaseNote;
    @JsonProperty("soc_version")
    private String socVersion;
    @JsonProperty("sw_usage")
    private String swUsage;
    private String testReportPath;
    @JsonProperty("test_notes")
    private String testNotes;
    @JsonProperty("update_name")
    private String updateName;
    @JsonProperty("upgradeMethod_path")
    private String upgradeMethodPath;
    @JsonProperty("upload_list_base")
    private String uploadListBase;
    @JsonProperty("upload_list_file")
    private String uploadListFile;
}
