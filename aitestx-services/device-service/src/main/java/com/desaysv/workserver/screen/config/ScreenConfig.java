package com.desaysv.workserver.screen.config;

import com.desaysv.workserver.config.ProjectConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ScreenConfig extends ProjectConfig {

    private String serialAliasName;

    private DisplayConfig display;

    private TouchPointMatrixConfig touchPointMatrix;

    public ScreenConfig() {
        display = new DisplayConfig();
        touchPointMatrix = new TouchPointMatrixConfig();
    }

}
