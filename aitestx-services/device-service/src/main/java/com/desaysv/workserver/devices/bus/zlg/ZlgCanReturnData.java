package com.desaysv.workserver.devices.bus.zlg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ZlgCanReturnData {

    /**
     * 时间戳
     */
    public long timestamp;

    /**
     * 帧的标识符。
     */
    public int id;

    /**
     * CAN类型 CAN CANFD CANFD加速
     */
    private String eventType;

    /**
     * 流向 TX发送方向 RX接收方向
     */
    private String dir;

    /**
     * DLC
     */
    private int dlc;
    /**
     * 负载数据（CANFD支持最大64字节）
     */
    public byte[] data = new byte[64];
}
