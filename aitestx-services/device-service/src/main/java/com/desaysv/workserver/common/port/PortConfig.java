package com.desaysv.workserver.common.port;

import jssc.SerialPort;
import lombok.Data;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-6 18:28
 * @description :
 * @modified By :
 * @since : 2022-6-6
 */
@Data
public class PortConfig {
    public int baudRate;
    public int dataBit;
    public int stopBit;
    public int parity;

    public PortConfig(int baudRate) {
        this.baudRate = baudRate;
        this.dataBit = SerialPort.DATABITS_8;
        this.stopBit = SerialPort.STOPBITS_1;
        this.parity = SerialPort.PARITY_NONE;
    }
}
