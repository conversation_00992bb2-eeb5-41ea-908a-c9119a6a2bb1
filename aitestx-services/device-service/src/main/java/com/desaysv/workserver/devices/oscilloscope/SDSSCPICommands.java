package com.desaysv.workserver.devices.oscilloscope;

public class SDSSCPICommands {
    // 通用命令适用于查询设备的基本信息和执行基本操作
    public static final String IDN = "*IDN?\n";  //IDN?是用于查询设备的 ID。返回由四个不同的字段组成，提供有关设备商、设备型号、序列号和固件版本的信息。
    public static final String OPC = "*OPC?"; //当示波器完成所有待处理命令后，向输出缓冲区返回 1。*OPC？查询可以在重叠的命令之间使用，以确保一个命令在下一个命令开始之前完成。
    public static final String RESET_INSTRUMENTS = "*RST\n"; //*RST 重置示波器到默认设置。此命令等同于前面板上的 Default 按键。
    public static final String CLEAR_SCREEN = ":CLEar";
    //:SYST:ZST?
    public static final String SYSTEM_STATUS = ":SYST:ZST?"; //查询示波器状态。此命令返回一个 16 位二进制值，其中每个位表示一个特定的状态。
//    public static final String AUTOSET_EXECUTE = ":AUTOSet EXECute\n"; //根据信号的幅度、频率等信息自动设置示波器的触发、垂直、水平参数，快捷地显示信号。不建议在低频事件（<100Hz）中使用 Autoset。

    //Root(:)命令系统命令子系统用于查询示波器的基本信息或执行基本操作。
    public static final String AUTOSET_EXECUTE = ":AUToset\n"; //根据信号的幅度、频率等信息自动设置示波器的触发、垂直、水平参数，快捷地显示信号。不建议在低频事件（<100Hz）中使用 Autoset。

    /**
     * 描述 设置或查询示波器的采集模式。此命令可以提供高速波形捕获速率，以帮助捕
     * 获信号异常。
     * 命令格式 :ACQuire:AMODe <mode>
     * :ACQuire:AMODe?
     * 参数说明 <mode>:= {FAST|SLOW}
     * FAST：快采。快速采集波形
     * SLOW：慢采。慢速采集波形
     * <p>
     * 返回格式 {FAST|SLOW}
     * 示例 设置快采模式：
     * :ACQuire:AMODe FAST
     * ACQ:AMOD FAST
     * 查询当前采集模式：
     * ACQ:AMOD?
     * 返回值：
     * FAST
     */
    public static final String ACQUIRE_AMODE = ":ACQuire:AMODe %s";

    /**
     * 描述 设置清除示波器的扫描并重新开始采集，此设置等价于前面板上的
     * Clear Sweeps 按钮。
     * 命令格式 :ACQuire:CSWeep
     * 示例 清除采集扫描：
     * :ACQuire:CSWeep
     * ACQ:CSW
     */
    public static final String CLEAR_SWEEPS = ":ACQuire:CSWeep";


    /**
     * 描述 设置或查询示波器的模式。
     * 命令格式 :ACQuire:MODE <mode_type>
     * :ACQuire:MODE?
     * 参数说明 <mode_type>:= {YT|XY|ROLL}
     * ⚫ YT：该模式绘制幅度(Y)与时间(T)的关系。
     * ⚫ XY：该模式绘制通道 X 与通道 Y 的关系，通常称为利萨如曲线。
     * ⚫ ROLL：该模式为滚动模式，绘制幅度(Y)与时间(T)的关系，但从显示屏的右
     * 侧开始写入波形。这类似于“带状图”记录，非常适合每秒钟发生几次的慢速事件。
     * 返回格式 {YT|XY|ROLL}
     * 示例 设置示波器的 YT 模式：
     * :ACQuire:MODE YT
     * ACQ:MODE YT
     * 问询示波器的当前模式：
     * ACQ:MODE?
     * 返回值：
     * YT
     */

    public static final String SET_ACQUIRE_MODE = ":ACQuire:MODE %s";


    /**
     * 功能描述：设置或查询测量开关状态。
     * 命令格式：:MEASure<state>
     * :MEASure?
     * 参数说明: <state>:= {ON|OFF}
     * 返回格式: {ON|OFF}
     * 示例: 设置开启测量:
     * :MEASure ON
     * MEAS ON
     * 查询测量开关状态:
     * MEAS?
     * 返回值:ON
     */
    public static final String SET_MEASURE_STATUS = ":MEASure %s\n";
    public static final String FETCH_MEASURE_STATUS = ":MEASure";

    /**
     * 描述 设置或查询测量类型。
     * 命令格式 :MEASure:MODE <type>
     * :MEASure:MODE?
     * 参数说明 <type>:= {SIMPle|ADVanced}
     * ⚫ SIMPle：基本测量。显示指定源的所有测量项的测量值，测量项支持垂直
     * 测量、水平测量和混合测量。
     * ⚫ ADVanced：高级测量。显示不同源、不同测量项的测量值。该模式下也
     * 可显示统计结果、直方图。
     * 返回格式 {SIMPle|ADVanced}
     * 示例 设置测量类型为基本测量：
     * :MEASure:MODE SIMPle
     * MEAS:MODE SIMP
     * 查询测量类型：
     * MEAS:MODE?
     * 返回值：
     * SIMPle
     */
    public static final String SET_MEASURE_MODE = ":MEASure:MODE <type>";

    /**
     * 描述 清除所有的基本测量项。
     * 命令格式 :MEASure:SIMPle:CLEar
     * 示例 清除所有的基本测量项：
     * :MEASure:SIMPle:CLEar
     * MEAS:SIMP:CLE
     * 关联命令 :MEASure:SIMPle:ITEM
     */
    public static final String MEASURE_SIMPLE_CLEAR = ":MEASure:SIMPle:CLEar";

    /**
     * 设置或查询基本测量的源。
     */
    public static final String MEASURE_SIMPLE_SOURCE = ":MEASure:SIMPle:SOURce C%d";


    /**
     * 描述 查询基本测量中指定测量项的值。
     * 命令格式 :MEASure:SIMPle:VALue? <type>
     * 参数说明 <type>:=
     * {PKPK|MAX|MIN|AMPL|TOP|BASE|LEVELX|CMEAN|MEAN|STDEV|VSTD|
     * RMS|CRMS|MEDIAN|CMEDIAN|OVSN|FPRE|OVSP|RPRE|ULOWer|PER|F
     * REQ|TMAX|TMIN|PWID|NWID|DUTY|NDUTY|WID|NBWID|DELAY|TIMEL|R
     * ISE|FALL|RISE20T80|FALL80T20|CCJ|PAREA|NAREA|AREA|ABSAREA|C
     * YCLES|REDGES|FEDGES|EDGES|PPULSES|NPULSES|PACArea|NACAre
     * a|ACArea|ABSACArea|ALL}
     * 注意：
     * ⚫ 相关参数描述见“表 5-1 测量参数表”。
     * ⚫ ALL 查询所有基本测量项的值。
     * <p>
     * 返回格式 浮点型 NR3 格式
     * 示例 查询基本测量的最大值：
     * MEAS:SIMP:VAL? MAX
     * 返回值：
     * 2.000E+00
     * 关联命令 :MEASure:SIMPle:ITEM
     */
    public static final String MEASURE_SIMPLE_VALUE = ":MEASure:SIMPle:VALue? %s\n";


    public static final String SET_STATUS = ":TRIGger:%s\n";


    /**
     * 描述 设置示波器状态为 Stop。此命令等同于前面板上的 Run|Stop 按键的 Stop。
     * 命令格式 :TRIGger:STOP
     * 示例 设置示波器触发状态为 Stop：
     * :TRIGger:STOP
     * TRIG:STOP
     * 关联命令 :TRIGger:RUN
     */

    public static final String STOP = ":TRIGger:STOP\n";


    /**
     * 描述 设置示波器状态为 Run，并保持当前触发模式。
     * 命令格式 :TRIGger:RUN
     * 示例 如果示波器当前触发模式为 SINGle，且已触发一帧为 Stop 状态，设置示波器
     * 状态为 Run 后，将保持为 SINGle 模式重新采集一帧：
     * :TRIGger:RUN
     * TRIG:RUN
     * 关联命令 :TRIGger:STOP
     */

    public static final String RUN = ":TRIGger:RUN\n";

    /**
     * 描述 查询示波器当前触发状态。
     * 命令格式 :TRIGger:STATus?
     * 返回格式 <status>:= {Arm|Ready|Auto|Trig'd|Stop|Roll}
     * 示例 查询示波器当前触发状态：
     * :TRIG:STAT?
     * 返回值：
     * Stop
     */
    public static final String TRIGGER_STATUS = ":TRIGger:STATus?\n";

    /**
     * 描述 设置或查询触发模式。
     * 命令格式 :TRIGger:MODE <mode>
     * :TRIGger:MODE?
     * 参数说明 <mode>:= {SINGle|NORMal|AUTO|FTRIG}
     * ⚫ AUTO：在自动触发模式中，如果超过定时值仍未找到满足触发条件的波形，
     * 示波器将进行强制采集一帧波形数据，在示波器上显示。
     * ⚫ NORMal：只有满足触发条件时才会进行触发和采集；不满足条件时保持上
     * 一次波形显示，等待下一次触发。
     * ⚫ SINGle：当输入的信号满足触发条件时，示波器即进行捕获并将波形稳定
     * 显示在屏幕上。此后，即使再有满足条件的信号，示波器也不予理会。需要
     * 进行再次捕获须重新进行单次设置。
     * ⚫ FTRIG：无论输入的信号是否满足触发条件，强制触发一帧。
     * 返回格式 {SINGle|NORMal|AUTO|FTRIG}
     * 示例 设置示波器触发模式为 SINGle：
     * :TRIGger:MODE SINGle
     * TRIG:MODE SING
     * 查询示波器触发模式：
     * TRIG:MODE?
     * 返回值：
     * SINGle
     */
    public static final String TRIGGER_MODE = ":TRIGger:MODE %s";

    /**
     * 设置或查询指定高级测量项的信源 A。
     */
    public static final String MEASURE_ADVANCED_SOURCE_1 = ":MEASure:ADVanced:P1:SOURce1 C%d\n";

    /**
     * 设置或查询指定高级测量项的信源 B。
     */
    public static final String MEASURE_ADVANCED_SOURCE_2 = ":MEASure:ADVanced:P<n>:SOURce2 C%d\n";

    //关联命令
    /**
     * 功能描述：设置示波器触发源
     * 命令格式：:MEASure:ADVanced:P<n>:TYPE <parameter>
     * 参数说明:<n>:=测量项序号,整型NR1格式,取值范围[1,12]
     * <parameter>:={PKPK|MAX|MIN|AMPL|TOP|BASE|LEVELX|CMEAN MEAN|STDDEVIVSTD
     * RMS|CRMS|MEDIAN|CMEDIAN|OVSN|ULOWer|FPREJOVSPIRIPREIPERIF
     * REQ|TMAX|TMINIPWID[NWID[DUTY|NDUTY|WID|NBWIDELAY TIMELIR
     * ISE|FALL RISE10T90|FALL90T10 CCJPAREA|NAREA|ARIEA|ABSAREAIC
     * YCLESIREDGESIFEDGESIEDGESIPPULSES|NPULSESIPHÁISKEWIFRRI
     * FRF|FFR|FFF|LRR|LRF|LFR|LFF|PACArea NACArea|ACARea|ABSACArea
     * PSLOPE|NSLOPE|TSRTSF|THR|THF|DTIMe 1|DTIMe2|DTIMe3|DTIMe4}
     * 示例
     * :MEASure:ADVanced:P1:TYPE AMPLitude  //设置第一个高级测量项的测量类型为幅值
     * :MEASure:ADVanced:P1:TYPE DUTY  //设置第一个高级测量项的测量类型为占空比
     * :MEASure:ADVanced:P1:TYPE FREQ //设置第一个高级测量项的测量类型为频率
     * :MEASure:ADVanced:P1:TYPE PER //设置第一个高级测量项的测量类型为周期
     */
    public static final String SET_MEASURE_TYPE = ":MEASure:ADVanced:P%d:TYPE %s"; //设置查询指定高级测量项的测量类型。:MEASure:ADVanced:P<n>:TYPE <parameter> <n>:= 测量项序号，整型 NR1 格式，取值范围 [1,12] <parameter>:=AMPL（幅值）/ DUTY（正占空比） / NDUTY（负占空比）/ 正脉宽与周期的比值/FREQ（频率）/ PER（周期）

    /**
     * 功能描述：清除所有的高级测量项。
     */
    public static final String CLEAR_ALL_MEASURE = ":MEASure:ADVanced:CLEar";

    /**
     * 功能描述：查询指定高级测量项的测量值。
     * 命令格式：:MEASure:ADVanced:P<n>:VALue?
     * <n>:=测量项序号,整型NR1格式,取值范围[1,12]
     * 示例：:MEASure:ADVanced:P1:VALue?  //查询第一个高级测量项的测量值。
     * 返回值：浮点型NR3格式，单位根据测量类型而定。
     */

    public static final String FETCH_MEASURE_VALUE = ":MEASure:ADVanced:P%d:VALue?"; //设置查询指定高级测量项的测量类型。:MEASure:ADVanced:P<n>:TYPE <parameter> <n>:= 测量项序号，整型 NR1 格式，取值范围 [1,12] <parameter>:=AMPL（幅值）/ DUTY（正占空比） / NDUTY（负占空比）/ 正脉宽与周期的比值/FREQ（频率）/ PER（周期）


    /**
     * 功能描述：垂直档位
     * 语法：:CHANnel<n>:SCALe <scale>
     * <n>:=通道序号,整型NR1格式
     * <scale>:=垂直档位值,浮点型NR3格式
     * <p>
     * 示例：:CHANnel1:SCALe 5.00E-02 //设置垂直档位为50mV/div
     * 返回值：浮点型NR3格式，
     */
    public static final String VERTICAL_SCALE = ":CHANnel%d:SCALe %s";

    /**
     * 功能描述：垂直档位
     */
    //:TIMebase:SCALe 1.00E-07
    public static final String TIMEBASE_SCALE = ":TIMebase:SCALe %s\n";
    //CHAN1:SCAL 5.00E-02
    public static final String CHAN1_SCALE = "CHAN1:SCAL %s\n";


    // 用于格式化需要特定值的命令
    public static String formatCommand(String command, Object... values) {
        return String.format(command, values);
    }

}
