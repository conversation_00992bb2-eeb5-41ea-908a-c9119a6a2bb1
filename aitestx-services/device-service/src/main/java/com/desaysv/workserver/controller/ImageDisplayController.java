package com.desaysv.workserver.controller;

import com.desaysv.workserver.model.DateFolderInfo;
import com.desaysv.workserver.model.TemplateImageInfo;
import com.desaysv.workserver.service.ImageDisplayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图像展示控制器
 * 提供获取图片文件夹和图片信息的API
 */
@Slf4j
@Controller
@RequestMapping("/api/images")
public class ImageDisplayController {

    private static final String BASE_PATH = "D:\\FlyTest\\data\\server\\system\\images\\";

    @Autowired
    private ImageDisplayService imageDisplayService;

    /**
     * 获取图片的Base64编码数据
     *
     * @param path 图片路径
     * @return Base64编码的图片数据
     */
    @GetMapping("/base64-image")
    public ResponseEntity<Map<String, Object>> getImageAsBase64(@RequestParam String path) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("接收Base64图片请求路径: {}", path);

            // 验证路径是否包含标准命名的图片文件
            if (!path.contains("template.jpg") && !path.contains("origin.jpg")) {
                log.warn("请求的Base64图片路径不包含标准文件名 (template.jpg 或 origin.jpg): {}", path);
            }

            // 检查是否已经包含完整路径
            Path imagePath;
            if (path.startsWith(BASE_PATH)) {
                // 已经是完整路径
                imagePath = Paths.get(path);
            } else {
                // 需要拼接基础路径
                imagePath = Paths.get(BASE_PATH, path);
            }

            log.info("尝试加载图片: {}", imagePath.toString());

            if (Files.exists(imagePath)) {
                // 读取图片文件到字节数组
                byte[] imageData = Files.readAllBytes(imagePath);

                // 转换为Base64
                String base64Data = Base64.getEncoder().encodeToString(imageData);

                response.put("success", true);
                response.put("base64Data", base64Data);
                response.put("fileName", imagePath.getFileName().toString());
                try {
                    response.put("contentType", Files.probeContentType(imagePath));
                } catch (Exception e) {
                    response.put("contentType", "image/jpeg");
                }

                log.info("Base64图片加载成功: {}, 大小: {} 字节", imagePath, imageData.length);
                return ResponseEntity.ok(response);
            } else {
                log.error("Base64图片文件不存在: {}", imagePath);
                response.put("success", false);
                response.put("message", "文件不存在: " + imagePath);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            log.error("获取Base64图片数据失败: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "获取图片数据失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取所有日期文件夹
     *
     * @return 日期文件夹列表
     */
    @GetMapping("/date-folders")
    @ResponseBody
    public ResponseEntity<List<DateFolderInfo>> getDateFolders() {
        try {
            List<DateFolderInfo> folders = imageDisplayService.getAllDateFolders();
            return ResponseEntity.ok(folders);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取特定日期文件夹下的模板图片信息
     *
     * @param folderName 日期文件夹名称
     * @return 模板图片信息列表
     */
    @GetMapping("/template-images")
    @ResponseBody
    public ResponseEntity<List<TemplateImageInfo>> getTemplateImages(@RequestParam String folderName) {
        try {
            List<TemplateImageInfo> images = imageDisplayService.getTemplateImages(folderName);
            return ResponseEntity.ok(images);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取图片内容
     * 使用FileSystemResource直接返回文件，避免字节处理问题
     *
     * @param path 图片相对路径
     * @return 图片资源
     */
    @GetMapping("/image-content")
    @ResponseBody
    public ResponseEntity<?> getImageContent(@RequestParam String path) {
        try {
            log.info("接收到图片请求路径: {}", path);

            // 解码路径
            String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
            log.info("解码后的路径: {}", decodedPath);

            // 构建文件路径
            File imageFile = new File(BASE_PATH, decodedPath);
            Path imagePath = imageFile.toPath();

            // 检查文件是否存在
            if (!imageFile.exists() || !imageFile.canRead()) {
                log.error("图片文件不存在或无法读取: {}", imageFile.getAbsolutePath());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("图片不存在或无法读取");
            }

            log.info("文件存在，准备返回: {}, 大小: {} 字节", imageFile.getAbsolutePath(), imageFile.length());

            // 设置正确的内容类型
            MediaType mediaType;
            try {
                // 尝试检测文件的实际MIME类型
                mediaType = MediaType.parseMediaType(Files.probeContentType(imagePath));
            } catch (Exception e) {
                // 如果无法检测，默认为JPEG
                mediaType = MediaType.IMAGE_JPEG;
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(mediaType);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            // 方法1：直接返回文件系统资源
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new FileSystemResource(imageFile));
        } catch (Exception e) {
            log.error("获取图片时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("获取图片时发生错误: " + e.getMessage());
        }
    }

    /**
     * 备用方法，通过直接读取文件内容返回图片
     *
     * @param path 图片相对路径
     * @return 图片内容
     */
    @GetMapping("/direct-image")
    @ResponseBody
    public ResponseEntity<byte[]> getDirectImage(@RequestParam String path) {
        try {
            // 解码路径
            String decodedPath = URLDecoder.decode(path, StandardCharsets.UTF_8.name());
            log.info("接收图片请求: {}", decodedPath);

            // 验证路径是否包含标准命名的图片文件
            if (!decodedPath.contains("template.jpg") && !decodedPath.contains("origin.jpg")) {
                log.warn("请求的图片路径不包含标准文件名 (template.jpg 或 origin.jpg): {}", decodedPath);
            }

            // 构建文件路径
            Path imagePath = Paths.get(BASE_PATH, decodedPath);
            File imageFile = imagePath.toFile();

            // 检查文件是否存在
            if (!imageFile.exists() || !imageFile.canRead()) {
                log.error("Direct Image: 图片文件不存在或无法读取: {}", imageFile.getAbsolutePath());
                return ResponseEntity.notFound().build();
            }

            // 读取文件内容
            byte[] imageBytes = Files.readAllBytes(imagePath);
            log.info("Direct Image: 成功读取图片，大小: {} 字节", imageBytes.length);

            // 设置正确的内容类型
            MediaType mediaType;
            try {
                mediaType = MediaType.parseMediaType(Files.probeContentType(imagePath));
            } catch (Exception e) {
                mediaType = MediaType.IMAGE_JPEG;
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(mediaType);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);
            headers.setContentLength(imageBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(imageBytes);
        } catch (Exception e) {
            log.error("Direct Image: 获取图片时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 打开图像展示页面
     * 重定向到图像展示HTML页面
     *
     * @return 重定向到图像展示页面
     */
    @GetMapping("/viewer")
    public String viewImageDisplay() {
        return "redirect:/image-display.html";
    }

    /**
     * 主路径访问图像展示
     *
     * @return 重定向到图像展示页面
     */
    @GetMapping("")
    public String defaultView() {
        return "redirect:/image-display.html";
    }
}
