package com.desaysv.workserver.config.can;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Data
public class CanConfigParameter implements Serializable {
    private int channel = 1; //通道1开始
    private String protocol;//协议
    private boolean canFdStandard;//CANFD标准
    private boolean accelerate;//CANFD加速 "是"/"否"
    private String arbitrationBps;//仲裁域波特率
    private String dataBps;//数据域波特率
    private String userDefinedBaudrate;//自定义波特率
    private boolean normalWorkMode;//工作模式 "只听模式"/"正常模式"
    private boolean terminalResistanceEnabled;//终端电阻 "使能"/"禁能"
    private boolean canBusUtilizationRateEnabled;//上报总线利用率 "使能"/"禁能"
    private int canBusUtilizationRateCycle;//总线利用率周期(ms)
    private String sendRetry;//发送重试
    private boolean canFd; //CANFD使能

    public int getDataBpsValue() {
        return getBps(dataBps, 2 * 1000 * 1000);
    }

    public int getArbitrationBpsValue() {
        return getBps(arbitrationBps, 500 * 1000);
    }

    private int getBps(String input, int defaultValue) {
        if (input == null) {
            return defaultValue;
        }
        String pattern = "^(\\w+?)bps";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(input);

        if (matcher.find()) {
            String result = matcher.group(1);
            if (result.endsWith("M")) {
                return Integer.parseInt(result.replace("M", "")) * 1000 * 1000;
            } else if (result.endsWith("k")) {
                return Integer.parseInt(result.replace("k", "")) * 1000;
            } else {
                return Integer.parseInt(result);
            }
        }
        return defaultValue;
    }
}

