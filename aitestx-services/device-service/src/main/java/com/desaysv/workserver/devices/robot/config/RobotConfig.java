package com.desaysv.workserver.devices.robot.config;

import com.desaysv.workserver.config.DeviceConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机械臂配置
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RobotConfig extends DeviceConfig {

    private ScaledMonitorAreaConfig monitorArea = new ScaledMonitorAreaConfig();

    private RobotParametersConfig parameters = new RobotParametersConfig();

    private String calibrationCameraAliasName;

    private boolean enableSlideRail; //是否启用滑轨
}