package com.desaysv.workserver.devices.camera.config;

import lombok.Builder;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/5/12 13:49
 */
@Data
@Builder
public class TakePhotoConfig {
    private boolean takePhoto;
    private int type;
    private String savePath;

    public TakePhotoConfig() {
        this.takePhoto = false;
        this.type = 0;
        this.savePath = "";
    }

    public TakePhotoConfig(boolean takePhoto, int type, String savePath) {
        this.takePhoto = takePhoto;
        this.type = type;
        this.savePath = savePath;
    }
}
