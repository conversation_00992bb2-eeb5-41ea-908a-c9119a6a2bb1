package com.desaysv.workserver.devices.resistance;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceParams;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: QinHao
 * @description: 程控电阻仪设备
 * @date: 2024/7/30 14:35
 */
@Slf4j
public class RemoteResistanceDevice extends Device {
    public static final List<String> IP_ADDRESS = new ArrayList<>(Collections.singletonList(DeviceParams.defaultRemoteResistancePort));

    public static final String MSG_HEAD = "01100005000204";
    public static final String MSG_GET = "0103 0005 0002 D40A ";

    public RemoteResistanceDevice() {
    }

    public RemoteResistanceDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_RESISTANCE;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Resistance.REMOTE_RESISTANCE;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        return true;
    }

    public static String generateSendStr(float floatValue) {
        String formattedHexString = String.format("%08X", Float.floatToIntBits(floatValue));
        String str = MSG_HEAD + formattedHexString;
        int[] arr = new int[str.length() / 2];
        for (int i = 0; i < str.length(); i += 2) {
            arr[i / 2] = Integer.parseInt(str.substring(i, i + 2), 16);
        }
        int CRC = calculateCRC(arr);
        String CRCStr = String.format("%02X%02X", CRC & 0xFF, (CRC >> 8) & 0xFF);
        String result = str + CRCStr;
        result = result.replaceAll("(.{4})(?!$)", "$1 ");
        return result;
    }

    private static int calculateCRC(int[] data) {
        int CRC = 0xFFFF;
        for (int datum : data) {
            CRC ^= datum;
            for (int j = 0; j < 8; j++) {
                if ((CRC & 0x0001) != 0) {
                    CRC = (CRC >> 1) ^ 0xA001;
                } else {
                    CRC >>= 1;
                }
            }
        }
        return CRC;
    }

    public static float convertToDecimal(String hexString) {
        // 将16进制字符串转换为32位整数
        int intBits = (int) Long.parseLong(hexString, 16);
        // 使用 Float.intBitsToFloat 方法将整数转换为浮点数
        return Float.intBitsToFloat(intBits);
    }

    public OperationResult resistanceSet(ResistanceData resistanceData) {
        return executeResistanceOperation(resistanceData, generateSendStr(resistanceData.getResistance()), "05000251");
    }

    public OperationResult resistanceGet(ResistanceData resistanceData) {
        return executeResistanceOperation(resistanceData, MSG_GET, null);
    }

    private OperationResult executeResistanceOperation(ResistanceData resistanceData, String message, String expectedResponse) {
        OperationResult operationResult = new OperationResult();
        ResistanceConnect resistanceConnect = new ResistanceConnect();
        try {
            resistanceConnect.connect(resistanceData.getDeviceIP(), resistanceData.getChannel(), 2000, 2000);
            resistanceConnect.sendMessage(message);
            String response = resistanceConnect.receiveMessage();
            if (response != null) {
                if (expectedResponse != null) {
                    response = response.substring(6, 14);
                    if (expectedResponse.equals(response)) {
                        operationResult.ok();
                    }
                } else {
                    operationResult.ok();
                    response = response.substring(6, 14);
                    response = String.valueOf(convertToDecimal(response));
                    System.out.println(response);
                    operationResult.setData(response);
                }
            }
        } catch (SocketTimeoutException e) {
            operationResult.fail();
            operationResult.setMessage("连接超时");
            log.info("连接超时");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            resistanceConnect.disconnect();
        }
        return operationResult;
    }

}
