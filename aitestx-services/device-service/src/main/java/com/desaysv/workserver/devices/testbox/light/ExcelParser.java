package com.desaysv.workserver.devices.testbox.light;

import com.alibaba.excel.EasyExcel;

import java.io.InputStream;
import java.util.Map;

public class ExcelParser {
    public interface KeyGenerator {
        String generate(TestBoxDeviceConfig config);
    }

    public static Map<String, TestBoxDeviceConfig> parse(InputStream inputStream,
                                                         KeyGenerator keyGenerator) {
        DeviceConfigListener listener = new DeviceConfigListener(
                (keyGenerator != null) ? (com.desaysv.workserver.devices.testbox.light.KeyGenerator) keyGenerator :
                        config -> config.getSerialKey() + "_" + config.getParamKey()
        );
        EasyExcel.read(inputStream, TestBoxDeviceConfig.class, listener)
                .sheet()
                .doRead();
        return listener.getResult();
    }
}