package com.desaysv.workserver.devices.speaker;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.File;

/**
 * 扬声器消息封装类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpeakerMessage {

    /**
     * 要播报的文本内容
     */
    private String text;

    /**
     * 要播放的音频文件
     */
    private String audioFilePath;


    public File getAudioFile() {
        if (!StringUtils.isEmpty(audioFilePath)) {
            return new File(audioFilePath);
        }
        return null;
    }

    /**
     * 创建一个只包含文本的消息
     *
     * @param text 要播报的文本
     * @return SpeakerMessage实例
     */
    public static SpeakerMessage withText(String text) {
        SpeakerMessage message = new SpeakerMessage();
        message.setText(text);
        return message;
    }

    public static SpeakerMessage withAudioFile(String audioFilePath) {
        SpeakerMessage message = new SpeakerMessage();
        message.setAudioFilePath(audioFilePath);
        return message;
    }

}