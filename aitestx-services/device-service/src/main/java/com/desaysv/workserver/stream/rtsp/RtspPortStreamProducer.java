package com.desaysv.workserver.stream.rtsp;


import com.desaysv.workserver.stream.PortStreamProducerAdapter;
import com.desaysv.workserver.stream.IStreamRecorder;
import com.desaysv.workserver.entity.ImageBuffer;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.*;

import javax.imageio.ImageIO;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * RTSP视频流生产者
 */
@Slf4j
//@Component
//@Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
public class RtspPortStreamProducer extends PortStreamProducerAdapter implements IStreamRecorder {

    //    @Autowired
    private final static RtspServer rtspServer = new RtspServer();
    private final ExecutorService imageExecutor = Executors.newSingleThreadExecutor();
    //推流
    private FrameRecorder recorder;

    public RtspPortStreamProducer(int w, int h) {
        super(w, h);
    }

    @Override
    public synchronized void close() throws Exception {
        super.close();
        rtspServer.close();
        closeRecorder();
    }

    @Override
    public void stopRecorder() throws FrameRecorder.Exception {
        if (recorder != null) {
            log.info("停止推流");
            recorder.stop();
        }
    }

    @Override
    public void closeRecorder() throws FrameRecorder.Exception {
        if (recorder != null) {
            log.info("关闭推流");
            recorder.close();
            recorder = null;
        }
    }

    @Override
    protected void startStream(String deviceModel, int devicePort) throws Exception {
        if (!rtspServer.isAlive()) {
            rtspServer.start();
            Thread.sleep(1500);
            log.info("RTSP服务器已启动");
        }
        setUrl(String.format("rtsp://localhost:8554/camera/%s/%s", deviceModel, devicePort));
        log.info("创建推流:{}", getUrl());
        //TODO: 后续考虑开放网络视频流功能
        startGrabber(deviceModel);
        startRecorder();
    }

    @Override
    public void startRecorder() throws FrameRecorder.Exception {
        recorder = FFmpegFrameRecorder.createDefault(getUrl(), getWidth(), getHeight());
        recorder.setOption("rtsp_transport", "tcp");
        // 设置缓存大小，提高画质、减少卡顿花屏
        recorder.setOption("buffer_size", "2048000");
        // 降低编码延时
        recorder.setVideoOption("tune", "zerolatency");
        // 提升编码速度
        recorder.setVideoOption("preset", "ultrafast");
        // 视频质量参数
        recorder.setVideoOption("crf", "20");
        recorder.setOption("threads", "4"); // 设置线程数
        // 视频编码格式
        recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
        recorder.setPixelFormat(getGrabber().getPixelFormat());
        // 视频格式
        recorder.setFormat("rtsp");
        // 视频帧率(保证视频质量的情况下最低25，低于25会出现闪屏)
        recorder.setVideoBitrate(8000000);
        recorder.setFrameRate(30);
        recorder.setGopSize(60);
        // recorder.setOption("vcodec", "libx264");
        // recorder.setFrameRate(grabber.getFrameRate() == 0 ? 60 : grabber.getFrameRate());
        // log.info("grabber->FrameRate:{}", grabber.getFrameRate());
        // recorder.setSampleRate(grabber.getSampleRate());
        // log.info("grabber->SampleRate:{}", grabber.getSampleRate());
        // 设置视频比特率,单位:b
        // recorder.setVideoBitrate(grabber.getVideoBitrate() == 0 ? 20000000 : grabber.getVideoBitrate());
        // log.info("grabber->VideoBitrate:{}", grabber.getVideoBitrate());
        // recorder.setAspectRatio(grabber.getAspectRatio());
        // log.info("grabber->AspectRatio:{}", grabber.getAspectRatio());
        // recorder.setVideoCodec(avcodec.AV_CODEC_ID_MPEG4);
        // 设置像素格式，yuv420p,像素
        // recorder.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
        // 设置视频质量
        // recorder.setVideoQuality(avutil.FF_LAMBDA_SHIFT);
        // 音频预留
        //  recorder.setAudioOption("crf", "0");
        //  recorder.setAudioQuality(0);
        //  recorder.setAudioBitrate(grabber.getAudioBitrate());
        //  recorder.setAudioOptions(grabber.getAudioOptions());
        //  recorder.setSampleRate(44100);
        //  建议从grabber获取AudioChannels
        //  recorder.setAudioChannels(1);
        //  recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
        recorder.start();
        log.info("开始推流");
    }

    @Override
    protected void distributeStream(ImageBuffer imageBuffer) {
        imageExecutor.submit(() -> {
            try {
                recorder.record(imageBuffer.getFrame());
            } catch (FrameRecorder.Exception e) {
                log.error(e.getMessage(), e);
                try {
                    log.info("恢复视频流录制...");
                    stopRecorder();
//                        recorder = FFmpegFrameRecorder.createDefault(getUrl(), getWidth(), getHeight());
                    startRecorder();
                } catch (FrameRecorder.Exception ex) {
                    handleExceptionAndClose(e);
                }
            }
        });
    }

    public static void main(String[] args) {
//        VideoCapture videoCapture = new VideoCapture(0);
//        try {
//            videoCapture.open(0);
//            Mat frame = new Mat();    //每一帧图片，cv的拍照是读取一个frame，录制视频则要循环获取frame
//            if (videoCapture.isOpened()) {
//                videoCapture.set(Videoio.CAP_PROP_FRAME_WIDTH, 1624);
//                videoCapture.set(Videoio.CAP_PROP_FRAME_HEIGHT, 1234);
//                videoCapture.read(frame);
//                ImageIO.write(new Java2DFrameConverter().convert(new OpenCVFrameConverter.ToMat().convert(frame)), "png", new File("D:\\uidp4666\\Desktop\\AiX\\test.png"));
//            }else{
//                System.out.println("no open");
//            }
        FrameGrabber grabber = new OpenCVFrameGrabber(0);
//            FFmpegLogCallback.set();
        try {
            grabber.setImageWidth(1624);
            grabber.setImageHeight(1234);
            grabber.setOption("threads", "4"); // 设置线程数
//            grabber.setOption(String.valueOf(Videoio.CAP_PROP_FRAME_WIDTH), "1624");
//            grabber.setOption(String.valueOf(Videoio.CAP_PROP_FRAME_HEIGHT), "1234");
            grabber.start();
            ImageIO.write(new Java2DFrameConverter().convert(grabber.grabFrame()), "png", new File("D:\\uidp4666\\Desktop\\AiX\\test.png"));
            log.info("相机分辨率:{}x{}", grabber.getImageWidth(), grabber.getImageHeight());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

}
