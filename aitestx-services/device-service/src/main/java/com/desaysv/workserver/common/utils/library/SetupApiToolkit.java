package com.desaysv.workserver.common.utils.library;

import com.sun.jna.Native;
import com.sun.jna.platform.win32.Guid;
import com.sun.jna.platform.win32.SetupApi;
import com.sun.jna.platform.win32.WinNT;
import com.sun.jna.win32.W32APIOptions;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.sun.jna.platform.win32.SetupApi.*;
import static com.sun.jna.platform.win32.WinError.ERROR_NO_MORE_ITEMS;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-9 11:31
 * @description :
 * @modified By :
 * @since : 2022-6-9
 */
public class SetupApiToolkit {

    public static final int SPDRP_DEVICEDESC = 0x00000000;
    public static final int SPDRP_HARDWAREID = 0x00000001;
    public static final int SPDRP_COMPATIBLEIDS = 0x00000002;
    public static final int SPDRP_UNUSED0 = 0x00000003;
    public static final int SPDRP_SERVICE = 0x00000004;
    public static final int SPDRP_UNUSED1 = 0x00000005;
    public static final int SPDRP_UNUSED2 = 0x00000006;
    public static final int SPDRP_CLASS = 0x00000007;
    public static final int SPDRP_CLASSGUID = 0x00000008;
    public static final int SPDRP_DRIVER = 0x00000009;
    public static final int SPDRP_CONFIGFLAGS = 0x0000000A;
    public static final int SPDRP_MFG = 0x0000000B;
    public static final int SPDRP_FRIENDLYNAME = 0x0000000C;
    public static final int SPDRP_LOCATION_INFORMATION = 0x0000000D;
    public static final int SPDRP_PHYSICAL_DEVICE_OBJECT_NAME = 0x0000000E;
    public static final int SPDRP_CAPABILITIES = 0x0000000F;
    public static final int SPDRP_UI_NUMBER = 0x00000010;
    public static final int SPDRP_UPPERFILTERS = 0x00000011;
    public static final int SPDRP_LOWERFILTERS = 0x00000012;
    public static final int SPDRP_BUSTYPEGUID = 0x00000013;
    public static final int SPDRP_LEGACYBUSTYPE = 0x00000014;
    public static final int SPDRP_BUSNUMBER = 0x00000015;
    public static final int SPDRP_ENUMERATOR_NAME = 0x00000016;
    public static final int SPDRP_SECURITY = 0x00000017;
    public static final int SPDRP_SECURITY_SDS = 0x00000018;
    public static final int SPDRP_DEVTYPE = 0x00000019;
    public static final int SPDRP_EXCLUSIVE = 0x0000001A;
    public static final int SPDRP_CHARACTERISTICS = 0x0000001B;
    public static final int SPDRP_ADDRESS = 0x0000001C;
    public static final int SPDRP_UI_NUMBER_DESC_FORMAT = 0X0000001D;
    public static final int SPDRP_DEVICE_POWER_DATA = 0x0000001E;
    public static final int SPDRP_REMOVAL_POLICY = 0x0000001F;
    public static final int SPDRP_REMOVAL_POLICY_HW_DEFAULT = 0x00000020;
    public static final int SPDRP_REMOVAL_POLICY_OVERRIDE = 0x00000021;
    public static final int SPDRP_INSTALL_STATE = 0x00000022;
    public static final int SPDRP_LOCATION_PATHS = 0x00000023;

    private interface EnhancedSetupApi extends SetupApi {
        EnhancedSetupApi ENHANCED_INSTANCE = Native.load("setupapi", EnhancedSetupApi.class, W32APIOptions.DEFAULT_OPTIONS);

        boolean SetupDiGetDeviceInstanceId(WinNT.HANDLE DeviceInfoSet, SP_DEVINFO_DATA DeviceInfoData,
                                           char[] DeviceInstanceId, int DeviceInstanceIdSize, int[] RequiredSize);

        boolean SetupDiGetDeviceRegistryProperty(WinNT.HANDLE DeviceInfoSet, SP_DEVINFO_DATA DeviceInfoData, int Property,
                                                 int[] PropertyRegDataType, char[] PropertyBuffer, int PropertyBufferSize, int[] RequiredSize);
    }

    private static String charArrayToString(char[] charArray) {
        StringBuilder name = new StringBuilder();
        for (char c : charArray) {
            if (c != 0)
                name.append(c);
        }
        return name.toString();
    }

    @Data
    public static class WindowsDevice {
        private String instanceId;
        private String friendName;

        public WindowsDevice(String instanceId, String friendName) {
            this.instanceId = instanceId;
            this.friendName = friendName;
        }
    }

    public static Optional<WindowsDevice> getDeviceByName(String friendName) {
        return getAllDevice().stream().filter(device -> device.getFriendName().contains(friendName)).findFirst();
    }


    public static List<WindowsDevice> getAllDevice() {
        List<WindowsDevice> deviceList = new ArrayList<>();
        WinNT.HANDLE deviceInfoSet = EnhancedSetupApi.INSTANCE.SetupDiGetClassDevs(null, null, null,
                DIGCF_ALLCLASSES | DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
        int memberIndex = 0;
        int maxSize = 256;

        while (true) {
            char[] deviceIdChars = new char[maxSize];
            SP_DEVINFO_DATA deviceInfoData = new SP_DEVINFO_DATA();
            deviceInfoData.cbSize = deviceInfoData.size();
            if (!EnhancedSetupApi.INSTANCE.SetupDiEnumDeviceInfo(deviceInfoSet, memberIndex, deviceInfoData)) {
                if (Native.getLastError() == ERROR_NO_MORE_ITEMS) {
                    break;
                }
            }
            boolean result1 = EnhancedSetupApi.ENHANCED_INSTANCE.SetupDiGetDeviceInstanceId(deviceInfoSet, deviceInfoData, deviceIdChars, deviceIdChars.length, null);
            if (!result1) {
                continue;
            }
            memberIndex++;

            char[] propertyBuffer = new char[maxSize];
            boolean result2 = EnhancedSetupApi.ENHANCED_INSTANCE.SetupDiGetDeviceRegistryProperty(deviceInfoSet, deviceInfoData, SPDRP_FRIENDLYNAME, null, propertyBuffer, propertyBuffer.length, null);
            if (!result2) {
                continue;
            }
            String instanceId = charArrayToString(deviceIdChars);
            String friendName = charArrayToString(propertyBuffer);
            deviceList.add(new WindowsDevice(instanceId, friendName));
        }
        if (deviceInfoSet != null) {
            EnhancedSetupApi.INSTANCE.SetupDiDestroyDeviceInfoList(deviceInfoSet);
        }

        return deviceList;

    }


    public static List<String> getDeviceId(String classGuid) {
        List<String> deviceList = new ArrayList<>();
        Guid.GUID guid = new Guid.GUID(classGuid);

        WinNT.HANDLE deviceInfoSet = EnhancedSetupApi.INSTANCE.SetupDiGetClassDevs(guid, null, null, DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
        SP_DEVINFO_DATA deviceInfoData = new SP_DEVINFO_DATA();
        deviceInfoData.cbSize = deviceInfoData.size();

        int maxSize = 256;
        char[] deviceIdChars = new char[maxSize];
        for (int i = 0; i < maxSize; i++) {
            EnhancedSetupApi.INSTANCE.SetupDiEnumDeviceInfo(deviceInfoSet, i, deviceInfoData);
            if (Native.getLastError() == ERROR_NO_MORE_ITEMS) {
                break;
            }
            boolean result = EnhancedSetupApi.ENHANCED_INSTANCE.SetupDiGetDeviceInstanceId(deviceInfoSet, deviceInfoData, deviceIdChars, deviceIdChars.length, null);
            if (result) {
                deviceList.add(charArrayToString(deviceIdChars));
            }
        }
        if (deviceInfoSet != null) {
            EnhancedSetupApi.INSTANCE.SetupDiDestroyDeviceInfoList(deviceInfoSet);
        }

        return deviceList;
    }

    public static void main(String[] args) {
        String guid = "4d36e978-e325-11ce-bfc1-08002be10318";
        System.out.println(SetupApiToolkit.getDeviceByName("COM16"));
    }
}
