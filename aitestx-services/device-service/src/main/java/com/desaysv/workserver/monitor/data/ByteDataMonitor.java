package com.desaysv.workserver.monitor.data;

import com.desaysv.workserver.exceptions.device.DeviceReadException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-30 17:06
 * @description :
 * @modified By :
 * @since : 2022-6-30
 */
public interface ByteDataMonitor extends DataMonitor {

    void monitorByteData(MonitorAction monitorAction, MonitorListener<byte[]> monitorListener) throws DeviceReadException;

}
