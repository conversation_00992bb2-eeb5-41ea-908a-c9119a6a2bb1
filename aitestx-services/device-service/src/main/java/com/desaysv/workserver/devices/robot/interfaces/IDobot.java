package com.desaysv.workserver.devices.robot.interfaces;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.devices.robot.base.CoordinateSystem;
import com.desaysv.workserver.devices.robot.base.DisplayTouchPoint;
import com.desaysv.workserver.devices.robot.base.RobotPose;
import com.desaysv.workserver.devices.robot.type.mg400.entity.ArcEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.MoveEntity;
import com.desaysv.workserver.devices.robot.type.mg400.entity.RobotReply;
import com.desaysv.workserver.model.SpeedCoordinates;
import com.desaysv.workserver.utils.sse.SseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Dobot机械臂接口
 */
public interface IDobot {

    Logger log = LoggerFactory.getLogger(IDobot.class.getSimpleName());

    int AUTO_UP_HEIGHT = 15; //TODO: 可配置
    String ROBOT_TOUCH_COORDINATE_SUBSCRIBE_ID = "robotTouchCoordinate";
    String ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID = "robotSwipeCoordinate";

    /**
     * 点动
     *
     * @param command 点动命令（X+,X-,Y+,Y-,Z+,Z-...)
     * @return RobotReply
     */
    RobotReply moveJog(String command);

    /**
     * 停止点动
     *
     * @return RobotReply
     */
    RobotReply stopMoveJog();


    /**
     * 滑动
     *
     * @param moveEntityList 滑动坐标列表
     * @return RobotReply
     */
    RobotReply swipe(List<MoveEntity> moveEntityList);


    /**
     * 滑动
     *
     * @param moveEntityList 滑动坐标列表
     * @param autoUp         是否自动抬起
     * @return RobotReply
     */
    RobotReply swipe(List<MoveEntity> moveEntityList, boolean autoUp);

    /**
     * 移动
     *
     * @param moveEntityList 移动坐标列表
     * @return RobotReply
     */
    RobotReply move(List<MoveEntity> moveEntityList);

    /**
     * 滑动两点
     *
     * @param startPoint        起点
     * @param endPoint          终点
     * @param swipeDurationCalc 是否计算滑动时间
     * @return RobotReply
     */
    default RobotReply swipeTwoPoint(MoveEntity startPoint, MoveEntity endPoint, boolean swipeDurationCalc) {
        //移动到第一个点
        log.info("准备移动到滑动起点:{}", startPoint);
        RobotReply robotReply = touch(startPoint, false);
        RobotReply robotReplyWithMotionDuration = new RobotReply();
        if (robotReply.isOk()) {
            //滑动到第二个点
            log.info("滑动到终点:{}", endPoint);
            robotReplyWithMotionDuration = moveLine(endPoint, swipeDurationCalc);
            if (robotReply.isOk()) {
                endPoint.setZ(endPoint.getZ() + AUTO_UP_HEIGHT);
                log.info("移动到滑动终点上方:{}", endPoint);
                robotReply = moveLine(endPoint);
            }
        }
        robotReplyWithMotionDuration.setOk(robotReply.isOk());
        robotReplyWithMotionDuration.setMessage(robotReply.getMessage());
        robotReplyWithMotionDuration.setReplyDuration(robotReply.getReplyDuration());
        return robotReplyWithMotionDuration;
    }

    default void outputLog(String text, boolean verbose) {
        if (verbose) {
            log.info(text);
        } else {
            log.debug(text);
        }
    }

    default RobotReply smartTouch(MoveEntity moveEntity, boolean autoUp, boolean verbose) {
        return smartTouch(moveEntity, 0, autoUp, verbose);
    }

    /**
     * 智能点击
     *
     * @param moveEntity            坐标
     * @param longPressMilliSeconds 长按时间
     * @param autoUp                是否自动抬高
     * @param verbose               是否显示log
     * @return RobotReply
     */
    default RobotReply smartTouch(MoveEntity moveEntity, float longPressMilliSeconds, boolean autoUp, boolean verbose) {
        MoveEntity upPoint = (MoveEntity) moveEntity.clone();
        upPoint.setZ(moveEntity.getZ() + AUTO_UP_HEIGHT);
        RobotReply robotReply;

        //移动到上方
        outputLog(String.format("准备移动到上方坐标:%s", upPoint), verbose);
        robotReply = move(upPoint);
        if (robotReply.isOk()) {
            //移动到目标点
            outputLog(String.format("准备移动到坐标位置:%s", moveEntity), verbose);
            robotReply = move(moveEntity);
            if (longPressMilliSeconds > 0) {
                try {
                    outputLog(String.format("长按保持%fms", longPressMilliSeconds), verbose);
                    TimeUnit.MILLISECONDS.sleep((long) longPressMilliSeconds);
                } catch (InterruptedException e) {
                    log.warn(e.getMessage(), e);
                }
            }
        }
        if (robotReply.isOk() && autoUp) {
            //移动到上方
            outputLog(String.format("准备移动到上方坐标:%s", upPoint), verbose);
            robotReply = move(upPoint);
            //下面是sse发送点击坐标数据暂时注释掉
            //sendPointMsg(ROBOT_TOUCH_COORDINATE_SUBSCRIBE_ID,moveEntity);
        }
        return robotReply;
    }

    /**
     * 智能滑动（自动抬起）
     *
     * @param moveEntityList 滑动坐标列表
     * @return RobotReply
     */
    default RobotReply smartSwipe(List<MoveEntity> moveEntityList) {
        return smartSwipe(moveEntityList, true);
    }

    /**
     * 智能滑动
     *
     * @param moveEntityList 滑动坐标列表
     * @param autoUp         是否自动抬起
     * @return RobotReply
     */
    default RobotReply smartSwipe(List<MoveEntity> moveEntityList, boolean autoUp) {
        //移动到第一个点
        MoveEntity startPoint = moveEntityList.get(0);
        log.info("准备移动到滑动起点:{}", startPoint);
        RobotReply robotReply = touch(startPoint, false);
        if (robotReply.isOk()) {
            //下面是sse发送点击坐标数据暂时注释掉
//            sendPointMsg(ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID, startPoint);
            //滑动剩余的点
            for (MoveEntity moveEntity : moveEntityList.subList(1, moveEntityList.size())) {
                robotReply = moveLine(moveEntity);
                if (!robotReply.isOk()) {
                    return robotReply;
                }
                //下面是sse发送点击坐标数据暂时注释掉
//                sendPointMsg(ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID, moveEntity);
            }
            //自动抬起
            if (autoUp) {
                MoveEntity endPoint = moveEntityList.get(moveEntityList.size() - 1);
                endPoint.setZ(endPoint.getZ() + AUTO_UP_HEIGHT);
                log.info("移动到滑动终点上方:{}", endPoint);
                return moveLine(endPoint);
            }
        }
        return robotReply;
    }

    default void sendPointMsg(String msgID, MoveEntity moveEntity) {
        DisplayTouchPoint displayTouchPoint = new DisplayTouchPoint();
        displayTouchPoint.setX(moveEntity.getY());
        displayTouchPoint.setY(moveEntity.getX());
        SseUtils.pubMsg(msgID, JSON.toJSONString(displayTouchPoint));
    }

    /**
     * 直线移动
     *
     * @param moveEntity 坐标
     * @return RobotReply
     */
    default RobotReply moveLine(MoveEntity moveEntity) {
        return moveLine(moveEntity, false);
    }

    /**
     * 直线移动
     *
     * @param moveEntity       坐标
     * @param moveDurationCalc 是否计算移动时间
     * @return RobotReply
     */
    RobotReply moveLine(MoveEntity moveEntity, boolean moveDurationCalc);

    RobotReply moveLineQuickly(List<MoveEntity> moveEntityList);

    /**
     * 关节运动
     *
     * @param moveEntity 坐标
     * @return RobotReply
     */
    RobotReply moveJoint(MoveEntity moveEntity);

    /**
     * 点击
     *
     * @param moveEntity 坐标
     * @return RobotReply
     */
    RobotReply touch(MoveEntity moveEntity);

    /**
     * 点击
     *
     * @param speedCoordinates 加速度坐标
     * @return RobotReply
     */
    RobotReply touch(SpeedCoordinates speedCoordinates) throws OperationFailNotification;

    /**
     * 点击
     *
     * @param moveEntity 坐标
     * @param autoUp     是否自动抬起
     * @return RobotReply
     */
    RobotReply touch(MoveEntity moveEntity, boolean autoUp);

    /**
     * 点击
     *
     * @param moveEntity 坐标
     * @param autoUp     是否自动抬高
     * @param verbose    是否显示log
     * @return RobotReply
     */
    RobotReply touch(MoveEntity moveEntity, boolean autoUp, boolean verbose);

    /**
     * 圆弧画线
     *
     * @param arcEntity 圆弧
     * @return RobotReply
     */
    RobotReply arc(ArcEntity arcEntity);

    /**
     * 画圆
     *
     * @param moveEntityList 画圆坐标列表
     * @return RobotReply
     */
    RobotReply circle(List<MoveEntity> moveEntityList);

    /**
     * 移动
     *
     * @param moveEntity 坐标
     * @return RobotReply
     */
    default RobotReply move(MoveEntity moveEntity) {
        if (moveEntity.isJoint()) {
            return moveJoint(moveEntity);
        } else {
            return moveLine(moveEntity);
        }
    }


    /**
     * 直线移动最小距离
     *
     * @param direction 移动方向
     * @return RobotReply
     */
    default RobotReply moveMinimumLineDistance(String direction) {
        return RobotReply.ok();
    }

    /**
     * 直线移动距离
     *
     * @param direction 移动方向
     * @param distance  移动距离
     * @return RobotReply
     */
    default RobotReply moveLineDistance(String direction, float distance) {
        return RobotReply.ok();
    }


    /**
     * 获取反馈数据
     *
     * @return 位姿
     */
    RobotPose fetchFeedbackData();

    /**
     * 获取位姿
     *
     * @return RobotPose
     */
    RobotPose fetchPose(CoordinateSystem coordinateSystem);
    /**
     * @param pressure:
      * @return RobotReply
     * @description 气压控制
     */
    RobotReply air(boolean pressure)throws InterruptedException;


}
