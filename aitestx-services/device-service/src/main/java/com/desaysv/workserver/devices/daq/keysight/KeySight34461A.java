package com.desaysv.workserver.devices.daq.keysight;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.action_sequence.BaseRegexRule;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.common.scpi.ScpiCommandConstants;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.daq.DataAcquirementConfig;
import com.desaysv.workserver.devices.daq.base.DaqDevice;
import com.desaysv.workserver.devices.daq.base.IDaqCompare;
import com.desaysv.workserver.devices.daq.base.IVoltCurrDaqAcquireHandler;
import com.desaysv.workserver.entity.CurrentDirection;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.filemanager.project.DaqFileManager;
import com.desaysv.workserver.monitor.MeasurementComparator;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaInstrument;
import xyz.froud.jvisa.JVisaResourceManager;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Keysight 34461A六位半数字万用表底层接口
 */
@Slf4j
public class KeySight34461A extends DaqDevice implements MeasurementComparator, IVoltCurrDaqAcquireHandler, IDaqCompare {
    @JSONField(serialize = false)
    private JVisaResourceManager visaResourceManager;

    @JSONField(serialize = false)
    private JVisaInstrument instrument;
    // 新增成员变量
    private volatile boolean isCollecting = false;
    private final List<Float> currentWindow = Collections.synchronizedList(new LinkedList<Float>());
    private static final int WINDOW_SIZE = 20;
    private final List<Float> fullHistory = Collections.synchronizedList(new ArrayList<>());
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    @Override
    public String getDeviceModel() {
        return DeviceModel.Daq.KEYSIGHT_34461A;
    }

    public KeySight34461A() {
        this(new DeviceOperationParameter());
    }

    public KeySight34461A(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            visaResourceManager = new JVisaResourceManager();
        } catch (JVisaException e) {
            log.warn(e.getMessage(), e);
        }
    }

    public String sendAndReceiveString(String command) throws JVisaException {
        if (instrument != null) {
            return instrument.queryString(command);
        }
        throw new JVisaException("Keysight 34461A仪器未连接");
    }

    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        if (isSimulated()) {
            return true;
        }
        if (visaResourceManager != null) {
            try {
                instrument = visaResourceManager.openInstrument(getDeviceName());
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        return super.open();
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        if (instrument != null) {
            try {
                instrument.write(ScpiCommandConstants.LOCAL);
                instrument.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        if (visaResourceManager != null) {
            try {
                visaResourceManager.close();
            } catch (JVisaException e) {
                log.error(e.getMessage(), e);
            }
        }
        return super.close();
    }


    private void checkInstrument() {
        if (instrument == null && !isSimulated()) {
            throw new RuntimeException("仪器JVisaInstrument实例不存在");
        }
    }

    @JSONField(serialize = false)
    public float getValue(String command) throws DeviceReadException {
        checkInstrument();
        if (instrument != null) {
            try {
                String value = sendAndReceiveString(command);
                return Float.parseFloat(value);
            } catch (JVisaException e) {
                log.error(String.format("Keysight %s %s", getDeviceName(), command), e);
            }
        }
        throw new DeviceReadException(String.format("%s命令发送失败", command));
    }

    @Override
//    @JsonIgnore
    public float fetchCurrent(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        float current;
        if (currentDirection != null && currentDirection.equals(CurrentDirection.DC.name())) {
            current = getCurrentDC();
            log.info("{}获取直流电流:{}A", getDeviceName(), current);
        } else {
            current = getCurrentAC();
            log.info("{}获取交流电流:{}A", getDeviceName(), current);
        }
        return current;
    }

    @Override
    public float fetchVoltage(Integer deviceChannel, String currentDirection) throws DeviceReadException {
        float voltage;
        if (currentDirection != null && currentDirection.equals(CurrentDirection.DC.name())) {
            voltage = getVoltageDC();
            log.info("{}获取直流电压:{}V", getDeviceName(), voltage);
        } else {
            voltage = getVoltageAC();
            log.info("{}获取交流电压:{}V", getDeviceName(), voltage);
        }
        return voltage;
    }

    private float getRandomValue() {
        return (float) RandomUtil.randomDouble(1, 10);
    }

    @Override
    @JSONField(serialize = false)
    public float getVoltageDC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return getValue(ScpiCommandConstants.MEASURE_VOLTAGE_DC);
    }

    @Override
    @JSONField(serialize = false)
    public float getVoltageAC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return getValue(ScpiCommandConstants.MEASURE_VOLTAGE_AC);
    }

    @Override
    @JSONField(serialize = false)
    public float getCurrentDC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return getValue(ScpiCommandConstants.MEASURE_CURRENT_DC);
    }

    @Override
    @JSONField(serialize = false)
    public float getCurrentAC() throws DeviceReadException {
        if (isSimulated()) {
            return getRandomValue();
        }
        return getValue(ScpiCommandConstants.MEASURE_CURRENT_AC);
    }


    /**
     * 静态电流采集
     *
     * @param dataAcquirementConfig: 采集持续时间（秒）
     */
    public OperationResult staticCurrentAcquire(DataAcquirementConfig dataAcquirementConfig) {
        int duration = dataAcquirementConfig.getDuration();
        float interval = dataAcquirementConfig.getInterval();
        OperationResult operationResult = new OperationResult();
        boolean infinite = duration == -1;
        if (infinite) {
            log.info("无限循环采集静态电流");
        } else {
            log.info("总共采集静态电流{}秒", duration);
        }
        duration *= 1000;
        long beginTime = System.currentTimeMillis();
        long acquireEllipseTime = 0;
        try {
            DaqFileManager<Object> fileManager = new DaqFileManager<>(getExecutionContext().get().getProjectName(),
                    "静态电流", 500);
            while (infinite || acquireEllipseTime <= duration) {
                if (Thread.currentThread().isInterrupted()) {
                    break;
                }
                Object feedback;
                try {
                    feedback = getCurrentAC();
                    System.out.printf("采集静态电流：%fA%n", feedback);
                } catch (DeviceReadException e) {
                    feedback = e.getMessage();
                    System.out.printf("采集静态电流：%s%n", e.getMessage());
                }
                acquireEllipseTime = System.currentTimeMillis() - beginTime;
                fileManager.write(feedback);
                try {
                    TimeUnit.SECONDS.sleep((long) interval);
                } catch (InterruptedException e) {
                    break;
                }
            }
            fileManager.finish();
            operationResult.setOk(true);
        } catch (IOException e) {
            operationResult.setOk(false);
            operationResult.setMessage(e.getMessage());
            log.error(e.getMessage(), e);
        }
        return operationResult;
    }

    /**
     * 开始采集电流
     */
    public void startCurrentCollection(String interval) {
        clearCurrentCache();
        if (isCollecting) {
            log.warn("电流采集已在进行中");
            return;
        }
        Float seconds = BaseRegexRule.getSecondsOfDefaultMills(interval);
        isCollecting = true;

        executorService.submit(() -> {
            try {
                while (isCollecting) {
                    float current = getCurrentDC();
                    addCurrentToWindow(current);
                    TimeUnit.SECONDS.sleep(seconds.longValue());
                }
            } catch (Exception e) {
                log.error("电流采集过程中发生异常", e);
            } finally {
                isCollecting = false;
            }
        });
    }

    private void addCurrentToWindow(float current) {
        currentWindow.add(current);
        fullHistory.add(current);

        if (currentWindow.size() > WINDOW_SIZE) {
            currentWindow.remove(0);
        }
    }

    /**
     * 获取整个采集过程中的最大电流值
     *
     * @return 最大电流（A）
     */
    public float getTotalMaxCurrent() {
        if (fullHistory.isEmpty()) {
            log.warn("尚未采集到任何电流数据");
            return 0.0f;
        }
        Optional<Float> max = fullHistory.stream().max(Float::compare);
        return max.orElse(0.0f);
    }

    /**
     * 获取整个采集过程中的最小电流值
     *
     * @return 最小电流（A）
     */
    public float getTotalMinCurrent() {
        if (fullHistory.isEmpty()) {
            log.warn("尚未采集到任何电流数据");
            return 0.0f;
        }
        Optional<Float> min = fullHistory.stream().min(Float::compare);
        return min.orElse(0.0f);
    }

    /**
     * 获取整个采集过程中的平均电流值
     *
     * @return 平均电流（A）
     */
    public float getTotalAvgCurrent() {
        if (fullHistory.isEmpty()) {
            log.warn("尚未采集到任何电流数据");
            return 0.0f;
        }
        double sum = fullHistory.stream().mapToDouble(Float::doubleValue).sum();
        return (float) (sum / fullHistory.size());
    }

    @Override
    public CurrentStatistics stopCurrentCollection(float baseValue, float threshold) {
        isCollecting = false;
        if (fullHistory.isEmpty()) {
            log.warn("电流数据为空，稳定性判断无效");
            return new CurrentStatistics(0f, 0f, 0f, false, 0);
        }

        List<Float> validCurrents = new ArrayList<>();
        for (Float current : fullHistory) {
            if (current >= 0.0f) {
                validCurrents.add(current);
            }
        }

        if (validCurrents.isEmpty()) {
            log.warn("过滤后无有效电流数据（全部为负值）");
            return new CurrentStatistics(0f, 0f, 0f, false, 0);
        }

        float max = Collections.max(validCurrents);
        float min = Collections.min(validCurrents);
        float avg = (float) validCurrents.stream().mapToDouble(Float::doubleValue).average().orElse(0.0);

        boolean isStable = true;

        for (Float current : validCurrents) {
            if (current < (baseValue - threshold) || current > (baseValue + threshold)) {
                isStable = false;
                break;
            }
        }

        return new CurrentStatistics(max, min, avg, isStable, validCurrents.size());
    }


    @Override
    public void clearCurrentCache() {
        currentWindow.clear();
        fullHistory.clear();
        log.info("电流采集缓存已清空");
    }

    public static void main(String[] args) throws DeviceOpenRepeatException, DeviceOpenException, DeviceCloseException, DeviceReadException {
        KeySight34461A keySight34461A = new KeySight34461A(null);
        System.out.println(keySight34461A.getVoltageDC());
        System.out.println(keySight34461A.getVoltageAC());
        System.out.println(keySight34461A.getCurrentDC());
        System.out.println(keySight34461A.getCurrentAC());
        keySight34461A.close();
    }
}
