package com.desaysv.workserver.factory.concrete;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.canoe.VectorLinFDX;
import com.desaysv.workserver.devices.bus.tosun.lin.TC1016;
import com.desaysv.workserver.devices.bus.tosun.lin.TC1026;
import com.desaysv.workserver.devices.bus.zlg.ZlgLin;
import com.desaysv.workserver.devices.bus.zlg.ZlgLinDtu400UEwgr;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.factory.DeviceRegisterForm;
import com.desaysv.workserver.factory.interfaces.AbstractLinFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@Lazy
public class LinFactory implements AbstractLinFactory {

    @Override
    public Device createDevice(DeviceRegisterForm deviceRegisterForm) {
        String deviceModel = deviceRegisterForm.getDeviceModel();
        switch (deviceModel) {
            case DeviceModel.Bus.VECTOR_LIN:
                return createVectorLinDevice(deviceRegisterForm);
            case DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR:
                return createZlgLinFdDtu400UEwgrDevice(deviceRegisterForm);
            case DeviceModel.Bus.ZLG_USBCANFD_200U:
                return createToZlg200ULinDevice(deviceRegisterForm);
            case DeviceModel.Bus.TC1016:
                return createTC1016Device(deviceRegisterForm);
            case DeviceModel.Bus.TC1026:
                return createTC1026Device(deviceRegisterForm);
        }
        return null;
    }


    @Override
    public Device createVectorLinDevice(DeviceRegisterForm deviceRegisterForm) {
        return new VectorLinFDX(deviceRegisterForm.getDeviceOperationParameter());
    }

    @Override
    public Device createTC1016Device(DeviceRegisterForm deviceRegisterForm) {
        return new TC1016(deviceRegisterForm.getDeviceOperationParameter());
    }
    @Override
    public Device createTC1026Device(DeviceRegisterForm deviceRegisterForm) {
        return new TC1026(deviceRegisterForm.getDeviceOperationParameter());
    }
    @Override
    public Device createToZlg200ULinDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZlgLin(deviceRegisterForm.getDeviceOperationParameter());
    }
    @Override
    public Device createZlgLinFdDtu400UEwgrDevice(DeviceRegisterForm deviceRegisterForm) {
        return new ZlgLinDtu400UEwgr(deviceRegisterForm.getDeviceOperationParameter());
    }


}
