package com.desaysv.workserver.devices.testbox;


public enum State {
    FLOATING("00", "悬空"),
    PULL_UP_5V("01", "上拉5V"),
    PULL_UP_12V("02", "上拉12V"),
    PULL_DOWN_TO_GND("00", "下拉到地"),
    NA("", "NA");

    private final String value;
    private final String description;
    public static final String RES_BOARD_CARD = "电阻板卡";
    public static final String SWITCH_BOARD_CARD = "继电器板卡";
    public static final String HL_OUTPUT_BOARD_CARD = "电平输出板卡";
    public static final String HL_ACQUIRE_BOARD_CARD = "电平采集板卡";
    public static final String PWM_ACQUIRE_BOARD_CARD = "PWM采集板卡";
    public static final String PWM_OUTPUT_BOARD_CARD = "PWM输出板卡";
    State(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    // 根据描述获取状态，用于解析输入
    public static State fromDescription(String description) {
        for (State state : State.values()) {
            if (state.getDescription().equalsIgnoreCase(description)) {
                return state;
            }
        }
        return NA;
    }


    /**
     * 根据板卡类型和状态描述获取对应的值。
     * @param boardType 板卡类型标识符。
     * @param stateDescription 状态描述字符串。
     * @return 对应状态的值。
     */
    //电平输出：悬空00、上拉12v01、下拉到地02
    // PWM输出板卡：悬空：00、上拉到12V：01
    // 电平采集板卡：悬空：00、上拉12v：01
    // 电阻板卡：悬空 00、上拉到5V：01、上拉到12V：02、下拉到地：03
    public static String getStatusValue(String boardType, String stateDescription) {
        State state = State.fromDescription(stateDescription);
        switch (boardType) {
            case HL_ACQUIRE_BOARD_CARD:
            case PWM_OUTPUT_BOARD_CARD:
                return state == State.PULL_UP_12V ? "01" : "00";
            case RES_BOARD_CARD:
//                if (state == State.PULL_UP_5V || state == State.PULL_UP_12V || state == State.PULL_DOWN_TO_GND) {
//                    return state.getValue();
//                } else {
//                    throw new IllegalArgumentException("Invalid state for resistor board: " + state);
//                }
                return state == State.FLOATING ? "03" : state.getValue();
            case HL_OUTPUT_BOARD_CARD:
                return "";
            default:
                // 如果是未知板卡类型，则返回默认值
                return "";
        }
    }
}