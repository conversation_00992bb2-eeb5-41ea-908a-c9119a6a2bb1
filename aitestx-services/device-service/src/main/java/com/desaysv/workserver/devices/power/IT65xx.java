package com.desaysv.workserver.devices.power;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.power.protocol.CommProtocol;
import com.desaysv.workserver.devices.power.protocol.IT65Protocol;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceReadException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import xyz.froud.jvisa.JVisaException;
import xyz.froud.jvisa.JVisaResourceManager;

import java.util.Arrays;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/15 20:54
 * @description : IT65系列电源
 * @modified By :
 * @since : 2023/5/15
 **/
public class IT65xx extends DefaultPower {
    public IT65xx() {
        this(new DeviceOperationParameter());
    }

    public IT65xx(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        setCommProtocol(CommProtocol.USB);
        setPowerProtocol(new IT65Protocol(this));
    }

    @Override
    public int getNumberChannels() {
        return 1;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Power.IT65xx;
    }


    public static void main(String[] args) throws DeviceOpenException, DeviceCloseException, JVisaException, DeviceReadException {
        //FIXME：测试JVISA报错乱码
        //TODO：测试JVISA 官方highlevel代码
        //TODO：测试JVISA对ASRL13::INSTR RS232的getVoltage()支持
        JVisaResourceManager resourceManager = new JVisaResourceManager();
        String[] res = resourceManager.findResources();
        System.out.println(Arrays.toString(res));
        DeviceOperationParameter operationParameter = new DeviceOperationParameter();
        operationParameter.put("protocol", CommProtocol.USB);
        IT65xx power = new IT65xx(operationParameter);
        power.setDeviceName(res[0]);
        power.open();
        int channel = 0;
        power.outputOff(channel);
        power.outputOn(channel);
        power.setVoltage(channel, 12);
        float readVoltage = power.fetchVoltage(channel);
        System.out.println("readVoltage---" + readVoltage);
        power.setCurrent(channel, 10);
        float readCurrent = power.fetchCurrent(channel);
        System.out.println("readCurrent---" + readCurrent);
        power.close();
        resourceManager.close();
    }

}
