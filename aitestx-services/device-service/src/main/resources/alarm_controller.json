[{"id": 0, "level": 0, "en": {"description": "No error", "cause": "", "solution": ""}, "zh_CN": {"description": "无错误", "cause": "", "solution": ""}}, {"id": 16, "level": 5, "en": {"description": "The planned point is closed to the shoulder singularity point", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "规划位置接近肩奇异点", "cause": "", "solution": "重新选取运动点位"}}, {"id": 17, "level": 5, "en": {"description": "Inverse kinematics error with no solution", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "逆解算无解", "cause": "", "solution": "重新选取运动点位"}}, {"id": 18, "level": 5, "en": {"description": "Inverse kinematics error with result out of working area", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "逆解结果限位", "cause": "", "solution": "重新选取运动点位"}}, {"id": 19, "level": 5, "en": {"description": "The starting point and the end point are the same when the JUMP command, ARC command or Circle command is running", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "JUMP或ARC或Circles指令点位重复", "cause": "", "solution": "重新选取运动点位"}}, {"id": 20, "level": 5, "en": {"description": "The points of arc are wrong", "cause": "", "solution": "Enter the correct points"}, "zh_CN": {"description": "圆弧点位错误", "cause": "", "solution": "请重新输入合适的点位"}}, {"id": 21, "level": 5, "en": {"description": "Parameters of JUMP command are wrong,The starting height or end height is negative or the zLimit value is lower than the starting point or the end point", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "抬升高度、下降高度为负或者zLimit低于起始点或结束点的高度", "cause": "", "solution": "输入正确参数"}}, {"id": 22, "level": 5, "en": {"description": "Arm orientation error", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "手势切换错误", "cause": "", "solution": "重新选取运动点位"}}, {"id": 23, "level": 5, "en": {"description": "The planned point is out of range of the workspace in MOVL mode", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "直线运动过程中规划点超出工作空间", "cause": "", "solution": "重新选取运动点位"}}, {"id": 24, "level": 5, "en": {"description": "The planned point is out of range of the workspace in ARC mode", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "圆弧运动过程中规划点超出工作空间", "cause": "", "solution": "重新选取运动点位"}}, {"id": 25, "level": 5, "en": {"description": "The planned point is out of range of the workspace in JUMP mode", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "JUMP过程中规划点超出工作空间", "cause": "", "solution": "重新选取运动点位"}}, {"id": 26, "level": 5, "en": {"description": "The planned point is closed to the wrist singularity point", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "规划位置接近腕奇异点", "cause": "", "solution": "重新选取运动点位"}}, {"id": 27, "level": 5, "en": {"description": "The planned point is closed to the elbow singularity point", "cause": "Reselect the movement points", "solution": ""}, "zh_CN": {"description": "规划位置接近肘奇异点", "cause": "", "solution": "重新选取运动点位"}}, {"id": 28, "level": 5, "en": {"description": "The motion command is wrong", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "运动指令模式错误", "cause": "", "solution": "内部软件错误，重启或联系厂商"}}, {"id": 29, "level": 5, "en": {"description": "Speed parameter is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "速度输入参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 32, "level": 5, "en": {"description": "Inverse kinematics error with shoulder singularity when robot moving ", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "运动过程逆解算肩部奇异", "cause": "", "solution": "重新选取运动点位"}}, {"id": 33, "level": 5, "en": {"description": "Inverse kinematics error with no solution when robot moving", "cause": "", "solution": "Reselect movement points"}, "zh_CN": {"description": "运动过程逆解算无解", "cause": "", "solution": "重新选取运动点位"}}, {"id": 34, "level": 5, "en": {"description": "Inverse kinematics error with result out of working area when robot moving", "cause": "", "solution": "Reselect movement points"}, "zh_CN": {"description": "运动过程逆解算限位", "cause": "", "solution": "重新选取运动点位"}}, {"id": 35, "level": 5, "en": {"description": "Inverse kinematics with wrist singularity when robot moving", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "运动过程逆解算腕奇异", "cause": "", "solution": "重新选取运动点位"}}, {"id": 36, "level": 5, "en": {"description": "Inverse kinematics with elbow singularity when robot moving", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "运动过程逆解算肘奇异", "cause": "", "solution": "重新选取运动点位"}}, {"id": 37, "level": 5, "en": {"description": "The Joint angle is changed over 180 degree", "cause": "", "solution": "Reselect the movement points"}, "zh_CN": {"description": "运动过程关节目标位置变化大于180度", "cause": "", "solution": "重新选取运动点位"}}, {"id": 48, "level": 5, "en": {"description": "Joint1 is overspeed", "cause": "", "solution": "Reset the speed or reselect the movement point away from the singularity"}, "zh_CN": {"description": "关节1超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 49, "level": 5, "en": {"description": "Joint2 overspeed", "cause": "", "solution": "Reset the speed or re-select the movement point away from the singularity"}, "zh_CN": {"description": "关节2超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 50, "level": 5, "en": {"description": "Joint3 overspeed", "cause": "", "solution": "Reset the speed or re-select the movement point away from the singularity"}, "zh_CN": {"description": "关节3超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 51, "level": 5, "en": {"description": "Joint4 overspeed", "cause": "", "solution": "Reset the speed or re-select the movement point away from the singularity"}, "zh_CN": {"description": "关节4超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 52, "level": 5, "en": {"description": "Joint5 overspeed", "cause": "", "solution": "Reset the speed or re-select the movement point away from the singularity"}, "zh_CN": {"description": "关节5超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 53, "level": 5, "en": {"description": "Joint6 overspeed", "cause": "", "solution": "Reset the speed or re-select the movement point away from the singularity"}, "zh_CN": {"description": "关节6超速", "cause": "", "solution": "重新设置速度或重新选取运动点位远离奇异点"}}, {"id": 54, "level": 5, "en": {"description": "Joint1 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节1位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 55, "level": 5, "en": {"description": "Joint2 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节2位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 56, "level": 5, "en": {"description": "Joint3 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节3位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 57, "level": 5, "en": {"description": "Joint4 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节4位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 58, "level": 5, "en": {"description": "Joint5 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节5位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 59, "level": 5, "en": {"description": "Joint6 position lag error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节6位置超差", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 64, "level": 5, "en": {"description": "Positive limit alarm of Joint1", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节1正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 65, "level": 5, "en": {"description": "Negative limit alarm of Joint1", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节1负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 66, "level": 5, "en": {"description": "Positive limit alarm of Joint2", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节2正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 67, "level": 5, "en": {"description": "Negative limit alarm of Joint2", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节2负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 68, "level": 5, "en": {"description": "Positive limit alarm of Joint3", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节3正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 69, "level": 5, "en": {"description": "Negative limit alarm of Joint3", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节3负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 70, "level": 5, "en": {"description": "Positive limit alarm of Joint4", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节4正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 71, "level": 5, "en": {"description": "Negative limit alarm of Joint4", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节4负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 72, "level": 5, "en": {"description": "Positive limit alarm of Joint5", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节5正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 73, "level": 5, "en": {"description": "Negative limit alarm of Joint5", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节5负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 74, "level": 5, "en": {"description": "Positive limit alarm of Joint6", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节6正向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 75, "level": 5, "en": {"description": "Negative limit alarm of Joint6", "cause": "", "solution": "Jog the right joints towards the opposite direction"}, "zh_CN": {"description": "关节6负向限位", "cause": "", "solution": "反向点动脱离限位"}}, {"id": 76, "level": 5, "en": {"description": "Posture joint self-interference: type1", "cause": "", "solution": "Leave the wrong position and reselect the movement point"}, "zh_CN": {"description": "姿态关节自干涉", "cause": "", "solution": "离开错误位置，重新选取运动点位"}}, {"id": 77, "level": 5, "en": {"description": "Posture joint self-interference: type2", "cause": "", "solution": "Leave the wrong position and reselect the movement point"}, "zh_CN": {"description": "姿态关节自干涉", "cause": "", "solution": "离开错误位置，重新选取运动点位"}}, {"id": 78, "level": 5, "en": {"description": "Posture joint self-interference: type3", "cause": "", "solution": "Leave the wrong position and reselect the movement point"}, "zh_CN": {"description": "姿态关节自干涉", "cause": "", "solution": "离开错误位置，重新选取运动点位"}}, {"id": 80, "level": 0, "en": {"description": "Joint1 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节1丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 81, "level": 0, "en": {"description": "Joint2 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节2丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 82, "level": 0, "en": {"description": "Joint3 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节3丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 83, "level": 0, "en": {"description": "Joint4 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节4丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 84, "level": 0, "en": {"description": "Joint5 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节5丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 85, "level": 0, "en": {"description": "Joint6 lose step", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节6丢步", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 96, "level": 0, "en": {"description": "Joint1 status is wrong ", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节1轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 97, "level": 0, "en": {"description": "Joint1 Drive State Disable", "cause": "", "solution": "Re-enable joint 1"}, "zh_CN": {"description": "关节1轴状态下使能", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 99, "level": 0, "en": {"description": "Joint2 status is wrong", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节2轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 100, "level": 0, "en": {"description": "Joint<PERSON> is disabled ", "cause": "", "solution": "Enable all joints"}, "zh_CN": {"description": "关节2轴状态下使能", "cause": "", "solution": "重新使能所有关节"}}, {"id": 102, "level": 0, "en": {"description": "Joint3 status is wrong ", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节3轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 103, "level": 0, "en": {"description": "<PERSON><PERSON> is disabled", "cause": "", "solution": "Enable all joints, or contact technical support engineer"}, "zh_CN": {"description": "关节3轴状态下未使能", "cause": "", "solution": "重新使能所有关节或联系技术支持工程师"}}, {"id": 105, "level": 0, "en": {"description": "Joint4 status is wrong ", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节4轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 106, "level": 0, "en": {"description": "<PERSON><PERSON> is disabled", "cause": "", "solution": "Enable all joints, or contact technical support engineer"}, "zh_CN": {"description": "关节4轴状态下未使能", "cause": "", "solution": "重新使能所有关节或联系技术支持工程师"}}, {"id": 108, "level": 0, "en": {"description": "Joint5 status is wrong ", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节5轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 109, "level": 0, "en": {"description": "Joint5 Drive State Disable", "cause": "", "solution": "Enable all joints, or contact technical support engineer"}, "zh_CN": {"description": "关节5轴状态下使能", "cause": "", "solution": "重新使能所有关节或联系技术支持工程师"}}, {"id": 111, "level": 0, "en": {"description": "Joint6 status is wrong ", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "关节6轴状态错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 112, "level": 0, "en": {"description": "Enable all joints, or contact technical support engineer", "cause": "", "solution": "Re-enable joint 6"}, "zh_CN": {"description": "关节6轴状态下使能", "cause": "", "solution": "重新使能所有关节或联系技术支持工程师"}}, {"id": 114, "level": 5, "en": {"description": "Homing error", "cause": "", "solution": "Please operate homing procedure  again"}, "zh_CN": {"description": "机器人回零失败", "cause": "", "solution": "重新回零"}}, {"id": 115, "level": 0, "en": {"description": "Fail to enable controller", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "机器人使能失败", "cause": "", "solution": "检查硬件是否正常，重新使能"}}, {"id": 116, "level": 0, "en": {"description": "The emergency stop button is pressed", "cause": "", "solution": "release emergency stop button，Clear the alarm and power on again"}, "zh_CN": {"description": "急停按钮按下", "cause": "", "solution": "松开急停按钮，清除告警，重新上电"}}, {"id": 117, "level": 5, "en": {"description": "Collision detection", "cause": "", "solution": "Keep away from the obstruction,  clear the alarm and continue to run robot"}, "zh_CN": {"description": "碰撞检测", "cause": "", "solution": "避开障碍物，清除警报并继续运行"}}, {"id": 118, "level": 0, "en": {"description": "Security I/O is disconnected", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "安全IO掉线", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系厂商"}}, {"id": 119, "level": 0, "en": {"description": "Electronic skin collision detection", "cause": "", "solution": "Keep away from the obstruction,  clear the alarm and continue to run robot"}, "zh_CN": {"description": "电子皮肤碰撞检测", "cause": "", "solution": "避开障碍物，清除警报，继续运行"}}, {"id": 120, "level": 5, "en": {"description": "Six dimension force is not enabled", "cause": "", "solution": "Enable six dimensions"}, "zh_CN": {"description": "六维力未使能", "cause": "", "solution": "使能六维力"}}, {"id": 121, "level": 5, "en": {"description": "Fail to initialize the controller", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "初始化失败", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 122, "level": 0, "en": {"description": "Contactor is open", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "接触器未闭合", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 123, "level": 0, "en": {"description": "The feedback board did not feedback the power on signal", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板未反馈上电信号，上电失败", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 124, "level": 0, "en": {"description": "AC detection board status is wrong", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "AC检测板状态错误", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 125, "level": 0, "en": {"description": "The feedback board data is not updated", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板数据未更新", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 126, "level": 0, "en": {"description": "The switch on the controller board is pressed", "cause": "", "solution": "Power on again， or contact technical support engineer"}, "zh_CN": {"description": "控制柜面板开关被按下", "cause": "", "solution": "重新上电，或联系技术支持工程师"}}, {"id": 127, "level": 0, "en": {"description": "CAN NOT connect to modbus of the feedback board", "cause": "", "solution": "Power on again， or contact technical support engineer"}, "zh_CN": {"description": "馈能板modbus无法连接", "cause": "", "solution": "重新上电，或联系技术支持工程师"}}, {"id": 128, "level": 0, "en": {"description": "Internal error - time overflow", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "内部错误-超时", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 130, "level": 0, "en": {"description": "Universal IO board offline", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "通用IO板掉线", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系厂商"}}, {"id": 131, "level": 0, "en": {"description": "Terminal IO board offline", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "末端IO板掉线", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系厂商"}}, {"id": 144, "level": 5, "en": {"description": "The selected points are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "选择点位不合适", "cause": "", "solution": "输入正确参数"}}, {"id": 146, "level": 5, "en": {"description": "The target point is out range of workspace when inching robot", "cause": "", "solution": "Check whether the target point is in the workspace of the robot"}, "zh_CN": {"description": "寸动参数错误", "cause": "", "solution": "确认目标点位是否限位"}}, {"id": 160, "level": 5, "en": {"description": "Pause error reporting during track reproduction", "cause": "", "solution": "Pause function is not supported during track reproduction"}, "zh_CN": {"description": "轨迹复现过程中暂停报错", "cause": "", "solution": "轨迹复现过程中不支持暂停功能"}}, {"id": 161, "level": 5, "en": {"description": "Control mode switching error", "cause": "", "solution": "System error, restart controller or contact technical support engineer"}, "zh_CN": {"description": "控制模式切换错误", "cause": "", "solution": "系统错误，重新启动或联系技术支持工程师"}}, {"id": 176, "level": 5, "en": {"description": "Force Control Command timeout", "cause": "", "solution": ""}, "zh_CN": {"description": "力控指令超时", "cause": "", "solution": ""}}, {"id": 177, "level": 5, "en": {"description": "Error FC Pause", "cause": "", "solution": "Force control state does not support pause, restart after stopping"}, "zh_CN": {"description": "力控暂停错误", "cause": "", "solution": "力控状态不支持暂停，停止后重新运行"}}, {"id": 178, "level": 5, "en": {"description": "Force Control sensor data overrange", "cause": "", "solution": "check Force Control sensor"}, "zh_CN": {"description": "力控传感器数据异常", "cause": "", "solution": "检查力控传感器"}}, {"id": 179, "level": 5, "en": {"description": "Force Control sensor disabled", "cause": "", "solution": "enable Force Control sensor"}, "zh_CN": {"description": "力控传感器未启用", "cause": "", "solution": "启用力控传感器"}}, {"id": 192, "level": 5, "en": {"description": "CAN NOT pause in tracking process", "cause": "", "solution": "Rerun the script"}, "zh_CN": {"description": "不支持跟踪过程中暂停", "cause": "", "solution": "重新运行脚本"}}, {"id": 193, "level": 5, "en": {"description": "CAN NOT run joint interpolated motion commands in tracking  process", "cause": "", "solution": "Select the correct motion command"}, "zh_CN": {"description": "不支持跟踪过程中使用使用关节类型的运动指令", "cause": "", "solution": "选择合适的运动指令"}}, {"id": 194, "level": 5, "en": {"description": "Tracking limit", "cause": "", "solution": "Increase tracking range or speed up"}, "zh_CN": {"description": "跟踪超限", "cause": "", "solution": "增加跟踪范围或者提速"}}, {"id": 208, "level": 5, "en": {"description": "The fitting point is not enough", "cause": "", "solution": "The trajectory needs 4 points at least"}, "zh_CN": {"description": "轨迹拟合点数太少", "cause": "", "solution": "轨迹拟合需要至少四个点"}}, {"id": 209, "level": 5, "en": {"description": "Playback preprocessing is failed", "cause": "", "solution": "Record a new trajectory"}, "zh_CN": {"description": "StartPath预处理失败", "cause": "", "solution": "录制新的轨迹或设置合适的参数。"}}, {"id": 1772, "level": 0, "en": {"description": "Contactor is open", "cause": "", "solution": "Check whether the hardware is working properly, and restart the the controller， or contact technical support engineer"}, "zh_CN": {"description": "接触器未闭合", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 1773, "level": 0, "en": {"description": "The feedback board fails to supply the power-on signal.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板未反馈上电信号，上电失败", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 1775, "level": 0, "en": {"description": "The AC detection board status is error", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "AC检测板状态错误", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 1776, "level": 0, "en": {"description": "Data of the feedback board is not updated", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板数据未更新", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 4096, "level": 5, "en": {"description": "Failed to open the mechanical parameters file", "cause": "", "solution": "Check the file path and restart the controller"}, "zh_CN": {"description": "打开机械参数文件失败", "cause": "", "solution": "检查文件位置是否正确并重新启动"}}, {"id": 8192, "level": 5, "en": {"description": "Failed to open the project file", "cause": "", "solution": "Check the file path and restart the controller"}, "zh_CN": {"description": "打开工程文件失败", "cause": "", "solution": "检查文件位置是否正确并重新启动"}}, {"id": 8193, "level": 5, "en": {"description": "Failed to open the program file", "cause": "", "solution": "Check the file path and restart the controller"}, "zh_CN": {"description": "打开程序文件失败", "cause": "", "solution": "检查文件位置是否正确并重新启动"}}, {"id": 8194, "level": 5, "en": {"description": "Failed to open the global variable file", "cause": "", "solution": "Check the file path and restart the controller"}, "zh_CN": {"description": "打开全局参数文件失败", "cause": "", "solution": "检查文件位置是否正确并重新启动"}}, {"id": 8195, "level": 5, "en": {"description": "Failed to open the teaching  point file", "cause": "", "solution": "Check the file path and restart the controller"}, "zh_CN": {"description": "打开示教点文件失败", "cause": "", "solution": "检查文件位置是否正确并重新启动"}}, {"id": 8196, "level": 0, "en": {"description": "Failed to run debugger", "cause": "", "solution": "<PERSON><PERSON> debugger"}, "zh_CN": {"description": "启动调试进程失败", "cause": "", "solution": "重新运行调试进程"}}, {"id": 8197, "level": 0, "en": {"description": "Failed to parse project file", "cause": "", "solution": "<PERSON><PERSON> debugger"}, "zh_CN": {"description": "解析工程文件失败", "cause": "", "solution": "重新运行调试进程"}}, {"id": 8198, "level": 0, "en": {"description": "Project file content is empty", "cause": "", "solution": "<PERSON><PERSON> debugger"}, "zh_CN": {"description": "工程文件内容为空", "cause": "", "solution": "重新运行调试进程"}}, {"id": 8199, "level": 0, "en": {"description": "The project file does not exist", "cause": "", "solution": "<PERSON><PERSON> debugger"}, "zh_CN": {"description": "工程文件不存在", "cause": "", "solution": "重新运行调试进程"}}, {"id": 8200, "level": 0, "en": {"description": "Failed to parse CPUs", "cause": "", "solution": "<PERSON><PERSON> debugger"}, "zh_CN": {"description": "解析CPUS失败", "cause": "", "solution": "重新运行调试进程"}}, {"id": 8448, "level": 0, "en": {"description": "Failed to get robot type", "cause": "", "solution": "Confirm robot type file"}, "zh_CN": {"description": "获取机器人类型失败", "cause": "", "solution": "确认机器类型文件"}}, {"id": 12288, "level": 0, "en": {"description": "Press the emergency stop button", "cause": "", "solution": "Clear the alarm and power on again"}, "zh_CN": {"description": "紧急停止按键按下", "cause": "", "solution": "清除告警并重新上电"}}, {"id": 12289, "level": 0, "en": {"description": "Detecte external emergency-stopped status", "cause": "", "solution": "Clear the alarm and power on again "}, "zh_CN": {"description": "检测到外部急停状态", "cause": "", "solution": "清除告警并重新上电"}}, {"id": 12290, "level": 0, "en": {"description": "Axis1 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴1不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12291, "level": 0, "en": {"description": "Axis2 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴2不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12292, "level": 0, "en": {"description": "Axis3 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴3不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12293, "level": 0, "en": {"description": "Axis4 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴4不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12294, "level": 0, "en": {"description": "Axis5 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴5不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12295, "level": 0, "en": {"description": "Axis6 not in Bus Mode", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "轴6不在总线模式", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 12296, "level": 0, "en": {"description": "The robot is powered off", "cause": "", "solution": "Clear the alarm and power on again， or contact technical support engineer"}, "zh_CN": {"description": "本体下电", "cause": "", "solution": "清除告警，并重新上电，或联系技术支持工程师"}}, {"id": 16384, "level": 5, "en": {"description": "Detecte obstacles in the operateion area", "cause": "", "solution": "Clear obstacles and continue to operate the robot"}, "zh_CN": {"description": "检测到该区域有障碍物", "cause": "", "solution": "清理障碍物，继续运行"}}, {"id": 16386, "level": 5, "en": {"description": "The inverse kinematics solution is failed，the planned target points are wrong", "cause": "", "solution": "Clear the alarm and re-teach point "}, "zh_CN": {"description": "逆解算失败，点位错误", "cause": "", "solution": "清除告警，并重新示教点位"}}, {"id": 20480, "level": 0, "en": {"description": "Modbus of feedback board  is disconnected ", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板Modbus掉线", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 20481, "level": 0, "en": {"description": "Before power supply, the voltage is higher than 51V", "cause": "", "solution": "The voltage is too high，please contact technical support engineer"}, "zh_CN": {"description": "供电前，电压高于51V", "cause": "", "solution": "本体供电电压过高,请联系技术支持工程师"}}, {"id": 20482, "level": 0, "en": {"description": "Before power supply, the voltage is lower than 45V", "cause": "", "solution": "The voltage is too low，please contact technical support engineer"}, "zh_CN": {"description": "供电前，电压低于24V", "cause": "", "solution": "本体供电电压过低,请联系技术支持工程师"}}, {"id": 20483, "level": 0, "en": {"description": "The hardware circuit related to the current-limiting chip is abnormal", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "限流芯片相关硬件电路异常", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 20484, "level": 0, "en": {"description": "After power supply, the voltage is higher than 55V", "cause": "", "solution": "The voltage is too high，please contact technical support engineer"}, "zh_CN": {"description": "供电后，电压高于55V", "cause": "", "solution": "本体供电电压过高,请联系技术支持工程师"}}, {"id": 20485, "level": 0, "en": {"description": "After power supply, the voltage is lower than 41.4V", "cause": "", "solution": "The voltage is too low，please contact technical support engineer"}, "zh_CN": {"description": "供电后，电压低于41.4V", "cause": "", "solution": "本体供电电压过低,请联系技术支持工程师"}}, {"id": 20486, "level": 0, "en": {"description": "Internal first stabilized voltage is abnormal", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "内部第一路稳压值异常", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 20487, "level": 0, "en": {"description": "Internal second stabilized voltage is abnormal", "cause": "", "solution": "System error, please contact technical support engineer"}, "zh_CN": {"description": "内部第二路稳压值异常", "cause": "", "solution": "系统错误，请联系技术支持工程师"}}, {"id": 20489, "level": 0, "en": {"description": "The feedback current is higher than 17A", "cause": "", "solution": "The feedback current is too high, please contact technical support engineer"}, "zh_CN": {"description": "检测馈电电流大于17A", "cause": "", "solution": "馈电电流过大，请联系技术支持工程师"}}, {"id": 20490, "level": 0, "en": {"description": "Overpower protection of cement resistance", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "水泥电阻的功率保护", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 20491, "level": 0, "en": {"description": "DC bus current is higher  than 26A", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "母线电流大于26A", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 20493, "level": 5, "en": {"description": "The fan circuit of the feedback board is short-circuited", "cause": "", "solution": "Check whether the hardware is working properly,  and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板电扇电路短路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 20494, "level": 5, "en": {"description": "The fan circuit of the feedback board is broken", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "馈能板电扇电流断路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24576, "level": 0, "en": {"description": "Modbus of I/O board is  disconnected ", "cause": "", "solution": "Check whether the hardware is working properly, and restart the collaborative robot， or contact technical support engineer"}, "zh_CN": {"description": "安全IO板Modbus掉线", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24578, "level": 0, "en": {"description": "The contactor did not close as required", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "接触器连续1s没有按要求闭合", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24579, "level": 0, "en": {"description": "The feedback board does not return an electrified signal during 2s", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "控制上电的状态下，馈能板2s没有返回上电信号", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24581, "level": 0, "en": {"description": " AC status is abnormal", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "AC状态检测错误", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24582, "level": 0, "en": {"description": " After 5 minutes of powering on, the feeback board data did not update for 1 min", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer"}, "zh_CN": {"description": "上电5分钟后，馈能板数据数据持续1分钟没更新", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 24583, "level": 0, "en": {"description": " The switch on the controller is pressed", "cause": "", "solution": "Clear the alarm and power on again"}, "zh_CN": {"description": "面板开关被按下", "cause": "", "solution": "清除告警，并重新上电"}}, {"id": 24584, "level": 0, "en": {"description": "The communication of feedback board modbus is interrupted", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller, or contact technical support engineer"}, "zh_CN": {"description": "馈能板Modbus通信中断", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 32768, "level": 5, "en": {"description": "SpeedFactor command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedFactor指令无输入参数", "cause": "", "solution": "输入正确参数"}}, {"id": 32769, "level": 5, "en": {"description": "Parameters of SpeedFactor command are  out of range", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedFactor指令参数超出范围", "cause": "", "solution": "输入正确参数"}}, {"id": 32770, "level": 5, "en": {"description": "Parameters of DO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "DO 参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 32771, "level": 5, "en": {"description": "Parameters of DI command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "DI 参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 32772, "level": 5, "en": {"description": "DIGroup params number err", "cause": "", "solution": "params is too much"}, "zh_CN": {"description": "DIGroup 参数数量错误", "cause": "", "solution": "点位过多，最好不要一次输入超过100个点"}}, {"id": 32773, "level": 5, "en": {"description": "DIGroup have some params's index out of range", "cause": "", "solution": "input correct params"}, "zh_CN": {"description": "DIGroup 参数中存在的点位索引 超出边界", "cause": "", "solution": "请核对点位范围"}}, {"id": 32774, "level": 5, "en": {"description": "DIGroup have some params's index type error", "cause": "", "solution": "input correct params"}, "zh_CN": {"description": "DIGroup 参数中存在的点位索引的类型错误", "cause": "", "solution": "只能输入数字"}}, {"id": 32775, "level": 5, "en": {"description": "DIGroup err internal ", "cause": "", "solution": "internal error has happend,try again."}, "zh_CN": {"description": "DIGroup 发生内部错误", "cause": "", "solution": "再次尝试，如果一直存在错误，请联系技术支持"}}, {"id": 32776, "level": 5, "en": {"description": "DOGroup params number err", "cause": "", "solution": "params is too much"}, "zh_CN": {"description": "DOGroup 参数数量错误", "cause": "", "solution": "点位过多，最好不要一次输入超过100个点"}}, {"id": 32777, "level": 5, "en": {"description": "DOGroup have some params's index out of range", "cause": "", "solution": "input correct params"}, "zh_CN": {"description": "DOGroup 参数中存在的点位索引 超出边界", "cause": "", "solution": "请核对点位范围"}}, {"id": 32778, "level": 5, "en": {"description": "DOGroup have some params's index type error", "cause": "", "solution": "input correct params"}, "zh_CN": {"description": "DOGroup 参数中存在的点位索引的类型错误", "cause": "", "solution": "只能输入数字"}}, {"id": 32779, "level": 5, "en": {"description": "DOGroup have some params's value type error", "cause": "", "solution": "input correct params,number type and boolean is needed eg.(0/1) || (true/false) || (ON/OFF)"}, "zh_CN": {"description": "DOGroup 参数中存在的点位输出值的类型错误", "cause": "", "solution": "请输入数字(0/1)或bool(true/false)或内置的 ON/OFF"}}, {"id": 32780, "level": 5, "en": {"description": "DOGroup have some params's isqueue type error", "cause": "", "solution": "eg.(0/1) || (true/false) || (ON/OFF) . true means queue command(default),false:Immediate command"}, "zh_CN": {"description": "DOGroup 参数中存在的可选参数 指令类型的类型错误", "cause": "", "solution": "请输入数字(0/1)或bool(true/false)或内置的 ON/OFF 真 : 队列指令(默认) 假: 立即指令"}}, {"id": 32781, "level": 5, "en": {"description": "DOGroup err internal ", "cause": "", "solution": "internal error has happend,try again."}, "zh_CN": {"description": "DOGroup 发生内部错误", "cause": "", "solution": "再次尝试，如果一直存在错误，请联系技术支持"}}, {"id": 32785, "level": 5, "en": {"description": "Parameters of AI command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AI 参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 32801, "level": 5, "en": {"description": "Parameters number of AO command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AO 参数个数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 32802, "level": 5, "en": {"description": "Index parameter of AO command is wrong", "cause": "", "solution": "Enter the correct parameters, the index parameter can only be set to 1 or 2"}, "zh_CN": {"description": "AO指令索引参数错误", "cause": "", "solution": "输入正确参数，参数只能为1 或者2 "}}, {"id": 32803, "level": 5, "en": {"description": "Voltage parameter of AO command is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 0.0 to 10.0"}, "zh_CN": {"description": "AO电压值参数错误，超过边界", "cause": "", "solution": "输入正确参数,取值范围[0，10.0]"}}, {"id": 32804, "level": 5, "en": {"description": "index parameter of AO command is out of range", "cause": "", "solution": "Enter the correct parameters, value is 0 or 1"}, "zh_CN": {"description": "AO索引值错误", "cause": "", "solution": "输入正确参数,取值范围是 0  或 1 "}}, {"id": 32849, "level": 5, "en": {"description": "AO mode err", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AO 模式错误", "cause": "", "solution": "输入正确参数"}}, {"id": 33024, "level": 5, "en": {"description": "CP command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "CP指令无输入参数", "cause": "", "solution": "输入正确参数"}}, {"id": 33025, "level": 5, "en": {"description": "Input parameters of CP command is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 0 to 100"}, "zh_CN": {"description": "CP指令参数超出范围", "cause": "", "solution": "输入正确参数,取值范围[0，100]"}}, {"id": 33280, "level": 5, "en": {"description": "Arch command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch指令无输入参数", "cause": "", "solution": "请输入正确参数"}}, {"id": 33281, "level": 5, "en": {"description": "Index parameter of Arch command is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 0 to 9"}, "zh_CN": {"description": "Arch指令索引超出范围", "cause": "", "solution": "输入正确参数，取值范围[0，9]"}}, {"id": 33282, "level": 5, "en": {"description": "The parameters coorespongding to index parameter of Arch command are not configured", "cause": "", "solution": "Please set index parameters"}, "zh_CN": {"description": "Arch指令索引对应参数尚未设置", "cause": "", "solution": "请设置索引参数"}}, {"id": 33536, "level": 5, "en": {"description": "LimZ command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "LimZ指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 33537, "level": 5, "en": {"description": "Input parameters of LimZ instruction out of range", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "LimZ指令参数超出范围", "cause": "", "solution": "输入正确参数"}}, {"id": 33792, "level": 5, "en": {"description": "Speed command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Speed指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 33793, "level": 5, "en": {"description": "Parameter of Speed command is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "Speed指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 34048, "level": 5, "en": {"description": "Accel command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Accel指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 34049, "level": 5, "en": {"description": "Parameter of Accel command is out of range ", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "Accel指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 34304, "level": 5, "en": {"description": "Jerk command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jerk指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 34305, "level": 5, "en": {"description": "Parameter of Jerk command is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "Jerk指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 34560, "level": 5, "en": {"description": "SpeedS command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedS指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 34561, "level": 5, "en": {"description": "Parameter of SpeedS command is out of range ", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "SpeedS指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 34816, "level": 5, "en": {"description": "SpeedR command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedR指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 34817, "level": 5, "en": {"description": "Parameter of SpeedR commandis is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "SpeedR指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 35072, "level": 5, "en": {"description": "AccelS command has no input parameters", "cause": "", "solution": "Enter the correct parameters, value ranges from 1 to 100"}, "zh_CN": {"description": "AccelS指令无输入参数", "cause": "", "solution": "请输入正确参数，取值范围[1, 100]"}}, {"id": 35073, "level": 5, "en": {"description": "Parameter of AccelS command is out of range", "cause": "", "solution": "Enter the correct parameters, volue ranges from 0 to 100"}, "zh_CN": {"description": "AccelS指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 35328, "level": 5, "en": {"description": "AccelR command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AccelR指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 35329, "level": 5, "en": {"description": "Parameter of AccelR command is out of range", "cause": "", "solution": "Enter the correct parameters, volue ranges from 0 to 100"}, "zh_CN": {"description": "AccelR指令比例参数超出范围[1, 100]", "cause": "", "solution": "输入正确参数"}}, {"id": 35584, "level": 5, "en": {"description": "JerkS command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "JerkS指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 35585, "level": 5, "en": {"description": "Parameter of JerkS instruction is out of range", "cause": "", "solution": "Enter the correct parameters, value ranges from 0 to 100"}, "zh_CN": {"description": "JerkS指令比例参数超出范围[1, 100]", "cause": "", "solution": "输入正确参数"}}, {"id": 35840, "level": 5, "en": {"description": "JerkR command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "JerkR指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 35841, "level": 5, "en": {"description": "Parameter of JerkR command is out of range", "cause": "", "solution": "Enter the correct parameters,value ranges from 0 to 100"}, "zh_CN": {"description": "JerkR指令比例参数超出范围", "cause": "", "solution": "输入正确参数，取值范围[1, 100]"}}, {"id": 36096, "level": 5, "en": {"description": "Go command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Go指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36097, "level": 5, "en": {"description": "Go command has no point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Go指令缺少坐标点参数", "cause": "", "solution": "请输入正确参数"}}, {"id": 36098, "level": 5, "en": {"description": "Point parameter of Go command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Go指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36099, "level": 5, "en": {"description": "Control parameter of Go command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Go指令控制参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36100, "level": 5, "en": {"description": "MoveJ command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJ指令无输入参数", "cause": "", "solution": "输入正确参数"}}, {"id": 36101, "level": 5, "en": {"description": "MoveJ command has no point parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJ指令缺少坐标点参数", "cause": "", "solution": "输入正确参数"}}, {"id": 36102, "level": 5, "en": {"description": "MoveJ command has no point parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJ指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 36103, "level": 5, "en": {"description": "Point parameter of RP command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RP指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36104, "level": 5, "en": {"description": "Offset parameters of RP command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RP指令偏移量参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36105, "level": 5, "en": {"description": "Point parameter of RJ command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RJ指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36106, "level": 5, "en": {"description": "Offset parameters of RJ command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RJ指令偏移值点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36107, "level": 5, "en": {"description": "GoR command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoR指令无输入参数", "cause": "", "solution": "输入正确参数"}}, {"id": 36108, "level": 5, "en": {"description": "Point parameter of GoR command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoR指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36109, "level": 5, "en": {"description": "MoveJR instruction has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJR指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36110, "level": 5, "en": {"description": "Point parameter of MoveJR command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJR指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36111, "level": 5, "en": {"description": "GoIO command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoIO指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36112, "level": 5, "en": {"description": "Point parameter of GoIO command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoIO指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36113, "level": 5, "en": {"description": "I/O parameters of GoIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoIO指令I/O参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36114, "level": 5, "en": {"description": "MoveIO command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveIO指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36115, "level": 5, "en": {"description": "Point parameter of MoveIO command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveIO指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36116, "level": 5, "en": {"description": "I/O parameters of MoveIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveIO指令I/O参数错误", "cause": "", "solution": "请输入正确参数"}}, {"id": 36117, "level": 5, "en": {"description": "MoveJIO command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJIO指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36118, "level": 5, "en": {"description": "Point parameter of MoveJIO command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJIO指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36119, "level": 5, "en": {"description": "I/O parameters of MoveJIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJIO指令I/O参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36120, "level": 5, "en": {"description": "MoveJ command has no point parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJ指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 36121, "level": 5, "en": {"description": "MoveR command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveR指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36122, "level": 5, "en": {"description": "Point parameter of MoveR command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveR指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 36352, "level": 5, "en": {"description": "Move command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Move指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36353, "level": 5, "en": {"description": "Move command has no point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Move指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 36354, "level": 5, "en": {"description": "Point parameter of Move command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Move指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36355, "level": 5, "en": {"description": "Control parameter of Move command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Move指令控制参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36608, "level": 5, "en": {"description": "Arch3 command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch3指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36609, "level": 5, "en": {"description": "Arch3 command has no point parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch3指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 36610, "level": 5, "en": {"description": "Point parameter of Arch3 command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch3指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36611, "level": 5, "en": {"description": "Control parameter of Arch3 command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch3指令控制参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36864, "level": 5, "en": {"description": "Jump command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jump指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 36865, "level": 5, "en": {"description": "Jump command has no point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jump指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 36866, "level": 5, "en": {"description": "Point parameter of Jump command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jump指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 36867, "level": 5, "en": {"description": "Control parameter of Jump command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jump指令控制参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 40960, "level": 5, "en": {"description": "Circle3 command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Circle3指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 40961, "level": 5, "en": {"description": "Circle3 command has no point parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Circle3指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 40962, "level": 5, "en": {"description": "Point parameters of Circle3 command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Circle3指令坐标点参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 40963, "level": 5, "en": {"description": "Control parameter of Circle3 command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Circle3指令控制参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 41216, "level": 5, "en": {"description": "The Spiral command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 41217, "level": 5, "en": {"description": "The Spiral command lacks a coordinate point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 41218, "level": 5, "en": {"description": "Wrong Spiral command coordinate point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41220, "level": 5, "en": {"description": "Wrong Spiral command control parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令控制参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41221, "level": 5, "en": {"description": "Wrong Spiral command threshold parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令阈值参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41222, "level": 5, "en": {"description": "Wrong Spiral command radius parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Spiral指令半径参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41472, "level": 5, "en": {"description": "The Rotation command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 41473, "level": 5, "en": {"description": "The Rotation command lacks a coordinate point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令缺少坐标点参数", "cause": "", "solution": "请输入参数"}}, {"id": 41474, "level": 5, "en": {"description": "Wrong Rotation command coordinate point parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令坐标点参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41475, "level": 5, "en": {"description": "Wrong Rotation command control parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令控制参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41476, "level": 5, "en": {"description": "Wrong Rotation command speed parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令速度参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41477, "level": 5, "en": {"description": "Wrong Rotation command max Torque parameter", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Rotation指令最大力矩参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41728, "level": 5, "en": {"description": "command Liner has no parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "liner 指令无输入参数", "cause": "", "solution": "请输入参数"}}, {"id": 41732, "level": 5, "en": {"description": "command Liner optional parameters err", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "liner 指令可选参数错误", "cause": "", "solution": "请输入参数"}}, {"id": 41984, "level": 5, "en": {"description": "Jack direction parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "插孔方向参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 41985, "level": 5, "en": {"description": "Jack speed parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "插孔速度参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 41986, "level": 5, "en": {"description": "Jack force parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "插孔力参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 41987, "level": 5, "en": {"description": "Jack max speed parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "插孔最大速度参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 41988, "level": 5, "en": {"description": "Jack mode parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "插孔模式参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42240, "level": 5, "en": {"description": "6 d force return to zero parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "六维力回零参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42241, "level": 5, "en": {"description": "Six dimensions are not turned on", "cause": "", "solution": "Turn on six dimensions"}, "zh_CN": {"description": "六维力没有开启", "cause": "", "solution": "开启六维力"}}, {"id": 42242, "level": 5, "en": {"description": "Six dimensions parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "六维力参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42243, "level": 5, "en": {"description": "Six force sensor has no data over 1 second", "cause": "", "solution": "check the connect of the sensor"}, "zh_CN": {"description": "六维力数据异常，超过1s没有获得数据", "cause": "", "solution": "检查硬件连接"}}, {"id": 42496, "level": 5, "en": {"description": "FCAct start parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "力控启动参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42497, "level": 5, "en": {"description": "FCAct has no input parameter", "cause": "", "solution": "input parameter"}, "zh_CN": {"description": "FCAct指令无输入参数", "cause": "", "solution": "输入参数"}}, {"id": 42498, "level": 5, "en": {"description": "FCAct parameter out of range 0/1", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCAct指令参数超出范围0/1", "cause": "", "solution": "输入正确参数"}}, {"id": 42499, "level": 5, "en": {"description": "FCDeact parameters error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCDeact 关闭力控参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42500, "level": 5, "en": {"description": "FCSetComplianceNormal has no input parameter", "cause": "", "solution": "input parameter"}, "zh_CN": {"description": "FCSetComplianceNormal 指令无输入参数", "cause": "", "solution": "输入参数"}}, {"id": 42501, "level": 5, "en": {"description": "FCSetCompliance parameters error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCSetCompliance参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42502, "level": 5, "en": {"description": "FCSetComplianceNormal has no input parameter", "cause": "", "solution": "input parameter"}, "zh_CN": {"description": "FCSetComplianceNormal无输入参数", "cause": "", "solution": "输入参数"}}, {"id": 42503, "level": 5, "en": {"description": "FCSetCompliance parameters error ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCSetCompliance 指令参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42504, "level": 5, "en": {"description": "FCLoad ID error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "负载辨识 ID 错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42505, "level": 5, "en": {"description": "FCCalib has no input parameter", "cause": "", "solution": "input parameter"}, "zh_CN": {"description": "FCCalib 无输入参数", "cause": "", "solution": "输入参数"}}, {"id": 42506, "level": 5, "en": {"description": "FCCalib parameters error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCalib参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42507, "level": 5, "en": {"description": "FCCalib optional parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCalib可选参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 42508, "level": 5, "en": {"description": "Optional parameter IsInside error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 IsInside 选项错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42509, "level": 5, "en": {"description": "Optional parameter timeout error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 timeout 选项错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42510, "level": 5, "en": {"description": "Optional parameter IsAlarm error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 IsAlarm 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42511, "level": 5, "en": {"description": "DataAlarmCheckNotExistFCCondForceParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond参数中 force 不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42512, "level": 5, "en": {"description": "Force param in FCCond error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 中 Force 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42513, "level": 5, "en": {"description": "FCCond Optional parameter Force error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 可选参数中 Force 项错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42514, "level": 5, "en": {"description": "DataAlarmCheckNotExistFCCondTCPSpeedParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond TCP 速度参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42515, "level": 5, "en": {"description": "DataAlarmCheckFCCondTCPSpeedErrorParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond TCP速度参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42516, "level": 5, "en": {"description": "Optional parameter Speed error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 可选参数中 Speed 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42517, "level": 5, "en": {"description": "Optional parameter Torque not exist.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 力矩参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42518, "level": 5, "en": {"description": "DataAlarmCheckFCCondTorqueErrorParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 力矩(Torque)参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42519, "level": 5, "en": {"description": "Optional parameter Torque error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond 可选参数 Torque 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42520, "level": 5, "en": {"description": "DataAlarmCheckNotExistFCCondDisplacParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond Displac 不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42521, "level": 5, "en": {"description": "DataAlarmCheckFCCondDisplacErrorParam", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond Displac 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42522, "level": 5, "en": {"description": "Optional parameter Displac error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCCond Displac 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42523, "level": 5, "en": {"description": "Optional parameter SpeedF error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef SpeedF 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42524, "level": 5, "en": {"description": "Optional parameter TimeOut error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef可选参数中 TimeOut 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42525, "level": 5, "en": {"description": "FCRef’s Optional parameter Direction error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef可选参数中 Direction 选项错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42526, "level": 5, "en": {"description": "FCRef's Optional parameter Distance error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef Distance 选项错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42527, "level": 5, "en": {"description": "FCRef Optional parameter SpeedL error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef 的可选参数 SpeedL 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42544, "level": 5, "en": {"description": "FCRefForce parameter not exist", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefForce 参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42545, "level": 5, "en": {"description": "FCRefForce's Optional parameter error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefForce 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42546, "level": 5, "en": {"description": "FCRefForce parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefForce 参数错误 ", "cause": "", "solution": "输入正确的参数"}}, {"id": 42547, "level": 5, "en": {"description": "FCRefTorque parameter not exist", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefTorque 参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42548, "level": 5, "en": {"description": "FCRefTorque's optional parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefTorque 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42549, "level": 5, "en": {"description": "FCRefTorque parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefTorque 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42550, "level": 5, "en": {"description": "FCRefline has no input params", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefline 无输入参数 ", "cause": "", "solution": "输入正确的参数"}}, {"id": 42551, "level": 5, "en": {"description": "FCRefline's optional parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefline 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42552, "level": 5, "en": {"description": "FCRefline's parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefline 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42553, "level": 5, "en": {"description": "FCRefRot's parameter not exist", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefRot 参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42554, "level": 5, "en": {"description": "FCRef's optional parameter rotate error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefrotate 可选参数 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42555, "level": 5, "en": {"description": "FCRef's parameter rotate error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef参数rotate错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42556, "level": 5, "en": {"description": "FCRefSpiral parameter not exist.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefSpiral 参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 42557, "level": 5, "en": {"description": "FCRefSpiral's optional parameter error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefSpiral 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42558, "level": 5, "en": {"description": "FCRefSpiral's parameter error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRefSpiral参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42559, "level": 5, "en": {"description": "FCRef limitValue error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "FCRef 错误的限制范围", "cause": "", "solution": "输入正确的参数"}}, {"id": 42752, "level": 5, "en": {"description": "SetSafeSkin not exist param", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "电子皮肤感应无输入参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42753, "level": 5, "en": {"description": "SetSafeSkin parameters error ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "电子皮肤感应参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 42754, "level": 5, "en": {"description": "SetSafeSkin parameters out of range", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "电子皮肤感应参数超限", "cause": "", "solution": "输入正确的参数"}}, {"id": 45056, "level": 5, "en": {"description": "Optional parameters of Circle3 command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Circle3指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45057, "level": 5, "en": {"description": "Optional parameters of Jump command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jump指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45058, "level": 5, "en": {"description": "Optional parameters of Arch command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45059, "level": 5, "en": {"description": "Optional parameters of Arch3 command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Arch3 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45060, "level": 5, "en": {"description": "Optional parameters of Jerk command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Jerk 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45061, "level": 5, "en": {"description": "Optional parameters of JerkR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "JerkR 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45062, "level": 5, "en": {"description": "Optional parameters of JerkS command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "JerkS 可选参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 45063, "level": 5, "en": {"description": "Optional parameters of Accel command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Accel 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45064, "level": 5, "en": {"description": "Optional parameters of AccelR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AccelR指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45065, "level": 5, "en": {"description": "Optional parameters of AccelS command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "AccelS 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45066, "level": 5, "en": {"description": "Optional parameters of SpeedFactor command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedFactor 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45067, "level": 5, "en": {"description": "Optional parameters of Speed command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Speed 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45068, "level": 5, "en": {"description": "Optional parameters of SpeedR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SpeedR 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45069, "level": 5, "en": {"description": "Optional parameters of LimZ command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "LimZ 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45070, "level": 5, "en": {"description": "Optional parameters of CP command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "CP 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45071, "level": 5, "en": {"description": "Optional parameters of DO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "DO 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45072, "level": 5, "en": {"description": "Optional parameters of Go command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GO指令可选 参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 45073, "level": 5, "en": {"description": "Optional parameters of Move command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Move 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45074, "level": 5, "en": {"description": "Optional parameters of MoveJ command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJ 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45075, "level": 5, "en": {"description": "Optional parameter of Ecp command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Ecp 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45076, "level": 5, "en": {"description": "Optional parameters of EcpSet command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "EcpSet 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45077, "level": 5, "en": {"description": "Optional parameters of SetExicit Mode command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetExicitMode 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45078, "level": 5, "en": {"description": "Optional parameters of <PERSON><PERSON><PERSON> command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "Pallet 可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45079, "level": 5, "en": {"description": "Optional parameter CP is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数 CP 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45080, "level": 5, "en": {"description": "Optional parameter tool is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 TooL 参数错误", "cause": "", "solution": "输入正确参数"}}, {"id": 45081, "level": 5, "en": {"description": "Optional parameter user is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中的 user 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45082, "level": 5, "en": {"description": "Optional parameter speed is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 speed 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45083, "level": 5, "en": {"description": "Optional parameter SpeedS is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 SpeedS 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45084, "level": 5, "en": {"description": "Optional parameter Accel is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 Accel 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45085, "level": 5, "en": {"description": "Optional parameter AccelS is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 accels 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45086, "level": 5, "en": {"description": "Optional parameter Arch is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数 Arch 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45087, "level": 5, "en": {"description": "Optional parameter start is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 start 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45088, "level": 5, "en": {"description": "Optional parameter zlimit is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 zlimit 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45089, "level": 5, "en": {"description": "Optional parameter  end is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 end 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45090, "level": 5, "en": {"description": "Optional parameter sync is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 sync 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45091, "level": 5, "en": {"description": "Optional parameter arm is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中 arm 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45092, "level": 5, "en": {"description": "Optional parameter ForceControl is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "可选参数中的 ForceControl 错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45136, "level": 5, "en": {"description": "Optional parameters of MoveR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveR指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45137, "level": 5, "en": {"description": "Optional parameters of GoR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoR指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45138, "level": 5, "en": {"description": "Optional parameters of MoveJR command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJR指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45139, "level": 5, "en": {"description": "Optional parameters of GoIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GoIO指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45140, "level": 5, "en": {"description": "Optional parameters of MoveIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveIO指令可选参数错误 ", "cause": "", "solution": "输入正确的参数"}}, {"id": 45141, "level": 5, "en": {"description": "Optional parameters of MoveJIO command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "MoveJIO指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45142, "level": 5, "en": {"description": "Optional parameters of Path Recur command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "轨迹复现可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45312, "level": 5, "en": {"description": "Optional parameters of LoadSwitch command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "LoadSwitch指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45313, "level": 5, "en": {"description": "Optional parameters of LoadSet command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "LoadSet指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45568, "level": 5, "en": {"description": "Optional parameters of SetABZ command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetABZ指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45569, "level": 5, "en": {"description": "Optional parameter are of GetABZ command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "GetABZ()指令可选参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45824, "level": 5, "en": {"description": "Input parameters of SetToolBaudRate command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetToolBaudRate()指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45825, "level": 5, "en": {"description": "Input parameters of SetDOMode command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetDOMode()指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45826, "level": 5, "en": {"description": "Input parameters of SetToolPower command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetToolPower()指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45827, "level": 5, "en": {"description": "Input parameters of SetTool485 baud rate type error ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485() 的波特率参数类型错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45828, "level": 5, "en": {"description": "Input parameters of SetTool485 baudrate range error 1200 ~ 4500000", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485() 波特率参数的范围不对 1200 ~ 4500000", "cause": "", "solution": "输入正确的参数"}}, {"id": 45829, "level": 5, "en": {"description": "Input parameters of SetTool485 stop bits type error ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485() 的停止位的参数类型错误 ", "cause": "", "solution": "输入正确的参数"}}, {"id": 45830, "level": 5, "en": {"description": "Input parameters of SetTool485 stop bit value error ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485()指令的停止位的值错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 45831, "level": 5, "en": {"description": "Input parameters of SetTool485 parity type error need a string N/O/E", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485()指令校验位类型错误 需要一个字符串  N/O/E ", "cause": "", "solution": "输入正确的参数"}}, {"id": 45832, "level": 5, "en": {"description": "Input parameters of SetTool485 parity value error ,string N/O/E", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485()指令的值错误, 需要一个字符串 N    O  E ", "cause": "", "solution": "输入正确的参数"}}, {"id": 45833, "level": 5, "en": {"description": "Input parameters of SetTool485  number error  1 ~ 3 params.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SetTool485()指令参数数量 错误 需要输入 1 ~ 3 个 参数", "cause": "", "solution": "输入正确的参数"}}, {"id": 46096, "level": 5, "en": {"description": "Input parameters of collisionLogInfo is wrong,interger only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "collisionLogInfo() 参数错误,只允许输入整形数字", "cause": "", "solution": "输入正确的参数"}}, {"id": 46097, "level": 5, "en": {"description": "Input parameters of setCollisionLevel command is wrong,interger only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "setCollisionLevel() 参数错误,只允许输入整形", "cause": "", "solution": "输入正确的参数"}}, {"id": 46080, "level": 5, "en": {"description": "Input parameters of setExcitMod command is wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "ExcitMode() 参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 46336, "level": 5, "en": {"description": "Input parameters of StartPath command are wrong", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "StartPath()指令参数错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 46337, "level": 5, "en": {"description": "StartPath command has no input parameters", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "StartPath指令输入参数不存在", "cause": "", "solution": "输入正确的参数"}}, {"id": 49425, "level": 5, "en": {"description": "InitCam(\"CAMn\") params number error", "cause": "", "solution": "enter camer's name in string, eg. InitCam(\"CAMn\")"}, "zh_CN": {"description": "InitCam(\"CAMn\") 的 参数个数不对", "cause": "", "solution": "只输入一个相机的名称即可   eg. InitCam(\"CAMn\")"}}, {"id": 49426, "level": 5, "en": {"description": "InitCam(\"CAMn\") has recved a wrong type params. need string", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "InitCam(\"CAMn\") 的输入参数1的类型出错, 只能输入字符串", "cause": "", "solution": "输入正确的相机名称!"}}, {"id": 49427, "level": 5, "en": {"description": "InitCam(\"CAMn\") has recved a wrong camer name, can not find this camer.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "InitCam(\"CAMn\") 参数1内容错误,相机名称出错，配置文件找不到这个相机", "cause": "", "solution": "正确输入相机的名称,注意大小写！"}}, {"id": 49441, "level": 5, "en": {"description": "TriggerCam(\"CAMn\") params number err , only need one camer name.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "TriggerCam(\"CAMn\") 的参数数量错误，只输入一个相机名称", "cause": "", "solution": "输入正确的参数"}}, {"id": 49442, "level": 5, "en": {"description": "TriggerCam(\"CAMn\") params type error,string only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "TriggerCam(\"CAMn\")参数1类型错误,只允许是string", "cause": "", "solution": "输入正确的参数"}}, {"id": 49443, "level": 5, "en": {"description": "TriggerCam(\"CAMn\") params content error, can not find this camer!", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "TriggerCam(\"CAMn\") 的参数1内容错误，找不到这个相机", "cause": "", "solution": "输入正确的参数"}}, {"id": 49457, "level": 5, "en": {"description": "SendCam(\"CAMn\",\"string\") params number err , only need camer name and string to send.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SendCam(\"CAMn\",\"string\") 的参数数量错误，需要输入两个参数，一个相机名称，一个发送的字符串", "cause": "", "solution": "输入正确的参数"}}, {"id": 49458, "level": 5, "en": {"description": "SendCam(\"CAMn\",\"string\") params 1 type error,string only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SendCam(\"CAMn\",\"string\") 参数 1 类型错误,只允许是字符串string", "cause": "", "solution": "输入正确的参数"}}, {"id": 49459, "level": 5, "en": {"description": "SendCam(\"CAMn\",\"string\") params 2 type error, string only ", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SendCam(\"CAMn\",\"string\") 的参数 2 类型错误，只允许是字符串", "cause": "", "solution": "输入正确的参数"}}, {"id": 49460, "level": 5, "en": {"description": "SendCam(\"CAMn\",\"string\") params 1 content error, can not find this camer!", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "SendCam(\"CAMn\",\"string\") 参数 1内容错误 找不到这个相机", "cause": "", "solution": "输入正确的参数"}}, {"id": 49473, "level": 5, "en": {"description": "RecvCam(\"CAMn\") params number err , need one camer name.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RecvCam(\"CAMn\") 的参数数量错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 49474, "level": 5, "en": {"description": "RecvCam(\"CAMn\") params 1  type error,string only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RecvCam(\"CAMn\")参数 1 类型错误,只允许是string", "cause": "", "solution": "输入正确的参数"}}, {"id": 49475, "level": 5, "en": {"description": "RecvCam(\"CAMn\") params 2  type error,bool only", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RecvCam(\"CAMn\") 的参数2类型错误 需要一个bool 量", "cause": "", "solution": "输入正确的参数"}}, {"id": 49476, "level": 5, "en": {"description": "RecvCam(\"CAMn\",true/false) params 1 content error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RecvCam(\"CAMn\", true / false) 的参数1的内容错误 找不到这个相机", "cause": "", "solution": "输入正确的参数"}}, {"id": 49477, "level": 5, "en": {"description": "RecvCam(\"CAMn\",true/false) params 2 content error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "RecvCam(\"CAMn\", true / false) 的参数2 内容错误", "cause": "", "solution": "输入正确的参数"}}, {"id": 49489, "level": 5, "en": {"description": "DestroyCam(\"CAMn\") params number error", "cause": "", "solution": "enter camer's name in string, eg. InitCam(\"CAMn\")"}, "zh_CN": {"description": "DestroyCam(\"CAMn\") 的 参数个数不对", "cause": "", "solution": "只输入一个相机的名称即可   eg. DestroyCam(\"CAMn\")"}}, {"id": 49490, "level": 5, "en": {"description": "DestroyCam(\"CAMn\") has recved a wrong type params. need string", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "DestroyCam(\"CAMn\") 的输入参数的类型出错, 只能输入字符串", "cause": "", "solution": "输入正确的相机名称!"}}, {"id": 49491, "level": 5, "en": {"description": "DestroyCam(\"CAMn\") has recved a wrong camer name, can not find this camer.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "DestroyCam(\"CAMn\") 的相机名称出错，配置文件找不到这个相机", "cause": "", "solution": "正确输入相机的名称,注意大小写！"}}, {"id": 49664, "level": 5, "en": {"description": "Current master number is maximum, can not created.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "创建Modbus主站个数超过最大数量 4", "cause": "", "solution": "主站不能创建超过最大数量！"}}, {"id": 49665, "level": 5, "en": {"description": "Current master number is maximum, can not created.", "cause": "", "solution": "Not to create this master!"}, "zh_CN": {"description": "创建Modbus主站个数超过最大数量 4", "cause": "", "solution": "主站不能创建超过最大数量！"}}, {"id": 49666, "level": 5, "en": {"description": "Input params is error.", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "输入参数错误", "cause": "", "solution": "正确输入参数！"}}, {"id": 49667, "level": 5, "en": {"description": "Init socket failed.", "cause": "", "solution": "Please check input params."}, "zh_CN": {"description": "初始化失败！", "cause": "", "solution": "请检查输入参数！"}}, {"id": 49668, "level": 5, "en": {"description": "Connect failed.", "cause": "", "solution": "Please check network!"}, "zh_CN": {"description": "连接失败！", "cause": "", "solution": "请检查输入网络！"}}, {"id": 49669, "level": 5, "en": {"description": "Input params is error.", "cause": "", "solution": "Please check input params."}, "zh_CN": {"description": "输入参数错误", "cause": "", "solution": "请检查输入参数！"}}, {"id": 49670, "level": 5, "en": {"description": "Input params is error.", "cause": "", "solution": "Please check input params."}, "zh_CN": {"description": "输入参数错误", "cause": "", "solution": "请检查输入参数！"}}, {"id": 49671, "level": 5, "en": {"description": "Input params is error.", "cause": "", "solution": "Please check input params."}, "zh_CN": {"description": "输入参数错误", "cause": "", "solution": "请检查输入参数！"}}, {"id": 49672, "level": 5, "en": {"description": "Input params is error.", "cause": "", "solution": "Please check input params."}, "zh_CN": {"description": "输入参数错误", "cause": "", "solution": "请检查输入参数！"}}, {"id": 60944, "level": 0, "en": {"description": "Not in OP state, in boot state, the system will try to switch from station to Op.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "不在OP模式，在BOOT状态，系统将尝试切换从站到OP", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60945, "level": 0, "en": {"description": "Not in OP state, in Init state, the system will try to switch from station to Op.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "不在OP状态，在Init状态，系统将尝试切换从站到OP", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60946, "level": 0, "en": {"description": "Not in OP state, in PREOP state, the system will try to switch from station to Op.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "不在OP状态，在PREOP状态，系统将尝试切换从站到OP", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60948, "level": 0, "en": {"description": "Not in OP state, in SAFEOP state, the system will try to switch from station to Op.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "不在OP状态，在SAFEOP状态，系统将尝试切换从站到OP", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60949, "level": 0, "en": {"description": "Not in OP state, but may be offline.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "不在OP状态,可能在离线状态", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60960, "level": 0, "en": {"description": "Safety IO is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "安全IO掉线，请确认控制器与安全IO板之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60961, "level": 0, "en": {"description": "General IO is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "通用IO掉线，请确认安全IO板与通用IO板之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60962, "level": 0, "en": {"description": "Joint 1 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节1掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60963, "level": 0, "en": {"description": "Joint 2 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节2掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60964, "level": 0, "en": {"description": "Joint 3 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节3掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60965, "level": 0, "en": {"description": "Joint 4 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节4掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60966, "level": 0, "en": {"description": "Joint 5 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节5掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60967, "level": 0, "en": {"description": "Joint 6 is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "关节6掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 60968, "level": 0, "en": {"description": "Terminal IO is disconnected, please confirm the EtherCAT link between the controller and the safety IO board.", "cause": "", "solution": "Check whether the hardware is working properly, and restart the controller， or contact technical support engineer."}, "zh_CN": {"description": "末端IO掉线，请确认控制柜与本体之间的EtherCAT链路", "cause": "", "solution": "检查硬件是否正常并重新启动，或联系技术支持工程师"}}, {"id": 49713, "level": 5, "en": {"description": "the number of params of CyHelix  is error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "CyHelix 的参数个数存在错误", "cause": "", "solution": "检查输入"}}, {"id": 49714, "level": 5, "en": {"description": "the range of params of CyHelix  is error", "cause": "", "solution": "Enter the correct parameters"}, "zh_CN": {"description": "CyHelix 的参数范围存在错误", "cause": "", "solution": "检查输入"}}]