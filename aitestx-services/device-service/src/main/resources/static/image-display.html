<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像识别结果查看器</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 10px;
            margin: 0;
            width: 100%;
            height: 100vh;
            overflow-x: hidden;
        }
        .container-fluid {
            width: 100%;
            padding-right: 15px;
            padding-left: 15px;
            margin-right: auto;
            margin-left: auto;
        }
        .folder-selector {
            margin-bottom: 20px;
        }
        .image-container img {
            max-width: 400px;
            max-height: 300px;
            border: 1px solid #ddd;
            object-fit: contain;
            cursor: pointer;
        }
        .similarity-high {
            color: green;
            font-weight: bold;
        }
        .similarity-medium {
            color: orange;
            font-weight: bold;
        }
        .similarity-low {
            color: red;
            font-weight: bold;
        }
        .spinner-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }
        .table-responsive {
            width: 100%;
            overflow-x: auto;
        }
        .modal-dialog-xl {
            max-width: 90%;
            margin: 1.75rem auto;
        }
        .modal-content img {
            max-width: 100%;
            max-height: 80vh;
        }
        .img-error {
            padding: 20px;
            background-color: #f8d7da;
            color: #721c24;
            text-align: center;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        .image-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 400px;
            height: 200px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
        }
        .retry-options {
            margin-top: 10px;
            display: flex;
            gap: 8px;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <h1 class="mb-4">图像识别结果查看器</h1>
        </div>
    </div>

    <div class="row folder-selector">
        <div class="col-md-4">
            <h3>选择日期文件夹</h3>
            <select id="folderSelect" class="form-select">
                <option value="">加载中...</option>
            </select>
        </div>
    </div>

    <div id="loading" class="spinner-container">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div id="imageResults" class="table-responsive">
                <!-- 表格内容将在这里动态生成 -->
            </div>
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">查看图片</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="放大图片">
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // 获取应用的上下文路径
    const getContextPath = () => {
        // 从当前URL获取上下文路径
        const pathArray = window.location.pathname.split('/');
        if (pathArray[1] === 'AITestX') {
            return '/AITestX';
        }
        return '';
    };

    // 构建API URL，添加时间戳防止缓存
    const buildApiUrl = (endpoint) => {
        let url = `${getContextPath()}${endpoint}`;
        // 为所有请求添加时间戳，防止缓存
        url += (endpoint.includes('?') ? '&' : '?') + `t=${new Date().getTime()}`;
        return url;
    };

    // 增强的图片加载功能
    function loadImageWithFallback(imgElement, path, templateName, isTemplate, onClickHandler) {
        // 设置图片属性
        imgElement.alt = isTemplate ? '模板图片' : '原图';
        imgElement.className = 'img-fluid';
        imgElement.setAttribute('data-path', path);
        imgElement.onclick = onClickHandler;

        // 检查父节点是否存在，如果存在才添加加载指示器
        if (imgElement.parentNode) {
            // 添加加载指示器
            const loadingPlaceholder = document.createElement('div');
            loadingPlaceholder.className = 'image-loading';
            loadingPlaceholder.innerHTML = '<div class="spinner-border" role="status"></div>';
            imgElement.parentNode.insertBefore(loadingPlaceholder, imgElement);
            imgElement.style.display = 'none';

            // 记录加载开始时间，用于超时处理
            const startTime = Date.now();

            // 设置超时
            const timeoutId = setTimeout(() => {
                if (imgElement.style.display === 'none') {
                    console.warn(`图片加载超时: ${path}`);
                    if (imgElement.parentNode) { // 确保元素仍在DOM中
                        handleImageError(imgElement, templateName, isTemplate);
                    }
                    if (loadingPlaceholder.parentNode) {
                        loadingPlaceholder.remove();
                    }
                    imgElement.style.display = '';
                }
            }, 15000); // 15秒超时

            // 处理加载完成事件
            imgElement.onload = function() {
                clearTimeout(timeoutId);
                if (loadingPlaceholder.parentNode) {
                    loadingPlaceholder.remove();
                }
                imgElement.style.display = '';
                console.log(`图片加载成功: ${path} (耗时: ${Date.now() - startTime}ms)`);

                // 预加载模态框大图
                const preloadImg = new Image();
                preloadImg.src = this.src;
            };

            // 处理加载失败
            imgElement.onerror = function() {
                clearTimeout(timeoutId);
                console.warn(`尝试通过Base64加载: ${path}`);

                // 尝试使用Base64方式加载 - 注意修改了BASE_PATH问题
                fetch(buildApiUrl(`/api/images/base64-image?path=${encodeURIComponent(BASE_PATH + path)}`))
                    .then(response => {
                        if (!response.ok) throw new Error(`HTTP 错误: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.base64Data) {
                            imgElement.src = `data:image/jpeg;base64,${data.base64Data}`;
                            imgElement.style.display = '';
                            if (loadingPlaceholder.parentNode) {
                                loadingPlaceholder.remove();
                            }
                            console.log(`Base64图片加载成功: ${path}`);
                        } else {
                            throw new Error(data.message || '获取Base64图片数据失败');
                        }
                    })
                    .catch(error => {
                        console.error('Base64加载失败:', error);
                        if (imgElement.parentNode) { // 确保元素仍在DOM中
                            handleImageError(imgElement, templateName, isTemplate);
                        }
                        if (loadingPlaceholder.parentNode) {
                            loadingPlaceholder.remove();
                        }
                        imgElement.style.display = '';
                    });
            };
        } else {
            // 如果没有父节点，只设置图片属性但不添加加载指示器
            console.warn(`图片元素没有父节点，无法添加加载指示器: ${path}`);
        }

        // 尝试直接加载图片 - 使用唯一的时间戳，避免时间戳重复
        const timestamp = Date.now() + Math.floor(Math.random() * 1000);
        const directImageUrl = buildApiUrl(`/api/images/direct-image?path=${encodeURIComponent(path)}&_=${timestamp}`);
        imgElement.src = directImageUrl;
    }

    // 改进的图片错误处理
    function handleImageError(img, templateName, isTemplate) {
        // 检查元素是否仍在DOM中
        if (!img.parentNode) {
            console.error(`无法处理图片错误: 元素已不在DOM中`);
            return;
        }

        console.error(`图片加载失败: ${img.src}`);

        const container = document.createElement('div');
        container.className = 'img-error';

        // 显示更详细的错误信息
        const errorType = isTemplate ? '模板图片' : '原图';
        const errorText = document.createElement('p');
        errorText.textContent = `${errorType}加载失败: ${templateName || '未知模板'}`;
        container.appendChild(errorText);

        // 显示图片路径信息
        const pathInfo = document.createElement('div');
        pathInfo.style.fontSize = '12px';
        pathInfo.style.wordBreak = 'break-all';
        try {
            const path = img.getAttribute('data-path');
            pathInfo.textContent = path ? `路径: ${path}` : '无法获取路径信息';
        } catch (e) {
            pathInfo.textContent = '解析路径时出错';
        }
        container.appendChild(pathInfo);

        // 添加重试选项区域
        const retryOptions = document.createElement('div');
        retryOptions.className = 'retry-options';

        // 普通重试按钮
        const retryBtn = document.createElement('button');
        retryBtn.className = 'btn btn-sm btn-warning';
        retryBtn.textContent = '重试加载';
        retryBtn.onclick = function() {
            // 显示加载中状态
            container.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 重新加载中...';

            // 获取图片路径
            const path = img.getAttribute('data-path');
            if (path) {
                setTimeout(() => {
                    if (container.parentNode) { // 确保元素仍在DOM中
                        const newImg = document.createElement('img');
                        container.parentNode.replaceChild(newImg, container);
                        loadImageWithFallback(newImg, path, templateName, isTemplate, img.onclick);
                    }
                }, 500);
            } else {
                container.innerHTML = '无法获取图片路径，无法重试加载';
            }
        };
        retryOptions.appendChild(retryBtn);

        // Base64加载按钮
        const base64Btn = document.createElement('button');
        base64Btn.className = 'btn btn-sm btn-outline-secondary';
        base64Btn.textContent = 'Base64加载';
        base64Btn.onclick = function() {
            container.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 通过Base64加载中...';

            const path = img.getAttribute('data-path');
            if (path) {
                fetch(buildApiUrl(`/api/images/base64-image?path=${encodeURIComponent(BASE_PATH + path)}`))
                    .then(response => {
                        if (!response.ok) throw new Error(`HTTP错误: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        if (data.success && data.base64Data) {
                            if (container.parentNode) { // 确保元素仍在DOM中
                                const newImg = document.createElement('img');
                                newImg.src = `data:image/jpeg;base64,${data.base64Data}`;
                                newImg.alt = img.alt;
                                newImg.className = img.className;
                                newImg.onclick = img.onclick;
                                newImg.setAttribute('data-path', path);
                                container.parentNode.replaceChild(newImg, container);
                            }
                        } else {
                            throw new Error(data.message || '获取Base64图片数据失败');
                        }
                    })
                    .catch(error => {
                        if (container.parentNode) { // 确保元素仍在DOM中
                            container.innerHTML = `Base64加载失败: ${error.message}<br>`;
                            container.appendChild(retryOptions);
                        }
                    });
            } else {
                container.innerHTML = '无法获取图片路径，无法重试加载';
            }
        };
        retryOptions.appendChild(base64Btn);

        container.appendChild(retryOptions);

        // 确保元素仍在DOM中后再替换
        if (img.parentNode) {
            img.parentNode.replaceChild(container, img);
        }
    }

    // 添加常量定义
    const BASE_PATH = "D:\\FlyTest\\data\\server\\system\\images\\";
    const TEMPLATE_FILENAME = "template.jpg";
    const ORIGIN_FILENAME = "origin.jpg";

    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 加载日期文件夹
        fetchDateFolders();

        // 添加下拉框变化事件监听
        document.getElementById('folderSelect').addEventListener('change', function() {
            const selectedFolder = this.value;
            if (selectedFolder) {
                loadTemplateImages(selectedFolder);
            } else {
                document.getElementById('imageResults').innerHTML =
                    '<div class="alert alert-warning">请选择一个日期文件夹</div>';
            }
        });

        // 初始化模态框
        window.imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
    });

    // 获取所有日期文件夹
    function fetchDateFolders() {
        fetch(buildApiUrl('/api/images/date-folders'))
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                renderFolderSelect(data);
            })
            .catch(error => {
                console.error('获取日期文件夹失败:', error);
                document.getElementById('folderSelect').innerHTML =
                    '<option value="">加载失败</option>';
            });
    }

    // 渲染文件夹下拉框
    function renderFolderSelect(folders) {
        const select = document.getElementById('folderSelect');

        // 清空下拉框
        select.innerHTML = '';

        // 添加默认选项
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '请选择日期文件夹';
        select.appendChild(defaultOption);

        if (folders.length === 0) {
            const option = document.createElement('option');
            option.value = '';
            option.textContent = '没有可用的文件夹';
            option.disabled = true;
            select.appendChild(option);
            return;
        }

        // 添加每个文件夹选项
        folders.forEach((folder, index) => {
            const option = document.createElement('option');
            option.value = folder.name;
            option.textContent = folder.name;
            select.appendChild(option);

            // 默认选中第一个文件夹
            if (index === 0) {
                option.selected = true;
                loadTemplateImages(folder.name);
            }
        });
    }

    // 加载模板图片
    function loadTemplateImages(folderName) {
        document.getElementById('loading').style.display = 'flex';
        document.getElementById('imageResults').innerHTML = '';

        fetch(buildApiUrl(`/api/images/template-images?folderName=${folderName}`))
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                renderTemplateImages(data, folderName);
            })
            .catch(error => {
                console.error('加载模板图片失败:', error);
                document.getElementById('imageResults').innerHTML =
                    '<div class="alert alert-danger">加载模板图片失败: ' + error.message + '</div>';
            })
            .finally(() => {
                document.getElementById('loading').style.display = 'none';
            });
    }

    // 渲染模板图片表格
    function renderTemplateImages(templates, folderName) {
        const container = document.getElementById('imageResults');

        if (templates.length === 0) {
            container.innerHTML = `<div class="alert alert-info">文件夹 ${folderName} 中没有模板图片</div>`;
            return;
        }

        // 创建表格标题
        const tableTitle = document.createElement('h3');
        tableTitle.textContent = `文件夹: ${folderName} 的所有模板`;
        tableTitle.className = 'mt-2 mb-3';
        container.appendChild(tableTitle);

        // 创建表格
        const table = document.createElement('table');
        table.className = 'table table-striped table-bordered';

        // 创建表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr class="table-dark">
                <th style="min-width: 150px;">模板名称</th>
                <th style="min-width: 400px;">模板图片</th>
                <th style="min-width: 400px;">原图</th>
                <th style="min-width: 100px;">相似度</th>
            </tr>
        `;
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');
        templates.forEach(item => {
            const row = document.createElement('tr');

            // 模板名称列
            const templateNameCell = document.createElement('td');
            templateNameCell.textContent = item.templateName;
            row.appendChild(templateNameCell);

            // 模板图片列
            const templateImgCell = document.createElement('td');
            templateImgCell.className = 'image-container';
            if (item.templateImagePath) {
                const img = document.createElement('img');
                // 先将图片添加到DOM中
                templateImgCell.appendChild(img);

                console.log(`加载模板图片: ${item.templateImagePath} (应该以 ${TEMPLATE_FILENAME} 结尾)`);

                // 使用增强的图片加载功能
                loadImageWithFallback(
                    img,
                    item.templateImagePath,
                    item.templateName,
                    true,
                    function() {
                        const modalImg = document.getElementById('modalImage');
                        modalImg.src = this.src;
                        modalImg.onerror = function() {
                            this.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNjAiIGhlaWdodD0iMTYwIiB2aWV3Qm94PSIwIDAgMTYwIDE2MCIgZmlsbD0ibm9uZSI+CiAgPHJlY3Qgd2lkdGg9IjE2MCIgaGVpZ2h0PSIxNjAiIGZpbGw9IiNGOEQ3REEiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzcyMUMyNCIgZm9udC1zaXplPSIxNiIgZm9udC1mYW1pbHk9InN5c3RlbS11aSwtYXBwbGUtc3lzdGVtLEJsaW5rTWFjU3lzdGVtRm9udCxTZWdvZSBVSSxSb2JvdG8sVWJ1bnRhLE5vdG8gU2FucyxzYW5zLXNlcmlmIj4KICAgIOWbvueJh+WKoiDovb3lpLHotKUKICAgIDwvdGV4dD4KPC9zdmc+Cg==';
                        };
                        document.getElementById('imageModalLabel').textContent = `模板图片: ${item.templateName}`;
                        window.imageModal.show();
                    }
                );
            } else {
                templateImgCell.textContent = `无模板图片 (${TEMPLATE_FILENAME})`;
            }
            row.appendChild(templateImgCell);

            // 原图列
            const originalImgCell = document.createElement('td');
            originalImgCell.className = 'image-container';
            if (item.originalImagePath) {
                const img = document.createElement('img');
                // 先将图片添加到DOM中
                originalImgCell.appendChild(img);

                console.log(`加载原图: ${item.originalImagePath} (应该以 ${ORIGIN_FILENAME} 结尾)`);

                // 使用增强的图片加载功能
                loadImageWithFallback(
                    img,
                    item.originalImagePath,
                    item.templateName,
                    false,
                    function() {
                        const modalImg = document.getElementById('modalImage');
                        modalImg.src = this.src;
                        modalImg.onerror = function() {
                            this.src = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNjAiIGhlaWdodD0iMTYwIiB2aWV3Qm94PSIwIDAgMTYwIDE2MCIgZmlsbD0ibm9uZSI+CiAgPHJlY3Qgd2lkdGg9IjE2MCIgaGVpZ2h0PSIxNjAiIGZpbGw9IiNGOEQ3REEiLz4KICA8dGV4dCB4PSI1MCUiIHk9IjUwJSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzcyMUMyNCIgZm9udC1zaXplPSIxNiIgZm9udC1mYW1pbHk9InN5c3RlbS11aSwtYXBwbGUtc3lzdGVtLEJsaW5rTWFjU3lzdGVtRm9udCxTZWdvZSBVSSxSb2JvdG8sVWJ1bnRhLE5vdG8gU2FucyxzYW5zLXNlcmlmIj4KICAgIOWbvueJh+WKoiDovb3lpLHotKUKICAgIDwvdGV4dD4KPC9zdmc+Cg==';
                        };
                        document.getElementById('imageModalLabel').textContent = `原图: ${item.templateName}`;
                        window.imageModal.show();
                    }
                );
            } else {
                originalImgCell.textContent = `无原图 (${ORIGIN_FILENAME})`;
            }
            row.appendChild(originalImgCell);

            // 相似度列
            const similarityCell = document.createElement('td');
            const similarityValue = parseFloat(item.similarity).toFixed(4);
            let similarityClass = '';
            if (item.similarity >= 0.9) {
                similarityClass = 'similarity-high';
            } else if (item.similarity >= 0.7) {
                similarityClass = 'similarity-medium';
            } else {
                similarityClass = 'similarity-low';
            }
            similarityCell.innerHTML = `<span class="${similarityClass}">${similarityValue}</span>`;
            row.appendChild(similarityCell);

            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        container.appendChild(table);
    }
</script>
</body>
</html>
