<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.desaysv</groupId>
        <artifactId>aitestx-services</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>windows-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.microsoft.playwright</groupId>
            <artifactId>playwright</artifactId>
            <version>1.49.0</version>
        </dependency>

<!--        <dependency>-->
        <!--            <groupId>io.github.bonigarcia</groupId>-->
        <!--            <artifactId>webdrivermanager</artifactId>-->
        <!--            <version>${webdrivermanager.version}</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>slf4j-jdk14</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.httpcomponents.client5</groupId>-->
        <!--                    <artifactId>httpclient5</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>com.github.docker-java</groupId>-->
        <!--            <artifactId>docker-java-transport-httpclient5</artifactId>-->
        <!--            <version>3.2.14</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.apache.httpcomponents.client5</groupId>-->
        <!--                    <artifactId>httpclient5</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.seleniumhq.selenium</groupId>-->
        <!--            <artifactId>selenium-java</artifactId>-->
        <!--            <version>${selenium.version}</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.desaysv</groupId>
            <artifactId>aitestx-core</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.httpcomponents.client5</groupId>-->
<!--            <artifactId>httpclient5</artifactId>-->
<!--        </dependency>-->
    </dependencies>
</project>