import com.microsoft.playwright.*;

public class PlaywrightDemo {
    public static void main(String[] args) {
        try (Playwright playwright = Playwright.create()) {
            // 启动浏览器
            Browser browser = playwright.chromium().launch(
                new BrowserType.LaunchOptions()
                    .setHeadless(false)  // 设置为 false 可以看到浏览器界面
            );
            
            // 创建新的浏览器上下文
            BrowserContext context = browser.newContext();
            
            // 创建新的页面
            Page page = context.newPage();
            
            // 导航到指定网页
            page.navigate("https://www.baidu.com");
            
            // 等待搜索框出现
            page.waitForSelector("#kw");
            
            // 在搜索框中输入文字
            page.fill("#kw", "Playwright 自动化测试");
            
            // 点击搜索按钮
            page.click("#su");
            
            // 等待搜索结果加载
            page.waitForLoadState();
            
            // 获取页面标题
            String title = page.title();
            System.out.println("页面标题: " + title);
            
            // 等待 3 秒
            Thread.sleep(3000);
            
            // 关闭浏览器
            browser.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}