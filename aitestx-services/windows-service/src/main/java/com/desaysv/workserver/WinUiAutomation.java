package com.desaysv.workserver;

import com.sun.jna.Pointer;
import com.sun.jna.platform.win32.User32;
import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.platform.win32.WinUser;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.event.InputEvent;

@Slf4j
public class WinUiAutomation {

    @Getter
    private static WinUiAutomation instance = new WinUiAutomation();
    // 创建 Robot 对象
    private Robot robot;

    public WinUiAutomation() {
        try {
            robot = new Robot();
        } catch (AWTException e) {
            log.error(e.getMessage(), e);
        }
    }

    public WinUiAutomation bringToFront(String hwndName) {
        log.info("窗口置于前端:{}",hwndName);
        // 根据窗口句柄获取 Element 对象
        WinDef.HWND hwnd = new WinDef.HWND();
        hwnd.setPointer(Pointer.createConstant(Long.parseLong(hwndName, 16)));
        WinUser.WINDOWPLACEMENT placement = new WinUser.WINDOWPLACEMENT();
        User32.INSTANCE.GetWindowPlacement(hwnd, placement);

        int showCmd;
        if (placement.showCmd == User32.SW_SHOWMAXIMIZED) {
            showCmd = User32.SW_SHOWMAXIMIZED;
        } else {
            showCmd = User32.SW_RESTORE;
        }

        User32.INSTANCE.ShowWindow(hwnd, showCmd);
        User32.INSTANCE.SetForegroundWindow(hwnd);
        return this;
    }

    public WinUiAutomation click(int x, int y) {
        // 移动鼠标到指定位置并点击
        log.info("PC点击{},{}", x, y);
        robot.mouseMove(x, y);
        robot.mousePress(InputEvent.BUTTON1_DOWN_MASK);
        robot.mouseRelease(InputEvent.BUTTON1_DOWN_MASK);
        return this;
    }

    public static void main(String[] args) {
        new WinUiAutomation().bringToFront("00260C56").click(1, 2);
    }


}
