package com.desaysv.workserver;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.WaitUntilState;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.awt.*;

@Slf4j
@Data
public class WebBrowserManager {

    public enum WebDriverType {
        FIREFOX, CHROME
    }

    private WebDriverType webDriverType = WebDriverType.CHROME;

    @Getter
    private static WebBrowserManager instance = new WebBrowserManager();

    private Playwright playwright;
    private Browser browser;
    private Page page;

    private abstract class WebBrowserExceptionTemplate {
        public final void process() throws OperationFailNotification {
            try {
                printLine();
                log.info("当前页面标题:{}", getPage().title());
                doProcess();
            } catch (PlaywrightException e) {
                OperationResult operationResult = OperationResult.staticFail();
                operationResult.setMessage("浏览器异常:\n" + e.getMessage());
                operationResult.setData(e);
                throw new OperationFailNotification(operationResult);
            }
        }

        private void printLine() {
            log.info("~~~~~~~~~~~~~开始浏览器操作~~~~~~~~~~~~~");
        }

        protected abstract void doProcess() throws OperationFailNotification;
    }

    private void configBrowser(WebDriverType webDriverType) {
        log.info("正在配置{} playwright...", webDriverType.name());
        playwright = Playwright.create();

        BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                .setHeadless(false);

        if (webDriverType.equals(WebDriverType.FIREFOX)) {
            browser = playwright.firefox().launch(launchOptions);
        } else {
            browser = playwright.chromium().launch(launchOptions);
        }
//        browser.onDisconnected(b -> {
//            log.warn("浏览器已断开连接 (disconnected event)");
//            browser = null;
//            page = null;
//            playwright = null;
//        });
        page = browser.newPage();
        log.info("{} playwright配置完成", webDriverType.name());
    }

    public Page getPage() {
        if (page == null || browser == null || playwright == null) {
            log.info("浏览器未初始化，开始配置...");
            configBrowser(webDriverType);
            return page;
        }
        try {
            // 显式检查浏览器连接状态
            if (page.isClosed() || !browser.isConnected()) {
                throw new PlaywrightException("浏览器已断开连接");
            }
            // 测试页面是否可用
            page.title();
        } catch (PlaywrightException e) {
            log.warn("浏览器已关闭或状态异常，重新初始化... 异常信息: {}", e.getMessage());
            try {
                // 清理可能的资源
                if (page != null) {
                    page.close();
                }
                if (browser != null) {
                    browser.close();
                }
                if (playwright != null) {
                    playwright.close();
                }
            } catch (Exception ex) {
                log.warn("清理旧浏览器资源时出现异常", ex);
            }
            // 重新配置浏览器
            configBrowser(webDriverType);
        }
        return page;
    }

    private String getSelector(String xPath) {
        return "xpath=" + xPath;
    }

    private void selectTab(String title) throws OperationFailNotification {
        for (Page p : browser.contexts().get(0).pages()) {
            if (p.title().contains(title)) {
                page = p;
                log.info("切换到页面\"{}\"成功", page.title());
                return;
            }
        }
        throw new OperationFailNotification("未找到目标页面: " + title);
    }

    public void closeTab(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() throws OperationFailNotification {
                String tabName = browserAction.getTabName();
                log.info("关闭标签页 - 目标标签页:\"{}\"", tabName);
                for (Page p : browser.contexts().get(0).pages()) {
                    if (p.title().contains(tabName)) {
                        p.close();
                        log.info("关闭标签页成功");
                        if (!browser.contexts().get(0).pages().isEmpty()) {
                            page = browser.contexts().get(0).pages().get(0);
                            log.info("切换到第一个标签页 - 当前标签页:\"{}\"", page.title());
                        }
                        return;
                    }
                }
                log.error("关闭标签页失败 - 未找到目标标签页");
                throw new OperationFailNotification("未找到要关闭的页面: " + tabName);
            }
        }.process();
    }

    public void switchToTab(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() throws OperationFailNotification {
                log.info("切换标签页 - 目标标签页:\"{}\"", browserAction.getTabName());
                selectTab(browserAction.getTabName());
                log.info("切换标签页成功");
            }
        }.process();
    }

    public void maximize() throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() {
                log.info("浏览器窗口最大化");
                Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
                page.setViewportSize((int) screenSize.getWidth(), (int) screenSize.getHeight());
                log.info("浏览器窗口最大化完成 - 尺寸:{}x{}", screenSize.getWidth(), screenSize.getHeight());
            }
        }.process();
    }

    public void checkText(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() throws OperationFailNotification {
                String expectedText = browserAction.getInputText();
                String xpath = browserAction.getXPath();

                log.info("检查文本 - 目标文本:\"{}\", 元素定位:{}", browserAction.getInputText(), xpath);
                // 添加显式等待，确保元素可见
                ElementHandle element = getPage().waitForSelector(getSelector(xpath),
                        new Page.WaitForSelectorOptions().setTimeout(15000));

                if (element != null) {
                    // 打印更多调试信息
                    log.info("元素标签: {}", element.evaluate("el => el.tagName"));
                    log.info("元素 value: {}", element.getAttribute("value"));
                    log.info("元素 textContent: {}", element.textContent());
                    log.info("元素 innerHTML: {}", element.evaluate("el => el.innerHTML"));

                    // 更灵活的文本匹配
                    String actualText = element.getAttribute("value");
                    if (actualText == null) {
                        actualText = element.textContent();
                    }

                    log.info("实际文本内容为: {}", actualText);

                    if (actualText == null || !actualText.contains(expectedText)) {
                        log.error("检查文本失败 - 未找到目标文本");
                        throw new OperationFailNotification(String.format("未找到目标字符串:%s", expectedText));
                    }
                    log.info("检查文本成功 - 找到匹配文本:{}", expectedText);
                } else {
                    log.error("检查文本失败 - 未找到元素");
                    throw new OperationFailNotification("未找到元素");
                }
            }
        }.process();
    }


    public void openUrl(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() {
                log.info("打开URL - {}", browserAction.getUrl());
                // 配置导航选项：超时60秒
                Page.NavigateOptions options = new Page.NavigateOptions()
                        .setTimeout(60000)  // 超时时间设为60秒
                        .setWaitUntil(WaitUntilState.DOMCONTENTLOADED);
                getPage().navigate(browserAction.getUrl(), options);
                log.info("打开URL成功");
            }
        }.process();
        maximize();
    }

    public void click(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() {
                log.info("点击元素 - 元素定位:{}", browserAction.getXPath());
                getPage().click(getSelector(browserAction.getXPath()));
                log.info("点击元素成功");
            }
        }.process();
    }

    public void inputText(BrowserAction browserAction) throws OperationFailNotification {
        new WebBrowserExceptionTemplate() {
            @Override
            public void doProcess() {
                log.info("输入文本 - 文本内容:\"{}\", 元素定位:{}", browserAction.getInputText(), browserAction.getXPath());
                getPage().fill(getSelector(browserAction.getXPath()), browserAction.getInputText());
                log.info("输入文本成功");
            }
        }.process();
    }

    public static void main(String[] args) throws InterruptedException {
        WebBrowserManager manager = WebBrowserManager.getInstance();
        Page page = manager.getPage();
        page.navigate("https://www.baidu.com", new Page.NavigateOptions().setWaitUntil(WaitUntilState.DOMCONTENTLOADED));
        page.fill("#kw", "Playwright");
        page.click("#su");
        page.close();
        manager.playwright.close();
    }
}