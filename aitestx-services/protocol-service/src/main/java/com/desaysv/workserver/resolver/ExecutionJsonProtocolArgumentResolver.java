package com.desaysv.workserver.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.ExecutionJsonProtocol;
import com.desaysv.workserver.base.operation.base.ExecutionJsonObject;
import com.desaysv.workserver.protocol.ExecutionProtocolFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-22 18:18
 * @description :
 * @modified By :
 * @since : 2022-3-22
 */
@Component
//no lazy
public class ExecutionJsonProtocolArgumentResolver extends JsonProtocolArgumentResolver {

    @Autowired
    private ExecutionProtocolFactory executionProtocolFactory;

    public ExecutionJsonProtocolArgumentResolver() {
        addResolver(this);
    }


    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(ExecutionJsonProtocol.class);
    }


    @Override
    public Object resolveArgument(MethodParameter parameter, @Nullable ModelAndViewContainer mvcContainer, NativeWebRequest nativeWebRequest, @Nullable WebDataBinderFactory binderFactory) throws Exception {
        ExecutionJsonObject executionJsonObject = JSONObject.parseObject(getRequestString(nativeWebRequest), ExecutionJsonObject.class);
        return executionProtocolFactory.product(executionJsonObject);
    }
}
