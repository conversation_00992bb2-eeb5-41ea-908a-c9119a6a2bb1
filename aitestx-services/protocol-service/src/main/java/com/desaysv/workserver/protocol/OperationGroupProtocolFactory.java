package com.desaysv.workserver.protocol;

import com.desaysv.workserver.base.operation.base.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 14:02
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Component
@Slf4j
@Lazy
public class OperationGroupProtocolFactory implements ProtocolFactory {

    @Autowired
    private OperationProtocolFactory operationProtocolFactory;

    @Override
    public OperationGroup product(ActionJsonObject actionJsonObject) {
        OperationGroupJsonObject operationGroupJsonObject = (OperationGroupJsonObject) actionJsonObject;
//        log.debug("operationJsonObject:{}", operationJsonObject);
        OperationGroup operationGroup = new OperationGroup();
        operationGroup.setProjectName(operationGroupJsonObject.getProjectName());
        operationGroup.setGroupName(operationGroupJsonObject.getGroupName());
        List<Operation> operationList = new ArrayList<>();
        for (OperationJsonObject operationJsonObject : operationGroupJsonObject.getOperationList()) {
            operationList.add(operationProtocolFactory.product(operationJsonObject));
        }
        operationGroup.setOperationList(operationList);
        return operationGroup;
    }
}
