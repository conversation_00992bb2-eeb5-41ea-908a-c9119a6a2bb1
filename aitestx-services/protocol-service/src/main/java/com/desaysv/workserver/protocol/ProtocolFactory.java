package com.desaysv.workserver.protocol;

import com.desaysv.workserver.base.operation.base.JsonAction;
import com.desaysv.workserver.base.operation.base.ActionJsonObject;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 14:01
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
public interface ProtocolFactory {

    JsonAction product(ActionJsonObject actionJsonObject);

}


