package com.desaysv.workserver.protocol;

import com.desaysv.workserver.base.execution.Execution;
import com.desaysv.workserver.base.operation.base.ActionJsonObject;
import com.desaysv.workserver.base.operation.base.ExecutionJsonObject;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationJsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 14:02
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Component
@Slf4j
@Lazy
public class ExecutionProtocolFactory implements ProtocolFactory {

    @Autowired
    private OperationProtocolFactory operationProtocolFactory;

    @Override
    public Execution product(ActionJsonObject actionJsonObject) {
        ExecutionJsonObject executionJsonObject = (ExecutionJsonObject) actionJsonObject;
        Execution execution = new Execution();
        execution.setTestCycle(executionJsonObject.getTestCycle());
        execution.setExecutionIndex(executionJsonObject.getExecutionIndex());
        execution.setOperationContext(executionJsonObject.getOperationContext());
        List<Operation> operationList = new ArrayList<>();
        for (OperationJsonObject operationJsonObject : executionJsonObject.getOperationList()) {
            Operation operation = operationProtocolFactory.product(operationJsonObject);
            operationList.add(operation);
        }
        execution.setOperationList(operationList);
        return execution;
    }
}
