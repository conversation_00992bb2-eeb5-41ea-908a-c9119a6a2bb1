package com.desaysv.workserver.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.OperationJsonProtocol;
import com.desaysv.workserver.base.operation.base.OperationJsonObject;
import com.desaysv.workserver.protocol.OperationProtocolFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 13:54
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
@Component
//no lazy
public class OperationJsonProtocolArgumentResolver extends JsonProtocolArgumentResolver {

    @Autowired
    private OperationProtocolFactory operationProtocolFactory;

    public OperationJsonProtocolArgumentResolver() {
        addResolver(this);
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(OperationJsonProtocol.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        OperationJsonObject operation = JSONObject.parseObject(getRequestString(nativeWebRequest), OperationJsonObject.class);
        return operationProtocolFactory.product(operation);
    }
}
