package com.desaysv.workserver.service.impl;

import com.desaysv.workserver.mapper.TestcaseFileMapper;
import com.desaysv.workserver.model.TestScriptFile;
import com.desaysv.workserver.service.TestCaseFileService;
import com.desaysv.workserver.bo.TestScriptFileSelector;
import com.desaysv.workserver.vo.testcase.TestcaseFileVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-3 10:55
 * @description :
 * @modified By :
 * @since : 2022-8-3
 */
@Service
@Lazy
public class TestCaseFileServiceImpl implements TestCaseFileService {

    @Autowired
    private TestcaseFileMapper testcaseFileMapper;

    @Override
    public boolean addTestCaseFile(TestScriptFile testScriptFile) {
        return testcaseFileMapper.insert(testScriptFile) > 0;
    }

    @Override
    public boolean updateTestCaseFile(TestScriptFile testScriptFile) {
        return testcaseFileMapper.updateByTestScriptFileUUID(testScriptFile) > 0;
    }

    @Override
    public TestScriptFile getFileByTestCaseFileUUID(String testCaseFileUUID) {
        return testcaseFileMapper.selectByTestCaseFileUUID(testCaseFileUUID);
    }

    @Override
    public TestScriptFile getFileByCondition(TestcaseFileVo testcaseFileVo) {
        return testcaseFileMapper.selectByCondition(testcaseFileVo);
    }

    @Override
    public List<TestScriptFile> getAllTestCaseFiles(TestcaseFileVo testcaseFileVo) {
        return testcaseFileMapper.selectAll(testcaseFileVo);
    }

    @Override
    public boolean deleteTestCaseFile(String testCaseFileUUID) {
        return testcaseFileMapper.deleteByTestCaseFileUUID(testCaseFileUUID) > 0;
    }

    @Override
    public boolean clearAllTestCaseFiles(TestcaseFileVo testcaseFileVo) {
        return testcaseFileMapper.deleteAll(testcaseFileVo) >= 0;
    }

    @Override
    public boolean updateAllSelected(TestScriptFileSelector testScriptFileSelector) {
        return testcaseFileMapper.updateAllSelected(testScriptFileSelector) > 0;
    }
}
