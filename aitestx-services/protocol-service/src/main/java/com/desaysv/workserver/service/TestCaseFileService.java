package com.desaysv.workserver.service;

import com.desaysv.workserver.model.TestScriptFile;
import com.desaysv.workserver.bo.TestScriptFileSelector;
import com.desaysv.workserver.vo.testcase.TestcaseFileVo;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-8-3 10:52
 * @description :
 * @modified By :
 * @since : 2022-8-3
 */
public interface TestCaseFileService {

    boolean addTestCaseFile(TestScriptFile testScriptFile);

    boolean updateTestCaseFile(TestScriptFile testScriptFile);

    TestScriptFile getFileByTestCaseFileUUID(String testCaseFileUUID);

    TestScriptFile getFileByCondition(TestcaseFileVo testcaseFileVo);

    List<TestScriptFile> getAllTestCaseFiles(TestcaseFileVo testcaseFileVo);

    boolean deleteTestCaseFile(String testCaseFileUUID);

    boolean clearAllTestCaseFiles(TestcaseFileVo testcaseFileVo);

    boolean updateAllSelected(TestScriptFileSelector testScriptFileSelector);
}
