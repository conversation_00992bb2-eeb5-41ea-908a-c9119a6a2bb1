package com.desaysv.workserver.stream.video;

import com.desaysv.workserver.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.*;
import org.springframework.scheduling.annotation.Async;

import javax.imageio.ImageIO;
import javax.websocket.EncodeException;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

@Slf4j
public class MediaTransfer {
    private static FrameGrabber grabber;
    private static boolean isStart = false;

    @Async
    public void live() {
//        log.info("连接相机：" + deviceNumber + ",开始创建grabber");
        grabber = createGrabber("src/main/resources/videos/4k.mp4");
        startCameraPush();
    }

    private FFmpegFrameGrabber createGrabber(String filePath) {
        FFmpegFrameGrabber grabber;
        grabber = new FFmpegFrameGrabber(filePath);
        return grabber;
    }

    private OpenCVFrameGrabber createGrabber(int deviceNumber) {
        OpenCVFrameGrabber grabber = null;
        try {
            grabber = OpenCVFrameGrabber.createDefault(deviceNumber);
        } catch (FrameGrabber.Exception e) {
            log.error(e.getMessage(), e);
        }
        return grabber;
    }

    private byte[] imageToBytes(BufferedImage bImage, String format) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            ImageIO.write(bImage, format, out);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
        return out.toByteArray();
    }

    private int getInterval(FrameGrabber grabber) {
        double frameRate = grabber.getFrameRate();
        System.out.println("frameRate:" + frameRate);
        if (frameRate == 0.0) {
            frameRate = 30;
        }
        // 假设一秒钟15帧，那么两帧间隔就是(1000/15)毫秒
        int interVal = (int) (1000 / frameRate);
        // 发送完一帧后sleep的时间，不能完全等于(1000/frameRate)，不然会卡顿，
        // 要更小一些，这里取八分之一
        interVal /= 8;
        return interVal;
    }

    private void startCameraPush() {
        Java2DFrameConverter java2DFrameConverter = new Java2DFrameConverter();
        while (true) {
//            if (grabber == null) {
//                log.info("重试连接相机：" + deviceNumber + ",开始创建grabber");
//                grabber = createGrabber(deviceNumber);
//                log.info("创建grabber成功");
//            }
            try {
                if (grabber != null && !isStart) {
                    grabber.start();
                    isStart = true;
                    log.info("启动grabber成功");
                }
                if (grabber != null) {
                    Frame frame = grabber.grabFrame();
                    if (null == frame) {
                        continue;
                    }
                    BufferedImage bufferedImage = java2DFrameConverter.getBufferedImage(frame);
                    byte[] bytes = imageToBytes(bufferedImage, "jpg");
                    WebSocketServer.sendAllByObject(new Image(bytes));
                }
            } catch (FrameGrabber.Exception | RuntimeException e) {
                log.error(e.getMessage(), e);
                if (grabber != null) {
                    try {
                        grabber.stop();
                    } catch (FrameGrabber.Exception ex) {
                        log.error(ex.getMessage(), ex);
                    } finally {
                        grabber = null;
                        isStart = false;
                    }
                }
                break;
            } catch (EncodeException | IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}