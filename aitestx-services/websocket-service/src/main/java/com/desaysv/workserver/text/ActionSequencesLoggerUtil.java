package com.desaysv.workserver.text;

import com.desaysv.workserver.entity.LogMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/8/7 15:29
 * @description : TODO://是否需要加队列，以及一个线程池
 * @modified By :
 * @since : 2023/8/7
 **/
@Slf4j
public class ActionSequencesLoggerUtil {
    private static LogProcessor logProcessor;

    static {
        WebSocketLogSender sender = new WebSocketLogSender();
        initialize(sender);
    }

    private static void initialize(LogSender logSender) {
        logProcessor = new LogProcessor(logSender);
    }

    // Info level logging
    public static void info(String format, Object... objects) {
        outputLog("info", format, objects);
    }

    // Debug level logging
    public static void debug(String format, Object... objects) {
        outputLog("debug", format, objects);
    }

    // Warn level logging
    public static void warn(String format, Object... objects) {
        outputLog("warn", format, objects);
    }

    // Error level logging
    public static void error(String format, Object... objects) {
        outputLog("error", format, objects);
    }


    private static void outputLog(String level, String format, Object... objects) {
        level = level.toLowerCase();
        // 根据指定的日志级别输出日志
        switch (level) {
            case "info":
                log.info(format, objects);
                break;
            case "debug":
                log.debug(format, objects);
                break;
            case "warn":
                log.warn(format, objects);
                break;
            case "error":
                log.error(format, objects);
                break;
            default:
                log.info(format, objects);
                break;
        }

        String message;
        try {
            String formatStr = format.replaceAll("\\{}", "%s");
            String result = formatStr.replaceAll("%", "%%"); // 转义所有%字符
//            String result = formatStr.replaceAll("(?<!%)%(?![A-Za-z])", "%%");
            message = String.format(result, objects);
        } catch (Exception e) {
            // 如果格式化失败，直接拼接原始字符串和参数
            message = format + " " + Arrays.toString(objects);
        }
        sendLogInfo(level, message);
    }

    private static void sendLogInfo(String level, String message) {
        // 使用实例方法
        if (logProcessor != null) {
            logProcessor.queueLogMessage(new LogMessage(level, message));
        }
    }

}