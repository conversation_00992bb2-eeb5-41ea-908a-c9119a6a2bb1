package com.desaysv.workserver.text;

import org.jetbrains.annotations.NotNull;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

public class TextWebSocketManager extends TextWebSocketHandler {

    private final static ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final static ConcurrentHashMap<String, Consumer<String>> responseCallbacks = new ConcurrentHashMap<>();
    private final static ConcurrentHashMap<String, CompletableFuture<String>> responseFutures = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String clientId = Objects.requireNonNull(session.getUri()).getQuery().split("=")[1];
        sessions.put(clientId, session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String clientId = Objects.requireNonNull(session.getUri()).getQuery().split("=")[1];
        sessions.remove(clientId);
        responseCallbacks.remove(clientId);
        CompletableFuture<String> future = responseFutures.remove(clientId);
        if (future != null) {
            future.completeExceptionally(new Exception("Connection closed"));
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, @NotNull Throwable exception) throws Exception {
        String clientId = Objects.requireNonNull(session.getUri()).getQuery().split("=")[1];
        Consumer<String> callback = responseCallbacks.remove(clientId);
        if (callback != null) {
            callback.accept("Error: " + exception.getMessage());
        }
        CompletableFuture<String> future = responseFutures.remove(clientId);
        if (future != null) {
            future.completeExceptionally(exception);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String clientId = Objects.requireNonNull(session.getUri()).getQuery().split("=")[1];
        String payload = message.getPayload();
        Consumer<String> callback = responseCallbacks.remove(clientId);
        if (callback != null) {
            callback.accept(payload);
        }
        CompletableFuture<String> future = responseFutures.remove(clientId);
        if (future != null) {
            future.complete(payload);
        }
    }

    public static void sendAndRecv(String clientId, String message, Consumer<String> callback) throws Exception {
        WebSocketSession session = sessions.get(clientId);
        if (session == null) {
            throw new IllegalArgumentException("Client not found");
        }
        responseCallbacks.put(clientId, callback);
        session.sendMessage(new TextMessage(message));
    }

    public static String sendAndRecv(String clientId, String message, long timeout) throws Exception {
        return sendAndRecv(clientId, message, timeout, TimeUnit.SECONDS);
    }

    public static String sendAndRecv(String clientId, String message, long timeout, TimeUnit unit) throws Exception {
        WebSocketSession session = sessions.get(clientId);
        if (session == null) {
            throw new IllegalArgumentException("text websocket client not found");
        }

        CompletableFuture<String> future = new CompletableFuture<>();
        responseFutures.put(clientId, future);
        session.sendMessage(new TextMessage(message));

        try {
            return timeout == -1 ? future.get() : future.get(timeout, unit);
        } catch (TimeoutException e) {
            responseFutures.remove(clientId);
            throw new TimeoutException("Response timeout after " + timeout + " " + unit.toString().toLowerCase());
        }
    }


}