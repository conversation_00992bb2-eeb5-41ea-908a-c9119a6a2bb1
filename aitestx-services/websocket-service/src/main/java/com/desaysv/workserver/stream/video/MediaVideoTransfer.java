package com.desaysv.workserver.stream.video;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.*;

import java.io.OutputStream;

@Slf4j
public class MediaVideoTransfer {
    @Setter
    private OutputStream outputStream;

    @Setter
    private String rtspTransportType;

    private FrameGrabber grabber;

    private FrameRecorder recorder;

    private boolean isStart = false;

    /**
     * 开启获取rtsp流
     */
    public void live() {
        boolean isSuccess = createGrabber();
        if (isSuccess) {
            log.info("创建grabber成功");
        } else {
            log.info("创建grabber失败");
        }
        startCameraPush();
    }

    /**
     * 构造视频抓取器
     *
     * @return 创建成功与否
     */
    private boolean createGrabber() {
        // 获取视频源
        try {
            grabber = OpenCVFrameGrabber.createDefault(0);
            grabber.setOption("rtsp_transport", rtspTransportType);
            grabber.start();
            isStart = true;

            recorder = new FFmpegFrameRecorder(outputStream, grabber.getImageWidth(), grabber.getImageHeight());
//            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            System.out.println("outputStream:" + outputStream);
            recorder.setFormat("flv");
            recorder.setFrameRate(grabber.getFrameRate());
            recorder.setSampleRate(grabber.getSampleRate());
            recorder.setAudioChannels(grabber.getAudioChannels());
            recorder.setFrameRate(grabber.getFrameRate());
            return true;
        } catch (FrameGrabber.Exception e) {
            log.error(e.getMessage(), e);
            stop();
            reset();
            return false;
        }
    }

    /**
     * 推送图片（摄像机直播）
     */
    private void startCameraPush() {
        if (grabber == null) {
            boolean isSuccess = createGrabber();
            if (isSuccess) {
                log.info("创建grabber成功");
            } else {
                log.info("创建grabber失败");
            }
        }
        try {
            if (grabber != null) {
                recorder.start();
                Frame frame;
                while (isStart && (frame = grabber.grabFrame()) != null) {
                    recorder.setTimestamp(grabber.getTimestamp());
                    recorder.record(frame);
                }
                stop();
                reset();
            }
        } catch (FrameGrabber.Exception | RuntimeException | FrameRecorder.Exception e) {
            log.error(e.getMessage(), e);
            stop();
            reset();
        }
    }

    private void stop() {
        try {
            if (recorder != null) {
                recorder.stop();
            }
            if (grabber != null) {
                grabber.stop();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void reset() {
        recorder = null;
        grabber = null;
        isStart = false;
    }
}
