package com.desaysv.workserver.stream.video;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.PostConstruct;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

// ITestX/videos/getVideo
@Slf4j
public class WsVideoStreamController {
    AtomicInteger sign = new AtomicInteger();
    ConcurrentHashMap<Integer, String> pathMap = new ConcurrentHashMap<>();
    ConcurrentHashMap<Integer, PipedOutputStream> outputStreamMap = new ConcurrentHashMap<>();
    ConcurrentHashMap<Integer, PipedInputStream> inputStreamMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        putVideoPath("src/main/resources/videos/4k.mp4");
    }

    public void putVideoPath(String path) {
        try {
            int id = 1;
            pathMap.put(id, path);
            PipedOutputStream pipedOutputStream = new PipedOutputStream();
            PipedInputStream pipedInputStream = new PipedInputStream();
            pipedOutputStream.connect(pipedInputStream);
            outputStreamMap.put(id, pipedOutputStream);
            inputStreamMap.put(id, pipedInputStream);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @GetMapping("/getVideo")
    public void getVideo(HttpServletRequest request, HttpServletResponse response) {
        int id = 1;
        String path = pathMap.get(1);
        response.addHeader("Content-Disposition", "attachment;filename=" + path + ".flv");
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            System.out.println("ServletOutputStream:" + outputStream);
            write(id, outputStream);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

    }

    private void write(int id, OutputStream outputStream) {
        try {
            PipedOutputStream pipedOutputStream = outputStreamMap.get(id);
            new Thread(() -> {
                MediaVideoTransfer mediaVideoTransfer = new MediaVideoTransfer();
                mediaVideoTransfer.setOutputStream(outputStream);
                mediaVideoTransfer.setRtspTransportType("udp");
                mediaVideoTransfer.live();
            }).start();

            print(inputStreamMap.get(id), outputStream);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            close(inputStreamMap.get(id), outputStreamMap.get(id), outputStream);
        }

    }

    private void print(InputStream inputStream, OutputStream outputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int length;
        while ((length = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, length);
        }
    }

    private void close(Closeable... closeables) {
        for (Closeable closeable : closeables) {
            if (closeable != null) {
                try {
                    closeable.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

}