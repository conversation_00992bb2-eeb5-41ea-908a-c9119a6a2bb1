package com.desaysv.workserver.stream.video;

import lombok.extern.slf4j.Slf4j;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-18 9:28
 * @description :
 * @modified By :
 * @since : 2022-2-18
 */
@Slf4j
public class VideoStreamReader {

    public static String readImageBase64(String filePath) throws IOException {
        byte[] data = readImage(filePath);
        // 对字节数组进行Base64编码，得到Base64编码的字符串
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);

    }

    public static byte[] readImage(String filePath) throws IOException {
        BufferedImage bi = ImageIO.read(new File(filePath));
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(bi, "png", out);
        byte[] mData = out.toByteArray();
        out.close();
        return mData;
    }

    public static byte[] readBytes(String filePath) throws IOException {
        File file = new File(filePath);
        long fileSize = file.length();
        FileInputStream fis = new FileInputStream(file);
        byte[] buffer = new byte[(int) fileSize];
        int offset = 0;
        int numRead = 0;
        while (offset < buffer.length && (numRead = fis.read(buffer, offset, buffer.length - offset)) >= 0) {
            offset += numRead;
        }

        if (offset != buffer.length) {
            throw new IOException("Could not completely read file " + file.getName());
        }
        fis.close();
        return buffer;
    }

    public static void main(String[] args) {
        try {
            String bytes = VideoStreamReader.readImageBase64("src/main/resources/big.png");
            System.out.println(bytes.length());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
