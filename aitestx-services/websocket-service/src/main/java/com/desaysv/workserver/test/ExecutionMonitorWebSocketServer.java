package com.desaysv.workserver.test;

import com.desaysv.workserver.WebSocketServer;
import com.desaysv.workserver.WebSocketServerListener;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;


/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-25 4:19
 * @description :
 * @modified By :
 * @since : 2022-7-25
 */
@Slf4j
@Component
@Lazy
@ServerEndpoint(value = "/monitor/execution")
public class ExecutionMonitorWebSocketServer extends WebSocketServer {
    private final Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    private static String sharedSessionId;
    private static final Lock lock = new ReentrantLock();
    private static final Condition condition = lock.newCondition();
    private static final Map<String, WebSocketServerListener> webSocketServerHandlerMap = new HashMap<>();

    public static WebSocketServer getExecutionMonitorWebSocketServer(WebSocketServerListener handler) {
        if (sharedSessionId == null) {
            try {
                lock.lock();
                log.info("等待websocket连接");
                condition.await();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
            } finally {
                lock.unlock();
            }
        }
        addWebSocketListener(handler);
        webSocketServerHandlerMap.putIfAbsent(sharedSessionId, handler);
        log.info("websocket sessionId:{}", sharedSessionId);
        return getWebSocketMap().get(sharedSessionId);
    }

    @OnOpen
    public void onOpen(Session session) {
        sharedSessionId = session.getId();
        log.info("新的WebSocket连接: {}", session.getId());
        super.onOpen(session);
        log.info("ExecutionMonitorWebSocketServer:{}", this);
        log.info("ExecutionMonitorWebSocketServer:{}", session);
        log.info("ExecutionMonitorWebSocketServer:{}", session.getId());
        log.info("ExecutionMonitorWebSocketServer:{}", Thread.currentThread());
        try {
            lock.lock();
            condition.signalAll();
        } finally {
            lock.unlock();
        }
    }

    @OnClose
    public void onClose(Session session) {
        super.onClose(session);
        log.info("WebSocket连接关闭: {}", session.getId());
        WebSocketServerListener handler = webSocketServerHandlerMap.get(sharedSessionId);
        if (handler != null) {
            removeWebSocketListener(handler);
        }
        webSocketServerHandlerMap.remove(sharedSessionId);
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        log.info("from {}'s message:{}", session, message);
    }

    @OnError
    public void onError(Session session, Throwable error) {
        super.onError(session, error);
    }


    /**
     * 发送ws消息（异步）
     * FIXME：保证ws每次发送的size一样
     *
     * @param executionPreview
     */
    public void sendExecutionPreview(final ExecutionPreview executionPreview) {
        sendExecutionPreview(executionPreview, true);
    }

    public void sendExecutionPreview(final ExecutionPreview executionPreview, boolean async) {
        log.info("发送{}通知到客户端，第{}行", executionPreview.getStatus(), executionPreview.getNotification().getPosition());
        String message = gson.toJson(executionPreview);
        if (async) {
            asyncSendMessageByText(message);
        } else {
            syncSendMessageByText(message);
        }

    }

}
