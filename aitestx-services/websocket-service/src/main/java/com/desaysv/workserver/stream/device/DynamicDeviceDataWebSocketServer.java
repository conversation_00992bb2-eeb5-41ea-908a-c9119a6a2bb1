package com.desaysv.workserver.stream.device;

import com.desaysv.workserver.stream.config.WebSocketConfigurator;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

/**
 * 设备数据WebSocket分发(设置监控类型)
 */
@ServerEndpoint(value = "/monitor/device/{monitorType}/{deviceAliasName}",
        encoders = {JsonDataEncoder.class},
        configurator = WebSocketConfigurator.class)
@Component
@Lazy
public class DynamicDeviceDataWebSocketServer extends DeviceDataWebSocketServer {

    @OnOpen
    public void onOpen(Session session,
                       @PathParam("monitorType") String monitorType,
                       @PathParam("deviceAliasName") String deviceAliasName) {
        super.onOpen(session, monitorType, deviceAliasName);
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        super.onMessage(session, message);
    }

    @OnClose
    public void onClose(Session session) {
        super.onClose(session);
    }

    @OnError
    public void onError(Session session, Throwable e) {
        super.onError(session, e);
    }


}
