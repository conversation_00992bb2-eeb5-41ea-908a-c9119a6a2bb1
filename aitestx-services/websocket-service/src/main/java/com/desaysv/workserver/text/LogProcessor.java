package com.desaysv.workserver.text;

import com.desaysv.workserver.entity.LogMessage;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class LogProcessor {
    private final BlockingQueue<LogMessage> logQueue = new LinkedBlockingQueue<>();

    public LogProcessor(LogSender logSender) {
        Thread logProcessorThread = new Thread(() -> {
            while (true) {
                try {
                    LogMessage message = logQueue.take();
                    logSender.sendLogMessage(message);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });

        logProcessorThread.start();
    }

    public void queueLogMessage(LogMessage message) {
        try {
            logQueue.put(message);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}