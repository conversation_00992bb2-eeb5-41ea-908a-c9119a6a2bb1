package com.desaysv.workserver.stream.video;

import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-18 11:18
 * @description :
 * @modified By :
 * @since : 2022-2-18
 */
@Slf4j
public class JavaCV {

    public static List<byte[]> grabberFFmpegImage(String filePath) throws IOException {
        List<byte[]> bytes = new ArrayList<>();
        FFmpegFrameGrabber grabber = FFmpegFrameGrabber.createDefault(filePath);
        grabber.start();
        int grabSize = grabber.getLengthInFrames();
        log.info("grabSize:{}", grabSize);
        for (int i = 0; i < grabSize; i++) {
            Frame frame = grabber.grabImage();
            bytes.add(doExecuteFrame(frame, i));
        }
        grabber.stop();
        return bytes;
    }

    private static byte[] doExecuteFrame(Frame frame, int index) throws IOException {
        if (frame == null || frame.image == null) {
            return new byte[]{};
        }

        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage image = converter.getBufferedImage(frame);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(image, "png", byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }
}
