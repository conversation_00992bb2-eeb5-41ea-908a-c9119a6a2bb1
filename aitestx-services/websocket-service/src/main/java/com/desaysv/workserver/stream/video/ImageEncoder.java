package com.desaysv.workserver.stream.video;

import cn.hutool.core.codec.Base64;
import org.apache.commons.lang3.ArrayUtils;

import javax.websocket.EncodeException;
import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

public class ImageEncoder implements Encoder.Text<Image> {

    @Override
    public String encode(Image image) throws EncodeException {
        String base64Image = null;
        if (image != null && !ArrayUtils.isEmpty(image.getImageByte())) {
            base64Image = Base64.encode(image.getImageByte());
        }
        return base64Image;
    }

    @Override
    public void init(EndpointConfig endpointConfig) {

    }

    @Override
    public void destroy() {

    }

}