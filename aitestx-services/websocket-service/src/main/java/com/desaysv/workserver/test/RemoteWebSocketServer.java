package com.desaysv.workserver.test;


import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.net.URI;
import java.util.Set;
import java.util.concurrent.*;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2024/9/10 9:54
 */
@ServerEndpoint("/remoteMonitor/websocket")
@Slf4j
@Component
@Lazy
public class RemoteWebSocketServer extends WebSocketServer {
    // 存储所有客户端连接
    private static final Set<Session> clients = new CopyOnWriteArraySet<>();
    // 心跳检测间隔（秒）
    private static final int PING_INTERVAL = 30;
    // 重连间隔（秒）
    private static final int RECONNECT_INTERVAL = 5;
    // 最大重连次数
    private static final int MAX_RECONNECT_ATTEMPTS = 5;

    // 用于心跳机制的调度线程池
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    @OnOpen
    public void onOpen(Session session) {
        log.info("已连接新客户端: {}", session.getId());
        clients.add(session);
        startHeartbeat(session);  // 开启心跳机制
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("消息来源{}: {}", session.getId(), message);
        //组装回复的消息
        if (session.isOpen()) {
            RemoteOperation remoteOperation = new RemoteOperation();
            remoteOperation.setCommand(RemoteOperationCommand.REPLY_RESULT);
            remoteOperation.setStatus(RemoteOperationStatus.RECEIVED);
            session.getAsyncRemote().sendText(JSON.toJSONString(remoteOperation));
        }
        broadcast(session, message);
    }


    @OnClose
    public void onClose(Session session, CloseReason reason) {
        log.info("客户端已断开连接: {} 原因: {}", session.getId(), reason);
        clients.remove(session);
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        log.error("会话时出错 {}: {}", session.getId(), throwable.getMessage());
        clients.remove(session);
        tryReconnect(session); // 尝试重连
    }

    // 心跳机制：每隔固定时间发送Ping请求
    private void startHeartbeat(Session session) {
        scheduler.scheduleAtFixedRate(() -> {
            if (session.isOpen()) {
                try {
                    log.info("发送ping {}", session.getId());
                    session.getAsyncRemote().sendPing(null);
                } catch (IOException e) {
                    log.error("发送ping出错 {}: {}", session.getId(), e.getMessage());
                    tryReconnect(session);
                }
            }
        }, 0, PING_INTERVAL, TimeUnit.SECONDS);
    }

    // 重连机制：在一定时间内尝试重新连接
    private void tryReconnect(Session session) {
        // 使用 Future 管理重连的任务
        final CompletableFuture<Boolean> reconnectFuture = new CompletableFuture<>();

        scheduler.scheduleAtFixedRate(new Runnable() {
            private int attempts = 0;

            @Override
            public void run() {
                if (session.isOpen() || attempts >= MAX_RECONNECT_ATTEMPTS) {
                    // 如果连接已打开或重连次数超出限制，则终止
                    reconnectFuture.complete(false);
                    return;
                }

                attempts++;
                log.info("尝试重新连接: {} (尝试 {})", session.getId(), attempts);
                try {
                    // 这里可以模拟一个重连的过程，例如重新建立一个 WebSocket 连接
                    URI uri = session.getRequestURI();
                    WebSocketContainer container = ContainerProvider.getWebSocketContainer();
                    container.connectToServer(RemoteWebSocketServer.class, uri);
                    reconnectFuture.complete(true); // 重连成功
                } catch (Exception e) {
                    log.error("重新连接尝试 {} 失败: {}", attempts, session.getId());
                }
            }
        }, 0, RECONNECT_INTERVAL, TimeUnit.SECONDS);

        // 重连结束时的回调
        reconnectFuture.thenAccept(success -> {
            if (!success) {
                log.info("无法重新连接: {} 已经尝试" + MAX_RECONNECT_ATTEMPTS + " 次", session.getId());
            } else {
                log.info("已成功重新连接: {}", session.getId());
            }
        });
    }

    // 广播消息到所有客户端
    public static void broadcast(Session session, String message) {
        for (Session client : clients) {
            if (client.isOpen() && !client.equals(session)) {
                client.getAsyncRemote().sendText(message);
            }
        }
    }

    // 关闭 WebSocket 服务器时清理资源
    public static void shutdown() {
        scheduler.shutdown();
        for (Session session : clients) {
            try {
                session.close();
            } catch (IOException e) {
                log.error("关闭时出错： {}", e.getMessage());
            }
        }
        clients.clear();
    }

}
