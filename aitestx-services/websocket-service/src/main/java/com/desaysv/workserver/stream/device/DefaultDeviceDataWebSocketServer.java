package com.desaysv.workserver.stream.device;

import com.desaysv.workserver.stream.config.WebSocketConfigurator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-30 20:29
 * @description :
 * @modified By :
 * @since : 2022-6-30
 */
@ServerEndpoint(value = "/monitor/device/{deviceName}",
        encoders = {JsonDataEncoder.class},
        configurator = WebSocketConfigurator.class)
@Slf4j
@Component
@Lazy
public class DefaultDeviceDataWebSocketServer extends DeviceDataWebSocketServer {

    @OnOpen
    public void onOpen(Session session,
                       @PathParam("deviceName") String deviceName) {
        super.onOpen(session, deviceName, null);
    }

    @OnMessage
    public void onMessage(Session session, String message) {
        super.onMessage(session, message);
    }

    @OnClose
    public void onClose(Session session) {
        super.onClose(session);
    }

    @OnError
    public void onError(Session session, Throwable e) {
        super.onError(session, e);
    }


}
