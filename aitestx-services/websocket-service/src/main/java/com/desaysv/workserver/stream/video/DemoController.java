package com.desaysv.workserver.stream.video;

import com.desaysv.workserver.WebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.servlet.ModelAndView;

import java.io.IOException;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-17 18:25
 * @description :
 * @modified By :
 * @since : 2022-2-17
 */
public class DemoController {
    @Autowired
    private WebSocketServer webSocketServer;

    @GetMapping("index")
    public ResponseEntity<String> index() {
        return ResponseEntity.ok("请求成功");
    }

    @GetMapping("page")
    public ModelAndView page() {
        return new ModelAndView("websocket");
    }

    @GetMapping("/pull/{toUserId}")
    public ResponseEntity<String> pullVideoStream(@PathVariable String toUserId) throws IOException {
        webSocketServer.sendBytes(VideoStreamReader.readImage("src/main/resources/big.png"), toUserId);
        return ResponseEntity.ok("Message send successful");
    }

}
