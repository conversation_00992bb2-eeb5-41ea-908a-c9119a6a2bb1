<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.desaysv</groupId>
        <artifactId>AITestXServer</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <artifactId>aitestx-services</artifactId>

    <modules>
        <module>device-service</module>
        <module>protocol-service</module>
        <module>video-service</module>
        <module>project-service</module>
        <module>picture-service</module>
        <module>vision-service</module>
        <module>widget-service</module>
        <module>android-service</module>
        <module>performance-service</module>
        <module>websocket-service</module>
        <module>common-service</module>
        <module>testcase-service</module>
        <module>windows-service</module>
        <module>script-service</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>

        <!-- Android Device Bridge java api -->
        <!--        <dependency>-->
        <!--            <groupId>com.android.tools.ddms</groupId>-->
        <!--            <artifactId>ddmlib</artifactId>-->
        <!--            <version>30.0.4</version>-->
        <!--        </dependency>-->


    </dependencies>


</project>