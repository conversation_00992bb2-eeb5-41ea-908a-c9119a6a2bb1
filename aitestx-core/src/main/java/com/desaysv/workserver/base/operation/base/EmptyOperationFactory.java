package com.desaysv.workserver.base.operation.base;

import com.desaysv.workserver.base.operation.method.EmptyOperationMethod;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.base.operation.parameter.EmptyOperationParameter;
import com.desaysv.workserver.base.operation.parameter.OperationParameter;
import com.desaysv.workserver.base.operation.targets.EmptyOperationTarget;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 14:58
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@Component
@Lazy
public class EmptyOperationFactory implements OperationAbstractFactory {

    @Override
    public OperationTarget createOperationTarget(Map<String, Object> operationTarget) {
        return new EmptyOperationTarget();
    }

    @Override
    public OperationMethod createOperationMethod(Map<String, Object> operationMethod) {
        return new EmptyOperationMethod();
    }

    @Override
    public OperationParameter createOperationParameter(Map<String, Object> operationParameter) {
        return new EmptyOperationParameter();
    }

}
