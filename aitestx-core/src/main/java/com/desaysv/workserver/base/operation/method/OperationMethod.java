package com.desaysv.workserver.base.operation.method;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-15 19:26
 * @description : 操作方法基础类
 * @modified By :
 * @since : 2022-3-15
 */
@Data
public class OperationMethod {

    public static OperationMethod Keyword(String keyword) {
        throw new RuntimeException("需要重新定义该方法");
    }

    @JSONField(serialize = false)
    private static Map<String, Class<? extends OperationMethod>> operationMethodMap = new HashMap<>();

    public static void registerMethod(String keyword, Class<? extends OperationMethod> clazz) {
        operationMethodMap.put(keyword, clazz);
    }

    public static Class<? extends OperationMethod> getOperationMethodClass(String keyword) {
        return operationMethodMap.get(keyword);
    }

    //可选参数
    private String methodName;

    private String keyword;

    private OperationMethod pairedOperationMethod;

    private Integer methodType = OperationMethodType.NORMAL.getValue();

    private String  pairedCode;//配对码

    public static String getKeywordFieldName() {
        return "keyword";
    }


    @JSONField(serialize = false)
    public void getConstants() throws IllegalAccessException {
        Field[] fields = getClass().getFields();
        for (Field field : fields) {
            if (Modifier.isStatic(field.getModifiers())) {
                Object value = field.get(getClass());
//                System.out.println(value);
//                field.set(getClass(), addPrefix((String) value));
            }
        }
    }

    public boolean isEquals(Object o) {
        if (!(o instanceof OperationMethod)) return false;
        OperationMethod that = (OperationMethod) o;
        return keyword.equals(that.keyword);
    }

    @Override
    public boolean equals(Object o) {
        return isEquals(o);
    }

    @Override
    public int hashCode() {
        return Objects.hash(keyword);
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "methodName=" + getMethodName() + "," +
                "keyword=" + getKeyword() +
                '}';
    }
}
