package com.desaysv.workserver.base.operation.method;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-15 19:27
 * @description : 设备操作方法
 * @modified By : lwj
 * @since : 2022-3-16
 */
public class DeviceOperationMethod extends OperationMethod {

    public static OperationMethod Keyword(String keyword) {
        OperationMethod operationMethod = new DeviceOperationMethod();
        operationMethod.setKeyword(keyword);
        registerMethod(keyword, DeviceOperationMethod.class);
        return operationMethod;
    }

    //电源
    public static final OperationMethod OUTPUT_ON = Keyword("outputOn");
    public static final OperationMethod OUTPUT_OFF = Keyword("outputOff");
    public static final OperationMethod SET_VOLTAGE = Keyword("setVoltage");
    public static final OperationMethod SET_CURRENT = Keyword("setCurrent");
    public static final OperationMethod MONITOR_VOLTAGE = Keyword("monitorVoltage");
    public static final OperationMethod MONITOR_CURRENT = Keyword("monitorCurrent");

    //CAN
    public static final OperationMethod SEND_CAN_MESSAGE = Keyword("sendCanMessage");
    public static final OperationMethod SEND_CAN_LOG_MESSAGE = Keyword("canReplay");
    public static final OperationMethod STOP_CAN_MESSAGE = Keyword("stopCanMessage");
    public static final OperationMethod MONITOR_CAN = Keyword("monitorCan");

    //通断板
    public static final OperationMethod PLUG_IN = Keyword("plugIn");
    public static final OperationMethod PLUG_OUT = Keyword("plugOut");

    //串口
    public static final OperationMethod SEND_SERIAL = Keyword("sendSerial");
    public static final OperationMethod MONITOR_SERIAL = Keyword("monitorSerial");

    //机械臂
    public static final OperationMethod CLICK = Keyword("click");
    public static final OperationMethod DOUBLE_CLICK = Keyword("doubleClick");
    public static final OperationMethod LONG_CLICK = Keyword("longClick");
    public static final OperationMethod SLIDE = Keyword("slide");
    public static final OperationMethod CALIBRATION = Keyword("calibration");

    //相机
    public static final OperationMethod CAMERA_ASSERT = Keyword("assert");
    public static final OperationMethod CAMERA_ASSERT_IF = Keyword("assertIf");
    public static final OperationMethod CAMERA_ASSERT_LOOP = Keyword("assertLoop");

    //声卡
    public static final OperationMethod SOUND_ASSERT = Keyword("assert");
    public static final OperationMethod MONITOR_VOLUME = Keyword("monitorVolume");
    public static final OperationMethod SOUND_CAPTURE = Keyword("soundCapture");
    public static final OperationMethod CALIBRATE_REFERENCE_VALUE = Keyword("calibrateReferenceValue");
    public static final OperationMethod SET_REFERENCE_VALUE = Keyword("setReferenceValue");

    public static final OperationMethod fetchFeedbackData = Keyword("fetchFeedbackData");
    public static final OperationMethod beginVoltageTriggerFunction = Keyword("beginVoltageTriggerFunction");

    public static final OperationMethod clickOneChannelStart = Keyword("clickOneChannelStart");
    public static final OperationMethod clickOneChannelStop = Keyword("clickOneChannelStop");



}
