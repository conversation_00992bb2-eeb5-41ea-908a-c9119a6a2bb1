package com.desaysv.workserver.base.operation.invoker;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.operation.base.CallResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 16:50
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class OperationResult extends CallResult {

    private String context; //上下文

    private String message = "";

    //FIXME:去除data的引用，防止内存不释放
    private Object data;

    private boolean retry;

    private int retryTimes;

    private Object result;

    private String uuid;

    private boolean pauseRequested; // 是否暂停

    public void addMessage(String message) {
        this.message += "\n" + message;
    }

    public static OperationResult staticOk() {
        OperationResult operationResult = new OperationResult();
        return operationResult.ok();
    }

    public static OperationResult staticFail() {
        OperationResult operationResult = new OperationResult();
        return operationResult.fail();
    }

    public static OperationResult staticFail(String message) {
        OperationResult operationResult = new OperationResult();
        return operationResult.fail(message);
    }

    public OperationResult ok() {
        return ok("操作成功");
    }

    public OperationResult ok(String message) {
        setOk(true);
        this.message = message;
        return this;
    }

    public OperationResult ok(Object data) {
        setOk(true);
        this.data = data;
        return this;
    }

    @JSONField(serialize = false)
    public boolean isFailed() {
        return !isOk();
    }

    public OperationResult fail(Object data) {
        setOk(false);
        this.data = data;
        return this;
    }

    public OperationResult fail(String message) {
        setOk(false);
        this.message = message;
        return this;
    }

    public OperationResult fail() {
        return fail("无法获取操作结果");
    }

}
