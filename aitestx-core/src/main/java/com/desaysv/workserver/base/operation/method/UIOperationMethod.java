package com.desaysv.workserver.base.operation.method;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 10:00
 * @description : UI操作方法基础类
 * @modified By :
 * @since : 2022-3-16
 */
public class UIOperationMethod extends OperationMethod {

    public static OperationMethod Keyword(String keyword) {
        OperationMethod operationMethod = new UIOperationMethod();
        operationMethod.setKeyword(keyword);
        registerMethod(keyword, UIOperationMethod.class);
        return operationMethod;
    }

    //点击
    public static final OperationMethod CLICK = Keyword("click");
    public static final OperationMethod DOUBLE_CLICK = Keyword("doubleClick");
    public static final OperationMethod LONG_CLICK = Keyword("longClick");

    //输入
    public static final OperationMethod INPUT_TEXT = Keyword("inputText");

    //滑动
//    public static final OperationMethod SLIDE = Keyword("slide");
    public static final OperationMethod SLIDE_UP = Keyword("slideUp");
    public static final OperationMethod SLIDE_DOWN = Keyword("slideDown");
    public static final OperationMethod SLIDE_LEFT = Keyword("slideLeft");
    public static final OperationMethod SLIDE_RIGHT = Keyword("slideRight");


    public static final OperationMethod ASSERT_IF = Keyword("assertIf");
    public static final OperationMethod ASSERT_LOOP = Keyword("assertLoop");

}
