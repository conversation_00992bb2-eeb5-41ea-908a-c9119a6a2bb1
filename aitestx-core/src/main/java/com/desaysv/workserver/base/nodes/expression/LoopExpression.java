package com.desaysv.workserver.base.nodes.expression;

import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;

import java.util.List;

public class LoopExpression implements Expression {

    private final int loopCycle;
    private List<Expression> expressions;

    public LoopExpression(int loopCycle) {
        this.loopCycle = loopCycle;
    }

    public void addSubExpression(Expression expression) {
        expressions.add(expression);
    }

    @Override
    public OperationResult interpret(Operation operation) {
        for (int i = 0; i < loopCycle; i++) {
            expressions.get(i).interpret(operation);
        }
        return null;
    }
}
