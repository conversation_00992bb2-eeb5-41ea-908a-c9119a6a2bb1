package com.desaysv.workserver.base.manager;

import lombok.Getter;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 客户端状态管理器
 */
@Component
@Lazy
public class ClientStatusManager {

    @Getter
    private static final List<ClientStatusListener> clientStatusListeners = new ArrayList<>();

    public static void addClientStatusListener(ClientStatusListener clientStatusListener) {
        clientStatusListeners.add(clientStatusListener);
    }

    public static void removeClientStatusListener(ClientStatusListener clientStatusListener) {
        clientStatusListeners.remove(clientStatusListener);
    }

}
