package com.desaysv.workserver.base.manager.testSuite;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestCaseExecuteInfo {
    private int actualResult;
    @NonNull
    private String endTime;

    private int failCycle;

    private int testCycle;
    // 启动集合接口先定义testSuiteId testcaseIndex

    private int testSuiteId;

    private int testcaseIndex;

    private int duration;
    @NonNull
    private String startTime;

}