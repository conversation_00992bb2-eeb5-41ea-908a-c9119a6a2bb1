package com.desaysv.workserver.base.variable;

import javax.script.ScriptException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JavaLanguageEngine implements LanguageEngine {
    private static final JavaLanguageEngine INSTANCE = new JavaLanguageEngine();
    private final ExpressionCalculator expressionCalculator;

    public static JavaLanguageEngine getInstance() {
        return INSTANCE;
    }

    public JavaLanguageEngine() {
        expressionCalculator = new ExpressionCalculator();
    }


    /**
     * 替换字符串中的所有变量为其实际值
     *
     * @param input 输入的字符串
     * @return 替换后的字符串
     */
    public String replaceVariables(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 使用StringBuilder来提高性能
        StringBuilder result = new StringBuilder(input);

        // 使用正则表达式匹配变量
        Pattern pattern = Pattern.compile("\\$[a-zA-Z_][a-zA-Z0-9_]*");
        Matcher matcher = pattern.matcher(input);

        // 记录偏移量（因为替换后字符串长度会变化）
        int offset = 0;

        while (matcher.find()) {
            String variable = matcher.group();
            Object value = getValue(variable);

            // 将值转换为字符串
            String replacement = value != null ? value.toString() : "";

            // 计算实际位置并替换
            int start = matcher.start() + offset;
            int end = matcher.end() + offset;
            result.replace(start, end, replacement);

            // 更新偏移量
            offset += replacement.length() - variable.length();
        }

        return result.toString();
    }

    /**
     * 提取字符串中的$变量
     *
     * @param input 输入的字符串
     * @return 包含所有独特$变量的列表
     */
    public static List<String> extractVariables(String input) {
        // 匹配以$开头，后面跟着字母、数字、下划线的变量
        Pattern pattern = Pattern.compile("\\$[a-zA-Z_][a-zA-Z0-9_]*");
        Matcher matcher = pattern.matcher(input);

        // 使用Set去重
        Set<String> uniqueVariables = new HashSet<>();
        while (matcher.find()) {
            uniqueVariables.add(matcher.group());
        }

        // 转换为List返回
        return new ArrayList<>(uniqueVariables);
    }

    @Override
    public Object getValue(String key) {
        if (key.startsWith("$")) {
            key = key.substring(1);
        }
        return expressionCalculator.getVariable(key);
    }

    @Override
    public void clear() {
        expressionCalculator.clearVariables();
    }

    @Override
    public Object eval(String expression) throws ScriptException {
        return expressionCalculator.evaluate(expression);
    }
}
