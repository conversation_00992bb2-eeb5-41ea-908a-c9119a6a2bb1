package com.desaysv.workserver.base.variable;


import javax.script.ScriptException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ExpressionCalculator {
    private final Map<String, Object> variables = new HashMap<>();
    // 匹配赋值表达式，例如：$i=1, $i+=1
    private static final Pattern ASSIGNMENT_PATTERN =
            Pattern.compile("\\$([a-zA-Z0-9]+)([+\\-*/]?=)(?!=)(.+)");
    // 匹配比较表达式，例如：$i==1, $i!=2
    private static final Pattern COMPARISON_PATTERN =
            Pattern.compile("\\$([a-zA-Z0-9]+)(==|!=|<=|>=|<|>)(.+)");

    private static final Pattern STRING_LITERAL_PATTERN =
            Pattern.compile("'([^']*)'|\"([^\"]*)\"");

    // 添加括号检查方法
    private void validateParentheses(String expression) throws ScriptException {
        Stack<Character> stack = new Stack<>();
        Map<Character, Character> brackets = new HashMap<>();
        brackets.put(')', '(');
        brackets.put(']', '[');
        brackets.put('}', '{');

        for (char c : expression.toCharArray()) {
            if (c == '(' || c == '[' || c == '{') {
                stack.push(c);
            } else if (c == ')' || c == ']' || c == '}') {
                if (stack.isEmpty()) {
                    throw new ScriptException("表达式错误: 发现未匹配的右括号 '" + c + "'");
                }
                char left = stack.pop();
                if (left != brackets.get(c)) {
                    throw new ScriptException(
                            String.format("表达式错误: 括号不匹配, 期望是 '%c' 但找到了 '%c'", brackets.get(c), left));
                }
            }
        }

        if (!stack.isEmpty()) {
            throw new ScriptException("表达式错误: 存在未闭合的左括号 '" + stack.pop() + "'");
        }
    }

    public Object evaluate(String expression) throws ScriptException {
        expression = expression.replaceAll("\\s+", "");

        // 在计算前验证括号匹配
        validateParentheses(expression);

        Matcher assignMatcher = ASSIGNMENT_PATTERN.matcher(expression);
        if (assignMatcher.matches()) {
            String variable = assignMatcher.group(1);
            String operator = assignMatcher.group(2);
            String valueExpr = assignMatcher.group(3);

            switch (operator) {
                case "=":
                    return handleSimpleAssignment(variable, valueExpr);
                case "+=":
                    return handleStringOrNumberCompoundAssignment(variable, operator, valueExpr);
                case "-=":
                case "*=":
                case "/=":
                    return handleCompoundAssignment(variable, operator, valueExpr);
                default:
                    throw new ScriptException("未知的赋值运算符: " + operator);
            }
        }

        Matcher compareMatcher = COMPARISON_PATTERN.matcher(expression);
        if (compareMatcher.matches()) {
            String variable = compareMatcher.group(1);
            String operator = compareMatcher.group(2);
            String valueExpr = compareMatcher.group(3);
            return handleComparison(variable, operator, valueExpr);
        }

        return calculateExpression(expression);
    }

    private Object handleStringOrNumberCompoundAssignment(String variable, String operator, String valueExpr)
            throws ScriptException {
        Object currentValue = variables.get(variable);
        if (currentValue == null) {
            throw new ScriptException("变量 " + variable + " 未定义");
        }

        Object newValue = calculateExpression(valueExpr);

        if (currentValue instanceof String) {
            String result = currentValue + String.valueOf(newValue);
            variables.put(variable, result);
            return result;
        } else {
            BigDecimal result = ((BigDecimal) currentValue).add((BigDecimal) newValue);
            variables.put(variable, result);
            return result;
        }
    }


    private Object handleSimpleAssignment(String variable, String valueExpr) {
        Object value = calculateExpression(valueExpr);
        variables.put(variable, value);
        return value;
    }

    private boolean handleComparison(String variable, String operator, String valueExpr) throws ScriptException {
        BigDecimal variableValue = (BigDecimal) variables.get(variable);
        if (variableValue == null) {
            throw new ScriptException("变量" + variable + "未定义.");
        }
        BigDecimal value = (BigDecimal) calculateExpression(valueExpr);
        switch (operator) {
            case "==":
                return variableValue.compareTo(value) == 0;
            case "!=":
                return variableValue.compareTo(value) != 0;
            case "<":
                return variableValue.compareTo(value) < 0;
            case ">":
                return variableValue.compareTo(value) > 0;
            case "<=":
                return variableValue.compareTo(value) <= 0;
            case ">=":
                return variableValue.compareTo(value) >= 0;
            default:
                throw new ScriptException("未知表达式比较符号: " + operator);
        }
    }


    private BigDecimal handleCompoundAssignment(String variable, String operator, String valueExpr) throws ScriptException {
        BigDecimal currentValue = (BigDecimal) variables.get(variable);
        if (currentValue == null) {
            throw new ScriptException("变量" + variable + "未定义.");
        }
        BigDecimal newValue = (BigDecimal) calculateExpression(valueExpr);

        BigDecimal result;
        switch (operator) {
            case "+=":
                result = currentValue.add(newValue);
                break;
            case "-=":
                result = currentValue.subtract(newValue);
                break;
            case "*=":
                result = currentValue.multiply(newValue);
                break;
            case "/=":
                result = currentValue.divide(newValue, 10, RoundingMode.HALF_UP);
                break;
            default:
                throw new IllegalArgumentException("未知运算符: " + operator);
        }

        variables.put(variable, result);
        return result;
    }

    private Object calculateExpression(String expression) {
        // 先检查是否为字符串字面量
        Matcher stringMatcher = STRING_LITERAL_PATTERN.matcher(expression);
        if (stringMatcher.matches()) {
            return stringMatcher.group(1) != null ? stringMatcher.group(1) : stringMatcher.group(2);
        }

        // 替换变量
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String varName = "$" + entry.getKey();
            if (expression.contains(varName)) {
                Object value = entry.getValue();
                if (value instanceof String) {
                    expression = expression.replace(varName, "'" + value + "'");
                } else {
                    expression = expression.replace(varName, value.toString());
                }
            }
        }

        // 再次检查是否为字符串表达式
        stringMatcher = STRING_LITERAL_PATTERN.matcher(expression);
        if (stringMatcher.matches()) {
            return stringMatcher.group(1) != null ? stringMatcher.group(1) : stringMatcher.group(2);
        }

        return parseExpression(expression);
    }



    private BigDecimal parseExpression(String expression) {
        // 处理加减
        List<String> addParts = new ArrayList<>();
        StringBuilder currentPart = new StringBuilder();
        boolean nextIsNegative = false;

        for (int i = 0; i < expression.length(); i++) {
            char c = expression.charAt(i);
            if (c == '+' || (c == '-' && i > 0)) {
                addParts.add(currentPart.toString());
                currentPart = new StringBuilder();
                if (c == '-') nextIsNegative = true;
                continue;
            }
            if (nextIsNegative) {
                currentPart.append('-');
                nextIsNegative = false;
            }
            currentPart.append(c);
        }
        addParts.add(currentPart.toString());

        // 计算所有部分
        BigDecimal result = BigDecimal.ZERO;
        for (String part : addParts) {
            if (!part.isEmpty()) {
                result = result.add(parseMultiplyDivide(part));
            }
        }

        return result;
    }

    private BigDecimal parseMultiplyDivide(String expression) {
        String[] parts = expression.split("[*/]");
        if (parts.length == 1) {
            return new BigDecimal(parts[0]);
        }

        BigDecimal result = new BigDecimal(parts[0]);
        int index = parts[0].length();
        for (int i = 1; i < parts.length; i++) {
            char operator = expression.charAt(index);
            BigDecimal value = new BigDecimal(parts[i]);
            if (operator == '*') {
                result = result.multiply(value);
            } else {
                result = result.divide(value, 10, RoundingMode.HALF_UP);
            }
            index += parts[i].length() + 1;
        }

        return result;
    }

    public Object getVariable(String name) {
        return variables.get(name);
    }

    public void clearVariables() {
        variables.clear();
    }

    // 添加一些辅助方法
    public void setVariable(String name, BigDecimal value) {
        variables.put(name, value);
    }

    public boolean hasVariable(String name) {
        return variables.containsKey(name);
    }

    public Map<String, Object> getAllVariables() {
        return new HashMap<>(variables);
    }

    public static void main(String[] args) throws ScriptException {
        ExpressionCalculator calc = new ExpressionCalculator();

// 使用 = 赋值
        System.out.println(calc.evaluate("$i=4"));  // 结果: 1

// 使用 == 赋值
        System.out.println(calc.evaluate("$i==4"));  // 结果: 1

// 复合赋值
        System.out.println(calc.evaluate("$i+=0.8"));  // 结果: 1.1

// 使用变量进行计算
        System.out.println(calc.evaluate("$j=$i*2"));  // 结果: 2.2

        System.out.println("------------");
// 查看变量值
        System.out.println(calc.getVariable("i"));  // 输出: 1.1
        System.out.println(calc.getVariable("j"));  // 输出: 2.2
    }
}