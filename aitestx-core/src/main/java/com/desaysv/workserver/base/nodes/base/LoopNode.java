package com.desaysv.workserver.base.nodes.base;

import com.desaysv.workserver.base.nodes.NodeType;
import com.desaysv.workserver.base.operation.base.Operation;
import lombok.Data;

import java.util.List;

/**
 * 循环节点
 */
@Data
public abstract class LoopNode {

    private NodeType nodeType;

    private boolean randomLoop = false;//是否随机循环
    private int cycle = 1;  //循环次数
    private Operation operation; //当前操作
    private List<LoopNode> nodes; //当前操作的子节点

    private int layer; //层级

    public LoopNode(Operation operation) {
        this.operation = operation;
    }

    public LoopNode() {

    }

    /**
     * 添加子节点
     *
     * @param node 子节点
     */
    public void add(LoopNode node) {
        nodes.add(node);
    }

    /**
     * 判断是否有子节点
     *
     * @return 是否有子节点
     */
    public boolean hasChild() {
        return nodes != null;
    }

    public abstract void accept(NodeVisitor visitor, LoopNode node, NodeContext nodeContext) throws NodeTerminateSignal, LoopBreakNotification;

    public void accept(NodeVisitor visitor) throws LoopBreakNotification {
        accept(visitor, null);
    }

    public abstract void accept(NodeVisitor visitor, NodeContext nodeContext) throws LoopBreakNotification;
}
