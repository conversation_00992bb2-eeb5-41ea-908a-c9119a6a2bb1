package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.nodes.base.CycleChangeContext;
import com.desaysv.workserver.base.operation.base.CallResult;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-13 13:56
 * @description :
 * @modified By :
 * @since : 2022-5-13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExecuteResult extends CallResult implements CycleChangeContext {

    private String tagName;

    private Map<Integer, List<OperationResult>> operationResults; //行号：历史操作结果列表
    private Map<String, OperationResult> operationResultsByUuid = Collections.synchronizedMap(new LinkedHashMap<>());

    public void putOperationResult(int operationIndex, OperationResult operationResult) {
        if (!operationResults.containsKey(operationIndex)) {
            operationResults.put(operationIndex, new ArrayList<>());
        }
        operationResults.get(operationIndex).add(operationResult);
        if (operationResult.getUuid() != null) {
            operationResultsByUuid.put(operationResult.getUuid(), operationResult);
        }
    }

    public void selfCheck() {
        setOk(checkResult());
    }

    public boolean checkResult() {
        return operationResults.values().stream().allMatch(results -> results.stream().allMatch(CallResult::isOk));
    }

}
