package com.desaysv.workserver.base.operation.invoker;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationCommand;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 10:12
 * @description : 操作命令调用者接口
 * @modified By :
 * @since : 2022-3-18
 */
@Setter
@Component
@Slf4j
@Lazy
public class OperationInvoker {

    private OperationCommand operationCommand;

    public OperationInvoker() {

    }

    public OperationInvoker(OperationCommand operationCommand) {
        this.operationCommand = operationCommand;
    }

    public OperationResult invoke(ExecutionContext executionContext, Operation operation) {
        return operationCommand.execute(executionContext, operation);
    }


}
