package com.desaysv.workserver.base.manager.testSuite;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

public class TestSuiteInfoManager {
    @Getter
    private static final List<TestSuiteInfoListener> testSuiteInfoListeners=new ArrayList<>();

    public static void addTestSuiteInfoListener(TestSuiteInfoListener testSuiteInfoListener){
        testSuiteInfoListeners.add(testSuiteInfoListener);
    }
    public static void removeTestSuiteInfoListener(TestSuiteInfoListener testSuiteInfoListener){
        testSuiteInfoListeners.remove(testSuiteInfoListener);
    }

}
