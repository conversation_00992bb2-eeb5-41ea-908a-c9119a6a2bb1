package com.desaysv.workserver.base.nodes;

import com.desaysv.workserver.base.nodes.base.LoopBreakNotification;
import com.desaysv.workserver.base.nodes.base.LoopNode;
import com.desaysv.workserver.base.nodes.base.NodeContext;

public interface NodeExecutor {
    void executeByNode(LoopNode node) throws LoopBreakNotification;

    void executeByNode(LoopNode node, NodeContext nodeContext) throws LoopBreakNotification;
}
