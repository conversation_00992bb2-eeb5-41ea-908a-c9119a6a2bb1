package com.desaysv.workserver.base.execution;

import lombok.Data;

import java.text.MessageFormat;
import java.util.Map;

@Data
public class TestResultReport {

    protected int sumCount;
    protected int passCount;
    protected int failCount;

    public double getFailRate() {
        return failCount / (sumCount * 1.0);
    }

    @Override
    public String toString() {
        return MessageFormat.format("{0,number,#.##%}({1}/{2})", getFailRate(), failCount, sumCount);
    }

    /**
     * 查询所有suite循环
     *
     * @return
     */
    public static TestResultReport reportAllLoop(TestResultReport... testResultReports) {
        TestResultReport finalTestResultReport = new TestResultReport();
        int sumCount = 0, passCount = 0, failCount = 0;
        for (TestResultReport testResultReport : testResultReports) {
            if (testResultReport == null) {
                continue;
            }
            sumCount += testResultReport.getSumCount();
            passCount += testResultReport.getPassCount();
            failCount += testResultReport.getFailCount();
        }
        finalTestResultReport.setSumCount(sumCount);
        finalTestResultReport.setPassCount(passCount);
        finalTestResultReport.setFailCount(failCount);
        return finalTestResultReport;
    }

    /**
     * 查询单个suite循环
     *
     * @param executeResults
     * @return
     */
    public static TestResultReport reportSingleLoop(Map<Integer, ExecuteResult> executeResults) {
        TestResultReport testResultReport = new TestResultReport();
        int sumCount = executeResults.size();
        int passCount = (int) executeResults.values().stream().filter(ExecuteResult::isOk).count();
        int failCount = sumCount - passCount;
        testResultReport.setSumCount(sumCount);
        testResultReport.setPassCount(passCount);
        testResultReport.setFailCount(failCount);
        return testResultReport;
    }

    public boolean valid() {
        return sumCount > 0;
    }
}
