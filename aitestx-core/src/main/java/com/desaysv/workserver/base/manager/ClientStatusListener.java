package com.desaysv.workserver.base.manager;

import com.desaysv.workserver.model.TestClient;

/**
 * 客户端状态监听器
 */
public interface ClientStatusListener {

    default void clientRegister(TestClient testClient) {

    }

    /**
     * 用户打开flytest
     */
    default void userRegister(ClientPackage clientPackage) {

    }

    /**
     * 用户登录
     */
    default void userLogin(ClientPackage clientPackage) {

    }

    /**
     * 用户退出
     */
    default void userLogout(ClientPackage clientPackage) {

    }

    /**
     * 客户端退出
     */
    default void clientExit(ClientPackage clientPackage) {

    }

    /**
     * 修改配置用户多邮件接收人
     */
    default void configurationUserEmails(ClientPackage clientPackage) {

    }

    /**
     * 修改配置用户脚本测试模式下多邮件接收人
     */
    default void configurationUserScriptEmails(ClientPackage clientPackage) {

    }

    /**
     * 修改配置用户robot多url
     */
    default void configurationUserRobotUrls(ClientPackage clientPackage) {

    }

    /**
     * 手动测试开始
     */
    default void startManuallyTest() {

    }
}
