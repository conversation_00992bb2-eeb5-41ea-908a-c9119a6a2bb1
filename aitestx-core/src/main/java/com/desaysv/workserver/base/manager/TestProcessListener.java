package com.desaysv.workserver.base.manager;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.OperationFailReason;
import com.desaysv.workserver.base.operation.method.MethodCollector;

/**
 * 测试过程监听器
 */
public interface TestProcessListener {

    //编译回调
    default boolean compileCompleted(MethodCollector methodCollector) {
        return true;
    }

    //testSuite回调
    default void testSuiteStart(ExecutionContext executionContext) {
    }

    default void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
    }

    default void testing(ExecutionContext executionContext, int currentTestCycle) {
    }

    default void testFailed(ExecutionContext executionContext, int currentTestCycle) {
        testFailed(executionContext, currentTestCycle, new OperationFailReason());
    }

    default void testFailed(ExecutionContext executionContext, int currentTestCycle, OperationFailReason reason) {
    }

    default void testPausing() {
    }

    default void testResume() {
    }

    default void testTerminated() {
    }

    //testCase回调

    default void testcaseStart(ExecutionContext executionContext) {
    }

    default void testCaseComplete(ExecutionContext executionContext, boolean isFailed) {
    }

}
