package com.desaysv.workserver.base.operation.targets;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 10:15
 * @description : 操作对象接口
 * @modified By :
 * @since : 2022-3-18
 */
//TODO： 创建一个OperationTarget孪生类
@Data
public class OperationTarget {

    @JSONField(serialize = false)
    @JsonIgnore
    protected ThreadLocal<ExecutionContext> executionContext = new ThreadLocal<>();

    //可选参数
    private String aliasName;

    //设备必选参数
    private String deviceModel;

    //设备通道
    private Integer channel;

    //设备类别
    private String deviceType;

    //设备序号
    private int deviceIndex;

    //用户自定义名称
    private String userDefinedName;


    //    @Override
//    public String toString() {
//        return getClass().getSimpleName() + "{" +
//                "aliasName=" + getAliasName() + "," +
//                "deviceModel=" + getDeviceModel() + "," +
//                "channel=" + getChannel() +
//                '}';
//    }
}
