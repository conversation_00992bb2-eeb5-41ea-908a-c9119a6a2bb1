package com.desaysv.workserver.base.manager;

import com.desaysv.workserver.base.operation.targets.OperationTarget;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-27 10:19
 * @description :
 * @modified By :
 * @since : 2022-6-27
 */
public class RegisterManager {

    private static final Map<String, OperationTarget> operationTargetMap = new LinkedHashMap<>();

    public static void registerToManager(String targetName, OperationTarget operationTarget) {
        operationTargetMap.put(targetName, operationTarget);
    }

    public static OperationTarget getOperationTarget(String targetName) {
        return operationTargetMap.get(targetName);
    }

}
