package com.desaysv.workserver.base.variable;

import javax.script.ScriptContext;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

public class JsScriptLanguageEngine implements LanguageEngine {
    private static final JsScriptLanguageEngine INSTANCE = new JsScriptLanguageEngine();
    private final ScriptEngine engine;


    public static JsScriptLanguageEngine getInstance() {
        return INSTANCE;
    }

    private JsScriptLanguageEngine() {
        ScriptEngineManager manager = new ScriptEngineManager();
        engine = manager.getEngineByName("JavaScript");
        if (engine != null) {
            engine.setBindings(engine.createBindings(), ScriptContext.ENGINE_SCOPE);
        }
    }

    @Override
    public Object getValue(String key) {
        if (engine != null) {
            engine.getContext().getBindings(ScriptContext.ENGINE_SCOPE).get(key);
        }
        return null;
    }


    @Override
    public void clear() {
        if (engine != null) {
            engine.getContext().getBindings(ScriptContext.ENGINE_SCOPE).clear();
        }
    }

    @Override
    public Object eval(String expression) throws ScriptException {
        if (engine != null) {
            return engine.eval(expression, engine.getBindings((ScriptContext.ENGINE_SCOPE)));
        }
        return null;
    }



}
