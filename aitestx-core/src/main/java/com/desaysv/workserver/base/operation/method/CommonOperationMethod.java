package com.desaysv.workserver.base.operation.method;

public class CommonOperationMethod extends OperationMethod {

    public static OperationMethod Keyword(String keyword) {
        OperationMethod operationMethod = new CommonOperationMethod();
        operationMethod.setKeyword(keyword);
        registerMethod(keyword, CommonOperationMethod.class);
        return operationMethod;
    }

    public static final OperationMethod BEGIN_LOOP = Keyword("beginLoop");
    public static final OperationMethod END_LOOP = Keyword("endLoop");

    public static final OperationMethod BEGIN_RANDOM_LOOP = Keyword("beginRandomLoop");
    public static final OperationMethod END_RANDOM_LOOP = Keyword("endRandomLoop");

    public static final OperationMethod LOAD_OPERATION_GROUP = Keyword("loadOperationGroup");
    public static final OperationMethod IF_EXPRESSION_SUCCESS = Keyword("ifExpressionSuccess");
    public static final OperationMethod END_IF = Keyword("endIf");
    public static final OperationMethod ELSE_IF = Keyword("elseIf");
    public static final OperationMethod BREAK_LOOP = Keyword("breakLoop");
}
