package com.desaysv.workserver.base.operation.base;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-23 15:32
 * @description :
 * @modified By :
 * @since : 2022-3-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationJsonObject extends ActionJsonObject {

    private Integer operationType;

    private Map<String, Object> operationTarget = new HashMap<>();

    private Map<String, Object> operationMethod = new HashMap<>();

    //TODO：后续可以拓展为OperationObject类
    private Object operationObject;

    private Map<String, Object> operationParameter = new HashMap<>();

    private Integer retry;

    private Object friendlyOperationObject;

    private boolean annotated; //注释

    private boolean background;

    private int lineNo; //所属行

    private String uuid;

    private OperationLabel label;

//    @JsonAnySetter
//    public void setter(String key, Object value) {
//        operationParameter.put(key, value);
//    }
//
//    @JsonAnyGetter
//    public Map<String, Object> getter() {
//        return operationParameter;
//    }


    /**
     * 静态实例工厂
     *
     * @return
     */
    public static OperationJsonObject newInstance() {
        return new OperationJsonObject();
    }


}
