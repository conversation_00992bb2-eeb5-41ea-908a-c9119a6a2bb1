package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.operation.base.CallResult;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationJsonObject;
import com.desaysv.workserver.base.operation.base.OperationType;
import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.response.ResultEntity;

import java.util.Arrays;
import java.util.Map;

public class ExecutionUtils {
    public static <T extends CallResult> ResultEntity<Map<String, Map<Integer, T>>> checkResults(Map<String, Map<Integer, T>> result) {
        if (result.values().stream().allMatch(m -> m.values().stream().allMatch(CallResult::isOk))) {
            return ResultEntity.ok(result);
        } else {
            return ResultEntity.fail(result);
        }
    }

    public static int checkOperationType(Operation operation) {
        if (operation.getOperationTarget() == null) {
            return OperationType.COMMON;
        }
        OperationTarget operationTarget = operation.getOperationTarget();
        if (operationTarget.getDeviceModel() == null) {
            return OperationType.COMMON;
        }
        return getOperationType(operation.getOperationTarget().getDeviceModel(), operation.getOperationMethod().getKeyword());
    }

    //FIXME：Image统一到Device
    public static int checkOperationType(OperationJsonObject operationJsonObject) {
        Map<String, Object> operationTarget = operationJsonObject.getOperationTarget();
        if (operationTarget.isEmpty()) {
            return OperationType.COMMON;
        }

        Object deviceModelObj = operationTarget.get("deviceModel");
        if (deviceModelObj == null) {
            return OperationType.COMMON;
        }

        String deviceModelStr = String.valueOf(deviceModelObj);
        Object keywordObj = operationJsonObject.getOperationMethod().get("keyword");
        String keyword = String.valueOf(keywordObj);

        return getOperationType(deviceModelStr, keyword);

    }

    private static int getOperationType(String deviceModel, String keyword) {
        boolean isAndroidOrVideoCapture = DeviceModel.Utils.contains(DeviceModel.Android.class, deviceModel) ||
                DeviceModel.Utils.contains(DeviceModel.VideoCapture.class, deviceModel);
        boolean isImageDevice = isAndroidOrVideoCapture ||
                DeviceModel.Utils.contains(DeviceModel.Camera.class, deviceModel);
        if (keyword != null) {
            if (isAndroidOrVideoCapture && Arrays.asList(
                    "screenshot",
                    "swipe",
                    "click",
                    "randomClick",
                    "randomSwipe",
                    "executeADBCommand",
                    "resumeScheduledVideoCaptureScreenShoot",
                    "pauseScheduledVideoCaptureScreenShoot",
                    "startScheduledVideoCaptureScreenShoot",
                    "videoCaptureScreenShoot",
                    "multiFingerSwipe"
            ).contains(keyword)) {
                return OperationType.DEVICE;
            }
        }

        return isImageDevice ? OperationType.IMAGE : OperationType.DEVICE;
    }

}
