package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.context.OperationContext;
import com.desaysv.workserver.base.operation.base.JsonAction;
import com.desaysv.workserver.base.operation.base.Operation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-14 13:43
 * @description :
 * @modified By :
 * @since : 2022-5-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Execution extends JsonAction {

    private Integer executionIndex; //执行索引

    private OperationContext operationContext; //上下文

    private String uuid; //脚本UUID

    private int testCycle = 1; //脚本测试次数

    private List<Operation> operationList; //脚本内容步骤列表

}
