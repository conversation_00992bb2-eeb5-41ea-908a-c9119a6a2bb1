package com.desaysv.workserver.base.manager;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClientInfoReceiveFromClient extends ClientPackage implements Cloneable {
    private String client; //客户端名称

    private String userName; //测试人员,建议使用公司Email收件人的拼音格式，如Zhang, san

    private String userId; //userId 测试人员Id

    private String project; //测试项目

    private String email; //邮件

    private String testUnit; //测试工位（电脑），格式：电脑名/账户名，如RVC1-01/ICI2 DEQ

    private String ipAddress; //测试电脑IP,符合IP地址定义，如127.0.0.1

    private String machine = ""; //测试机器名,例如，Android设备的Serial Number

    private String workstation = ""; //测试工位名,例如，煲机工位1

    private String buName; //事业单元名

    private Integer platformCode; //测试平台代号,参照测试平台代号定义表

    private String platformName; //测试平台名称，可不填，当测试平台未注册时，可补充该字段，用于被服务器标识

    private Integer statusCode; //测试状态代号,参照测试状态代号定义表
    private String remark = ""; //备注,填入备注信息
    private String startTime;
    private String endTime;
    private String peripherals;
    private int testSuiteId;

    private boolean offlineMode;
    private String[] emails; //用于多个邮箱
    private String[] urls;//robotUrls
    private String[] scriptEmails;//脚本测试时要发送的邮件列表
    private String[] multiTableUrls;//多维度表格url
    private boolean pauseWhenTestFailed;//脚本测试失败时是否暂停
    private boolean enableEmailSending;//脚本是否启用邮件发送
    private boolean enableSequenceEmailSending;//动作序列是否启用邮件发送
    private boolean enableSequenceRobotSending;//动作序列是否启用飞书机器人发送
    private boolean enableSequenceCloudDocSending;//动作序列是否启用云文档发送
    private boolean isSmokeTest;//是否是冒烟测试还是功能测试

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            ClientInfoReceiveFromClient clientInfo = new ClientInfoReceiveFromClient();
            clientInfo.setClient(client);
            clientInfo.setUserName(userName);
            clientInfo.setProject(project);
            clientInfo.setTestUnit(testUnit);
            clientInfo.setIpAddress(ipAddress);
            clientInfo.setMachine(machine);
            clientInfo.setWorkstation(workstation);
            clientInfo.setPlatformCode(platformCode);
            clientInfo.setPlatformName(platformName);
            clientInfo.setRemark(remark);
            clientInfo.setStartTime(startTime);
            clientInfo.setEndTime(endTime);
            clientInfo.setPeripherals(peripherals);
            clientInfo.setTestSuiteId(testSuiteId);
            clientInfo.setEmails(emails);
            clientInfo.setUrls(urls);
            clientInfo.setScriptEmails(scriptEmails);
            clientInfo.setMultiTableUrls(multiTableUrls);
            clientInfo.setPauseWhenTestFailed(pauseWhenTestFailed);
            clientInfo.setEnableEmailSending(enableEmailSending);
            clientInfo.setEnableSequenceEmailSending(enableSequenceEmailSending);
            clientInfo.setEnableSequenceRobotSending(enableSequenceRobotSending);
            clientInfo.setEnableSequenceCloudDocSending(enableSequenceCloudDocSending);
            clientInfo.setSmokeTest(isSmokeTest);
            return clientInfo;
        }
    }

    public ClientInfoReceiveFromClient() {
        this.client = "";
        this.userName = "";
        this.userId = "";
        this.project = "";
        this.email = "";
        this.testUnit = "";
        this.ipAddress = "";
        this.machine = "";
        this.workstation = "";
        this.buName = "";
        this.platformCode = 8;
        this.platformName = "";
        this.remark = "";
        this.startTime = "";
        this.endTime = "";
        this.peripherals = "";
    }
}
