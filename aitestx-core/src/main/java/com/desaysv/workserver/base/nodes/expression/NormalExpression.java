package com.desaysv.workserver.base.nodes.expression;

import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;

/**
 * 正常表达式
 */
public class NormalExpression implements Expression {

    private final Operation operation;

    public NormalExpression(Operation operation) {
        this.operation = operation;
    }

    @Override
    public OperationResult interpret(Operation operation) {
        return null;
    }
}
