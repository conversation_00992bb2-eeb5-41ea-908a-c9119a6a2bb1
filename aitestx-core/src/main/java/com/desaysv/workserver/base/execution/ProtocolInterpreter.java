package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.nodes.base.LoopBreakNotification;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.suite.ExecutionSuite;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 10:25
 * @description : 操作协议解析
 * @modified By :
 * @since : 2022-3-18
 */
public interface ProtocolInterpreter {
    Logger log = LogManager.getLogger(ProtocolInterpreter.class.getSimpleName());

    default Map<String, Map<Integer, ExecuteResult>> interpretByCaseName(String projectName, List<String> caseNames) {
        return new HashMap<>();
    }

    Map<String, Map<Integer, ExecuteResult>> interpret(ExecutionSuite executionSuite);

    Map<Integer, ExecuteResult> interpret(ExecutionContext executionContext, Execution execution) throws LoopBreakNotification;

    OperationResult interpret(Operation operation);

    OperationResult interpret(ExecutionContext executionContext, Operation operation);

    boolean pauseInterpret();

    boolean stopInterpret();

    boolean resumeInterpret();

    default void waitSeconds(int waitTime) {
        if (waitTime <= 0) {
            return;
        }
        try {
            Thread.sleep(waitTime);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

}
