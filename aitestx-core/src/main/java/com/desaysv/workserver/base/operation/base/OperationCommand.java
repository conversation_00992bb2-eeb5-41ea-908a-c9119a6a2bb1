package com.desaysv.workserver.base.operation.base;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ClassUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.SerializeDisabled;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.invoker.OperationResultFormatter;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.base.operation.method.OperationMethod;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.ReflectUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.UndeclaredThrowableException;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-18 9:56
 * @description : 操作命令解析接口
 * @modified By :
 * @since : 2022-3-18
 */
public interface OperationCommand {
    Logger log = LogManager.getLogger(OperationCommand.class.getSimpleName());

    /**
     * 执行操作命令
     *
     * @param operation 操作
     * @return 执行结果
     */
    OperationResult execute(ExecutionContext executionContext, Operation operation);

    default Object convertOperationObject(Object operationObject, Class<?> clazz) {
        if (operationObject instanceof Double) {
            if (clazz == float.class) {
                return ((Double) operationObject).floatValue();
            } else {
                return operationObject;
            }
        }
        return operationObject;
    }

    default boolean containDeviceChannel(Method method) {
        return ReflectUtils.getMethod(method, Integer.class, Operation.DEVICE_CHANNEL_OFFICIAL_NAME);
    }


    default OperationResult callOperationMethod(Object target, Operation operation) {
        return callOperationMethod(target, null, operation);
    }

    //TODO:预先编译方法反射，减少下次反射实践
    default OperationResult callOperationMethod(Object target, Integer deviceChannel, Operation operation) {
        //TODO：适配直接返回设备方法的返回类型(如String.class)
        Object result;
        OperationResult operationResult = new OperationResult();
        OperationMethod operationMethod = operation.getOperationMethod();
        String keyword = operationMethod.getKeyword();
        Object operationObject = operation.getOperationObject();
        //            OperationParameter operationParameter = operation.getOperationParameter();
//            List<Class<?>> types = TypeUtils.getTypes(operationObject);

        boolean hasOperationResultMethod = false;
//            for (Class<?> type : types) {
//                //调用装饰器接口
//                //FIXME：尝试改成判断返回值OperationResult
//                hasOperationResultMethod = ReflectionUtils.hasMethod(
//                        target,
//                        keyword,
//                        new Class[]{type, operationParameter.getClass()});
//                if (hasOperationResultMethod) {
//                    result = ReflectionUtils.invokeMethod(
//                            target,
//                            keyword,
//                            new Class[]{type, operationParameter.getClass()},
//                            new Object[]{operationObject, operationParameter});
//                    if (result instanceof OperationResult) {
//                        operationResult = (OperationResult) result;
//                    } else {
//                        operationResult.setData(result);
//                    }
//                    break;
//                }
//            }
        if (!hasOperationResultMethod) {
            //调用设备原生接口或者非封装接口
//                if (target instanceof AbstractDevice) {
//                    target = ((AbstractDevice) target).getDevice();
//                }

            List<ReflectUtils.MethodReflection> methodReflectionList = ReflectUtils.getReflectionByMethodName(target, keyword);
            // TODO: ParameterizedType 参数化类型 即带参数(泛型参数)的数据类型(带"<>"的数据类型)
            // TODO: 增加public方法判定
            // https://blog.csdn.net/Mudrock__/article/details/126219979
            if (methodReflectionList.isEmpty()) {
                operationResult.setOk(false);
                operationResult.setMessage(String.format("%s方法未定义", keyword));
            }
            //FIXME: 减少遍历
            for (ReflectUtils.MethodReflection methodReflection : methodReflectionList) {
                //只支持0或1个参数方法，后续考虑operationParameter扁平化不定数量参数
                Class<?> returnType = methodReflection.getReturnType();
                Class<?>[] paramTypes = methodReflection.getParamTypes();
                //TODO：检验匹配参数和operationObject，ok才进行下一步
                try {
                    Method method = methodReflection.getMethod();
                    Class<?> operationObjectClazz = null;
                    if (paramTypes.length > 0) {
                        List<Class<?>> paramTypeList = new ArrayList<>(Arrays.asList(paramTypes));
                        if (containDeviceChannel(method)) {
                            Parameter[] parameterArray = method.getParameters();
                            for (int i = 0; i < parameterArray.length; i++) {
                                Parameter parameter = parameterArray[i];
                                if (parameter.getName().equals(Operation.DEVICE_CHANNEL_OFFICIAL_NAME) && parameter.getType().equals(Integer.class)) {
                                    paramTypeList.remove(i);
                                    break;
                                }
                            }
                        }
                        if (!paramTypeList.isEmpty()) {
                            operationObjectClazz = paramTypeList.toArray(new Class<?>[]{})[0]; //取第一个参数
//                            if (operationObject instanceof JSON) { //fastjason2.JsonObject 没有继承JSON
                            // 父类.class.isAssignableFrom(子类.class)
                            if (JSONObject.class.isAssignableFrom(operationObjectClazz)) {
                                // JsonObject方法优先权
                                operationObjectClazz = JSONObject.class;
                            } else if (JSONArray.class.isAssignableFrom(operationObjectClazz)) {
                                // JsonArray方法优先权
                                //FIXME:无法获取list里面泛型
                                operationObjectClazz = JSONArray.class;
                            } else {
                                //TODO：JSONArray如何转化成泛型List<>?
                                try {
                                    if (operationObject instanceof JSONArray && List.class.isAssignableFrom(operationObjectClazz)) {
                                        //JSONArray
                                        //目前支持同类型元素的List，不支持Object[]
                                        if (methodReflection.getGenericType().length > 0) {
                                            operationObject = ((JSONArray) operationObject).toJavaList(methodReflection.getGenericType()[0]);
                                        } else {
                                            operationObject = ((JSONArray) operationObject).toJavaList(((JSONArray) operationObject).get(0).getClass());
                                        }
                                    } else if (operationObject instanceof JSONObject) {
                                        if (!ClassUtil.isSimpleTypeOrArray(operationObjectClazz)) {
                                            ((JSONObject) operationObject).remove(Operation.FRIENDLY_DISPLAY_FIELD_NAME);
                                            //JSONObject
                                            Set<String> keySet = ((JSONObject) operationObject).keySet();
                                            Set<String> fieldNames = new HashSet<>(Arrays.asList(ReflectUtils.getFieldNamesIncludingSuper(operationObjectClazz)));
                                            fieldNames.addAll(Arrays.asList(ReflectUtils.getJsonAliasValues(operationObjectClazz)));
                                            if (!fieldNames.containsAll(keySet)) {
                                                operationResult.setOk(false);
                                                operationResult.setMessage(String.format("%s不符合%s参数要求:%s", keySet, operationObjectClazz.getSimpleName(), fieldNames));
                                                log.warn(operationResult.getMessage());
                                                continue;
                                            }
                                            operationObject = JSON.parseObject(JSON.toJSONString(operationObject), operationObjectClazz);
                                        } else {
                                            operationResult.setOk(false);
                                            operationResult.setMessage(String.format("传入为JsonObject，但实际是%s", ArrayUtil.toString(paramTypes)));
                                            continue;
                                        }
                                    }
                                } catch (Exception e) {
                                    operationResult.setOk(false);
                                    operationResult.setMessage(ExceptionUtils.getExceptionString(e));
                                    continue;
                                }
                            }
                            //}
                        }

                    }
                    Object[] args;
                    if (containDeviceChannel(method)) {
                        args = operationObject == null ? new Object[]{deviceChannel} : new Object[]{deviceChannel, convertOperationObject(operationObject, operationObjectClazz)};
                    } else {
                        args = operationObject == null ? new Object[]{} : new Object[]{convertOperationObject(operationObject, operationObjectClazz)};
                    }
                    result = ReflectionUtils.invokeMethod(method, target, args);
                    if (result instanceof OperationResult) {
                        operationResult = (OperationResult) result;
                    } else if (result instanceof Boolean) {
                        operationResult.setOk((Boolean) result);
                        operationResult.setMessage(operationResult.isOk() ? "执行成功" : "执行失败");
                        operationResult.setData(result);
                    } else {
                        operationResult.setOk(true);
                        operationResult.setMessage("执行成功");
                        if (!(result instanceof SerializeDisabled)) {
                            operationResult.setData(result);
                        }
                    }
                    break;
                } catch (IllegalArgumentException | UndeclaredThrowableException e) {
                    Throwable cause = e.getCause();
                    if (cause != null) {
                        Throwable internalReason = cause.getCause();
                        if (internalReason instanceof OperationFailNotification) {
                            operationResult = ((OperationFailNotification) internalReason).getOperationResult();
                            break;
                        } else if (cause instanceof InvocationTargetException) {
                            Throwable ex = ((InvocationTargetException) cause).getTargetException();
                            log.error(ex.getMessage());
                            log.debug(ex.getMessage(), ex);
                            operationResult.setOk(false);
                            operationResult.setMessage(ExceptionUtils.getExceptionString(ex));
                            operationResult.setData(ex);
                        } else if (internalReason != null) {
                            operationResult.setOk(false);
                            operationResult.setMessage(ExceptionUtils.getExceptionString(internalReason));
                            operationResult.setData(internalReason);
                        } else {
                            log.error(cause.getMessage(), cause);
                            if (cause instanceof OperationFailNotification) {
                                OperationFailNotification operationFailNotification = (OperationFailNotification) cause;
                                operationResult = operationFailNotification.getOperationResult();
                            } else {
                                operationResult.setOk(false);
                                operationResult.setData(cause);
                                operationResult.setMessage(ExceptionUtils.getExceptionString(cause));
                            }
                            break;
                        }
                    } else {
//                        log.error(e.getMessage(), e);
                        operationResult.setOk(false);
                        operationResult.setMessage(ExceptionUtils.getExceptionString(e));
                        operationResult.setData(e);
                    }
                }
            }
        }
        if (operationResult.getData() instanceof OperationResultFormatter) {
            operationResult.setResult(operationResult.getData());
        }
        return operationResult;
    }


    static void main(String[] args) {
        System.out.println(ClassUtil.isSimpleTypeOrArray(String.class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(String[].class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(Integer.class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(float.class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(byte.class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(int.class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(int[].class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(byte[].class));
        System.out.println(ClassUtil.isSimpleTypeOrArray(MessageDigest[].class));
    }


}
