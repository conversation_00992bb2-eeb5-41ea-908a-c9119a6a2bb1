package com.desaysv.workserver.base.manager.testSuite;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Data
public class TestSuiteInfo {
    @Expose
    @NonNull
    private String hardwareVersion;
    @Expose
    @NonNull
    private String softwareVersion;
    @Expose
    @NonNull
    private String testboardVersion;

    @Expose
    @NonNull
    private String pcName;
    @Expose
    private int projectId;
    @Expose
    @NonNull
    private String startTime;
    @NonNull
    private List<TestCaseInfo> testCases;
    @Expose
    private int testType;
    @Expose
    @NonNull
    private String testsuiteName;
    @Expose
    private int toolId;
    @Expose
    @NonNull
    private String userId;
    private String pcAppRunId;
    @Expose
    private String pcUuid;
    @Expose
    private String toolVersion;
    @Expose
    private int checkTotal = 0;
    @Expose
    private int testCaseTotalAll = 0;
    @Expose
    private int testCaseTotalReview = 0;

    public String getInfoWithoutTestCases() {
        Gson gson = new GsonBuilder()
                .excludeFieldsWithoutExposeAnnotation()
                .setPrettyPrinting()
                .create();
        return gson.toJson(this);
    }
}
