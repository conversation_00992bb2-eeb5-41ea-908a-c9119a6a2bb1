package com.desaysv.workserver.base.context;

import com.desaysv.workserver.base.execution.TestResultReport;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class ExecutionContext {

    private String projectName;
    private String userName;
    private String clientName;
    private String userEmail;
    private String testSuiteName;
    private boolean pauseWhenTestFailed;
    private boolean enableSendingEmail = true;
    private boolean debugModeEnabled;
    private boolean fromActionSequence;
    private int testSuiteId;
    private int currentSuiteLoop; //当前测试集合循环
    private int testCycle; //测试循环
    private OperationContext operationContext;
    private Map<String, TestResultReport> currentTestResultReportMap = new HashMap<>();

}
