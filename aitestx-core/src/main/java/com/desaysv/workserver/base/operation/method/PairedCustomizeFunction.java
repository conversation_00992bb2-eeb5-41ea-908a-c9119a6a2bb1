package com.desaysv.workserver.base.operation.method;

import com.desaysv.workserver.base.execution.PairedIndex;
import com.desaysv.workserver.base.nodes.base.LoopNode;
import com.desaysv.workserver.base.operation.base.Operation;
import lombok.Data;

import java.util.List;

@Data
public class PairedCustomizeFunction {

    private Operation parentOperation;

    private String pairedCode;

    private List<Operation> operationList;

    private PairedIndex pairedIndex;

    private LoopNode node;

    public boolean isValid() {
        return operationList.size() > 0;
    }
}
