package com.desaysv.workserver.base.manager;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class TestClientInfo extends ClientPackage implements Cloneable {

    private String client; //客户端名称

    private String user; //测试人员,建议使用公司Email收件人的拼音格式，如Zhang, san

    private String project; //测试项目

    private String email; //邮件

    private String testUnit; //测试工位（电脑），格式：电脑名/账户名，如RVC1-01/ICI2 DEQ

    private String clientVersion; //客户端软件版本

    private String ipAddress; //测试电脑IP,符合IP地址定义，如127.0.0.1

    private String machine = ""; //测试机器名,例如，Android设备的Serial Number

    private String workstation = ""; //测试工位名,例如，煲机工位1

    private Integer buCode; //事业单元代号,参照事业单元代号定义表

    private Integer platformCode; //测试平台代号,参照测试平台代号定义表

    private String platformName; //测试平台名称，可不填，当测试平台未注册时，可补充该字段，用于被服务器标识

    private Integer statusCode; //测试状态代号,参照测试状态代号定义表

    private String remark = ""; //备注,填入备注信息

    @Override
    public Object clone() {
        try {
            return super.clone();
        } catch (CloneNotSupportedException e) {
            TestClientInfo clientInfo = new TestClientInfo();
            clientInfo.setClient(client);
            clientInfo.setUser(user);
            clientInfo.setProject(project);
            clientInfo.setTestUnit(testUnit);
            clientInfo.setClientVersion(clientVersion);
            clientInfo.setIpAddress(ipAddress);
            clientInfo.setMachine(machine);
            clientInfo.setWorkstation(workstation);
            clientInfo.setBuCode(buCode);
            clientInfo.setPlatformCode(platformCode);
            clientInfo.setPlatformName(platformName);
            clientInfo.setStatusCode(statusCode);
            clientInfo.setRemark(remark);
            return clientInfo;
        }
    }
}
