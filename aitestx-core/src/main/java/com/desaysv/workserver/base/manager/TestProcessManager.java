package com.desaysv.workserver.base.manager;

import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.operation.base.OperationFailReason;
import com.desaysv.workserver.base.operation.method.MethodCollector;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试过程管理器
 */
@Component
@Slf4j
@Lazy
public class TestProcessManager implements TestProcessListener {

    @Getter
    private static final Map<String, TestProcessListener> testProcessListeners = new HashMap<>();

    public static void addTestProcessListener(TestProcessListener testProcessListener) {
        addTestProcessListener(testProcessListener.getClass().getCanonicalName(), testProcessListener);
    }

    public static void addTestProcessListener(String key, TestProcessListener testProcessListener) {
//        log.info("添加测试状态监听器:{}", key);
        testProcessListeners.put(key, testProcessListener);
    }

    public static void removeTestProcessListener(String key) {
//        log.info("移除测试状态监听器:{}", key);
        testProcessListeners.remove(key);
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testSuiteStart(executionContext);
        }
    }

    public void testcaseStart(ExecutionContext executionContext) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testcaseStart(executionContext);
        }
    }

    @Override
    public void testing(ExecutionContext executionContext, int currentTestCycle) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testing(executionContext, currentTestCycle);
        }
    }

    @Override
    public void testCaseComplete(ExecutionContext executionContext, boolean isFailed) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testCaseComplete(executionContext, isFailed);
        }
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testComplete(executionContext, isFailed, isSendEmail);
        }
    }

    @Override
    public void testFailed(ExecutionContext executionContext, int currentTestCycle, OperationFailReason reason) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testFailed(executionContext, currentTestCycle, reason);
        }
    }

    @Override
    public void testPausing() {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testPausing();
        }
    }

    @Override
    public void testResume() {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testResume();
        }
    }

    @Override
    public void testTerminated() {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.testTerminated();
        }
    }

    @Override
    public boolean compileCompleted(MethodCollector methodCollector) {
        for (TestProcessListener listener : getTestProcessListeners().values()) {
            listener.compileCompleted(methodCollector);
        }
        return true;
    }

}
