package com.desaysv.workserver.base.operation.method;

import com.desaysv.workserver.base.execution.PairedIndex;
import com.desaysv.workserver.base.operation.base.Operation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@Component
public class MethodCollector {

    private final Map<Operation, PairedCustomizeFunction> pairedCustomizeMethodMap = new HashMap<>();


    public void clear() {
        pairedCustomizeMethodMap.clear();
    }

    public PairedCustomizeFunction registerCustomizeFunction(Operation operation,
                                                             List<Operation> operationList,
                                                             PairedIndex pairedIndex) {
        PairedCustomizeFunction customizeFunction = new PairedCustomizeFunction();
        customizeFunction.setParentOperation(operation);
        customizeFunction.setPairedCode(operation.getOperationMethod().getPairedCode());
        customizeFunction.setOperationList(operationList);
        customizeFunction.setPairedIndex(pairedIndex);
        pairedCustomizeMethodMap.put(operation, customizeFunction);
        return customizeFunction;
    }

    public PairedCustomizeFunction getFirstPairedCustomizeFunctionByKeyword(String keyword) {
        for (Map.Entry<Operation, PairedCustomizeFunction> entry : pairedCustomizeMethodMap.entrySet()) {
            Operation operation = entry.getKey();
            PairedCustomizeFunction function = entry.getValue();
            if (operation.getOperationMethod().getKeyword().equals(keyword)) {
                return function;
            }
        }
        return null;
    }

    public List<PairedCustomizeFunction> getAllPairedCustomizeFunctionByKeyword(String keyword) {
        return new ArrayList<>();
    }

    public PairedCustomizeFunction getPairedCustomizeFunctionByPairedCode(String pairedCode) {
        PairedCustomizeFunction pairedCustomizeFunction = new PairedCustomizeFunction();
        for (Map.Entry<Operation, PairedCustomizeFunction> entry : pairedCustomizeMethodMap.entrySet()) {
            if (entry.getKey().getOperationMethod().getPairedCode().equals(pairedCode)) {
                return entry.getValue();
            }
        }
        return null;
    }
}
