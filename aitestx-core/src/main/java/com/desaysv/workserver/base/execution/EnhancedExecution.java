package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.method.MethodCollector;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Data
public class EnhancedExecution {

    private final Execution execution;

//    @Getter(value = AccessLevel.PRIVATE)
//    @Setter(value = AccessLevel.NONE)
//    private final List<Operation> operationList;

    @Setter(value = AccessLevel.PRIVATE)
    private MethodCollector methodCollector;

    public EnhancedExecution(Execution execution, MethodCollector methodCollector) {
        this.execution = execution;
        this.methodCollector = methodCollector;
//        this.operationList = execution.getOperationList();
    }

    public List<Operation> getInnerOperationList(PairedIndex pairedIndex) {
        if (pairedIndex.invalid()) {
            return new ArrayList<>();
        }
        return execution.getOperationList().subList(pairedIndex.getBeginIndex() + 1, pairedIndex.getEndIndex());
    }

    public List<Operation> getInnerOperationList(Operation headerOperation) {
        String pairedCode = headerOperation.getOperationMethod().getPairedCode();
        PairedIndex pairedIndex = getPairedIndex(pairedCode);
        return getInnerOperationList(pairedIndex);
    }

    public List<Integer> getIndexesOfCustomizeFunction() {
        List<Operation> operationList = execution.getOperationList();
        List<Integer> indexes = new ArrayList<>();
        for (int i = 0; i < operationList.size(); i++) {
            Operation operation = operationList.get(i);
            if (operation.isSubOperationCollection()) {
                indexes.add(i);
            }
        }
        return indexes;
    }

    public PairedIndex getPairedIndex(String pairedCode) {
        List<Operation> operationList = execution.getOperationList();
        PairedIndex pairedIndex = new PairedIndex();
        for (int i = 0; i < operationList.size(); i++) {
            Operation operation = operationList.get(i);
            String methodPairedCode = operation.getOperationMethod().getPairedCode();
            if (methodPairedCode != null && methodPairedCode.equals(pairedCode)) {
                if (pairedIndex.getBeginIndex() == null) {
                    pairedIndex.setBeginIndex(i);
                } else if (pairedIndex.getEndIndex() == null) {
                    pairedIndex.setEndIndex(i);
                    break;
                }
            }
        }
        return pairedIndex;
    }

}
