package com.desaysv.workserver.base.execution;

import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import lombok.Data;

/**
 * 执行过程通知前端
 */
@Data
public class ExecutionNotification {

    private int executionIndex; //excel行

    private String caseName;

    private int cycle;

    private int totalCycle;

    private int position;

    private Operation operation;

    private OperationResult operationResult;

    private String extraMessage;

    private boolean userPausing;

    private boolean debugModeEnabled;

    private boolean background;

    private ExecutionResultReport executionResultReport = new ExecutionResultReport();

}
