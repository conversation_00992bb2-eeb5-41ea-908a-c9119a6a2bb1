package com.desaysv.workserver.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.desaysv.workserver.annotation.RequestSingleParam;
import com.desaysv.workserver.utils.StrUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ValueConstants;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Objects;

@Component
//no lazy
public class RequestSingleParamHandlerMethodArgumentResolver extends JsonProtocolArgumentResolver {

    public RequestSingleParamHandlerMethodArgumentResolver() {
        addResolver(this);
    }

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.hasParameterAnnotation(RequestSingleParam.class);
    }

    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest, WebDataBinderFactory webDataBinderFactory) throws Exception {
        RequestSingleParam requestSingleParam = methodParameter.getParameterAnnotation(RequestSingleParam.class);
        String reqString = getRequestString(nativeWebRequest);
        assert requestSingleParam != null;
        String value = requestSingleParam.value();
        if (Objects.equals(value, ValueConstants.DEFAULT_NONE)) {
            return StrUtils.trimBothEndsQuotation(StringEscapeUtils.unescapeJson(reqString));
        }
        JSONObject jsonObject = JSONObject.parseObject(reqString);
        return jsonObject.get(value);
    }
}
