package com.desaysv.workserver.resolver;

import lombok.Getter;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-29 13:54
 * @description :
 * @modified By :
 * @since : 2022-6-29
 */
public abstract class JsonProtocolArgumentResolver implements HandlerMethodArgumentResolver {

    @Getter
    private static final List<JsonProtocolArgumentResolver> resolvers = new ArrayList<>();

    protected void addResolver(JsonProtocolArgumentResolver resolver) {
        resolvers.add(resolver);
    }

    public String getRequestString(NativeWebRequest webRequest) throws IOException {
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);

        assert request != null;
        BufferedReader reader = request.getReader();
        StringBuilder sb = new StringBuilder();
        char[] buf = new char[1024];
        int len;
        while ((len = reader.read(buf)) != -1) {
            sb.append(buf, 0, len);
        }
        return sb.toString();
    }
}
