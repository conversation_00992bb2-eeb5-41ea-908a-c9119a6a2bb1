package com.desaysv.workserver;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 所有方法共享配置
 */
@Data
@Slf4j
public class SharedConfig {

    //单例
    @Getter
    private static SharedConfig instance = new SharedConfig();

    private String projectName;

    /**
     * 安全地获取当前项目名
     * @return 项目名，如果无法获取则返回null
     */
    public static String getCurrentProjectName() {
        try {
            RequestAttributes attributes = RequestContextHolder.currentRequestAttributes();
            SharedConfig sharedConfig = (SharedConfig) attributes.getAttribute(SharedConfig.class.getSimpleName(), RequestAttributes.SCOPE_REQUEST);
            
            if (sharedConfig != null && sharedConfig.getProjectName() != null) {
                return sharedConfig.getProjectName();
            }
            
            log.debug("SharedConfig中未找到项目名");
            return null;
        } catch (IllegalStateException e) {
            // 不在HTTP请求上下文中
            log.debug("不在HTTP请求上下文中，无法获取项目名");
            return null;
        }
    }

    /**
     * 安全地获取当前项目名，如果获取失败则返回默认值
     * @param defaultValue 默认项目名
     * @return 项目名或默认值
     */
    public static String getCurrentProjectName(String defaultValue) {
        String projectName = getCurrentProjectName();
        return projectName != null ? projectName : defaultValue;
    }
}
