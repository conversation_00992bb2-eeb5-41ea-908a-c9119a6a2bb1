package com.desaysv.workserver.action_sequence;

import static com.desaysv.workserver.action_sequence.ActionSequenceEventBus.PAUSE_ACTION_EVENT;

// 定义一个用于控制操作序列的接口，提供暂停和停止的默认方法
public interface IControllableAction {

    /**
     * 检查是否处于暂停状态
     * 从事件总线的事件状态中获取暂停操作事件的状态
     * 默认返回false，表示未暂停
     *
     * @return 是否暂停
     */
    default boolean isPause() {
        return (boolean) ActionSequenceEventBus.getEventStates().getOrDefault(PAUSE_ACTION_EVENT, false);
    }

    /**
     * 检查是否停止
     * 默认实现始终返回false
     * 子类可以重写此方法以提供特定的停止逻辑
     *
     * @return 是否停止
     */
    default boolean isStop() {
        return false;
    }
}
