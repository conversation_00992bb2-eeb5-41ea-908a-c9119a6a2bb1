package com.desaysv.workserver.action_sequence;

import com.desaysv.workserver.filemanager.project.ActionSequenceTestConfig;

/**
 * 动作序列配置管理器
 * 用于管理和存储动作序列的测试配置信息
 */
public class ActionSequenceConfigManager {

    /**
     * 当前使用的动作序列测试配置
     * 初始化为默认配置
     */
    private static ActionSequenceTestConfig currentConfig;

    /**
     * 设置新的测试配置
     *
     * @param config 要设置的新配置对象
     */
    public static void setConfig(ActionSequenceTestConfig config) {
        if (config != null) {
            currentConfig = config;
        }
    }

    /**
     * 获取当前的测试配置
     *
     * @return 当前使用的测试配置对象
     */
    public static ActionSequenceTestConfig getConfig() {
        if (currentConfig == null) {
            return ActionSequenceTestConfig.defaultConfig();
        }
        return currentConfig;
    }
}
