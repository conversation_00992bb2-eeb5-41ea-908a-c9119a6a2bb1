package com.desaysv.workserver.action_sequence;

import lombok.Getter;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 动作序列事件总线
 * 用于管理和存储动作序列相关的事件状态
 */
public class ActionSequenceEventBus {

    /**
     * 暂停动作事件的标识符
     */
    public final static String PAUSE_ACTION_EVENT = "pauseActionEvent";

    /**
     * 存储事件状态的并发哈希表
     * 使用ConcurrentHashMap确保线程安全
     */
    @Getter
    private static final ConcurrentHashMap<String, Object> eventStates = new ConcurrentHashMap<>();

    /**
     * 设置暂停状态
     *
     * @param pause 是否暂停
     */
    public static void setPause(boolean pause) {
        eventStates.put(PAUSE_ACTION_EVENT, pause);
    }

    /**
     * 移除指定的事件状态
     *
     * @param eventId 要移除的事件ID
     */
    public static void remove(String eventId) {
        eventStates.remove(eventId);
    }

}
