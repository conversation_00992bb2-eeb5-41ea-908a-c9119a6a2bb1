package com.desaysv.workserver.action_sequence;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

public class AtomicActualExpectedResult extends ActualExpectedResult {
    private final AtomicBoolean pass = new AtomicBoolean(false);
    private final AtomicReference<Double> value = new AtomicReference<>(Double.NaN);
    private final AtomicBoolean isComplete = new AtomicBoolean(false);

    @Override
    public void putResult(String operation, boolean passVal, double valueVal) {
        this.pass.set(passVal);
        this.value.set(valueVal);
    }

    @Override
    public void markComplete(boolean success) {
        this.isComplete.set(true);
        if (!success) this.pass.set(false);
    }

    // 线程安全的获取方法
    public boolean isComplete() {
        return isComplete.get();
    }

    public boolean isPass() {
        return pass.get();
    }

    public double getValue() {
        return value.get();
    }
}
