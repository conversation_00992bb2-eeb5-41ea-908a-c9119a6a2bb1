package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;

import java.io.File;

/**
 * 管理项目中特定于专门的A2L相关文件操作。
 * 继承自ProjectFileManager，提供专门的A2L相关文件处理功能。
 */
public class UdpA2lFileManager extends ProjectFileManager {
    /**
     * 存储A2L配置文件的目录路径
     */
    private File defaultA2lConfigPath;

    /**
     * 构造函数。
     *
     * @param projectName 将管理文件配置的项目名称
     */
    public UdpA2lFileManager(String projectName) {
        super(projectName);
    }

    /**
     * 初始化A2l文件配置存储的子路径。
     * 创建一个用于存储A2l文件相关配置文件的默认文件夹。
     *
     * @param dynamicFolderName
     */
    @Override
    protected void initSubPaths(String dynamicFolderName) {
        defaultA2lConfigPath = createFolder(udpConfigPath, "A2LConfig");
    }

    /**
     * 将A2l文件配置写入JSON文件。
     *
     * @param config 要写入文件的A2l文件配置字符串
     */
    public void writeUdpA2lConfig(String config) {
        File udpA2lConfig = new File(defaultA2lConfigPath, "udpA2lConfig.json");
        ThreadSafeFileUtils.writeFileFromString(udpA2lConfig, config, false);
    }

    /**
     * 读取A2l文件配置。
     *
     * @return 如果文件存在，返回A2l文件配置的字符串内容；否则返回空字符串
     */
    public String readUdpA2lConfig() {
        File udpA2lConfig = new File(defaultA2lConfigPath, "udpA2lConfig.json");
        if (udpA2lConfig.exists()) {
            return ThreadSafeFileUtils.readFileToString(udpA2lConfig);
        }
        return "";
    }

}
