package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;

import java.io.File;

/**
 * 管理项目中特定于屏幕的文件操作。
 * 继承自ProjectFileManager，提供专门的屏幕相关文件处理功能。
 */
public class ScreenFileManager extends ProjectFileManager {
    /**
     * 存储屏幕配置文件的目录路径
     */
    private File defaultScreenConfigPath;

    /**
     * 构造函数，为特定项目初始化ScreenFileManager。
     *
     * @param projectName 将管理屏幕配置的项目名称
     */
    public ScreenFileManager(String projectName) {
        super(projectName);
    }

    /**
     * 初始化屏幕配置存储的子路径。
     * 创建一个用于存储屏幕相关配置文件的默认文件夹。
     *
     * @param dynamicFolderName
     */
    @Override
    protected void initSubPaths(String dynamicFolderName) {
        defaultScreenConfigPath = createFolder(screenConfigPath, "default");
    }

    /**
     * 将屏幕配置写入JSON文件。
     *
     * @param screenConfig 要写入文件的屏幕配置字符串
     */
    public void writeScreenConfig(String screenConfig) {
        File userTouchDataDefinition = new File(defaultScreenConfigPath, "screenConfig.json");
        ThreadSafeFileUtils.writeFileFromString(userTouchDataDefinition, screenConfig, false);
    }

    /**
     * 读取屏幕配置JSON文件。
     *
     * @return 如果文件存在，返回屏幕配置的字符串内容；否则返回空字符串
     */
    public String readScreenConfig() {
        File userTouchDataDefinition = new File(defaultScreenConfigPath, "screenConfig.json");
        if (userTouchDataDefinition.exists()) {
            return ThreadSafeFileUtils.readFileToString(userTouchDataDefinition);
        }
        return "";
    }

}
