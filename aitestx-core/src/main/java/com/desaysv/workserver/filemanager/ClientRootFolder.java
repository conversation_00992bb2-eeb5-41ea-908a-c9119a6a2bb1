package com.desaysv.workserver.filemanager;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;

/**
 * FlyTest客户端安装文件夹
 */
@Getter
public class ClientRootFolder extends Folder {
    public static final File rootPath = new File("D:/FlyTest");
    public static final String TOOLS = "tools";

    private final Folder toolsFolder;
    private final Folder databaseFolder;
    private final Folder dataFolder;
    private final Folder libraryFolder;
    private final Folder logFolder;

    public ClientRootFolder() {
        super(rootPath);
        toolsFolder = createFolder(TOOLS);
        databaseFolder = createFolder("db");
        dataFolder = createFolder("data");
        libraryFolder = createFolder("library");
        logFolder = createFolder("log").createFolder("Details");
        logFolder.createFolder("server");
        logFolder.createFolder("client");
    }

}
