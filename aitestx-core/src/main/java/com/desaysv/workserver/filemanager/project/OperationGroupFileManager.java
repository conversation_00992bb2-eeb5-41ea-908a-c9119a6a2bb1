package com.desaysv.workserver.filemanager.project;

import com.alibaba.fastjson2.JSONArray;
import com.desaysv.workserver.base.operation.base.Operation;
import com.desaysv.workserver.base.operation.base.OperationGroup;
import com.desaysv.workserver.base.operation.base.OperationJsonObject;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 动作组合文件管理器
 */
@Slf4j
public class OperationGroupFileManager extends ProjectFileManager {

    public static final class _OperationGroupConstants {

        public File operationGroupFileDBPath; //操作组合存放路径

    }

    public _OperationGroupConstants operationGroupConstants;

    public OperationGroupFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        operationGroupConstants = new _OperationGroupConstants();
        operationGroupConstants.operationGroupFileDBPath = createFolder(fileDbPath, "operationGroups");
    }

    public boolean addOperationGroup(OperationGroup operationGroup) {
        File file = new File(operationGroupConstants.operationGroupFileDBPath, operationGroup.getGroupName() + ".json");
        return ThreadSafeFileUtils.writeFileFromString(file, JSONArray.toJSONString(operationGroup.getOperationList()), false);
    }

    public OperationGroup loadOperationGroup(String groupName) {
        final Optional<OperationGroup>[] operationGroupOptional = new Optional[]{Optional.empty()};
        try {
            Files.walkFileTree(Paths.get(operationGroupConstants.operationGroupFileDBPath.toURI()), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    String gpName = cn.hutool.core.io.FileUtil.mainName(file.getFileName().toString());
                    if (gpName.equals(groupName)) {
                        OperationGroup operationGroup = new OperationGroup();
                        operationGroup.setProjectName(getProjectName());
                        operationGroup.setGroupName(gpName);
                        try {
                            operationGroup.setOperationList(JSONArray.parseArray(ThreadSafeFileUtils.readFileToString(file.toFile()), Operation.class));
                        } catch (Exception e) {
                            log.error("Error parsing operation list from file: {}", file, e);

                            return FileVisitResult.CONTINUE;
                        }
                        operationGroupOptional[0] = Optional.of(operationGroup);
                        return FileVisitResult.TERMINATE;
                    }
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return operationGroupOptional[0].orElse(null);
    }

    public List<OperationGroup> loadOperationGroups() {
        List<OperationGroup> operationGroups = new ArrayList<>();
        try {
            Files.walkFileTree(Paths.get(operationGroupConstants.operationGroupFileDBPath.toURI()), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    OperationGroup operationGroup = new OperationGroup();
                    operationGroup.setProjectName(getProjectName());
                    operationGroup.setGroupName(cn.hutool.core.io.FileUtil.mainName(file.getFileName().toString()));
                    try {
                        operationGroup.setOperationList(JSONArray.parseArray(ThreadSafeFileUtils.readFileToString(file.toFile()), Operation.class));
                    } catch (Exception e) {
                        log.error("Error parsing operation list from file: {}", file, e);
                        return FileVisitResult.CONTINUE;
                    }
                    operationGroups.add(operationGroup);
                    return super.visitFile(file, attrs);
                }
            });
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return operationGroups;
    }

    public List<OperationJsonObject> loadJsonOperationGroup(OperationGroup operationGroup) {
        File file = new File(operationGroupConstants.operationGroupFileDBPath, operationGroup.getGroupName() + ".json");
        log.info("导入步骤组合:{}", file.getAbsolutePath());
        return JSONArray.parseArray(ThreadSafeFileUtils.readFileToString(file), OperationJsonObject.class);
    }

}
