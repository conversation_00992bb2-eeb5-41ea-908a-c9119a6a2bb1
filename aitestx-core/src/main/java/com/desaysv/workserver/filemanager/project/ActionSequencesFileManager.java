package com.desaysv.workserver.filemanager.project;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.utils.StrUtils;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 动作序列文件管理器
 * 继承自ProjectFileManager，专门用于管理动作序列测试配置文件
 */
@Slf4j
public class ActionSequencesFileManager extends ProjectFileManager {
    // 动作序列测试配置文件
    private File actionSequencesTestConfigFile;

    /**
     * 构造方法
     *
     * @param projectName 项目名称
     */
    public ActionSequencesFileManager(String projectName) {
        super(projectName);
    }

    /**
     * 初始化子路径
     * 创建动作序列测试配置文件
     *
     * @param dynamicFolderName 动态文件夹名称
     */
    @Override
    protected void initSubPaths(String dynamicFolderName) {
        // 在指定路径创建actionSequenceTestConfig.json文件
        actionSequencesTestConfigFile = new File(actionSequenceTestConfigPath, "actionSequenceTestConfig.json");
    }

    /**
     * 读取动作序列测试配置
     * 从JSON文件中反序列化配置信息
     *
     * @return ActionSequenceTestConfig 动作序列测试配置对象
     */
    public ActionSequenceTestConfig readActionSequencesTestConfig() {
        try {
            // 使用FileUtils读取文件内容，并使用fastjson反序列化为配置对象
            String config = ThreadSafeFileUtils.readFileToString(actionSequencesTestConfigFile);
            if (StrUtils.isEmpty(config)) {
                return ActionSequenceTestConfig.defaultConfig();
            }
            return JSON.to(ActionSequenceTestConfig.class, config);
        } catch (Exception e) {
            // 发生异常时记录错误日志，并返回一个新的空配置对象
            log.error(e.getMessage(), e);
            return ActionSequenceTestConfig.defaultConfig();
        }
    }

    /**
     * 写入动作序列测试配置
     * 将配置对象序列化为JSON并写入文件
     *
     * @param actionSequenceTestConfig 待写入的动作序列测试配置对象
     */
    public void writeActionSequencesTestConfig(ActionSequenceTestConfig actionSequenceTestConfig) {
        try {
            // 使用fastjson将配置对象转换为JSON字符串，并使用FileUtils写入文件
            ThreadSafeFileUtils.writeFileFromString(actionSequencesTestConfigFile, JSON.toJSONString(actionSequenceTestConfig));
        } catch (Exception e) {
            // 发生异常时记录错误日志
            log.error(e.getMessage(), e);
        }
    }

}