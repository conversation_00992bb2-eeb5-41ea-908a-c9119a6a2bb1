package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.Getter;

import java.io.File;

@Getter
public class LightTestBoxFileManager extends ProjectFileManager {
    private File lightTestBoxFilePath;

    public LightTestBoxFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        lightTestBoxFilePath = new File(lightTestBoxConfigPath, "lightTestBoxConfig.json");
    }

    public void writeConfigFile(String config) {
        ThreadSafeFileUtils.writeFileFromString(lightTestBoxFilePath, config, false);
    }

    public String readConfigFile() {
        return ThreadSafeFileUtils.readFileToString(lightTestBoxFilePath);
    }

    public boolean isConfigFileExist() {
        return lightTestBoxFilePath.exists();
    }

}
