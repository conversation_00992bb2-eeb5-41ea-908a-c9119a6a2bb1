package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.entity.ImageAlgorithmConfig;
import lombok.Data;

@Data
public class ActionSequenceTestConfig {
    private ImageAlgorithmConfig imageAlgorithmConfig;

    private String projectName;

    public static ActionSequenceTestConfig defaultConfig() {
        ActionSequenceTestConfig config = new ActionSequenceTestConfig();
        ImageAlgorithmConfig imageAlgorithmConfig = new ImageAlgorithmConfig();
        config.setImageAlgorithmConfig(imageAlgorithmConfig);
        return config;
    }
}
