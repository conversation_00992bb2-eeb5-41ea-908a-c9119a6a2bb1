package com.desaysv.workserver.filemanager.project;

import lombok.Getter;

import java.io.File;

/**
 * 测试脚本运行日志
 */
@Getter
public class TestCaseLogFileManager extends ProjectFileManager {

    private File testLoggerFolder; // 测试日志文件夹

    public TestCaseLogFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        File logFolder = createFolder(fileDbPath, "logs");
        testLoggerFolder = createFolder(logFolder, "testSuiteLog");
    }

    /**
     * 获取日志文件夹
     *
     * @param folder 文件夹
     * @return
     */
    public File getFolder(String folder) {
        return new File(testLoggerFolder, folder);
    }

}
