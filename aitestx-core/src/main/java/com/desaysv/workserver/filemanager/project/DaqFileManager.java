package com.desaysv.workserver.filemanager.project;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.bytedeco.leptonica.NUMA;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 数据采集文件管理器
 */
@Slf4j
public class DaqFileManager<T> extends ProjectFileManager implements Runnable {
    public static final String TXT_SUFFIX_WITH_DOT = ".txt";
    private boolean running; //运行标识符
    private final BlockingQueue<T> dataQueue; //数据队列
    private final NumberFormat numberFormat;
    private BufferedWriter bufferedWriter;
    private File dataFile;
    private final float fileSizeLimit;
    private final String fileName;
    private final SimpleDateFormat pathDateFormat = new SimpleDateFormat("yyyyMMdd_hhmmss");

    public DaqFileManager(String projectName, String fileName) throws IOException {
        this(projectName, fileName, -1);
    }

    /**
     * 构造器
     *
     * @param projectName   项目名
     * @param fileName      文件名
     * @param fileSizeLimit 文件限制大小（M）
     * @throws IOException
     */
    public DaqFileManager(String projectName, String fileName, float fileSizeLimit) throws IOException {
        super(projectName);
        this.fileSizeLimit = fileSizeLimit * 1024 * 1024;
        this.fileName = fileName;
        File folder = createFolder(daqFileConstants.daqFileDBPath, pathDateFormat.format(new Date()));
        dataFile = new File(folder, fileName + TXT_SUFFIX_WITH_DOT);
        bufferedWriter = new BufferedWriter(new FileWriter(dataFile));
        running = true;
        dataQueue = new LinkedBlockingQueue<>();
        Executors.newSingleThreadExecutor().execute(this);
        numberFormat = NumberFormat.getInstance();
        numberFormat.setGroupingUsed(false);// 不用科学计数
        numberFormat.setMaximumFractionDigits(6);
    }

    /**
     * 结束采集
     */
    public void finish() {
        running = false;
        log.info("采集结束，保存在文件夹:{}", dataFile.getParentFile().getAbsolutePath());
    }

    public static final class _DaqFileConstants {
        public File daqFileDBPath;
    }

    public _DaqFileConstants daqFileConstants;

    private void initDaqFilePaths() {
        daqFileConstants = new _DaqFileConstants();
        daqFileConstants.daqFileDBPath = createFolder(fileDbPath, "dataAcq");
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        initDaqFilePaths();
    }

    /**
     * 写入采集数据
     *
     * @param acquireData 采集数据
     */
    public void write(T acquireData) {
        dataQueue.add(acquireData);
    }

    @Override
    public void run() {
        try {
            int fileIndex = 0;
            while (running) {
                T data = dataQueue.take();
                Date now = new Date();
                SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                bufferedWriter.write(String.format("%s\t%s\n", ft.format(now), data instanceof Number ? numberFormat.format(data) : data));
                bufferedWriter.flush();
                if (fileSizeLimit > 0) {
                    //限定大小
                    long length = dataFile.length();
                    if (length > fileSizeLimit) {
                        //超过最大限制
                        bufferedWriter.close();
                        String newFileName = String.format("%s#%d_%s%s", fileName, ++fileIndex, pathDateFormat.format(new Date()), TXT_SUFFIX_WITH_DOT);
                        dataFile = new File(dataFile.getParent(), newFileName);
                        log.info("文件大小{}超过限制，重新创建新文件:{}", FileUtils.byteCountToDisplaySize(length), dataFile.getAbsolutePath());
                        bufferedWriter = new BufferedWriter(new FileWriter(dataFile));
                    }
                }
            }
            bufferedWriter.close();
        } catch (IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        File file = new File("D:\\helloworld.txt");
        System.out.println(file.getName());
//        NumberFormat numberFormat = NumberFormat.getInstance();
//        numberFormat.setGroupingUsed(false);// 不用科学计数
//        numberFormat.setMaximumFractionDigits(10);
//        System.out.println(numberFormat.format(0.00313866));
    }

}
