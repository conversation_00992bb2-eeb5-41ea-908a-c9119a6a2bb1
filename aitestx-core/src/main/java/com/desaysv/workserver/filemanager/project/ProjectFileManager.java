package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.filemanager.DefaultFileManager;
import com.desaysv.workserver.filemanager.FileManager;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-11 10:50
 * @description :
 * @modified By :
 * @since : 2022-5-11
 */
@Slf4j
public abstract class ProjectFileManager extends FileManager {

    private static final Map<String, ProjectFileManager> fileManagerMap = new ConcurrentHashMap<>();
    public static final File baseTestProjectPath = new File(baseAppDataPath, "projects");

    static {
        createFolder(baseTestProjectPath);
    }

    private final File testProjectPath;

    @Getter
    private static String projectName;

    public ProjectFileManager(String projectName, String dynamicFolderName) {
        ProjectFileManager.projectName = projectName;
        this.testProjectPath = new File(baseTestProjectPath, projectName);
        initPaths();
        initSubPaths(dynamicFolderName);
    }

    public ProjectFileManager(String projectName) {
        this(projectName, null);
    }


    public synchronized static <T extends ProjectFileManager> T of(Class<T> tClass) {
        return of(projectName, null, tClass);
    }

    public synchronized static <T extends ProjectFileManager> T of(String projectName, Class<T> tClass) {
        return of(projectName, null, tClass);
    }

    @SuppressWarnings("unchecked")
    public synchronized static <T extends ProjectFileManager> T of(String projectName, String dynamicFolderName, Class<T> tClass) {
        String qualifiedKey;
        if (dynamicFolderName == null) {
            qualifiedKey = projectName + "_" + tClass.getSimpleName();
        } else {
            qualifiedKey = projectName + "_" + dynamicFolderName + "_" + tClass.getSimpleName();
        }
        // TODO：加TestClient设置
        if (!fileManagerMap.containsKey(qualifiedKey)) {
            ProjectFileManager projectFileManager;
            try {
                if (dynamicFolderName == null) {
                    projectFileManager = tClass.getConstructor(String.class).newInstance(projectName);
                } else {
                    projectFileManager = tClass.getConstructor(String.class, String.class).newInstance(projectName, dynamicFolderName);
                }
            } catch (InvocationTargetException | InstantiationException | IllegalAccessException |
                     NoSuchMethodException e) {
                log.error(e.getMessage(), e);
                projectFileManager = dynamicFolderName == null ? new DefaultFileManager(projectName) : new DefaultFileManager(projectName, dynamicFolderName);
            }
            fileManagerMap.put(qualifiedKey, projectFileManager);
        }
        return (T) fileManagerMap.get(qualifiedKey);
    }

    protected File dbPath; //数据库文件夹
    protected File configPath;//配置文件夹

    protected File fileDbPath;
    protected File fdxConfigPath;
    protected File lightTestBoxConfigPath;
    protected File actionSequenceTestConfigPath;
    protected File deviceConfigPath;
    protected File screenConfigPath;
    protected File udpConfigPath;


    /**
     * 配置文件路径
     * device：设备相关
     * screen：屏幕相关
     * fdx：vector fdx相关
     * action_sequences：动作序列相关
     */
    public void initPaths() {
        createFolder(testProjectPath);
        //配置config路径
        configPath = createFolder(testProjectPath, "config");

        //配置子路径
        deviceConfigPath = createFolder(configPath, "device");
        screenConfigPath = createFolder(configPath, "screen");
        fdxConfigPath = createFolder(configPath, "fdx");
        lightTestBoxConfigPath = createFolder(configPath, "testBox");
        actionSequenceTestConfigPath = createFolder(configPath, "actionSequences");
        udpConfigPath = createFolder(configPath, "udp");

        //配置database路径
        dbPath = createFolder(testProjectPath, "database");
        fileDbPath = createFolder(dbPath, "fileDB");
    }

    protected abstract void initSubPaths(String dynamicFolderName);

}
