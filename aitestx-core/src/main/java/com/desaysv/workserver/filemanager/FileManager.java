package com.desaysv.workserver.filemanager;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

@Slf4j
public class FileManager {

    public static final String JSON_SUFFIX_WITH_DOT = ".json";
    public static final String JSON_SUFFIX = "json";

    public static final File baseFolder = new File(ClientRootFolder.rootPath, "data");
    public static final File baseAppDataPath = new File(baseFolder, "server");

    public static final File baseSystemPath = new File(baseAppDataPath, "system");
    public static final File baseConfigPath = new File(baseSystemPath, "config");

    static {
        createFolder(baseAppDataPath);
    }

    protected static File createFolder(File folder) {
        if (!folder.exists()) {
            log.info("创建文件夹:{}", folder.getAbsoluteFile());
            try {
                // 使用Java NIO的Files.createDirectories()方法，它对中文路径有更好的支持
                Path path = folder.toPath();
                Files.createDirectories(path);
            } catch (IOException e) {
                log.error("创建文件夹失败: {}", folder.getAbsolutePath(), e);
                // 如果NIO方法失败，尝试使用传统方法作为备选
                folder.mkdirs();
            }
        }
        return folder;
    }

    protected static File createFolder(File parent, String child) {
        File folder = new File(parent, child);
        return createFolder(folder);
    }
}
