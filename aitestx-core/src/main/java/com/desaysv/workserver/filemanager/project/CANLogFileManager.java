package com.desaysv.workserver.filemanager.project;

import java.io.File;

public class CANLogFileManager extends ProjectFileManager {
    private File CANLogFileFolder; // 测试日志文件夹

    public CANLogFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        File logFolder = createFolder(fileDbPath, "logs");
        CANLogFileFolder = createFolder(logFolder, "CANLogs");
    }

    public File getFolder(String folder) {
        return new File(CANLogFileFolder, folder);
    }

}
