package com.desaysv.workserver.filemanager.project;

import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.Getter;

import java.io.File;

@Getter
public class FdxFileManager extends ProjectFileManager {
    private File fdxFilePath;

    public FdxFileManager(String projectName) {
        super(projectName);
    }

    @Override
    protected void initSubPaths(String dynamicFolderName) {
        fdxFilePath = new File(fdxConfigPath, "fdxDescriptionConfig.json");
    }

    public void writeFdxConfig(String fdxConfig) {
        ThreadSafeFileUtils.writeFileFromString(fdxFilePath, fdxConfig, false);
    }

    public String readFdxConfig() {
        return ThreadSafeFileUtils.readFileToString(fdxFilePath);
    }

    public boolean isFdxConfigExist() {
        return fdxFilePath.exists();
    }

}
