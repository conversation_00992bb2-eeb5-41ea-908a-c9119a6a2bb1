package com.desaysv.workserver;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.websocket.Session;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public class WebSocketMessageHandler {

    // 在WebSocketMessageHandler类中添加消息队列
    private static final Map<String, BlockingQueue<String>> messageQueues = new ConcurrentHashMap<>();
    private static final Map<String, AtomicBoolean> processingFlags = new ConcurrentHashMap<>();
    // 添加一个Map来保存需要停止的会话ID
    private static final Map<String, AtomicBoolean> stopFlags = new ConcurrentHashMap<>();

    // 1. 固定线程池
    private static final ExecutorService executorService =
            Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

    // 重试常量和调度线程池
    private static final int MAX_RETRIES = 3;
    private static final long INITIAL_RETRY_DELAY = 50L;
    private static final ScheduledExecutorService retryScheduler =
            Executors.newScheduledThreadPool(1);

    public void sendMessage(Session session, String message, boolean async) {
        if (session == null || !session.isOpen() || StringUtils.isBlank(message)) {
            return;
        }

        sendSingleMessage(session, message, async);
    }

    private void sendSingleMessage(Session session, String message, boolean async) {
        if (session == null || !session.isOpen()) {
            return;
        }

        // 获取或创建会话的消息队列
        BlockingQueue<String> queue = messageQueues.computeIfAbsent(
                session.getId(), k -> new LinkedBlockingQueue<>());
        AtomicBoolean isProcessing = processingFlags.computeIfAbsent(
                session.getId(), k -> new AtomicBoolean(false));
        // 确保停止标志存在
        stopFlags.computeIfAbsent(session.getId(), k -> new AtomicBoolean(false));

        // 将消息添加到队列
        queue.add(message);

        // 如果没有处理中的消息，开始处理
        if (isProcessing.compareAndSet(false, true)) {
            // 2. 使用线程池而非 CompletableFuture.runAsync
            executorService.execute(() -> processMessageQueue(session));
        }
    }

    private void processMessageQueue(Session session) {
        if (session == null || !session.isOpen()) {
            return;
        }

        String sessionId = session.getId();
        BlockingQueue<String> queue = messageQueues.get(sessionId);
        AtomicBoolean isProcessing = processingFlags.get(sessionId);
        AtomicBoolean stopFlag = stopFlags.get(sessionId);

        try {
            while (session.isOpen() && !Thread.currentThread().isInterrupted() && !stopFlag.get()) {
                try {
                    // 3. poll 代替 take，超时后可继续检查退出条件
                    String message = queue.poll(100, TimeUnit.MILLISECONDS);
                    if (message == null) {
                        continue;
                    }

                    // 非阻塞重试调用
                    sendWithRetryAsync(session, message, sessionId, 0);
                } catch (InterruptedException e) {
                    log.info("消息处理线程被中断，会话id：{}", sessionId);
                    Thread.currentThread().interrupt(); // 重新设置中断标志
                    break;
                }
            }
        } catch (Exception e) {
            log.error("处理消息队列异常: {}", e.getMessage());
        } finally {
            isProcessing.set(false);

            // 清理资源
            if (!session.isOpen()) {
                stopFlag.set(true);
                messageQueues.remove(sessionId);
                processingFlags.remove(sessionId);
                stopFlags.remove(sessionId);
            }
        }
    }

    // 添加一个方法来停止特定会话的消息处理
    public void stopMessageProcessing(String sessionId) {
        AtomicBoolean stopFlag = stopFlags.get(sessionId);
        if (stopFlag != null) {
            stopFlag.set(true);
        }
    }

    private static void sendMessage(Session session, String message) {
        try {
            if (session != null && session.isOpen()) {
                session.getAsyncRemote().sendText(message, result -> {
                    if (result.isOK()) {
                        // 发送成功的逻辑（如果需要）
                    } else {
                        // 处理发送失败的情况
                        log.error("发送websocket消息失败，会话id：{}，异常：{}", session.getId(), result.getException());
                    }
                });
            }
        } catch (Exception e) {
            log.error("异步发送消息时出现异常: {}", e.getMessage());

        }
    }

    // 新增：非阻塞调度式重试
    private void sendWithRetryAsync(Session session, String message, String sessionId, int attempt) {
        if (session == null || !session.isOpen()) return;
        session.getAsyncRemote().sendText(message, result -> {
            if (result.isOK()) return;

            String errorMsg = Optional.ofNullable(result.getException())
                    .map(Throwable::getMessage).orElse("");
            if (attempt + 1 < MAX_RETRIES && errorMsg.contains("TEXT_FULL_WRITING")) {
                long delay = INITIAL_RETRY_DELAY * (1L << attempt);
                retryScheduler.schedule(
                        () -> sendWithRetryAsync(session, message, sessionId, attempt + 1),
                        delay, TimeUnit.MILLISECONDS
                );
            } else {
                log.error("发送WebSocket消息失败，会话ID：{}，尝试次数：{}，错误：{}", sessionId, attempt + 1, errorMsg);
            }
        });
    }

}