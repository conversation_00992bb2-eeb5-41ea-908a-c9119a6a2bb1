package com.desaysv.workserver.entity;

import com.desaysv.workserver.constants.AlgorithmSets;
import lombok.Data;

@Data
public class ImageAlgorithmConfig {
    // 图像等待策略常量 - 英文固定值
    public static final String WAIT_POLICY_WITHOUT_TIME = "NO_WAIT";
    public static final String WAIT_POLICY_FIXED_TIME = "PRE_WAIT";
    public static final String WAIT_POLICY_CONTINUOUS_CHECK = "CONTINUOUS_CHECK";
    public static final String WAIT_POLICY_FIXED_TIME_PLUS_CONTINUOUS_CHECK = "PRE_WAIT_PLUS_CONTINUOUS_CHECK";

    private boolean enableBinarization; //是否启用二值化

    private boolean enableOcrStrictMatch; //是否启用ocr严格匹配

    private boolean enableStopAll;//是否启用停止所有报文发送

    private float similarityThreshold; //相似度阈值

    private String algorithm = AlgorithmSets.strictTemplateMatching; //算法类型

    private float roiEnlargePercent = 0; //识别区域扩大百分比

    private boolean fullImageSearch = false; //是否启用整图搜索

    private boolean smartWaitEnabled; //是否启用智能等待

    private String imageWaitPolicy = WAIT_POLICY_WITHOUT_TIME; //图像等待策略：不等待、固定时间、持续判断、固定时间+持续判断

    private float preWaitTime = 0; //前置等待时间（秒），默认10秒，用于固定时间策略

    private float checkTimeout = 0; //检测超时时间（秒），默认10秒，用于持续判断策略

    public boolean isPreWait() {
        return imageWaitPolicy.equals(WAIT_POLICY_FIXED_TIME) ||
                imageWaitPolicy.equals(WAIT_POLICY_FIXED_TIME_PLUS_CONTINUOUS_CHECK);
    }

    public boolean isContinuousCheck() {
        return imageWaitPolicy.equals(WAIT_POLICY_CONTINUOUS_CHECK) ||
                imageWaitPolicy.equals(WAIT_POLICY_FIXED_TIME_PLUS_CONTINUOUS_CHECK);
    }

}
