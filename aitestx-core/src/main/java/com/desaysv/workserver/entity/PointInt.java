package com.desaysv.workserver.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointInt {

    private Integer x;
    private Integer y;

    public boolean isValid() {
        return x != null && y != null;
    }

    /**
     * 比较距离
     *
     * @param point 另一个点
     * @return 距离
     */
    public double compareDistance(PointInt point) {
        double deltaX = Math.pow(point.getX() - x, 2);
        double deltaY = Math.pow(point.getY() - y, 2);
        return Math.sqrt(deltaX + deltaY);
    }

    /**
     * x轴正向顺时针角度
     *
     * @param point 相对点
     * @return 角度
     */
    public double toAngle(PointInt point) {
        float angle = (float) Math.toDegrees(Math.atan2(point.y - y, point.x - x));
        if (angle < 0) {
            angle += 360;
        }

        return angle;
    }

    /**
     * 求与直线（开始点-结束点）的夹角
     *
     * @param startPoint 开始点
     * @param endPoint   结束点
     * @return 夹角
     */
    public double toIntersectionAngle(PointInt startPoint, PointInt endPoint) {
        return getAngleByThreePoint(this, endPoint, startPoint);
    }

    public static double getAngleByThreePoint(PointInt a, PointInt b, PointInt intersectionPoint) {
        return getAngleByThreePoint(a, b, intersectionPoint, true);
    }

    public static double getAngleByThreePoint(PointInt a, PointInt b, PointInt intersectionPoint, boolean toAngle) {
        double a_b_x = a.x - intersectionPoint.x;
        double a_b_y = a.y - intersectionPoint.y;
        double c_b_x = b.x - intersectionPoint.x;
        double c_b_y = b.y - intersectionPoint.y;
        double ab_mul_cb = a_b_x * c_b_x + a_b_y * c_b_y;
        double dist_ab = Math.sqrt(a_b_x * a_b_x + a_b_y * a_b_y);
        double dist_cd = Math.sqrt(c_b_x * c_b_x + c_b_y * c_b_y);
        double cosValue = ab_mul_cb / (dist_ab * dist_cd);
        double radian = Math.acos(cosValue);
        if (toAngle) {
            return radian * 180 / Math.PI;
        }
        return radian;
    }

    @Override
    public String toString() {
        return String.format("(%d,%d)", x, y);
    }


}
