package com.desaysv.workserver.entity;

import lombok.Data;
import org.bytedeco.opencv.opencv_core.Rect;

@Data
public class Rectangle {
    private int x;
    private int y;
    private int width;
    private int height;

    public Rectangle(int x, int y, int width, int height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
    }

    public Rect toRect() {
        return new Rect(x, y, width, height);
    }

    public String vertextString() {
        return String.format("左上角(%d,%d)->右下角(%d,%d)", x, y, x + width, y + height);
    }

    public static Rectangle fromRect(Rect rect) {
        return new Rectangle(rect.x(), rect.y(), rect.width(), rect.height());
    }
}
