package com.desaysv.workserver.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class EnhancedPointInt extends PointInt {

    private Integer gestureCode;
    private String gesture;

    public EnhancedPointInt(Integer x, Integer y, Integer gestureCode) {
        super(x, y);
        this.gestureCode = gestureCode;
    }

}
