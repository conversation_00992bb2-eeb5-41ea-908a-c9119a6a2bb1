package com.desaysv.workserver.entity.uiOperator;

import com.desaysv.workserver.base.operation.parameter.OperationParameter;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 11:36
 * @description : UI操作设备
 * @modified By :
 * @since : 2022-3-16
 */
public abstract class UIOperationDevice {

    /**
     * 点击
     *
     * @param parameter 操作参数
     * @return 是否点击成功
     */
    protected abstract boolean click(OperationParameter parameter);

    /**
     * 双击
     *
     * @param parameter 操作参数
     * @return 是否双击成功
     */
    protected abstract boolean dblClick(OperationParameter parameter);

    /**
     * 长按
     *
     * @param parameter 操作参数
     * @return 是否长按成功
     */
    protected abstract boolean longClick(OperationParameter parameter);


    /**
     * 向上滑动
     *
     * @param parameter 操作参数
     * @return 是否向上滑动成功
     */
    protected abstract boolean slideUp(OperationParameter parameter);

    /**
     * 向下滑动
     *
     * @param parameter 操作参数
     * @return 是否向下滑动成功
     */
    protected abstract boolean slideDown(OperationParameter parameter);

    /**
     * 向左滑动
     *
     * @param parameter 操作参数
     * @return 是否向左滑动成功
     */
    protected abstract boolean slideLeft(OperationParameter parameter);

    /**
     * 向右滑动
     *
     * @param parameter 操作参数
     * @return 是否向右滑动成功
     */
    protected abstract boolean slideRight(OperationParameter parameter);


}
