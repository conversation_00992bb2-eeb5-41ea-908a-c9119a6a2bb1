package com.desaysv.workserver.constants;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class AlgorithmSets {
    public static final String allAlgorithmMatching = "All";
    public static final String cvTemplateMatching = "cvTemplateMatching";
    public static final String strictTemplateMatching = "strictTemplateMatching";
    public static final String perPixelTemplateMatching = "perPixelTemplateMatching";
    public static final String colorMatching = "colorMatching";
    public static final String ocrMatching = "ocrMatching";
    public static final String featureMatching = "featureMatching";
    public static final String dynamicStrictTemplateMatching = "dynamicStrictTemplateMatching";
    public static final String lightBlinking = "lightBlinking";
    public static final String intelligentTemplateMatching = "intelligentTemplateMatching";

    public static final Set<String> allAlgorithmSets = new HashSet<>(
            Arrays.asList(
                    cvTemplateMatching,
                    strictTemplateMatching,
                    perPixelTemplateMatching,
                    colorMatching,
                    ocrMatching,
                    featureMatching,
                    dynamicStrictTemplateMatching,
                    lightBlinking,
                    intelligentTemplateMatching
            ));


    public static boolean isInAlgorithmSets(String algorithm) {
        for (String algorithmSet : allAlgorithmSets) {
            if (algorithmSet.equalsIgnoreCase(algorithm)) {
                return true;
            }
        }
        return false;
    }
}
