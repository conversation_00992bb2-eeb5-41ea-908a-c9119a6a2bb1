package com.desaysv.workserver.constants;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-3-16 18:21
 * @description : 设备类型常量
 * @modified By :
 * @since : 2022-3-16
 */
public class DeviceType {
    public static final String DEVICE_POWER = "powerType";
    public static final String DEVICE_CAN = "canType";
    public static final String DEVICE_LIN = "linType";
    public static final String DEVICE_ETHERNET = "ethernetType";
    public static final String DEVICE_PLUG = "plugType";
    public static final String DEVICE_SERIAL = "serialType";
    public static final String DEVICE_ROBOT = "robotType";
    public static final String DEVICE_CAMERA = "cameraType";
    public static final String DEVICE_USB_SWITCH = "usbSwitchType";
    public static final String DEVICE_DAQ = "daqType";
    public static final String DEVICE_ANDROID = "androidType";
    public static final String DEVICE_QNX = "qnxType";
    public static final String DEVICE_TEST_BOX = "testBoxType";
    public static final String DEVICE_AUTO_CLICKER = "autoClickerType";
    public static final String DEVICE_RESISTANCE = "resistanceType";
    public static final String DEVICE_SOUND_CARD = "soundCardType";
    public static final String DEVICE_ELECTRIC_RELAY = "relayType";
    public static final String DEVICE_IOS = "iosType";
    public static final String DEVICE_INSTRUMENT = "instrumentType";
    public static final String DEVICE_TCP_SERVER = "tcpServerType";
    public static final String DEVICE_OSCILLOSCOPE = "oscilloscopeType";
    public static final String DEVICE_VIDEO_CAPTURE = "videoCaptureType";
    public static final String DEVICE_SIGNAL_GENERATOR = "signalGeneratorType";
    public static final String DEVICE_ELECTRONIC_LOAD = "electronicLoadType";
    public static final String DEVICE_UDP = "udpDeviceType";
    public static final String DEVICE_TCP_CLIENT = "tcpClientType";
    public static final String DEVICE_DC_COLLECTOR = "dcCollectorType";
    public static final String DEVICE_SPEAKER = "speakerType"; // 音频输出，音箱
    public static final String COMMON_VIRTUAL = "virtualDeviceType";


    public static List<String> getAllDeviceTypes() throws IllegalAccessException {
        List<String> deviceTypes = new ArrayList<>();
        Field[] fields = DeviceType.class.getFields();
        for (Field field : fields) {
            if (Modifier.isStatic(field.getModifiers())) {
                String value = (String) field.get(DeviceType.class);
                deviceTypes.add(value);
            }
        }
        return deviceTypes;
    }

    public static void main(String[] args) {
        try {
            System.out.println(DeviceType.getAllDeviceTypes());
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
