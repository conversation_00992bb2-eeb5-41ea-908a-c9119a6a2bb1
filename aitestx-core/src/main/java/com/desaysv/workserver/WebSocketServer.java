package com.desaysv.workserver;

import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.socket.adapter.NativeWebSocketSession;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-2-17 16:25
 * @description : 用于接受处理客户端向服务端传递的信息
 * @modified By :
 * @since : 2022-2-17
 */
@Slf4j
public class WebSocketServer {

    @Getter
    private static final Map<String, List<WebSocketServer>> websocketServers = new ConcurrentHashMap<>();

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocketServerListener对象。
     */
    private static final CopyOnWriteArraySet<WebSocketServerListener> webSocketServerListenerSet = new CopyOnWriteArraySet<>();
    private final WebSocketMessageHandler messageHandler;

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static final AtomicInteger onlineCount = new AtomicInteger(0);

    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象
    @Getter
    private static final ConcurrentHashMap<String, WebSocketServer> webSocketMap = new ConcurrentHashMap<>();

    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    @Getter
    private Session session;

    //接收clientId
    private String clientId;


    public WebSocketServer() {
        this.messageHandler = new WebSocketMessageHandler();
    }

    public static void addWebSocketListener(WebSocketServerListener listener) {
        webSocketServerListenerSet.add(listener);
    }

    public static void removeWebSocketListener(WebSocketServerListener listener) {
        webSocketServerListenerSet.remove(listener);
    }

    private void triggerOnError() {
        for (WebSocketServerListener listener : webSocketServerListenerSet) {
            listener.onWebSocketError();
        }
    }

    private void triggerOnClose() {
        for (WebSocketServerListener listener : webSocketServerListenerSet) {
            listener.onWebSocketClose();
        }
    }

    public void registerWebSocketServer(String monitorType, WebSocketServer webSocketServer) {
        if (!websocketServers.containsKey(monitorType)) {
            websocketServers.put(monitorType, new ArrayList<>());
        }
        websocketServers.get(monitorType).add(webSocketServer);
    }

    public void unregisterWebSocketServer(String monitorType, WebSocketServer webSocketServer) {
        if (!websocketServers.containsKey(monitorType)) {
            return;
        }
        websocketServers.get(monitorType).remove(webSocketServer);
    }

    /**
     * 连接建立成功调用的方法
     *
     * @param session 连接会话
     */
    //, @PathParam("clientId") String clientId
    public void onOpen(Session session) {
        this.session = session;
        this.clientId = session.getId();
//        webSocketSet.add(this);
        if (webSocketMap.containsKey(clientId)) {
            webSocketMap.remove(clientId);
        } else {
            addOnlineCount();
        }
        webSocketMap.put(clientId, this);
        afterConnectionEstablished(session);
        log.info("websocket连接：{}，当前在线websocket数量为：{}", session.getRequestURI(), getOnlineCount());
    }

    /**
     * 连接关闭调用的方法
     */
    public void onClose(Session session) {
        if (webSocketMap.containsKey(clientId)) {
            webSocketMap.remove(clientId);
            subOnlineCount();
        }
        log.info("websocket退出：{}，当前在线websocket数量为：{}", session.getRequestURI(), getOnlineCount());
        triggerOnClose();
        // 清理消息队列，结束处理线程
        messageHandler.stopMessageProcessing(session.getId());
    }

    /**
     * 连接建立后处理事件
     *
     * @param session 连接会话
     */
    private void afterConnectionEstablished(Session session) {
        if (session instanceof NativeWebSocketSession) {
            final Session nativeSession = ((NativeWebSocketSession) session).getNativeSession(Session.class);
            if (nativeSession != null) {
                nativeSession.getUserProperties().put("org.apache.tomcat.websocket.BLOCKING_SEND_TIMEOUT", -1);
            }
        }
    }


    /**
     * 收到客户端消息后调用的方法
     *
     * @param session 连接会话
     * @param message 客户端发送过来的消息
     */
    public void onMessage(Session session, String message) {
        log.info("websocket消息：{}，报文：{}", clientId, message);
        if (StringUtils.isNoneBlank(message)) {
            //解析发送的报文
            JSONObject jsonObject = JSONObject.parseObject(message);
            if (jsonObject != null) {
                //追加发送人(防止串改)
                jsonObject.put("fromClientId", clientId);
                String toClientId = jsonObject.getString("toClientId");
                //传送给对应toClientId客户端的websocket
                if (StringUtils.isNoneBlank(toClientId) && webSocketMap.containsKey(toClientId)) {
                    webSocketMap.get(toClientId).asyncSendMessageByText(jsonObject.toJSONString());
                } else {
                    log.warn("请求的clientId：{}不在该服务器上", toClientId);
                }
            }
        }
    }

    /**
     * 通讯错误调用的方法
     *
     * @param session 连接会话
     * @param e       错误
     */
    public void onError(Session session, Throwable e) {
        log.error(String.format("websocket %s -> 发生异常", session.getRequestURI()), e);
        triggerOnError();
        // 清理消息队列
        messageHandler.stopMessageProcessing(session.getId());
    }

    /**
     * 发送自定义消息
     *
     * @param message  自定义消息
     * @param clientId 客户端id
     */
    public static void sendInfo(String message, @PathParam("clientId") String clientId) {
        log.info("发送消息到：{}，报文:{}", clientId, message);
        if (StringUtils.isNotBlank(clientId) && webSocketMap.containsKey(clientId)) {
            webSocketMap.get(clientId).syncSendMessageByText(message);
        } else {
            log.error("websocket {}不在线！", clientId);
        }
    }

    /**
     * 服务端主动推送文本消息
     *
     * @param message 文本消息
     * @throws IOException
     */
    protected void syncSendMessageByText(String message) {
        syncSendMessageByText(session, message);
    }

    protected void syncSendMessageByText(Session session, String message) {
        sendMessageByText(session, message, false);
    }

    protected void asyncSendMessageByText(String message) {
        sendMessageByText(session, message, true);
    }


    protected void sendMessageByText(Session session, String message, boolean async) {
        messageHandler.sendMessage(session, message, async);
    }

    /**
     * 发送Object数据
     *
     * @param message Object数据
     */
    public void sendMessageByObject(Object message) {
        if (message != null) {
            if (session.isOpen() && !Thread.currentThread().isInterrupted()) {
                session.getAsyncRemote().sendObject(message);
            }
        }
    }

    /**
     * 发送ByteBuffer数据
     *
     * @param message ByteBuffer数据
     */
    public void sendBinary(ByteBuffer message) {
        if (message != null) {
            if (session.isOpen() && !Thread.currentThread().isInterrupted()) {
                session.getAsyncRemote().sendBinary(message);
            } else {
                log.error("发送到websocket：{}，信息失败", clientId);
            }
        }
    }

    /**
     * 发送字节数据
     *
     * @param message  字节数据
     * @param clientId 客户端id
     */
    public void sendBytes(byte[] message, @PathParam("clientId") String clientId) {
        log.info("发送字节流到：{}", clientId);
        if (StringUtils.isNotBlank(clientId) && webSocketMap.containsKey(clientId)) {
//            webSocketMap.get(clientId).sendMessage(data);
            if (session.isOpen() && !Thread.currentThread().isInterrupted()) {
                session.getAsyncRemote().sendBinary(ByteBuffer.wrap(message));
            }
        } else {
            log.error("websocket{}不在线！", clientId);
        }
    }

    /**
     * 发送Object信息
     *
     * @param message Object信息
     * @throws EncodeException
     * @throws IOException
     */
    public static void sendAllByObject(Object message) throws EncodeException, IOException {
//        for (WebSocketServer ws : webSocketSet) {
//            ws.sendMessageByObject(message);
//        }
        if (!webSocketMap.isEmpty()) {
            Collection<WebSocketServer> values = webSocketMap.values();
            for (WebSocketServer ws : values) {
                ws.sendMessageByObject(message);
            }
        }
    }

    public static synchronized int getOnlineCount() {
        return onlineCount.get();
    }

    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount.addAndGet(1);
    }

    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount.decrementAndGet();
    }

}
