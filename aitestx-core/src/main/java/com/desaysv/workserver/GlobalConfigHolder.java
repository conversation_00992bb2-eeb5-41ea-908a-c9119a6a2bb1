package com.desaysv.workserver;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.utils.ThreadSafeFileUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;

@Slf4j
@Configuration
public class GlobalConfigHolder {

    private static final String CONFIG_FILE_NAME = "D:\\FlyTest\\data\\client\\app\\config\\globalConfig.json";

    // 提供给外部获取配置的方法
    @Getter
    @Setter
    private static volatile GlobalConfig globalConfig;

    public GlobalConfigHolder() {}

    // 静态代码块用于初始化配置
    static {
        try {
            loadConfig();
        } catch (Exception e) {
            log.error("全局配置初始化失败", e);
            globalConfig = new GlobalConfig(); // 失败也给个默认值
        }
    }

    // 加载配置的核心方法
    private static void loadConfig() throws IOException {
        File configFile = new File(CONFIG_FILE_NAME);

        // 如果文件不存在或长度为0，或者全为NUL字符，则创建默认配置
        if (!configFile.exists() || configFile.length() == 0 || isFileAllNUL(configFile)) {
            log.warn("配置文件不存在、为空或内容无效，正在初始化默认配置...");
            globalConfig = new GlobalConfig();
            save(); // 自动保存一个默认配置
            return;
        }

        log.info("全局配置文件路径: {}", configFile.getAbsolutePath());

        String content = ThreadSafeFileUtils.readFileToString(configFile);
        if (content == null || content.trim().isEmpty()) {
            log.warn("配置文件内容为空");
            globalConfig = new GlobalConfig();
            return;
        }

        try {
            globalConfig = JSON.parseObject(content, GlobalConfig.class);
        } catch (Exception e) {
            log.error("JSON 解析失败，内容为:\n{}", content);
            throw e;
        }
    }

    public static void save() {
        String config = JSON.toJSONString(globalConfig);
        try {
            File configFile = new File(CONFIG_FILE_NAME);
            FileWriter fileWriter = new FileWriter(configFile);
            fileWriter.write(config);
            fileWriter.flush();
            fileWriter.close();
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
    }

    // 检查文件是否全为NUL字符
    private static boolean isFileAllNUL(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            int read;
            while ((read = fis.read()) != -1) {
                if (read != 0) { // 如果读到了非NUL字符
                    return false;
                }
            }
        }
        return true; // 全部都是NUL字符
    }
}