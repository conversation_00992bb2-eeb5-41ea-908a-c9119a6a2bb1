package com.desaysv.workserver.entity;

import lombok.Data;

@Data
public class ColumnNameConstants {
    private String no;
    private String testCaseID;
    private String testKey;
    private String initialCondition;
    private String action;
    private String expectedResult;
    private String preconditionSequences;
    private String operationStepSequences;
    private String expectedResultSequences;
    private String choose;
    private String testResult;
    private String actualResult;
    private String tester;
    private String testTime;
    private String remark;
    private String targetTestTimes;
    private String testedTimes;
    private String testedPassTime;
    private String testScenario;

    private volatile static ColumnNameConstants instance;

    public static ColumnNameConstants getInstance() {
        if (instance == null) {
            synchronized (ColumnNameConstants.class) {
                if (instance == null) {
                    instance = new ColumnNameConstants();
                }
            }
        }
        return instance;
    }

    public void setColumnNames(ColumnNameConstants columnNameConstants) {
        if (columnNameConstants == null) return;
        this.testCaseID = columnNameConstants.getTestCaseID();
        this.testKey = columnNameConstants.getTestKey();
        this.initialCondition = columnNameConstants.getInitialCondition();
        this.action = columnNameConstants.getAction();
        this.expectedResult = columnNameConstants.getExpectedResult();
        this.preconditionSequences = columnNameConstants.getPreconditionSequences();
        this.operationStepSequences = columnNameConstants.getOperationStepSequences();
        this.expectedResultSequences = columnNameConstants.getExpectedResultSequences();
        this.choose = columnNameConstants.getChoose();
        this.testResult = columnNameConstants.getTestResult();
        this.actualResult = columnNameConstants.getActualResult();
        this.tester = columnNameConstants.getTester();
        this.testTime = columnNameConstants.getTestTime();
        this.remark = columnNameConstants.getRemark();
        this.targetTestTimes = columnNameConstants.getTargetTestTimes();
        this.testedTimes = columnNameConstants.getTestedTimes();
        this.testedPassTime = columnNameConstants.getTestedPassTime();
    }

}