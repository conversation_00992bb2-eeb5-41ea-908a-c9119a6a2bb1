package com.desaysv.workserver.entity;

import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

//自动升级结果通知消息类
@Data
public class UpgradeResultReportDto {
    private String projectName;
    private String version;
    private String testUnit;
    private String endTime;
    private String errorMessage;
    private List<String> upgradeResultList;
    private String filePath;//安装包路径
    private String jenkinsJobUrl;//jenkins任务地址
    private String upgradeSuccess;


     public UpgradeResultReportDto(){
     }

     public UpgradeResultReportDto(String projectName, String version, String testUnit, String endTime, String errorMessage){
          this.projectName = projectName;
          this.version = version;
          this.testUnit = testUnit;
          this.endTime = endTime;
          this.errorMessage = errorMessage;
     }

     public UpgradeResultReportDto(List<String> upgradeResultList, String filePath, String jenkinsJobUrl, String upgradeSuccess){
          this.upgradeResultList = upgradeResultList;
          this.filePath = filePath;
          this.jenkinsJobUrl = jenkinsJobUrl;
          this.upgradeSuccess = upgradeSuccess;
     }
}
