package com.desaysv.workserver.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class NotificationManager implements Serializable {
    private String buName;
    private String carFactory;
    private String version;
    private String filePath;
    private volatile static NotificationManager notificationManager;

    public static NotificationManager getInstance() {
        if (notificationManager == null) {
            synchronized (NotificationManager.class) {
                if (notificationManager == null) {
                    notificationManager = new NotificationManager();
                }
            }
        }
        return notificationManager;
    }


    public void setNotificationManager(NotificationManager notificationManager) {
        this.version = notificationManager.getVersion();
        this.buName = notificationManager.getBuName();
        this.carFactory = notificationManager.getCarFactory();
        this.filePath = notificationManager.getFilePath();
    }
}
