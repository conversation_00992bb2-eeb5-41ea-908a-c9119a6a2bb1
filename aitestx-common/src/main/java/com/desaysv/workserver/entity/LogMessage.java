package com.desaysv.workserver.entity;

import com.alibaba.fastjson2.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.websocket.Encoder;
import javax.websocket.EndpointConfig;

@Data
@AllArgsConstructor
@Slf4j
public class LogMessage implements Encoder.Text<LogMessage> {

    private String level; //日志等级
    private String message; //日志消息

    public LogMessage() {
    }

    @Override
    public String encode(LogMessage o) {
        try {
            return JSONObject.toJSONString(o);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public void init(EndpointConfig endpointConfig) {

    }

    @Override
    public void destroy() {

    }
}
