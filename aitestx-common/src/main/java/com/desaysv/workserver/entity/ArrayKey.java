package com.desaysv.workserver.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数组作为键
 *
 * @param <T>
 */
public final class ArrayKey<T> {
    private final List<T> values = new ArrayList<>();
    private final int hash;
    private final String stringValue;

    @SafeVarargs
    public ArrayKey(T... arrayValues) {
        values.addAll(Arrays.asList(arrayValues));
        hash = Arrays.hashCode(arrayValues);
        stringValue = values.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof ArrayKey)) return false;
        return values.equals(((ArrayKey<?>) o).values);
    }

    @Override
    public int hashCode() {
        return hash;
    }

    @Override
    public String toString() {
        return stringValue;
    }
}