package com.desaysv.workserver.excel;

import com.desaysv.workserver.entity.ColumnNameConstants;
import com.desaysv.workserver.response.ResultEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Slf4j
public class ExcelBuilder {
    private volatile static ExcelBuilder excelBuilder;
    private static final ColumnNameConstants columnNameConstants = ColumnNameConstants.getInstance();

    public static ExcelBuilder getInstance() {
        if (excelBuilder == null) {
            synchronized (ExcelBuilder.class) {
                if (excelBuilder == null) {
                    excelBuilder = new ExcelBuilder();
                }
            }
        }
        return excelBuilder;
    }

    /**
     * 写入非图片格式信息
     *
     * @描述：这是一个实体类，提供了相应的接口，用于操作Excel，在任意坐标处写入数据。
     * @参数：String newContent：你要输入的内容
     * int beginRow ：行坐标，Excel从 0 算起
     * int beginColumn ：列坐标，Excel从 0 算起
     * <AUTHOR>
    public static void setSheetCellValue(XSSFSheet sheet, int headerRowNumber, int beginRow, int beginColumn, String newContent) {
        beginRow += headerRowNumber;
//        System.out.println("beginRow:" + beginRow+", beginColumn:"+beginColumn+", newContent:"+newContent);
        XSSFRow row = sheet.getRow(beginRow);
        if (null == row) {
            //如果不做空判断，你必须让你的模板文件画好边框，beginRow和beginCell必须在边框最大值以内,否则会出现空指针异常
            row = sheet.createRow(beginRow);
        }
        XSSFCell cell = row.getCell(beginColumn);
        if (null == cell) {
            cell = row.createCell(beginColumn);
        }
//        cell.setCellType(CellType.STRING);
        //向单元格中放入值
        cell.setCellValue(newContent);
    }


    public ResultEntity<String> exportExcelCaseFile(String filePath, String outputFilePath,
                                                    Map<String, List<ExcelDataEntity>> sheetDataInfoMap, int headerRowNumber) {
        try (FileInputStream file = new FileInputStream(filePath);
             XSSFWorkbook workbook = new XSSFWorkbook(file)) {

            for (Map.Entry<String, List<ExcelDataEntity>> entry : sheetDataInfoMap.entrySet()) {
                String sheetName = entry.getKey();

                XSSFSheet sheet = workbook.getSheet(sheetName);
                if (sheet == null) {
                    log.warn("工作表 '{}' 不存在，跳过写入", sheetName);
                    continue;
                }

                List<ExcelDataEntity> data = entry.getValue();
                if (data == null || data.isEmpty()) {
                    continue;
                }

                // 批量写入数据
                writeSheetData(sheet, headerRowNumber, data);
            }
            // 保存修改后的工作簿到新文件
            try (FileOutputStream outFile = new FileOutputStream(outputFilePath)) {
                workbook.write(outFile);
            }
        } catch (IOException e) {
            log.error("处理Excel文件时发生错误：{}, {}", filePath, e.getMessage());
            return ResultEntity.fail("另一个程序正在打开此文件，导致进程无法访问！");
        } catch (org.apache.poi.EmptyFileException e) {
            log.error("文件为空：{}", filePath, e);
            return ResultEntity.fail("文件为空,请检查！");
        }
        return ResultEntity.ok("Excel文件导出成功");
    }

    private void writeSheetData(XSSFSheet sheet, int headerRowNumber, List<ExcelDataEntity> dataList) {
        int startRow = headerRowNumber; // 数据从表头下一行开始

        for (int i = 0; i < dataList.size(); i++) {
            ExcelDataEntity entity = dataList.get(i);
            XSSFRow row = sheet.getRow(startRow + i);
            if (row == null) {
                row = sheet.createRow(startRow + i);
            }
            for (int column = 0; column < 50; column++) {
                String value = entity.getColumnValue(entity, column + 1);
                XSSFCell cell = row.getCell(column);
                if (cell == null) {
                    cell = row.createCell(column);
                }
                cell.setCellValue(value);
            }
        }
    }

}