package com.desaysv.workserver.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.RowTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import static com.desaysv.workserver.utils.ArrayUtils.removeLastNull;

@Slf4j
@Component
@Lazy
public class ExcelListener extends AnalysisEventListener<HashMap<Integer, String>> {

    @Getter
    private final Map<String, ExcelSheetTable> excelTableMap = new LinkedHashMap<>();

    private final List<List<String>> headerLists = new ArrayList<>();

    /**
     * 组合表头
     *
     * @param headerLists 表头列表
     * @return 表头数组
     */
    private List<String> combineTableHeader(List<List<String>> headerLists) {
        List<String> combinedList = new ArrayList<>();
        int size = headerLists.get(0).size();
        for (int i = 0; i < size; i++) {
            StringBuilder combineHeader = new StringBuilder();
            for (List<String> headerList : headerLists) {
                String header = headerList.get(i);
                if (header != null) {
                    combineHeader.append(header).append("\n");
                }
            }
            String combinedString = combineHeader.toString();
            if (combinedString.isEmpty()) {
                combinedList.add(combinedString);
            } else {
                combinedList.add(combineHeader.substring(0, combineHeader.length() - 1));
            }
        }
        return combinedList;
    }


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext analysisContext) {
        //TODO：动态生成对应列名的类属性
        log.info("正在处理行头: {}", analysisContext.readRowHolder().getRowIndex());
        int headRowNumber = analysisContext.readWorkbookHolder().getHeadRowNumber();
        String sheetName = analysisContext.readSheetHolder().getSheetName();
        if (!excelTableMap.containsKey(sheetName)) {
            excelTableMap.put(sheetName, new ExcelSheetTable());
        }
        int rowIndex = analysisContext.readRowHolder().getRowIndex();
        if (rowIndex == 0) {
            //表头第一行解析，清除数据
            excelTableMap.get(sheetName).clear();
            headerLists.clear();
        }
        List<String> headerList = headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList());
        if (rowIndex < headRowNumber) {
            //在表头内解析
            headerLists.add(headerList);
        }
        if (rowIndex == headRowNumber - 1) {
            //表头最后一行解析
            if (headRowNumber >= 2) {  //表头两行，取第二行，不要走else的组合两行的表头
                excelTableMap.get(sheetName).getTableHeader().addAll(removeLastNull(headerLists.get(headerLists.size() - 1)));
            } else {
                List<String> headers = combineTableHeader(headerLists);
                excelTableMap.get(sheetName).getTableHeader().addAll(headers);
//                log.info("解析到一条头数据:{}", headers);
            }
        }
    }

    @Override
    public void invoke(HashMap<Integer, String> data, AnalysisContext analysisContext) {
        // 检查所有单元格是否为空或者为空字符串
//        boolean isAllEmpty = data.values().stream().allMatch(cellValue -> cellValue == null || cellValue.trim().isEmpty());
        if (RowTypeEnum.EMPTY.equals(analysisContext.readRowHolder().getRowType())) {
            // 检测到空行，停止分析
            log.info("检测到空行，停止分析:{}", analysisContext.readRowHolder().getRowIndex());
            // 停止当前sheet页的分析
            analysisContext.analysisEventProcessor().endSheet(analysisContext);
            throw new ExcelAnalysisStopException();
        }
//        log.info("正在解析:{}", analysisContext.readRowHolder().getRowIndex());
        String sheetName = analysisContext.readSheetHolder().getSheetName();
        if (excelTableMap.containsKey(sheetName)) {
            List<String> tableHeader = excelTableMap.get(sheetName).getTableHeader();

            int limit = Math.min(tableHeader.size(), data.size()); // 确定需要保留多少元素
            HashMap<Integer, String> newData = new LinkedHashMap<>(limit); // 提前指定容量以提高性能
            int count = 0; // 计数器
            for (Map.Entry<Integer, String> entry : data.entrySet()) {
                if (count >= limit) break; // 如果已经达到限制，停止循环
                newData.put(entry.getKey(), entry.getValue());
                count++;
            }
            excelTableMap.get(sheetName).getTableData().add(newData);
        }
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("Excel案例解析完成:{}", analysisContext.readSheetHolder().getSheetName());
    }


    public static void main(String[] args) {
        List<List<String>> headerLists = new ArrayList<>();
        headerLists.add(Arrays.asList("a", "b", "c"));
        headerLists.add(Arrays.asList("d", "e", "f"));
        System.out.println(new ExcelListener().combineTableHeader(headerLists));
    }


}
