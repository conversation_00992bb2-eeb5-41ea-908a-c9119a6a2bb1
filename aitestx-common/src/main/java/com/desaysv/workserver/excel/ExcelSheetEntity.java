package com.desaysv.workserver.excel;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class ExcelSheetEntity {
    private Integer sheetId;
    private Integer excelFileId;
    private String sheetName;
    private Integer selected;
    private String tableHeader;
    private String headerFilterOptions;

    public ExcelSheetEntity() {

    }

    public ExcelSheetEntity(Integer excelFileId, String sheetName, Integer selected, String tableHeader, String headerFilterOptions) {
        this.excelFileId = excelFileId;
        this.sheetName = sheetName;
        this.selected = selected;
        this.tableHeader = tableHeader;
        this.headerFilterOptions = headerFilterOptions;
    }
}