package com.desaysv.workserver.excel;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@Data
@Slf4j
public class ExcelDataEntity {
    private Integer dataId; //unused？
    private Integer sheetId;
    private Integer rowNumber;
    private String column1;
    private String column2;
    private String column3;
    private String column4;
    private String column5;
    private String column6;
    private String column7;
    private String column8;
    private String column9;
    private String column10;
    private String column11;
    private String column12;
    private String column13;
    private String column14;
    private String column15;
    private String column16;
    private String column17;
    private String column18;
    private String column19;
    private String column20;
    private String column21;
    private String column22;
    private String column23;
    private String column24;
    private String column25;
    private String column26;
    private String column27;
    private String column28;
    private String column29;
    private String column30;
    private String column31;
    private String column32;
    private String column33;
    private String column34;
    private String column35;
    private String column36;
    private String column37;
    private String column38;
    private String column39;
    private String column40;
    private String column41;
    private String column42;
    private String column43;
    private String column44;
    private String column45;
    private String column46;
    private String column47;
    private String column48;
    private String column49;
    private String column50;

    // 通用设置方法
    public static void setColumnValue(ExcelDataEntity entity, int columnIndex, String value) {
        if (columnIndex > 50) {
            return;
        }
        try {
            // 构造方法名，例如 "setColumn50"
            String methodName = "setColumn" + columnIndex;
            // 获取对应的方法
            Method method = ExcelDataEntity.class.getMethod(methodName, String.class);
            // 调用方法
            method.invoke(entity, value);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error(e.getMessage(), e);
        }
    }


    public String getColumnValue(ExcelDataEntity entity, int columnIndex) {
        try {
            // 构建方法名，例如 "getColumn50"
            String methodName = "getColumn" + columnIndex;
            // 获取方法对象
            Method method = ExcelDataEntity.class.getMethod(methodName);
            // 调用方法并返回结果
            return (String) method.invoke(entity);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null; // 或者返回一个默认值
        }
    }
}
