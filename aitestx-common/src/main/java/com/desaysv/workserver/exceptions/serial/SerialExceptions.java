package com.desaysv.workserver.exceptions.serial;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-6-7 9:50
 * @description :
 * @modified By :
 * @since : 2022-6-7
 */
public class SerialExceptions {


    public static class SerialPortParameterFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public SerialPortParameterFailure() {
        }

        @Override
        public String toString() {
            return "设置串口参数失败！";
        }

    }

    public static class PortOpenFail extends Exception {
        private final String portName;
        private static final long serialVersionUID = 1L;

        public PortOpenFail(String portName) {
            this.portName = portName;
        }

        @Override
        public String toString() {
            return String.format("串口端口\"%s\"打开错误！", portName);
        }
    }

    public static class PortInUse extends Exception {
        private final String portName;

        private static final long serialVersionUID = 1L;

        public PortInUse(String portName) {
            this.portName = portName;
        }

        @Override
        public String toString() {
            return String.format("串口端口\"%s\"已被占用！", portName);
        }
    }

    public static class NoSuchPort extends Exception {
        private final String portName;

        private static final long serialVersionUID = 1L;

        public NoSuchPort(String portName) {
            this.portName = portName;
        }

        @Override
        public String toString() {
            return String.format("没有该端口\"%s\"对应的串口设备！", portName);
        }
    }

    public static class NotSerialPort extends Exception {
        private final String portName;
        private static final long serialVersionUID = 1L;

        public NotSerialPort(String portName) {
            this.portName = portName;
        }

        @Override
        public String toString() {
            return String.format("端口\"%s\"指向设备不是串口类型！", portName);
        }
    }


    public static class TooManyListeners extends Exception {

        private static final long serialVersionUID = 1L;

        public TooManyListeners() {
        }

        @Override
        public String toString() {
            return "监听类对象过多！";
        }

    }

    public static class UnsupportedCommOperationException extends Exception {

        private static final long serialVersionUID = 1L;

        public UnsupportedCommOperationException() {
        }

        @Override
        public String toString() {
            return "操作不支持！";
        }

    }

    public static class SendDataToSerialPortFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public SendDataToSerialPortFailure() {
        }

        @Override
        public String toString() {
            return "向串口发送数据失败！";
        }
    }


    public static class ReadDataFromSerialPortFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public ReadDataFromSerialPortFailure() {
        }

        public ReadDataFromSerialPortFailure(Throwable e) {
            super(e);
        }

        @Override
        public String toString() {
            return String.format("从串口读取数据时出错: %s", getCause());
        }

    }

    public static class ReadTimeoutFromSerialPortFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public ReadTimeoutFromSerialPortFailure() {
        }

        public ReadTimeoutFromSerialPortFailure(String message) {
            super(message);
        }

        @Override
        public String toString() {
            return "从串口读取数据超时！";
        }

    }

    public static class SerialPortOutputStreamCloseFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public SerialPortOutputStreamCloseFailure() {
        }

        @Override
        public String toString() {
            return "关闭串口对象输出流出错！";
        }

    }

    public static class SerialPortInputStreamCloseFailure extends Exception {

        private static final long serialVersionUID = 1L;

        public SerialPortInputStreamCloseFailure() {
        }

        @Override
        public String toString() {
            return "关闭串口对象输入流出错！";
        }

    }
}
