package com.desaysv.workserver.utils;


import org.apache.commons.math3.distribution.TDistribution;
import org.apache.commons.math3.stat.correlation.PearsonsCorrelation;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 数学工具类
 */
public class MathUtils {

    private static class Pearson {
        private final double[] x;

        private final double[] y;

        private final PearsonsCorrelation p = new PearsonsCorrelation();

        public Pearson(double[] x, double[] y) {
            this.x = x;
            this.y = y;
        }

        public double getR() {

            return p.correlation(x, y);
        }

        public double getTValue() {
            double up = x.length - 2;
            double r = getR();
            double down = 1 - (r * r);
            return r * Math.sqrt(up / down);
        }

        /***
         *
         * @param flag：true=双侧，false=单侧
         * @return
         */
        public double getPValue(boolean flag) {
            TDistribution td = new TDistribution(x.length - 2);
            double t = getTValue();
            double cumulative = td.cumulativeProbability(t);
            double p = t > 0 ? 1 - cumulative : cumulative;
            return flag ? p * 2 : p;
        }
    }

    @Deprecated
    public static double getPersonRatio(double[] x, double[] y) {
        Pearson pearson = new Pearson(x, y);
        return pearson.getPValue(true);
    }

    public static Double getPearsonRelate(double[] xs, double[] ys) {
        return getPearsonRelate(Arrays.stream(xs).boxed().collect(Collectors.toList()),
                Arrays.stream(ys).boxed().collect(Collectors.toList()));
    }

    public static Double getPearsonRelate(List<Double> xs, List<Double> ys) {
        int n = xs.size();
        double Ex = xs.stream().mapToDouble(x -> x).sum();
        double Ey = ys.stream().mapToDouble(y -> y).sum();
        double Ex2 = xs.stream().mapToDouble(x -> Math.pow(x, 2)).sum();
        double Ey2 = ys.stream().mapToDouble(y -> Math.pow(y, 2)).sum();
        double Exy = IntStream.range(0, n).mapToDouble(i -> xs.get(i) * ys.get(i)).sum();
        double numerator = Exy - Ex * Ey / n;
        double denominator = Math.sqrt((Ex2 - Math.pow(Ex, 2) / n) * (Ey2 - Math.pow(Ey, 2) / n));
        if (denominator == 0) return 0.0;
        return numerator / denominator;
    }

}
