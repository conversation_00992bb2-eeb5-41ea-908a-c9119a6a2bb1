package com.desaysv.workserver.utils;
import com.alibaba.fastjson2.JSONObject;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Map;

/**
 * Map映射为Object
 */
public class MapToObj {

    public static <T> T mapToObj(Map source, Class<T> target) throws Exception {
        return mapToObj(source, target, -1);
    }

    public static <T> T mapToObj(Map source, Class<T> target, int superLayer) throws Exception {
        Field[] fields = AnnotationUtils.getAllFields(target, superLayer);
        T o = target.newInstance();
        for (Field field : fields) {
            Object val;
            if ((val = source.get(field.getName())) != null) {
                field.setAccessible(true);
                if (val instanceof JSONObject) {
                    val = JSONObject.parseObject(((JSONObject) val).toJSONString(), field.getType());
                }
                if (val instanceof BigDecimal) {
                    if (field.getType().equals(Float.class)) {
                        val = ((BigDecimal) val).floatValue();
                    } else {
                        val = ((BigDecimal) val).doubleValue();
                    }
                }

                field.set(o, val);
            }
        }
        if (o instanceof Map) {
            for (Field field : fields) {
                source.remove(field.getName());
            }
            ((Map) o).putAll(source);
        }
        return o;
    }

}
