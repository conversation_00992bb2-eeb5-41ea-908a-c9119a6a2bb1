package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 网络工具类
 */
@Slf4j
public class NetworkUtils {
    public static boolean isNetworkAvailable() {
        try {
            URL url = new URL("http://www.baidu.com");
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(3000);
            int responseCode = urlConnection.getResponseCode();
            if (responseCode == 200) {
                return true;
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    public static int getServerPort() {
        return 12399;
    }

    public static String getLocalServerUrl() {
        return String.format("127.0.0.1:%d", getServerPort());
    }

    /**
     * 检查IP地址是否有效
     *
     * @param ip IP地址
     * @return IP地址有效
     */
    public static boolean isIPValid(String ip) {
        String patternString = "(([0,1]?\\d?\\d|2[0-4]\\d|25[0-5])\\.){3}([0,1]?\\d?\\d|2[0-4]\\d|25[0-5])";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    public static synchronized boolean isUrlConnect(String urlString) {
        try {
            return isUrlConnect(urlString, 5000);
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 功能：检测当前URL是否可连接或是否有效
     *
     * @param urlString          指定URL网络地址
     * @param timeoutMillSeconds 超时时间（毫秒）
     * @return URL是否有效
     */
    public static synchronized boolean isUrlConnect(String urlString, int timeoutMillSeconds) throws IOException {
        URL url;
        if (urlString == null || urlString.isEmpty()) {
            return false;
        }

        url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setConnectTimeout(timeoutMillSeconds);
        connection.connect();
        return connection.getResponseCode() == 200;
    }

}
