package com.desaysv.workserver.utils;

import com.serotonin.modbus4j.base.ModbusUtils;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> lhy
 * @date : Created in 2023/5/29 11:15
 * @description :
 * @modified By :
 * @since : 2023/5/29
 **/
public class DataUtils {

    /**
     * 处理不同的十六进制格式0x或者不带0x
     *
     * @param hexString
     * @return
     */
    public static int parseHexString(String hexString) {
        // Remove the "0x" prefix if it exists
        if (hexString.startsWith("0x")) {
            hexString = hexString.substring(2);
        }
        // Parse the remaining string as a hexadecimal integer
        return Integer.parseInt(hexString, 16);
    }

    public static byte[] toByteArray(float f) {
        // 把float转换为byte[]
        int fbit = Float.floatToIntBits(f);

        byte[] b = new byte[4];
        for (int i = 0; i < 4; i++) {
            b[i] = (byte) (fbit >> (24 - i * 8));
        }

        // 翻转数组
        int len = b.length;
        // 建立一个与源数组元素类型相同的数组
        byte[] dest = new byte[len];
        // 为了防止修改源数组，将源数组拷贝一份副本
        System.arraycopy(b, 0, dest, 0, len);
        byte temp;
        // 将顺位第i个与倒数第i个交换
        for (int i = 0; i < len / 2; ++i) {
            temp = dest[i];
            dest[i] = dest[len - i - 1];
            dest[len - i - 1] = temp;
        }
        return dest;
    }


    public static int bytesToInt2(byte[] a) {
        int ans = 0;
        for (int i = 0; i < 4; i++) {
            ans <<= 8;//左移 8 位
            ans |= a[3 - i];//保存 byte 值到 ans 的最低 8 位上
        }
        return ans;
    }


    //将byte数组转换成int数组
    public static int[] byteToIntArray(byte[] a) {

        if ((a.length == 0) || (a.length % 4 != 0)) {
            return null;
        }

        int[] b = new int[a.length / 4];

        int value = 0;

        for (int i = 0; i < a.length / 4; i++) {

            //大字节序
//            value =    a[3+i*4] & 0xFF |
//                      (a[2+i*4] & 0xFF) << 8 |
//                      (a[1+i*4] & 0xFF) << 16 |
//                      (a[0+i*4] & 0xFF) << 24;

            //小字节序
            value = a[i * 4] & 0xFF |
                    (a[1 + i * 4] & 0xFF) << 8 |
                    (a[2 + i * 4] & 0xFF) << 16 |
                    (a[3 + i * 4] & 0xFF) << 24;


            b[i] = value;
        }

        return b;
    }


    /**
     * 将byte转换成double
     *
     * @param arr
     * @return
     */
    public static double bytesToDouble(byte[] arr) {
        long value = 0;
        for (int i = 0; i < 8; i++) {
            value |= ((long) (arr[i] & 0xff)) << (8 * i);
        }
        return Double.longBitsToDouble(value);
    }


    //float转int
    public static short[] floatToBytes(float data) {
        int temp = Float.floatToIntBits(data);
        return int32ToShorts(temp);
    }

    //int转short数组
    public static short[] int32ToShorts(int data) {
        byte[] bytes = int32ToBytes(data);
        return convertToShorts(bytes);
    }


    //int转short数组
    public static short[] int16ToShorts(int data) {
        byte[] bytes = new byte[2];
        bytes[0] = (byte) (data >> 8 & 0xFF);
        bytes[1] = (byte) (data & 0xFF);
        short[] shorts = convertToShorts(bytes);
        return shorts;
    }

    public static byte[] intArrToByteArrBigEndian(int[] intArray) {
        byte[] byteArr = new byte[intArray.length * 4];

        int offset = 0;
        for (int i = 0; i < intArray.length; i++) {
            int value = intArray[i];
            byteArr[offset++] = (byte) (value >> 24 & 0xFF);
            byteArr[offset++] = (byte) (value >> 16 & 0xFF);
            byteArr[offset++] = (byte) (value >> 8 & 0xFF);
            byteArr[offset++] = (byte) (value & 0xFF);
        }
        return byteArr;
    }


    public static byte[] int16ArrToByteArrBigEndian(int[] intArray) {
        byte[] byteArr = new byte[intArray.length * 2];

        int offset = 0;
        for (int i = 0; i < intArray.length; i++) {
            int value = intArray[i];
//            byteArr[offset++] = (byte) (value >> 24 & 0xFF);
//            byteArr[offset++] = (byte) (value >> 16 & 0xFF);
            byteArr[offset++] = (byte) (value >> 8 & 0xFF);
            byteArr[offset++] = (byte) (value & 0xFF);
        }
        return byteArr;
    }


    /**
     * 将int数值转换为占四个字节的byte数组
     * * 将int转为高字节在前，低字节在后的byte数组
     *
     * @param value 要转换的int值
     * @return byte数组
     */
    public static byte[] int32ToBytes(int value) {
        byte[] src = new byte[4];
        src[0] = (byte) ((value >> 24) & 0xFF);
        src[1] = (byte) ((value >> 16) & 0xFF);
        src[2] = (byte) ((value >> 8) & 0xFF);
        src[3] = (byte) (value & 0xFF);
        return src;
    }


    /**
     * 将高字节数组转换为int
     *
     * @param src byte[]
     * @return int
     */
    public static int bytesToInt(byte[] src) {
        int value;
        value = (src[3] & 0xFF)
                | ((src[2] & 0xFF) << 8)
                | ((src[1] & 0xFF) << 16)
                | ((src[0] & 0xFF) << 24);
        return value;
    }

    /**
     * 将byte数组数据转换成float
     *
     * @param arr
     * @return
     */
    public static float bytesToFloat(byte[] arr) {
        int value = 0;
        value = value | (arr[3] & 0xff);
        value = value | (arr[2] & 0xff) << 8;
        value = value | (arr[1] & 0xff) << 16;
        value = value | (arr[0] & 0xff) << 24;
        return Float.intBitsToFloat(value);
    }


    /**
     * 截取byte数组   不改变原数组
     *
     * @param b      原数组
     * @param off    偏差值（索引）
     * @param length 长度
     * @return 截取后的数组
     */
    public static byte[] subByte(byte[] b, int off, int length) {
        byte[] b1 = new byte[length];
        System.arraycopy(b, off, b1, 0, length);
        return b1;
    }

    public static short[] convertToShorts(byte[] data) {
        short[] shortData = new short[data.length / 2];
        for (int i = 0; i < shortData.length; i++)
            shortData[i] = ModbusUtils.toShort(data[i * 2], data[i * 2 + 1]);
        return shortData;
    }

    /**
     * 106      * 将长度为2的byte数组转换为16位int
     * 107      *
     * 108      * @param res
     * 109      *            byte[]
     * 110      * @return int
     * 111      *
     */
    public static int byteToInt16(byte[] res) {
        // res = InversionByte(res);
        // 一个byte数据左移24位变成0x??000000，再右移8位变成0x00??0000
        return (res[1] & 0xff) | ((res[0] << 8) & 0xff00);
    }


    public static byte[] floatToBytes(float[] floats, boolean bigEndian) {
        ByteBuffer buffer = ByteBuffer.allocate(floats.length * 4);
        if (bigEndian) {
            buffer.order(ByteOrder.BIG_ENDIAN);
        } else {
            buffer.order(ByteOrder.LITTLE_ENDIAN);
        }
        for (float f : floats) {
            buffer.putFloat(f);
        }
        return buffer.array();
    }


    public static String byteToBit(byte b) {
        return String.valueOf((byte) ((b >> 7) & 0x1)) +
                (byte) ((b >> 6) & 0x1) +
                (byte) ((b >> 5) & 0x1) +
                (byte) ((b >> 4) & 0x1) +
                (byte) ((b >> 3) & 0x1) +
                (byte) ((b >> 2) & 0x1) +
                (byte) ((b >> 1) & 0x1) +
                (byte) (b & 0x1);
    }


    public static String convertBigEndianTo16Bit(byte[] bytes) throws IllegalArgumentException {
        if (bytes == null || bytes.length != 2) {
            throw new IllegalArgumentException("Invalid input byte array");
        }
        short value = (short) ((bytes[1] & 0xFF) | (bytes[0] << 8));
        return convertTo16Bit(value);
    }


    public static String convertTo16Bit(short value) {
        String binaryString = Integer.toBinaryString(value);
        int length = binaryString.length();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < 16 - length; i++) {
            builder.append('0');
        }
        builder.append(binaryString);
        return builder.toString();
    }

    //将short类型转换为16个bit字符串
    public static String shortToBitString(short num) {
        StringBuilder sb = new StringBuilder();
        for (int i = 15; i >= 0; i--) {
            sb.append((num >> i) & 1);
        }
        return sb.toString();
    }


    //将16个bit字符串转换为short类型
    public static short bitStringToShort(String bitString) {
        short num = 0;
        for (int i = 0; i < 16; i++) {
            if (bitString.charAt(i) == '1') {
                num |= (1 << (15 - i));
            }
        }
        return num;
    }

    public static byte[] mergeArrays(byte[]... arrays) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        for (byte[] array : arrays) {
            if (array != null) {
                outputStream.write(array, 0, array.length);
            }
        }
        return outputStream.toByteArray();
    }

    public static String parseAndFormatPwmValue(String input, String pwmFlag) {
        // 正则表达式用于匹配 "pwm:" 后面的数字
        //String regex = "pwm:\\s*(\\d+)";
        String regex = String.format("%s:\\s*(\\d+)", pwmFlag);
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            // 提取匹配的数字
            String numberStr = matcher.group(1);
            int number = Integer.parseInt(numberStr);
            // 将数字转换为百分比形式
            double percentage = number / 100.0;
            // 格式化为字符串，保留两位小数
            return String.format("%.2f", percentage);
        } else {
            // 如果没有找到匹配项，则返回空字符串或抛出异常
            return "";
        }
    }

    /**
     * 高精度科学计数法转float
     *
     * @param input 科学计数法字符串
     * @return 精确四舍五入后的float值
     */
    public static float scientificToFloat(String input) {
        // 1. 分解尾数和指数
        String[] parts = input.split("[eE]");
        BigDecimal mantissa = new BigDecimal(parts[0]);
        int exponent = Integer.parseInt(parts[1]);

        // 2. 计算实际值 = 尾数 × 10^指数
        BigDecimal value = mantissa.scaleByPowerOfTen(exponent);

        // 3. 保留7位有效数字（float精度为6-9位）
        value = value.setScale(7 - value.precision() + value.scale(),
                RoundingMode.HALF_UP);

        // 4. 转换为float类型
        return value.floatValue();
    }

    public static double ifScientificToDouble(String input) {
        input = input.replace("−", "-").replace("＋", "+");
        try {
            if (input.matches("[-+]?\\d+(\\.\\d+)?[eE][-+]?\\d+")) {
                return new BigDecimal(input).doubleValue();
            } else {
                return Double.parseDouble(input);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入无效: " + input, e);
        }
    }


    public static void main(String[] args) {
        float value1 = 6.62607015E-34f;
        float value2 = 1.8888888E-5f;
        float value3 = -3.45E-6f;
        float value4 = 123.55f;

        // 使用 DecimalFormat
        DecimalFormat df = new DecimalFormat("0.######");
        System.out.println(df.format(value1));
        System.out.println(df.format(value2));
        System.out.println(df.format(value3));
        System.out.println(df.format(value4));

//        // 使用 String.format
//        System.out.println(String.format("%.20f", value1).replaceAll("0+$", "").replaceAll("\\.$", ""));
//        System.out.println(String.format("%.20f", value2).replaceAll("0+$", "").replaceAll("\\.$", ""));
//        System.out.println(String.format("%.20f", value3).replaceAll("0+$", "").replaceAll("\\.$", ""));
//        System.out.println(String.format("%.20f", value4).replaceAll("0+$", "").replaceAll("\\.$", ""));
//
//        System.out.println(df.format(ifScientificToDouble("6.62607015E−34")));
//        System.out.println(df.format(ifScientificToDouble("1.8888888e-5")));
//        System.out.println(df.format(ifScientificToDouble("-3.45E-6")));
//        System.out.println(df.format(ifScientificToDouble("0.01")));
//        System.out.println(DataUtils.parseHexString("0x344"));
//        System.out.println(DataUtils.parseAndFormatPwmValue("7,58xd_pwm: 2083", "pwm"));
    }

}
