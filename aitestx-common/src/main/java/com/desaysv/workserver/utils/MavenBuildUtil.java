package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Properties;

@Slf4j
public class MavenBuildUtil {
    public static String getBuildTime() {
        return getBuildTime(false);
    }

    public static String getBuildTime(boolean joint) {
        String compileDate = DateUtils.timeZoneTransfer(getProperty("maven.build.time"));
        return joint ? compileDate.replaceAll(" ", ".").replaceAll("[:/]", "") : compileDate;
    }

    public static String getBuildComputerName() {
        return getProperty("maven.build.host");
    }

    private static String getProperty(String propertyName) {
        String propertyValue = "";
        try {
            Properties properties = new Properties();
            try (InputStream in = MavenBuildUtil.class.getClassLoader().getResourceAsStream("build.properties")) {
                properties.load(in);
                // 处理东八区时间
                propertyValue = properties.getProperty(propertyName);
            }
        } catch (Exception e) {
            log.error("build.properties：获取属性异常: {}", String.valueOf(e));
        }
        return propertyValue;
    }


}