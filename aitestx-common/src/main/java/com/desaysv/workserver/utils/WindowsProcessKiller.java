package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Slf4j
public class WindowsProcessKiller {
    private static final String TASKLIST = "tasklist";
    private static final String KILL = "taskkill /IM ";

    public static void isProcessRunning(String serviceName) {

        try {
            java.lang.Process pro = Runtime.getRuntime().exec(TASKLIST);
            BufferedReader reader = new BufferedReader(new InputStreamReader(pro.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith(serviceName)) {
                    log.info(line);
//                    return true;
                }
            }

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

//        return false;
    }

    public static void killProcess(String serviceName) {

        try {
            Runtime.getRuntime().exec(KILL + serviceName);
            System.out.println(serviceName + " killed successfully!");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        WindowsProcessKiller.isProcessRunning("cmd.exe");
    }
}