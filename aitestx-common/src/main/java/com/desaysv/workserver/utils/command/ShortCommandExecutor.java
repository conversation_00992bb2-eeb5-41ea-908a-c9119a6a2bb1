package com.desaysv.workserver.utils.command;

import cn.hutool.core.io.FileUtil;
import com.desaysv.workserver.utils.ExceptionUtils;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

@Deprecated
@Slf4j
public class ShortCommandExecutor {
    private final static ShortCommandExecutor defaultCommandExecutor = new ShortCommandExecutor();

    private final static Map<String, ShortCommandExecutor> COMMAND_EXECUTORS = new HashMap<>();

    @Setter
    private String command;

    @Setter
    private boolean outputProcessingLog = true;

    private boolean stop = false;

    private Process process;

    private BufferedReader bufferedReader;

    private ShortCommandExecutor() {

    }

    public static void killAll() {
        Collection<ShortCommandExecutor> commandExecutors = COMMAND_EXECUTORS.values();
        if (!commandExecutors.isEmpty()) {
            log.info("终止所有命令");
        }
        for (ShortCommandExecutor executor : commandExecutors) {
            executor.kill();
        }
        COMMAND_EXECUTORS.clear();
    }

    public static CommandResponse defaultExecuteCommand(String command) {
        defaultCommandExecutor.setCommand(command);
        defaultCommandExecutor.setOutputProcessingLog(true);
        return defaultCommandExecutor.executeCommand(command, true);
    }

    public static ShortCommandExecutor build(String command, boolean killLastCommand) {
        if (killLastCommand) {
            return killLastAndBuild(command);
        } else {
            return build(command);
        }
    }

    public static ShortCommandExecutor build(String command) {
        ShortCommandExecutor commandExecutor = new ShortCommandExecutor();
        commandExecutor.setCommand(command);
        COMMAND_EXECUTORS.put(command, commandExecutor);
        return commandExecutor;
    }

    public static ShortCommandExecutor killLastAndBuild(String command) {
        ShortCommandExecutor commandExecutor = COMMAND_EXECUTORS.get(command);
        if (commandExecutor != null) {
            commandExecutor.kill();
        }
        return build(command);
    }

    public void remove() {
        COMMAND_EXECUTORS.remove(command);
    }


    public void kill() {
        try {
            stop = true;
            process.destroy();
            process.destroyForcibly();
            long pid = CommandUtils.getProcessId(process);
            CommandUtils.killProcess(pid, command);
            log.info("成功终止命令及相关进程:{}(pid:{})", command, pid);
        } catch (Exception e) {
            log.warn("终止上一次命令\"{}\"失败", command);
        } finally {
            remove();
        }
    }

    public CommandResponse execute() {
        return executeCommand(command, outputProcessingLog);
    }

    private CommandResponse executeCommand(String command, boolean outputProcessingLog) {
        //FIXME：长时间命令会卡死，比如发送cmd
        log.info("执行cmd命令:{}", command);
        CommandResponse commandResponse = new CommandResponse();

        try {
            // 构建执行命令
            ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
            if (FileUtil.isAbsolutePath(command)) {
                processBuilder.directory(FileUtil.getParent(new File(command), 1));
                log.info("命令执行目录:{}", processBuilder.directory());
            }
            //设置python脚本执行目录
            if (command.startsWith("python") && command.contains(".py")) {
                String commandPath = command.substring(command.indexOf(File.separator), command.lastIndexOf(File.separator));
                processBuilder.directory(new File(commandPath));
                log.info("python脚本执行目录:{}", processBuilder.directory());
            }
            // 重定向 ERROR 流(有些 JDK 版本 Java 命令通过 ERROR 流输出)
            processBuilder.redirectErrorStream(true);
            // 运行命令
            process = processBuilder.start();
            log.info("命令pid:{}", CommandUtils.getProcessId(process));

            String result;
            if (outputProcessingLog) {
                StringBuilder sb = new StringBuilder();
                bufferedReader = new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    log.info(line);
                    sb.append(line).append(System.lineSeparator());
                    if (stop) {
                        break;
                    }
                }
                result = sb.toString();
            } else {
                // 一次性获取运行结果
                result = IOUtils.toString(process.getInputStream(), "gbk").trim();
                log.info("命令执行结果:{}", result);
            }

            // 等到运行结束
            int exitCode = process.waitFor();
            commandResponse.setOk(exitCode == 0);
            if (commandResponse.isOk()) {
                commandResponse.setStandardOutput(result.trim());
            } else {
                commandResponse.setStandardError(result.trim());
            }

        } catch (Exception e) {
            log.warn("命令执行出错:", e);
            commandResponse.setOk(false);
            commandResponse.setStandardError(ExceptionUtils.getExceptionString(e));
        } finally {
            if (process != null) {
                process.destroy();
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    log.warn("关闭BufferedReader时出错:", e);
                }
            }
            remove();
        }
        return commandResponse;
    }


}