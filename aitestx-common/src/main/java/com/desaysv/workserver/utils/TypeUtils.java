package com.desaysv.workserver.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 类型工具类
 */
public class TypeUtils {

    public static List<Class<?>> getTypes(Object object) {
        List<Class<?>> types = new ArrayList<>();
        if (object instanceof BigDecimal) {
            types.add(BigDecimal.class);
        } else if (object instanceof Integer) {
            types.add(int.class);
            types.add(Integer.class);
        } else if (object instanceof String) {
            types.add(String.class);
        } else if (object instanceof Double) {
            types.add(double.class);
            types.add(Double.class);
        } else if (object instanceof Float) {
            types.add(float.class);
            types.add(Float.class);
        } else if (object instanceof Long) {
            types.add(long.class);
            types.add(Long.class);
        } else if (object instanceof Boolean) {
            types.add(boolean.class);
            types.add(Boolean.class);
        } else if (object instanceof Date) {
            types.add(Date.class);
        } else {
            types.add(Object.class);
        }
        return types;
    }
}
