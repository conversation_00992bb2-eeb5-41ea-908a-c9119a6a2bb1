package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Enumeration;
import java.util.zip.*;

/**
 * 压缩解压工具类
 */
@Slf4j
public class ZipUtils {

    public static void unZipFiles(File zipFile, File descDir) throws IOException {
        unZipFiles(zipFile, descDir, false);
    }

    public static void unZipFiles(File zipFile, File descDir, boolean replaceDescDir) throws IOException {
//        File destFile = new File(descDir);
        if (!descDir.exists()) {
            descDir.mkdirs();
        }
        // 解决zip文件中有中文目录或者中文文件
        ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
        for (Enumeration<? extends ZipEntry> entries = zip.entries(); entries.hasMoreElements(); ) {
            ZipEntry entry = entries.nextElement();
            InputStream in = zip.getInputStream(entry);
            String curEntryName = entry.getName();
            // 判断文件名路径是否存在文件夹
            int endIndex = curEntryName.lastIndexOf('/');
            String outPath;
            if (replaceDescDir) {
                curEntryName = curEntryName.replace(curEntryName.split("/")[0], descDir.getName());
                outPath = (descDir.getParent() + curEntryName).replaceAll("\\*", "/");
            } else {
                outPath = (descDir + "/" + curEntryName).replaceAll("\\*", "/");
            }
            if (endIndex != -1) {
                File file = new File(outPath.substring(0, outPath.lastIndexOf("/")));
                if (!file.exists()) {
                    file.mkdirs();
                }
            }

            // 判断文件全路径是否为文件夹,如果是上面已经上传,不需要解压
            File outFile = new File(outPath);
            if (outFile.isDirectory()) {
                continue;
            }
            OutputStream out = Files.newOutputStream(Paths.get(outPath));
            byte[] buf1 = new byte[1024];
            int len;
            while ((len = in.read(buf1)) > 0) {
                out.write(buf1, 0, len);
            }
            out.flush();
            in.close();
            out.close();
        }
        log.info("解压{}成功", zipFile.getName());
    }
}
