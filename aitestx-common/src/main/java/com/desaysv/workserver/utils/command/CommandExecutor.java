package com.desaysv.workserver.utils.command;

import cn.hutool.core.io.FileUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CommandExecutor {
    private static final long NO_TIMEOUT = -1;

    private static final ConcurrentHashMap<String, CommandInfo> runningCommands = new ConcurrentHashMap<>();

    @Getter
    public static class CommandInfo {
        private final String id;
        private final String command;
        private final Process process;
        private final long startTime;
        private final File workDir;
        private final Thread outputThread;
        private final Thread errorThread;
        private final ByteArrayOutputStream outputStream;
        private final ByteArrayOutputStream errorStream;

        public CommandInfo(String id, String command, Process process, File workDir,
                           Thread outputThread, Thread errorThread,
                           ByteArrayOutputStream outputStream, ByteArrayOutputStream errorStream) {
            this.id = id;
            this.command = command;
            this.process = process;
            this.workDir = workDir;
            this.startTime = System.currentTimeMillis();
            this.outputThread = outputThread;
            this.errorThread = errorThread;
            this.outputStream = outputStream;
            this.errorStream = errorStream;
        }

        public boolean matches(String searchCommand) {
            return command != null && searchCommand != null && command.contains(searchCommand);
        }

        public void cleanup() {
            try {
                log.info("开始清理命令资源: {}", id);

                if (process != null && process.isAlive()) {
                    CommandUtils.killWithOshi(process);
                }

                if (outputThread != null) {
                    outputThread.interrupt();
                }
                if (errorThread != null) {
                    errorThread.interrupt();
                }

                if (outputStream != null) {
                    outputStream.close();
                }
                if (errorStream != null) {
                    errorStream.close();
                }

                runningCommands.remove(id);
                log.info("命令资源清理完成: {}", id);
            } catch (Exception e) {
                log.error("清理命令资源时发生异常: {}", id, e);
            }
        }
    }

    public static ExecuteResult executeCommand(String command) throws IOException {
        return executeCommand(command, null, false);
    }

    public static ExecuteResult executeCommand(String command, String workDir, boolean showWindow) throws IOException {
        return executeCommand(command, workDir == null ? null : new File(workDir), null, NO_TIMEOUT, null, showWindow);
    }

    public static ExecuteResult executeCommand(
            String command,
            File workDir,
            Map<String, String> env,
            long timeoutMillis,
            Charset charset,
            boolean showWindow) throws IOException {

        if (command == null || command.trim().isEmpty()) {
            throw new IllegalArgumentException("命令不能为空");
        }

        //去除command两边的双引号
        command = command.replaceAll("^\"|\"$", "");

        if (workDir == null) {
            File commandFile = new File(command);
            if (commandFile.isAbsolute() && command.contains(File.separator)) {
                workDir = FileUtil.getParent(commandFile, 1);
            }
        }

        log.info("正在执行命令: {}", command);
        if (workDir != null) {
            log.info("工作目录: {}", workDir);
        }

        List<String> commands = new ArrayList<>();
        ProcessBuilder processBuilder = new ProcessBuilder(commands);
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            if (showWindow) {
                // 弹出新窗口并阻塞等待执行完成
                commands.add("cmd");
                commands.add("/c");
                commands.add("start");
                commands.add("/wait");
                commands.add("cmd");
                commands.add("/k");
                commands.add(command);
            } else {
                // 原有的方式执行命令
                commands.add("cmd");
                commands.add("/c");
                commands.add(command);

                // 删除 inheritIO
                // processBuilder.inheritIO();
            }
        } else {
            commands.add("sh");
            commands.add("-c");
            commands.add(command);
        }

        String commandId = UUID.randomUUID().toString();

        if (workDir != null) {
            processBuilder.directory(workDir);
        }
        if (env != null) {
            processBuilder.environment().putAll(env);
        }

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ByteArrayOutputStream errorStream = new ByteArrayOutputStream();

        Process process = processBuilder.start();

        Thread outputThread = startOutputThread(process.getInputStream(), outputStream);
        Thread errorThread = startOutputThread(process.getErrorStream(), errorStream);

        CommandInfo commandInfo = new CommandInfo(commandId, command, process, workDir,
                outputThread, errorThread, outputStream, errorStream);
        runningCommands.put(commandId, commandInfo);

        boolean isTimeout = false;
        int exitCode;
        long startTime = System.currentTimeMillis();

        try {
            if (timeoutMillis > 0) {
                isTimeout = !process.waitFor(timeoutMillis, TimeUnit.MILLISECONDS);
                if (isTimeout) {
                    process.destroy();
                    if (!process.waitFor(5, TimeUnit.SECONDS)) {
                        process.destroyForcibly();
                    }
                    exitCode = -1;
                } else {
                    exitCode = process.exitValue();
                }
            } else {
                exitCode = process.waitFor();
            }

            // 等待输出线程完全结束
            outputThread.join();
            errorThread.join();

            // 获取输出
            charset = charset != null ? charset : Charset.defaultCharset();
            String output = outputStream.toString(charset.name());
            String error = errorStream.toString(charset.name());

            // 实时记录输出日志
            if (!output.isEmpty()) {
                log.info("命令输出: {}", output);
            }
            if (!error.isEmpty()) {
                log.error("命令错误: {}", error);
            }

            long executionTime = System.currentTimeMillis() - startTime;
            ExecuteResult result = new ExecuteResult(exitCode, output, error, isTimeout, executionTime);

            if (result.isSuccess()) {
                log.info("命令执行成功，耗时 {} 毫秒", executionTime);
            } else {
                log.warn("命令执行失败: {}", result);
            }

            return result;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("命令执行被中断", e);
        } finally {
            commandInfo.cleanup();
        }
    }


    /**
     * 终止所有正在运行的命令
     *
     * @return 成功终止的命令数量
     */
    public static int killAll() {
        int killedCount = 0;
        List<CommandInfo> commands = new ArrayList<>(runningCommands.values());

        if (commands.isEmpty()) {
            log.warn("没有正在运行的命令");
            return 0;
        }

        for (CommandInfo commandInfo : commands) {
            log.info("正在终止命令[{}]: {}", commandInfo.getId(), commandInfo.getCommand());
            try {
                commandInfo.cleanup();
                killedCount++;
            } catch (Exception e) {
                log.error("终止命令[{}]时发生错误", commandInfo.getId(), e);
            }
        }

        log.info("已终止 {} 个命令", killedCount);
        return killedCount;
    }

    /**
     * 根据命令内容终止对应的进程
     *
     * @param command 要终止的命令内容（支持部分匹配）
     * @return 成功终止的命令数量
     */
    public static int killByCommand(String command) {
        if (command == null || command.trim().isEmpty()) {
            log.warn("命令内容不能为空");
            return 0;
        }

        int killedCount = 0;
        List<CommandInfo> matchedCommands = new ArrayList<>();

        // 找出所有匹配的命令
        for (CommandInfo info : runningCommands.values()) {
            if (info.matches(command)) {
                matchedCommands.add(info);
            }
        }

        if (matchedCommands.isEmpty()) {
            log.warn("未找到匹配的运行中命令: {}", command);
            return 0;
        }

        // 终止匹配的命令
        for (CommandInfo commandInfo : matchedCommands) {
            log.info("正在终止匹配的命令[{}]: {}", commandInfo.getId(), commandInfo.getCommand());
            try {
                commandInfo.cleanup();
                killedCount++;
            } catch (Exception e) {
                log.error("终止命令[{}]时发生错误", commandInfo.getId(), e);
            }
        }

        log.info("已终止 {} 个匹配的命令", killedCount);
        return killedCount;
    }

    /**
     * 启动一个线程用于读取命令执行的输出流
     *
     * @param input  输入流,通常是进程的标准输出或错误输出流
     * @param output 输出流,用于存储读取到的内容
     * @return 返回启动的线程对象
     */
    private static Thread startOutputThread(InputStream input, OutputStream output) {
        Thread thread = new Thread(() -> {
            try {
                byte[] buffer = new byte[4096]; // 增加缓冲区大小
                int length;
                while ((length = input.read(buffer)) != -1) {
                    output.write(buffer, 0, length);
                    output.flush(); // 确保立即写入

                    // 实时输出到日志
                    String content = new String(buffer, 0, length);
                    if (!content.trim().isEmpty()) {
                        log.debug("实时输出: {}", content.trim());
                    }
                }
            } catch (IOException e) {
                if (!Thread.currentThread().isInterrupted()) {
                    log.error("读取命令输出时发生错误", e);
                }
            }
        });
        thread.start();
        return thread;
    }

}