package com.desaysv.workserver.utils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 注解工具类
 * TODO: 后续考虑合并 @org.springframework.util。AnnotationUtils
 */
public class AnnotationUtils {

    public static Field[] getAllFields(Class<?> clazz) {
        return getAllFields(clazz, -1);
    }

    /**
     * 获取本类及其父类的字段属性
     *
     * @param clazz 当前类对象
     * @return 字段数组
     */
    public static Field[] getAllFields(Class<?> clazz, int superLayers) {
        List<Field> fieldList = new ArrayList<>();
        int layer = 0;
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
            if (superLayers >= 0 && ++layer > superLayers) {
                break;
            }
        }
        Field[] fields = new Field[fieldList.size()];
        return fieldList.toArray(fields);
    }

}