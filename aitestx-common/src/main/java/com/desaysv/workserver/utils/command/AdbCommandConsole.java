package com.desaysv.workserver.utils.command;

import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;

/**
 * @author: QinHao
 * @description:
 * @date: 2025/4/1 13:58
 */
@Slf4j
public class AdbCommandConsole {
    private final Process cmdProcess;
    private final BufferedWriter processWriter;
    private final BufferedReader processReader;
    private final Thread outputThread;
    private final LinkedBlockingDeque<String> outputQueue = new LinkedBlockingDeque<>(10); // 容量控制防止OOM

    public AdbCommandConsole() throws IOException {
        ProcessBuilder pb = new ProcessBuilder("cmd.exe");
        pb.redirectErrorStream(true);
        cmdProcess = pb.start();
        Charset gbkCharset = Charset.forName("GBK");
        processWriter = new BufferedWriter(new OutputStreamWriter(cmdProcess.getOutputStream()));
        processReader = new BufferedReader(new InputStreamReader(cmdProcess.getInputStream(), gbkCharset));

        outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = processReader.readLine()) != null) {
                    SseUtils.pubMsg(SseConstants.ADB_MSG, line);
                    outputQueue.offer(line, 1000, TimeUnit.MILLISECONDS);
//                    System.out.println(line); // 输出到控制台
                }
            } catch (IOException | InterruptedException e) {
                log.error(e.getMessage(), e);
            }
        });

        outputThread.setDaemon(true);
        outputThread.start();
    }

    /**
     * 执行命令并验证预期结果
     *
     * @param command        要执行的命令
     * @param timeoutMillis  超时时间（毫秒）
     * @param expectedResult 预期的结果字符串
     * @return 如果找到预期结果返回 true，否则返回 false
     */
    public boolean executeCommandWithValidation(String command,
                                                long timeoutMillis,
                                                String expectedResult)
            throws IOException, InterruptedException {
        log.info("开始执行命令: {}", command);

        executeCommand(command);
        final long deadline = System.currentTimeMillis() + timeoutMillis;

        while (System.currentTimeMillis() < deadline) {
            Iterator<String> iter = outputQueue.descendingIterator();
            while (iter.hasNext()) {
                String line = iter.next();
                if (line.contains(expectedResult)) {
                    log.info("成功找到预期结果: {}", expectedResult);
                    return true;
                }
            }
            String newLine = outputQueue.poll(
                    deadline - System.currentTimeMillis(),
                    TimeUnit.MILLISECONDS
            );
            if (newLine != null && newLine.contains(expectedResult)) {
                log.info("成功找到预期结果: {}", expectedResult);
                return true;
            }
        }
        log.warn("未能在指定时间内找到预期结果: {}", expectedResult);
        return false;
    }

    /**
     * 执行命令并自动重试（当出现禁止值时）
     *
     * @param command            要执行的命令
     * @param timeoutMillis      单次尝试等待时间（毫秒）
     * @param forbiddenValue     禁止出现的结果
     * @param maxAttempts        最大尝试次数
     * @param additionalCommands 附加命令列表
     */

    public boolean executeCommandWithRetry(String command,
                                           long timeoutMillis,
                                           String forbiddenValue,
                                           int maxAttempts,
                                           List<String> additionalCommands)
            throws IOException, InterruptedException {

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            outputQueue.clear();
            executeCommand(command);
            final long deadline = System.currentTimeMillis() + timeoutMillis;
            boolean forbiddenFound = false;
            while (System.currentTimeMillis() < deadline) {
                // 检测历史输出
                Iterator<String> iter = outputQueue.descendingIterator();
                while (iter.hasNext()) {
                    String line = iter.next();
                    if (line.contains(forbiddenValue)) {
                        forbiddenFound = true;
                        break;
                    }
                }
                if (forbiddenFound) break;

                // 等待新输出
                String newLine = outputQueue.poll(
                        deadline - System.currentTimeMillis(),
                        TimeUnit.MILLISECONDS
                );
                if (newLine != null && newLine.contains(forbiddenValue)) {
                    forbiddenFound = true;
                    break;
                }
            }

            if (!forbiddenFound) {
                return true;
            } else {
                log.warn("检测到禁止值 [{}]，即将重试 ({}/{})",
                        forbiddenValue, attempt, maxAttempts);
                // 发送附加命令
                for (String additionalCommand : additionalCommands) {
                    executeCommand(additionalCommand);
                }
            }
        }
        log.error("所有尝试均失败，检测到禁止值 [{}]，最大尝试次数 [{}]", forbiddenValue, maxAttempts);
        return false;
    }

    /**
     * 检测上一条指令执行后是否出现了指定值。
     *
     * @param expectedValue 预期要检测的值
     * @param timeoutMillis 超时时间（毫秒）
     * @return 如果在超时时间内检测到该值则返回 true，否则返回 false
     */
    public boolean checkExpected(String expectedValue, int timeoutMillis) throws InterruptedException {
        final long deadline = System.currentTimeMillis() + timeoutMillis;
        log.info("开始检测预期值: {}", expectedValue);

        while (System.currentTimeMillis() < deadline) {
            String line = outputQueue.poll(timeoutMillis, TimeUnit.MILLISECONDS);
            if (line == null) continue;
            if (line.contains(expectedValue)) {
                log.info("检测到预期值: {}", expectedValue);
                return true;
            }
        }
        log.warn("未在指定时间内检测到预期值: {}", expectedValue);
        return false;
    }

    /**
     * 检测上一条指令执行后是否未出现指定值。
     *
     * @param forbiddenValue 禁止出现的值
     * @param timeoutMillis  超时时间（毫秒）
     * @return 如果在超时时间内未检测到该值则返回 true，否则返回 false
     */
    public boolean checkForbidden(String forbiddenValue, int timeoutMillis) throws InterruptedException {
        final long deadline = System.currentTimeMillis() + timeoutMillis;
        log.info("开始检测禁止值: {}", forbiddenValue);

        while (System.currentTimeMillis() < deadline) {
            String line = outputQueue.poll(timeoutMillis, TimeUnit.MILLISECONDS);
            if (line == null) continue;
            if (line.contains(forbiddenValue)) {
                log.warn("检测到禁止值: {}", forbiddenValue);
                return false;
            }
        }
        log.info("未检测到禁止值: {}", forbiddenValue);
        return true;
    }


    /**
     * 执行命令（自动追加换行并立即提交）
     *
     * @return
     */
    public boolean executeCommand(String command) throws IOException {
        processWriter.write(command);
        processWriter.newLine();
        processWriter.flush();
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * 安全关闭命令行环境
     */
    public void shutdown() throws IOException, InterruptedException {
        try {
            executeCommand("exit"); // 发送退出命令
        } finally {
            processWriter.close();
            outputThread.join(1500); // 等待输出处理完成
            if (cmdProcess.isAlive()) {
                cmdProcess.destroyForcibly();
            }
        }
    }

    public static void main(String[] args) {
        try {
            AdbCommandConsole console = new AdbCommandConsole();
            // 执行示例命令
            console.executeCommandWithRetry("echo 1", 5000, "不是内部或外部命令", 1, Arrays.asList("echo test", "echo test2"));
            console.shutdown();
            System.out.println("控制台已关闭");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
