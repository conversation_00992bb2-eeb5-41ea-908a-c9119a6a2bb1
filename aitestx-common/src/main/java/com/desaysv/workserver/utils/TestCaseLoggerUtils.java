package com.desaysv.workserver.utils;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.FileAppender;
import lombok.Getter;
import org.slf4j.LoggerFactory;

import java.io.File;

public class TestCaseLoggerUtils {

    @Getter
    private static final Logger logger = (Logger) LoggerFactory.getLogger(ActionLogUtils.class);

    public static void init() {
    }

    public static void setAppender(String tag, File folder) {
        logger.detachAndStopAllAppenders();
        logger.addAppender(ActionLogUtils.createFileAppender(FileUtils.replaceSpecialChar(tag), folder));
    }

    public static void removeFileAppender() {
        logger.detachAndStopAllAppenders();
    }

    public static void main(String[] args) {
        // 获取 LoggerContext 对象
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
        try {
            // 根据需要创建不同的 FileAppender
            FileAppender<ILoggingEvent> appender1 = ActionLogUtils.createFileAppender("案例1", "D:/logs");
            FileAppender<ILoggingEvent> appender2 = ActionLogUtils.createFileAppender("案例2", "D:/logs");

            // 将 Appender 添加到 LoggerContext 中
//            Logger logger = context.getLogger(LogUtils.class);

            logger.addAppender(appender1);
            logger.addAppender(appender2);

            // 启动 LoggerContext
//            context.start();
            // 打印日志
            logger.info("This is a debug message for tag1");
            logger.debug("This is a debug message for tag2");
        } finally {
            // 停止 LoggerContext
            context.stop();
        }
    }
}
