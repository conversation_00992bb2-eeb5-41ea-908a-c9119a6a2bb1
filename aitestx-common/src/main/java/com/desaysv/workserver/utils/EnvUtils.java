package com.desaysv.workserver.utils;

import com.ice.jni.registry.RegStringValue;
import com.ice.jni.registry.Registry;
import com.ice.jni.registry.RegistryException;
import com.ice.jni.registry.RegistryKey;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 环境工具类
 */
@Slf4j
public class EnvUtils {

    public static void updatePath(String path) {
        String pathOldValue = System.getenv("path");
        try {
//            RegistryKey openPath = Registry.HKEY_LOCAL_MACHINE.openSubKey("SYSTEM\\CurrentControlSet\\Control\\Session Manager");
            RegistryKey openPath = Registry.HKEY_CURRENT_USER.openSubKey("SOFTWARE");
            RegistryKey subKey = openPath.createSubKey("Environment", "");
            String path_new_value = pathOldValue + path;
            subKey.setValue(new RegStringValue(subKey, "path", path_new_value));
            subKey.closeKey();
            Runtime.getRuntime().exec("taskkill /F /IM explorer.exe");
            TimeUnit.MILLISECONDS.sleep(500);
            Runtime.getRuntime().exec("explorer.exe");
        } catch (RegistryException | IOException | InterruptedException e) {
            log.error(e.getMessage(), e);
        }
    }

    @SuppressWarnings("unchecked")
    public static void setEnv(Map<String, String> newEnv) throws Exception {
        try {
            Class<?> processEnvironmentClass = Class.forName("java.lang.ProcessEnvironment");
            Field theEnvironmentField = processEnvironmentClass.getDeclaredField("theEnvironment");
            theEnvironmentField.setAccessible(true);
            Map<String, String> env = (Map<String, String>) theEnvironmentField.get(null);
            env.putAll(newEnv);
            Field theCaseInsensitiveEnvironmentField = processEnvironmentClass.getDeclaredField("theCaseInsensitiveEnvironment");
            theCaseInsensitiveEnvironmentField.setAccessible(true);
            Map<String, String> tmpEnv = (Map<String, String>) theCaseInsensitiveEnvironmentField.get(null);
            tmpEnv.putAll(newEnv);
        } catch (NoSuchFieldException e) {
            Class<?>[] classes = Collections.class.getDeclaredClasses();
            Map<String, String> env = System.getenv();
            for (Class<?> cl : classes) {
                if ("java.util.Collections$UnmodifiableMap".equals(cl.getName())) {
                    Field field = cl.getDeclaredField("m");
                    field.setAccessible(true);
                    Object obj = field.get(env);
                    Map<String, String> map = (Map<String, String>) obj;
                    map.clear();
                    map.putAll(newEnv);
                }
            }
        }
    }

    public static void addLibraryDir(String libraryPath) throws Exception {
        addLibraryDir(Collections.singletonList(libraryPath));
    }

    public static void addLibraryDir(List<String> libraryPaths) throws Exception {
        Field userPathsField = ClassLoader.class.getDeclaredField("usr_paths");
        userPathsField.setAccessible(true);
        String[] paths = (String[]) userPathsField.get(null);
        StringBuilder sb = new StringBuilder();
        for (String path : paths) {
            if (libraryPaths.contains(path)) {
                continue;
            }
            sb.append(path).append(';');
        }
        for (String path : libraryPaths) {
            sb.append(path).append(';');
        }
        //修改java.library.path
        System.setProperty("java.library.path", sb.toString());
        final Field sysPathsField = ClassLoader.class.getDeclaredField("sys_paths");
        sysPathsField.setAccessible(true);
        //修改完成后重新将sys_paths置为null,因为sys_paths为null的时候会去加载一次变量
        sysPathsField.set(null, null);
    }
}
