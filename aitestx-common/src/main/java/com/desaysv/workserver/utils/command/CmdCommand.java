package com.desaysv.workserver.utils.command;

import lombok.Data;

@Data
public class CmdCommand {

    private String command;

    private String workDir;

    private boolean judge;

    private String judgeText;

    private boolean killLastCommand;

    private boolean mustContained;

    private boolean showWindow;

    public CmdCommand(String command) {
        this.command = command;
        killLastCommand = true;
    }

}