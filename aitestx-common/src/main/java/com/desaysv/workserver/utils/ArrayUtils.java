package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数组工具类
 */
@Slf4j
public class ArrayUtils {

    /**
     * 检查数组是否为空
     *
     * @param array 要检查的数组
     * @return 如果数组为 null 或长度为 0，则返回 true；否则返回 false
     */
    public static <T> boolean isEmpty(T[] array) {
        return array == null || array.length == 0;
    }

    public static boolean isEmpty(List<?> array) {
        return array == null || array.isEmpty();
    }

    /**
     * 合并数组
     *
     * @param firstArray  第一个数组
     * @param secondArray 第二个数组
     * @return 合并后的数组
     */
    public static byte[] concat(byte[] firstArray, byte[] secondArray) {
        if (firstArray == null || secondArray == null) {
            return null;
        }
        byte[] bytes = new byte[firstArray.length + secondArray.length];
        System.arraycopy(firstArray, 0, bytes, 0, firstArray.length);
        System.arraycopy(secondArray, 0, bytes, firstArray.length, secondArray.length);
        return bytes;
    }

    public static byte[] intToByteArrayHighFirst(int i) {
        return new byte[]{
                (byte) i,
                (byte) (i >>> 8),
                (byte) (i >>> 16),
                (byte) (i >>> 24)};
    }

    public static int byteArrayToIntHighFirst(byte[] bytes) {
        return (bytes[0] & 0xff)
                | ((bytes[1] & 0xff) << 8)
                | ((bytes[2] & 0xff) << 16)
                | ((bytes[3] & 0xff) << 24);
    }


    public static byte[] intToByteArrayLowFirst(int i) {
        return new byte[]{
                (byte) (i >>> 24),
                (byte) (i >>> 16),
                (byte) (i >>> 8),
                (byte) i};
//        byte[] bytes = new byte[4];
//        bytes[0] = (byte) ((i >> 24) & 0xFF);
//        bytes[1] = (byte) ((i >> 16) & 0xFF);
//        bytes[2] = (byte) ((i >> 8) & 0xFF);
//        bytes[3] = (byte) (i & 0xFF);
//        return bytes;
    }

    public static int byteArrayToIntLowFirst(byte[] bytes) {
        return ((bytes[0] & 0xff) << 24)
                | ((bytes[1] & 0xff) << 16)
                | ((bytes[2] & 0xff) << 8)
                | (bytes[3] & 0xff);
    }

//    public static void main(String[] args) {
//        byte[] bytes = new byte[]{0, 0, 6, -42};
//        System.out.println(byteArrayToIntLowFirst(bytes));
//    }


    public static <T> void listToModel(List<?> list, T t) throws Exception {
        Field[] fields = t.getClass().getDeclaredFields();
        if (list.size() != fields.length) {
            return;
        }
        for (int k = 0, len = fields.length; k < len; k++) {
            // 根据属性名称,找寻合适的set方法
            String fieldName = fields[k].getName();
            String setMethodName = "set" + fieldName.substring(0, 1).toUpperCase()
                    + fieldName.substring(1);
            Method method;
            Class<?> clazz = t.getClass();
            try {
                method = clazz.getMethod(setMethodName, fields[k].getType());
            } catch (SecurityException e1) {
                e1.printStackTrace();
                return;
            } catch (NoSuchMethodException e1) {
                String newMethodName = "set" + fieldName.substring(0, 1).toLowerCase()
                        + fieldName.substring(1);
                try {
                    method = clazz.getMethod(newMethodName, fields[k].getType());
                } catch (SecurityException | NoSuchMethodException e) {
                    log.error(e.getMessage(), e);
                    return;
                }
            }
            method.invoke(t, list.get(k));
        }
    }

    public static List<String> removeLastNull(List<String> headerList) {
        if (!headerList.isEmpty() && headerList.get(headerList.size() - 1) == null) {
            return headerList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> {
                                // 从后向前移除 null 元素
                                while (!list.isEmpty() && list.get(list.size() - 1) == null) {
                                    list.remove(list.size() - 1);
                                }
                                return list;
                            }
                    ));
        }
        return headerList;
    }

}
