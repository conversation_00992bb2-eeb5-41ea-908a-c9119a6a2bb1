package com.desaysv.workserver.utils;

import java.util.regex.Pattern;

public class DtcValidatorUtils {
    /**
     * DTC状态校验核心方法
     * @param inputStr 8位十进制数字（前6位DTC码+后2位状态码）
     * @param dtcText 多行DTC信息文本
     * @return 校验通过状态
     */
    public static boolean checkDtcStatus(String inputStr, String dtcText) {
        // 1. 增强参数校验（基于ISO 14229标准） ----以为输入数据可能带有其他的乱码数据，因此取消输入必须为8位十进制数字的判断
//        if (inputStr == null || inputStr.length() != 8 || !inputStr.matches("\\d+")) {
//            throw new IllegalArgumentException("输入必须为8位十进制数字");
//        }
        if (dtcText == null || dtcText.trim().isEmpty()) {
            throw new IllegalArgumentException("DTC信息不能为空");
        }
        // 新增多行预处理逻辑
        String normalizedText = dtcText.replaceAll("\\r\\n|\\r|\\n", "\n")
                .replaceAll(" +", " "); // 统一空格格式
        // 2. 分离DTC码与状态码（兼容紧凑型和分隔型格式）
        String rawDtc = inputStr.substring(0, 6);
        String targetStatus = inputStr.substring(6, 8);
        String formattedDtc = formatDtc(rawDtc); // 转换为"XX XX XX"格式
        // 3. 构建多行匹配正则表达式
        String regex = buildValidationRegex(formattedDtc, targetStatus);

        return Pattern.compile(regex)
                .matcher(normalizedText)
                .find();
    }


    /** DTC码格式化工具
     * @param rawDtc 原始6位DTC码（如"950113"）
     * @return 格式化后的DTC码（如"95 01 13"）
     */
    private static String formatDtc(String rawDtc) {
        return rawDtc.replaceAll("(.{2})(?=.)", "$1 ");
    }

    /**
     * 构建正则表达式模式
     * @param formattedDtc 格式化后的DTC码
     * @param status 2位状态码
     * @return 匹配规则表达式
     */
    private static String buildValidationRegex(String formattedDtc, String status) {
        // 支持紧凑型/分隔型/混合型（如"95 0113"）
        String spacedPattern = formattedDtc.replaceAll(" ", "\\\\s*");
        String compactPattern = formattedDtc.replace(" ", "");
        return String.format(
                "(?m)^DTC:\\s*((?:%s)|(?:%s))\\s+状态:\\s*%s$",
                spacedPattern, compactPattern, status
        );
    }

    /**
     * DTC存在性校验函数（优化单字符串版本）
     *
     * @param dtcCode 六位DTC码（如"911716"）
     * @param dtcText DTC信息字符串（格式："DTC:91 17 16 状态:08"）
     * @return 存在状态
     */
    public static boolean checkDtcExistence(String dtcCode, String dtcText) {
        // 1. 增强参数校验（兼容ISO 14229和SAE J2012标准）
        if (dtcCode == null || dtcCode.length() != 6 || !dtcCode.matches("[0-9A-F]{6}")) {
            throw new IllegalArgumentException("DTC码必须为6位十六进制字符");
        }
        if (dtcText == null || dtcText.trim().isEmpty()) {
            return false;
        }

        // 2. 生成两种格式的DTC匹配模式
        String compactDtc = dtcCode; // 紧凑型如"911716"
        String spacedDtc = formatDtc2(dtcCode); // 带空格如"91 17 16"

        // 3. 构建弹性正则表达式（兼容带/不带空格、附加状态信息）
        String pattern = String.format(
                "(?i)DTC:\\s*((%s\\s*%s\\s*%s)|(%s))\\b",
                dtcCode.substring(0, 2), dtcCode.substring(2, 4), dtcCode.substring(4, 6), // 拆解为XX XX XX
                compactDtc // 原始紧凑格式
        );

        // 4. 执行多维度匹配
        return Pattern.compile(pattern)
                .matcher(dtcText)
                .find();
    }

    // DTC格式化工具方法（增强鲁棒性）
    private static String formatDtc2(String rawDtc) {
        return rawDtc.replaceAll("(.{2})(?!$)", "$1 "); // 每两位添加空格
    }

    public static void main(String[] args) {
        String dtcText = "DTC:9A 01 25 状态:09\n" +
                "DTC:9A 05 25 状态:09\n" +
                "DTC:90 03 13 状态:09\n" +
                "DTC:95 01 13 状态:08\n" +
                "DTC:95 02 13 状态:08\n" +
                "DTC:95 05 13 状态:08\n" +
                "DTC:95 06 00 状态:09\n" +
                "DTC:95 07 00 状态:09\n" +
                "DTC:95 08 00 状态:09";
        boolean checkDtcStatus = checkDtcStatus("95011301", dtcText);
        boolean found = checkDtcExistence("950119", dtcText);
        System.out.println(found);
        System.out.println(checkDtcStatus);
    }
}