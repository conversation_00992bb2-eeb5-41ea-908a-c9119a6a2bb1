package com.desaysv.workserver.utils.command;

import lombok.Getter;

/**
 * 命令执行结果
 */
@Getter
public class ExecuteResult {
    private final int exitCode;
    private final String output;
    private final String error;
    private final boolean timeout;
    private final long executionTime;

    public String getResponse() {
        return output + "\n" + error;
    }

    public ExecuteResult(int exitCode, String output, String error, boolean timeout, long executionTime) {
        this.exitCode = exitCode;
        this.output = output;
        this.error = error;
        this.timeout = timeout;
        this.executionTime = executionTime;
    }

    public boolean isSuccess() {
        return exitCode == 0 && !timeout;
    }

    @Override
    public String toString() {
        return String.format("ExecuteResult{exitCode=%d, timeout=%b, executionTime=%d ms, output='%s', error='%s'}",
                exitCode, timeout, executionTime,
                output != null ? output.substring(0, Math.min(output.length(), 200)) + "..." : "null",
                error != null ? error.substring(0, Math.min(error.length(), 200)) + "..." : "null");
    }

}
