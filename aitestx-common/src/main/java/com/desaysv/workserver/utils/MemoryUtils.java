package com.desaysv.workserver.utils;

public class MemoryUtils {
    public static void printMemory() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long allocatedMemory = runtime.totalMemory() - runtime.freeMemory();
        long freeMemory = runtime.freeMemory();

        System.out.println("Max Memory: " + maxMemory + " bytes");
        System.out.println("Allocated Memory: " + allocatedMemory + " bytes");
        System.out.println("Free Memory: " + freeMemory + " bytes");
    }
}
