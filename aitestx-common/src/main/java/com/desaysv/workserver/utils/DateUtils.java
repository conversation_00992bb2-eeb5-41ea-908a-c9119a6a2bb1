package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

@Slf4j
public class DateUtils {

    public static float getSeconds(float number, String unit) {
        float seconds = 0;
        switch (unit.toLowerCase()) {
            case "ms":
                seconds = number / 1000.0f;
                break;
            case "s":
                seconds = number;
                break;
            case "min":
                seconds = number * 60;
                break;
            case "h":
                seconds = number * 3600;
                break;
        }
        return seconds;
    }

    public static String getNowForFile() {
        Date dNow = new Date();
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return ft.format(dNow);
    }

    public static String getNow() {
        return getNow(false);
    }

    public static String getNow(boolean precise) {
        Date dNow = new Date();
        SimpleDateFormat ft = new SimpleDateFormat(precise ? "yyyy-MM-dd HH:mm:ss.SSS" : "yyyy-MM-dd HH:mm:ss");
        return ft.format(dNow);
    }

    /**
     * 时区转换(UTC转GMT+8:00)
     *
     * @param utcTime 时间字符串
     * @return
     */
    public static String timeZoneTransfer(String utcTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = sdf.parse(utcTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        Date locatlDate = null;
        assert utcDate != null;
        String localTime = sdf.format(utcDate.getTime());
        try {
            locatlDate = sdf.parse(localTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
        }
        return sdf.format(locatlDate);
    }

    /**
     * 将日期字节数组转换为格式化字符串
     * @param dateBytes 3字节数组（年、月、日）
     * @return "yymdd"格式字符串
     * @throws IllegalArgumentException 输入参数不合法时抛出
     */
    public static String decodeDate(byte[] dateBytes) {
        // 1. 参数校验
        if (dateBytes.length != 3) {
            throw new IllegalArgumentException("字节数组长度必须为3");
        }
        // 2. 字节转无符号整型
        int year = Byte.toUnsignedInt(dateBytes[0]);
        int month = Byte.toUnsignedInt(dateBytes[1]);
        int day = Byte.toUnsignedInt(dateBytes[2]);

        // 3. 有效性验证
        if (year > 99) { // 确保年份为2位数字
            throw new IllegalArgumentException("年份超出范围 (0-99)");
        }
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份超出范围 (1-12)");
        }
        if (day < 1 || day > 31) {
            throw new IllegalArgumentException("日期超出范围 (1-31)");
        }
        // 4. 月份转字母或数字
        String monthStr;
        if (month >= 10) {
            monthStr = String.valueOf((char) ('A' + month - 10)); // 10→A,11→B,12→C
        } else {
            monthStr = String.valueOf(month); // 1-9直接转字符串
        }

        // 5. 格式化为yymdd（2位年 + 1位月 + 2位日）
        return String.format("%02d%s%02d", year, monthStr, day);
    }

    public static void main(String[] args) {
        // 测试用例
        byte[] input = {
                (byte) 0x15, // 21
                (byte) 0x0B, // 11
                (byte) 0x18  // 24
        };

        try {
            String result = decodeDate(input);
            System.out.println("输出结果: " + result);  // 21_11_24
        } catch (IllegalArgumentException e) {
            System.err.println("错误: " + e.getMessage());
        }

        String s = decodeDate(new byte[] {0x19,0x03,0x1C});
        System.out.println("s--" + s);

        System.out.println("25328A0006".substring(0, 5));

    }

}
