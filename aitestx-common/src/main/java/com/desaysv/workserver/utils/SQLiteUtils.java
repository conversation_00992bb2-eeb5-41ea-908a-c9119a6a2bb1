package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;

@Slf4j
public class SQLiteUtils {

    public static boolean deleteTable(String dbPath, String tableName) {
        return executeUpdate(dbPath, String.format("DROP TABLE IF EXISTS %s", tableName));
    }

    public static boolean existColumn(String dbPath, String tableName, String columnName) {
        String url = String.format("jdbc:sqlite:%s", dbPath);
        try {
            // 连接到数据库
            Connection conn = DriverManager.getConnection(url);
            DatabaseMetaData metaData = conn.getMetaData();
            ResultSet rs = metaData.getColumns(null, null, tableName, columnName);
            conn.close();
            // 检查字段是否存在
            return rs.next();
            // 关闭连接
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public static boolean executeUpdate(String dbPath, String sql) {
        String url = String.format("jdbc:sqlite:%s", dbPath);
        try {
            // 连接到数据库
            Connection conn = DriverManager.getConnection(url);
            Statement statement = conn.createStatement();
            statement.executeUpdate(sql);
            statement.close();
            log.info("执行SQL:{}", sql);
            return true;
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

}