package com.desaysv.workserver.utils.command;

import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import oshi.SystemInfo;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 命令工具类
 */
//FIXME：部分方法没有管理执行的命令到CommandExecutor中
@Slf4j
public class CommandUtils {
    private static final String UUID_CMD = "C:\\Windows\\System32\\wbem\\wmic path win32_computersystemproduct get uuid";

    /**
     * 使用Oshi终止进程及其子进程
     *
     * @param process 要终止的进程
     */
    public static boolean killWithOshi(Process process) {
        try {
            return killWithOshi(getProcessId(process));
        } catch (IOException e) {
            log.error("使用Oshi终止进程时发生错误", e);
            return false;
        }
    }

    /**
     * 使用Oshi终止进程及其子进程
     *
     * @param pid 要终止的进程pid
     */
    public static boolean killWithOshi(long pid) {
        try {

            SystemInfo systemInfo = new SystemInfo();
            OperatingSystem os = systemInfo.getOperatingSystem();

            // 获取所有进程
            List<OSProcess> allProcesses = os.getProcesses();
            Set<Long> processTree = new HashSet<>();
            collectProcessTree(allProcesses, pid, processTree);

            // 按照逆序终止进程（先终止子进程）
            List<Long> orderedPids = new ArrayList<>(processTree);
            Collections.reverse(orderedPids);

            for (Long targetPid : orderedPids) {
                try {
                    OSProcess targetProcess = os.getProcess((int) targetPid.longValue());
                    if (targetProcess != null) {
                        log.info("正在终止进程: PID={}, 名称={}, 命令行={}",
                                targetPid,
                                targetProcess.getName(),
                                targetProcess.getCommandLine());

                        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                            Runtime.getRuntime().exec("taskkill /F /PID " + targetPid);
                        } else {
                            Runtime.getRuntime().exec("kill -9 " + targetPid);
                        }
                    }
                } catch (Exception e) {
                    log.warn("终止进程 {} 时发生错误: {}", targetPid, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("使用Oshi终止进程时发生错误", e);
            return false;
        }
        return true;
    }

    /**
     * 递归收集进程树
     */
    private static void collectProcessTree(List<OSProcess> allProcesses, long parentPid, Set<Long> processTree) {
        processTree.add(parentPid);

        for (OSProcess process : allProcesses) {
            if (process.getParentProcessID() == parentPid) {
                collectProcessTree(allProcesses, process.getProcessID(), processTree);
            }
        }
    }

    public static long getProcessId(Process process) throws IOException {
        try {
            Field f = process.getClass().getDeclaredField("handle");
            f.setAccessible(true);
            long handle = f.getLong(process);
            Kernel32 kernel = Kernel32.INSTANCE;
            WinNT.HANDLE winntHandle = new WinNT.HANDLE();
            winntHandle.setPointer(Pointer.createConstant(handle));
            return kernel.GetProcessId(winntHandle);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new IOException(e);
        }
    }

    interface Kernel32 extends com.sun.jna.Library {
        Kernel32 INSTANCE = com.sun.jna.Native.load("kernel32", Kernel32.class);

        long GetProcessId(WinNT.HANDLE Process);
    }

    interface WinNT extends com.sun.jna.platform.win32.WinNT {
        class HANDLE extends com.sun.jna.platform.win32.WinNT.HANDLE {
        }
    }

    public static String getPcUUID() {
        List<String> uuidInfo;
        try {
            uuidInfo = CommandUtils.executeCommandToArray(UUID_CMD);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return "";
        }
        return uuidInfo.size() < 2 ? "" : uuidInfo.get(1);
    }

    public static String getAppPID() {
        //暂不开放多进程使用
//        return "";
        return ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
    }

    @Deprecated
    private static boolean killSingleProcess(int pid) throws IOException, InterruptedException {
        log.info("终止pid:{}", pid);
        String command = "taskkill /F /T /PID " + pid;
        Process process = Runtime.getRuntime().exec(command);

        // 获取命令执行的输出流
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            System.out.println(line);
        }

        return process.waitFor() == 0;
    }

    @Deprecated
    private static void killAdbProcesses() throws IOException, InterruptedException {
        // 1. 先用tasklist命令找出所有adb相关进程
        Process tasklistProcess = Runtime.getRuntime().exec("tasklist /FI \"IMAGENAME eq adb.exe\" /FO CSV /NH");
        BufferedReader reader = new BufferedReader(new InputStreamReader(tasklistProcess.getInputStream()));

        String line;
        while ((line = reader.readLine()) != null) {
            if (line.contains("adb.exe")) {
                // 解析CSV格式输出获取PID
                String[] parts = line.split(",");
                if (parts.length >= 2) {
                    String pidStr = parts[1].replaceAll("\"", "").trim();
                    try {
                        int pid = Integer.parseInt(pidStr);
                        killSingleProcess(pid);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
            }
        }
        // 2. 使用taskkill命令强制结束所有adb进程
        Runtime.getRuntime().exec("taskkill /F /IM adb.exe");

        // 3. 确保adb server被完全关闭
        Runtime.getRuntime().exec("adb kill-server");
    }

    @Deprecated
    public static boolean killProcess(long pid, String cmd) throws IOException, InterruptedException {
        boolean result = killWithOshi(pid);
        if (cmd.contains("adb")) {
            log.info("终止adb命令");
            killAdbProcesses();
        }
        return result;
    }

    public static String killProcess(String processName) throws IOException {
        return executeCommandToString("taskkill /F /IM " + processName);
    }

    public static boolean findProcess(String processName) {
        BufferedReader reader = null;
        try {
            Process process = Runtime.getRuntime().exec("tasklist -fi " + '"' + "imagename eq " + processName + '"');
            reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(processName)) {
                    return true;
                }
            }
            return false;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }


    private static StringBuilder getStringBuilder(BufferedReader reader) throws IOException {
        StringBuilder builder = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            if (line.trim().isEmpty()) {
                continue;
            }
            builder.append(line.trim()).append(System.lineSeparator());
//            log.info(line);
        }
        return builder;
    }

    private static List<String> getStringArray(BufferedReader reader) {
        return reader.lines().filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
    }

    public static BufferedReader getProcessBufferReader(String command, String[] env, File dir) throws IOException {
        Process process = Runtime.getRuntime().exec(new String[]{"cmd", "/c", command}, env, dir);
        // 采用字符流读取缓冲池内容，腾出空间
        return new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
    }

    private static String basicExecuteCommandToString(String command, String[] env, File dir) throws IOException {
        BufferedReader bufferedReader = getProcessBufferReader(command, env, dir);
        return getStringBuilder(bufferedReader).toString();
    }

    private static List<String> basicExecuteCommandToArray(String command, String[] env, File dir) throws IOException {
        BufferedReader bufferedReader = getProcessBufferReader(command, env, dir);
        return getStringArray(bufferedReader);
    }

    //String
    public static String executeCommandToString(String command) throws IOException {
        return basicExecuteCommandToString(command, null, null);
    }

    public static String executeCommandToString(String command, String[] env) throws IOException {
        return basicExecuteCommandToString(command, env, null);
    }

    public static String executeCommandToString(String command, String[] env, File dir) throws IOException {
        return basicExecuteCommandToString(command, env, dir);
    }

    public static String executeCommandToString(String command, File dir) throws IOException {
        return basicExecuteCommandToString(command, new String[]{}, dir);
    }

    //Array
    public static List<String> executeCommandToArray(String command) throws IOException {
        return basicExecuteCommandToArray(command, null, null);
    }

    public static List<String> executeCommandToArray(String command, String[] env) throws IOException {
        return basicExecuteCommandToArray(command, env, null);
    }

    public static List<String> executeCommandToArray(String command, String[] env, File dir) throws IOException {
        return basicExecuteCommandToArray(command, env, dir);
    }

    public static CommandResponse executeCommand(String command) throws IOException {
        CommandResponse commandResponse = new CommandResponse();
        ExecuteResult executeResult = CommandExecutor.executeCommand(command);
        commandResponse.setOk(executeResult.isSuccess());
        commandResponse.setStandardOutput(executeResult.getOutput());
        commandResponse.setStandardError(executeResult.getError());
        return commandResponse;
    }

    public static CommandResponse executeCommand(CmdCommand command) throws IOException {
        if (command.isKillLastCommand()) {
            log.info("kill上次命令:{}", command.getCommand());
            CommandExecutor.killByCommand(command.getCommand());
        }
        CommandResponse commandResponse = new CommandResponse();
        ExecuteResult executeResult = CommandExecutor.executeCommand(command.getCommand(), command.getWorkDir(), command.isShowWindow());
        commandResponse.setOk(executeResult.isSuccess());
        commandResponse.setStandardOutput(executeResult.getOutput());
        commandResponse.setStandardError(executeResult.getError());
        // 判断命令执行结果是否符合预期，并根据 pauseOnMatch 和 pauseOnMismatch 决定行为
        if (command.isJudge()) {
            String result = executeResult.getResponse();
            boolean matched = result.contains(command.getJudgeText());
            if (command.isMustContained()) {
                if (!matched) {
                    commandResponse.setOk(false);
                    commandResponse.setStandardError(String.format("命令执行结果未包含关键字：%s，实际返回结果：%s", command.getJudgeText(), result));
                }
            } else {
                if (matched) {
                    commandResponse.setOk(false);
                    commandResponse.setStandardError(String.format("命令执行结果包含关键字：%s，实际返回结果：%s", command.getJudgeText(), result));
                }
            }
        }
        return commandResponse;
    }

    public static void main(String[] args) {

    }
}
