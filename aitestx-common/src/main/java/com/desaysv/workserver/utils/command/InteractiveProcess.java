package com.desaysv.workserver.utils.command;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/3/22 13:42
 */
@Slf4j
public class InteractiveProcess implements Closeable {
    private final Process process;
    private final ExecutorService executor;
    private final BlockingQueue<String> outputQueue = new LinkedBlockingQueue<>();
    private final BufferedWriter inputWriter;
    private volatile boolean isRunning = true;

    public InteractiveProcess(Process process, int timeout) {
        this.process = process;
        this.executor = Executors.newFixedThreadPool(2);

        // 输入流写入器（GBK编码）
        this.inputWriter = new BufferedWriter(
                new OutputStreamWriter(process.getOutputStream(), Charset.forName("GBK")));

        // 启动输出监听线程
        executor.submit(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), Charset.forName("GBK")))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    outputQueue.put(line);
                    log.info("[CMD Output] {}", line); // 实时日志输出
                }
            } catch (Exception e) {
                log.error("读取输出异常", e);
            } finally {
                isRunning = false;
            }
        });

        // 超时控制线程
        executor.submit(() -> {
            try {
                if (!process.waitFor(timeout, TimeUnit.SECONDS)) {
                    throw new TimeoutException("命令执行超时");
                }
            } catch (Exception e) {
                log.error("进程监控异常", e);
            }
        });
    }

    /**
     * 发送命令（自动追加换行）
     */
    public void sendCommand(String command) throws IOException {
        if (!isRunning) {
            throw new IllegalStateException("进程已结束");
        }
        inputWriter.write(command);
        inputWriter.newLine();
        inputWriter.flush();
    }

    /**
     * 读取最新输出（非阻塞）
     */
    public List<String> readOutput() {
        List<String> output = new ArrayList<>();
        outputQueue.drainTo(output);
        return output;
    }

    /**
     * 等待命令执行完成
     */
    public int waitFor() throws InterruptedException {
        executor.shutdown();
        executor.awaitTermination(1, TimeUnit.MINUTES);
        return process.exitValue();
    }

    @Override
    public void close() throws IOException {
        try {
            sendCommand("exit"); // 发送退出命令
        } finally {
            process.destroyForcibly();
            executor.shutdownNow();
            inputWriter.close();
        }
    }

    public static InteractiveProcess executeInteractiveCommand(String command, boolean showConsole, int timeout)
            throws IOException, TimeoutException {

        ProcessBuilder processBuilder = new ProcessBuilder("cmd.exe", "/c", command);
        processBuilder.redirectErrorStream(true);

        if (showConsole) {
            processBuilder.inheritIO();
        }

        Process process = processBuilder.start();
        return new InteractiveProcess(process, timeout);
    }
}
