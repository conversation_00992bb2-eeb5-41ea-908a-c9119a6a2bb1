package com.desaysv.workserver.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * JSON工具类
 */
@Slf4j
public class JsonUtils {

    public static String convertToValidJson(String input) {
        // 正则表达式匹配键值对，其中键为任意非空白字符，值为非空白字符序列
        String regex = "(?<=\\{|,)(\\s*)([^:\\s]+):([^,\\}]*)(?=,|})";
        Pattern pattern = Pattern.compile(regex);
        StringBuffer sb = new StringBuffer();
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            String key = matcher.group(2); // 匹配到的键名
            String value = matcher.group(3); // 匹配到的值
            // 将键名添加双引号，并确保内部的双引号被正确转义
            key = "\"" + key.replace("\"", "\\\"") + "\"";
            // 将整个值用双引号包围，并确保内部的双引号和反斜杠被正确转义
            value = "\"" + value.replace("\\", "\\\\").replace("\"", "\\\"") + "\"";
            // 组合新的键值对，并插入到结果字符串中
            matcher.appendReplacement(sb, key + ":" + value);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
