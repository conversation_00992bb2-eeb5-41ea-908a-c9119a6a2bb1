package com.desaysv.workserver.utils.sse;

public class SseConstants {

    public final static String ROBOT_TOUCH_COORDINATE_SUBSCRIBE_ID = "robotTouchCoordinate";
    public final static String ROBOT_SWIPE_COORDINATE_SUBSCRIBE_ID = "robotSwipeCoordinate";
    public final static String REPORT_COORDINATE_SUBSCRIBE_ID = "reportCoordinate";
    public final static String REPORT_SWIPE_COORDINATE_SUBSCRIBE_ID = "reportSwipeCoordinate";
    public final static String TEST_STEP_EXECUTE_SUBSCRIBE_ID = "testStepExecute";
    public final static String EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID = "excelCaseTestStatus";
    public final static String CHECK_RESULT_SUBSCRIBE_ID = "checkActualResult";
    public final static String TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID = "testStepExecuteResult";
    public final static String UPGRADE_NOTIFICATION_SUBSCRIBE_ID = "upgradeNotification";
    public static final String CAN_SEND_FINISH = "sendFinish";
    public static final String CAN_SEND_ERROR = "sendError";
    public static final String CAN_DBC_RECEIVER = "canDbcReceiver";
    public static final String CAN_DBC_RECEIVER_LIST = "canDbcReceiverList";
    public static final String CAN_FRAME_RECEIVER = "canFrameReceiver";
    public static final String CAN_FRAME_RECEIVER_LIST = "canFrameReceiverList";
    public static final String RUN_LOG = "runLog";
    public static final String TCP_MSG = "tcpMessage";
    public static final String ADB_MSG = "adbMessage";
    public static final String DIALOG = "dialog";
    public static final String DIALOG_JUDGE = "dialogJudge";
    public static final String PICTURE_SAVE = "pictureSaveSuccess";
    public static final String DEVICE_DISCONNECT_WARNING = "deviceDisconnectWarning";
    public static final String UDS_LOG_PATH_SUBSCRIBE_ID = "udsLogPathNotification";
}
