<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>AITestXServer</artifactId>
        <groupId>com.desaysv</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aitestx-common</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <commons.text.verson>1.10.0</commons.text.verson>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ice.jni</groupId>
            <artifactId>registry</artifactId>
            <scope>system</scope>
            <version>3.1.3</version>
            <systemPath>${pom.basedir}/../libs/registry.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons.text.verson}</version>
        </dependency>
        <!--        <dependency>-->
<!--            <groupId>net.sf.jsci</groupId>-->
<!--            <artifactId>jsci</artifactId>-->
<!--            <version>1.2</version>-->
<!--        </dependency>-->
    </dependencies>

</project>