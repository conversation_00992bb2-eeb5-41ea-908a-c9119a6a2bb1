package com.desaysv.workserver.vo.testcase;

import lombok.Data;

import java.util.Date;

@Data
public class TestCaseVo {

    private String testcaseUUID;

    private String moduleName;

    private String testCaseName;

    private String preCondition;

    private String operationalStep;

    private String expectationResult;

    private Integer testCycle;

    private Integer failCycle;

    private Integer sumTestCycle;

    private Float failTestRate;

    private Boolean pass;

    private Boolean testing;

    private Integer consumedTime;

    private String friendlyConsumedTime;

    private Date beginTestTime;

    private Date endTestTime;

}
