package com.desaysv.workserver.bo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.model.OriginalPicture;
import com.desaysv.workserver.model.roi.TemplateRoi;
import lombok.Data;

/**
 * 图像模板信息
 */
@Data
public class TemplateImageBo {

    //图像模板名
    private String name;

    //类型id
    private Integer roiTypeId;

    //起点
    private Double startX;

    //起点y
    private Double startY;

    //终点x
    private Double endX;

    //终点y
    private Double endY;

    //设备
    private String deviceUniqueCode;

    //项目
    private String projectName;

    //原图名
    private String originalPictureName;

    private boolean simulated;

    @JSONField(serialize = false)
    private TemplateRoi templateRoi;

    //是否启用快照
    private boolean imageSnapshotAvailable;

//    @JSONField(serialize = false)
//    public TemplateRoi getRoi() {
//        TemplateRoi roi = new TemplateRoi();
//        roi.setTypeId(roiTypeId);
//        roi.setStartX(startX);
//        roi.setStartY(startY);
//        roi.setEndX(endX);
//        roi.setEndY(endY);
//        return roi;
//    }

    @JSONField(serialize = false)
    public OriginalPicture getOriginalPicture() {
        OriginalPicture originalPicture = new OriginalPicture();
        originalPicture.setName(originalPictureName);
        return originalPicture;
    }

}
