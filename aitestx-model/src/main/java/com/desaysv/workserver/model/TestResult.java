package com.desaysv.workserver.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试结果
 */
@Data
public class TestResult {

    private Integer id;

    private String uuid;

    private Integer sumCycle;

    private Integer testCycle;

    private Integer failCycle;

    private String summary;

    @Setter(AccessLevel.NONE)
    private Date createTime;

    @Setter(AccessLevel.NONE)
    private Date updateTime;
}
