package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-20 14:08
 * @description : 图像模板类
 * @modified By :
 * @since : 2022-4-20
 */
@Data
public class TemplatePicture {

    //图像模板id
    private Long id;

    //图像模板名
    private String name;

    private String templatePictureUUID;

    private String originalPictureUUID;

    private Integer deviceTypeId;

    private Integer deviceIndex;
    //设备id
    private Integer deviceId;

    //项目id
    private Integer projectId;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;
}
