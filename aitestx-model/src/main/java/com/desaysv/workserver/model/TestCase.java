package com.desaysv.workserver.model;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试用例
 */
@Data
public class TestCase {

    private Integer id;

    private String testcaseUUID;

    private String moduleName;

    private String testCaseName;

    private String preCondition;

    private String operationalStep;

    private String expectationResult;

    private String testSuiteUUID;

    private String testResultUUID;

    private Boolean pass;

    private Boolean testing;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTestTime;

    //    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTestTime;

    @Setter(AccessLevel.NONE)
    private Date createTime;

    @Setter(AccessLevel.NONE)
    private Date updateTime;
}
