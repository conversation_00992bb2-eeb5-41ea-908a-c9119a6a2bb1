package com.desaysv.workserver.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExcelCaseModel {
    private Integer id;
    private String uuid;
    private String tableName;
    private String testCaseID;
    private String testKey;
    private String initialCondition;
    private String action;
    private String expectedResult;
    private String initTestSequences;
    private String actionTestSequences;
    private String expectedTestSequences;
    private String actualResult;
    private String testResult;
    private String tester;
    private String testTime;
    private String remark;
    private String targetTestTimes = "0";  //目标测试次数
    private String testedTimes = "0";  //已测试次数
    private String testedPassTimes = "0"; //已通过次数
}
