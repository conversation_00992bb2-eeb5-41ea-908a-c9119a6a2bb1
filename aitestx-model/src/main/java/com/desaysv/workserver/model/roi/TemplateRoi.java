package com.desaysv.workserver.model.roi;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-26 18:50
 * @description :
 * @modified By :
 * @since : 2022-4-26
 */
@Data
public class TemplateRoi {

    //模板ID
    private Long id;

    //类型id
    private Integer typeId;

    //起点x
    private Integer startX;

    //起点y
    private Integer startY;

    //终点x
    private Integer endX;

    //终点y
    private Integer endY;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

    @JSONField(serialize = false)
    private ScaledRoiRect scaledRoiRect;

    @JSONField(serialize = false)
    public AbsoluteRoiRect toRoiRect() {
        return new AbsoluteRoiRect(
                startX,
                startY,
                endX - startX,
                endY - startY);
    }

    public static TemplateRoi of(double startX, double startY, double endX, double endY, RectSize size, int roiTypeId) {
        TemplateRoi templateRoi = new TemplateRoi();
        ScaledRoiRect scaledRoiRect = new ScaledRoiRect();
        scaledRoiRect.setPointStart(new ScaledPoint(startX, startY));
        scaledRoiRect.setPointEnd(new ScaledPoint(endX, endY));

        int actualStartX = scaledRoiRect.getAbsolutePointStart(size).x;
        int actualStartY = scaledRoiRect.getAbsolutePointStart(size).y;

        int actualEndX = scaledRoiRect.getAbsolutePointEnd(size).x;
        int actualEndY = scaledRoiRect.getAbsolutePointEnd(size).y;
        templateRoi.setTypeId(roiTypeId);
        templateRoi.setStartX(actualStartX);
        templateRoi.setStartY(actualStartY);
        templateRoi.setEndX(actualEndX);
        templateRoi.setEndY(actualEndY);
        templateRoi.setScaledRoiRect(scaledRoiRect);
        return templateRoi;
    }

    @JSONField(serialize = false)
    public static TemplateRoi fromPercentTemplateRoi(PercentTemplateRoi percentTemplateRoi, RectSize size) {
        return of(percentTemplateRoi.getStartX(), percentTemplateRoi.getStartY(),
                percentTemplateRoi.getEndX(), percentTemplateRoi.getEndY(),
                size, percentTemplateRoi.getTypeId());
    }
}
