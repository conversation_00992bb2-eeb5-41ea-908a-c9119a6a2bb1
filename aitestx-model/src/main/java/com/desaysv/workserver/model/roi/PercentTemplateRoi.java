package com.desaysv.workserver.model.roi;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-26 18:50
 * @description :
 * @modified By :
 * @since : 2022-4-26
 */
@Data
public class PercentTemplateRoi {

    //模板ID
    private Long id;

    //模板UUID
    private String templatePictureUUID;

    //类型id
    private Integer typeId;

    //起点x
    private Double startX;

    //起点y
    private Double startY;

    //终点x
    private Double endX;

    //终点y
    private Double endY;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    private Date updateTime;

    @JSONField(serialize = false)
    private ScaledRoiRect scaledRoiRect;


    public ScaledRoiRect getScaledRoiRect() {
        scaledRoiRect = new ScaledRoiRect();
        scaledRoiRect.setPointStart(new ScaledPoint(startX, startY));
        scaledRoiRect.setPointEnd(new ScaledPoint(endX, endY));
        return scaledRoiRect;
    }

//    @JsonIgnore
//    public AbsoluteRoiRect toRoiRect() {
//        return new AbsoluteRoiRect(
//                startX,
//                startY,
//                endX - startX,
//                endY - startY);
//    }
}
