package com.desaysv.workserver.model.relay;

import lombok.Data;

/**
 * 电气继电器实体类
 * 用于表示电气继电器的基本信息及状态
 *
 * @Data 注解自动生成getter/setter、toString等方法
 */

@Data
public class ElectricRelayEntity {
    /**
     * 继电器唯一标识ID
     */
    private Integer id;

    /**
     * 设备别名
     */
    private String aliasName;

    /**
     * 所属项目名称
     */
    private String project;

    /**
     * 设备连接状态
     * 0表示断开，1表示连接
     */
    private String connectedStatus;

    /**
     * 继电器字段1-16是文本框字段
     */
    private String relayText;

    /**
     * 通道
     */
    private Integer channel;

    /**
     * 开关状态 on打开 off关闭
     */
    private String switchOn;

}

