package com.desaysv.workserver.model.roi;

import lombok.Data;

import java.awt.*;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-13 18:45
 * @description :
 * @modified By :
 * @since : 2022-5-13
 */
@Data
public class ScaledRoiRect {

    private ScaledPoint pointStart;

    private ScaledPoint pointEnd;

    public boolean isValid() {
        return pointStart != null && pointEnd != null;
    }

    private Point getAbsolutePoint(RectSize size, ScaledPoint point) {
        double x = point.getX();
        double y = point.getY();

        return new Point((int) (x * size.getWidth()), (int) (y * size.getHeight()));
    }

    public Point getAbsolutePointStart(RectSize size) {
        return getAbsolutePoint(size, pointStart);
    }

    public Point getAbsolutePointEnd(RectSize size) {
        return getAbsolutePoint(size, pointEnd);
    }

    public AbsoluteRoiRect toRect(RectSize size) {
        int actualStartX = getAbsolutePointStart(size).x;
        int actualStartY = getAbsolutePointStart(size).y;

        int actualEndX = getAbsolutePointEnd(size).x;
        int actualEndY = getAbsolutePointEnd(size).y;

        return new AbsoluteRoiRect(
                actualStartX,
                actualStartY,
                actualEndX - actualStartX,
                actualEndY - actualStartY
        );
    }
}
