package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试项目实体类
 */
@Data
public class TestProject {

    //测试项目id
    private Integer id;

    //测试项目名称
    private String name;

    //测试人员状态
    private short status;

    //测试项目Model名
    private String model;

    //项目客户ID
    private Integer customerId;

    //项目部门ID
    private Integer departmentId;

    //项目信息ID
    private Integer projectInfoId;

    //是否公共项目
    private boolean communal;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

}
