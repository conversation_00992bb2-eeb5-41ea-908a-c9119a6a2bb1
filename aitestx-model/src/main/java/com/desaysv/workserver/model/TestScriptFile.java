package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试脚本文件
 */
@Data
public class TestScriptFile {

    private Long id;

    private boolean selected;

    private String uuid;

    private String moduleName;

    private String caseName;

    private String comment;

    private Integer projectId;

    private Integer clientId;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName == null ? "" : moduleName;
    }

    public void setComment(String comment) {
        this.comment = comment == null ? "" : comment;
    }
}

