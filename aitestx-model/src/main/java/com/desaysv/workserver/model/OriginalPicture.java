package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-10 11:37
 * @description :
 * @modified By :
 * @since : 2022-5-10
 */
@Data
public class OriginalPicture {
    //原始图片id
    private Long id;

    //唯一记录码
    private String uuid;
    //原始图片名
    private String name;

    //原始图片宽度
    private Integer width;

    //原始图片高度
    private Integer height;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    private Date updateTime;
}
