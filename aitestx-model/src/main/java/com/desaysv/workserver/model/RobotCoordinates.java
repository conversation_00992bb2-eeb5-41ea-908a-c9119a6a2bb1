package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.model.roi.CoordinatesRoi;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-7-23 13:29
 * @description :
 * @modified By :
 * @since : 2022-7-23
 */
@Data
public class RobotCoordinates {

    private Long id;

    private String uuid;

    private String name;

    private double x;

    private double y;

    private double z;

    private double r;

    private double slideRail;

    private Integer projectId;

    private Integer deviceId;

    //    @J<PERSON><PERSON>ield(serialize = false)
    private CoordinatesRoi coordinatesRoi;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

}
