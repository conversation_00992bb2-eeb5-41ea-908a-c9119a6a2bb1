package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-4-27 11:36
 * @description :
 * @modified By :
 * @since : 2022-4-27
 */
@Data
public class TestMachine {

    //测试机器id
    private Integer id;

    //测试机器名称
    private String name;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;


}
