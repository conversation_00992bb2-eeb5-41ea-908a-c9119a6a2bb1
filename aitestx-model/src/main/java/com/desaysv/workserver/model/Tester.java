package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试人员实体类
 */
@Data
public class Tester {

    //测试人员id
    private Integer id;

    //测试人员名称
    private String name;

    //测试人员密码
    private String password;

    //测试人员状态
    private Integer status;

    //测试人员邮件
    private String email;

    //测试人员电话
    private String phone;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

}


