package com.desaysv.workserver.model;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.Date;

/**
 * 测试设备仪器
 */
@Data
public class TestDevice {

    //测试设备id
    private Integer id;

    //测试设备名
    private String name;

    //测试设备端口
    private Integer port;

    //测试设备波特率
    private Integer baudRate;

    //测试设备采样率
    private Integer sampleRate;

    //测试设备类型id
    private Integer typeId;

    //测试设备型号id
    private Integer modelId;

    //测试设备唯一编码
    private String uniqueCode;

    //测试设备缩写名
    private String aliasName;

    //测试设备参数(JSON字符串）
    private String operationParameter;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date createTime;

    @JSONField(serialize = false)
    @Setter(AccessLevel.NONE)
    private Date updateTime;

//    @JSONField(serialize = false)
//    public int getDevicePort() {
//        return port;
//    }
//
//    @JSONField(serialize = false)
//    public String getDeviceUniqueCode() {
//        return uniqueCode;
//    }
}
