package com.desaysv.workserver.model.roi;

import org.bytedeco.opencv.opencv_core.Rect;

/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-18 9:36
 * @description :
 * @modified By :
 * @since : 2022-5-18
 */
public class AbsoluteRoiRect extends Rect {

    public AbsoluteRoiRect(int _x, int _y, int _width, int _height) {
        super(_x, _y, _width, _height);
    }

    @Override
    public String toString() {
        return String.format("左上角(%d,%d)|w(%d)xh(%d)", x(), y(), width(), height());
    }

    public String vertextString() {
        return String.format("左上角(%d,%d)->右下角(%d,%d)", x(), y(), x() + width(), y() + height());
    }
}
