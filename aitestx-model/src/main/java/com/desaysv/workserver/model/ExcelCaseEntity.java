package com.desaysv.workserver.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ExcelCaseEntity {
    @ExcelProperty(value = "NO\n编号")
    private String no;
    @ExcelProperty(value = "REQ_ID\n需求编号")
    private String reqID;
    @ExcelProperty(value = "DesignMethod\n案例设计方法")
    private String designMethod;
    @ExcelProperty(value = "TC_ID\n案例编号")
    private String tcID;
    @ExcelProperty(value = "TestKey\n测试功能点")
    private String testKey;
    @ExcelProperty(value = "Initial_Condition\n初始条件")
    private String initialCondition;
    @ExcelProperty(value = "Action\n动作")
    private String action;
    @ExcelProperty(value = "Expected_Result\n预期结果")
    private String expectedResult;
    @ExcelProperty(value = "InitTestSequences\n初始条件序列")
    private String initTestSequences;
    @ExcelProperty(value = "ActionTestSequences\n操作步骤序列")
    private String actionTestSequences;
    @ExcelProperty(value = "ExpectedTestSequences\n预期结果序列")
    private String expectedTestSequences;
    @ExcelProperty(value = "Actual_Result\n实际结果")
    private String actualResult;
    @ExcelProperty(value = "Executed_Select_Y/N\n是否执行案例")
    private String executedSelectYOrN;
    @ExcelProperty(value = "If NO_why\n未执行的原因")
    private String ifNoWhy;
    @ExcelProperty(value = "Test_Result\n测试结果")
    private String testResult;
    @ExcelProperty(value = "PR_NO\nBUG编号")
    private String prNoBug;
    @ExcelProperty(value = "Tester\n测试人员")
    private String tester;
    @ExcelProperty(value = "TestTime\n测试时间")
    private String testTime;
    @ExcelProperty(value = "Priority\n案例优先级")
    private String priority;
    @ExcelProperty(value = "SW Version_Date\n软件版本和日期")
    private String swVersionDate;
    @ExcelProperty(value = "TestCase Version\n用例版本")
    private String testCaseVersion;
    @ExcelProperty(value = "Total case\n总用例数")
    private String totalCase;
    @ExcelProperty(value = "Executed\n执行案例数")
    private String executed;
    @ExcelProperty(value = "Result=Pass\n通过案例数")
    private String resultEqualsPass;
    @ExcelProperty(value = "Result=Fail\n不通过案例数")
    private String resultEqualsFail;
}
