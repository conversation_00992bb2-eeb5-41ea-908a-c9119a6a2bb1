package com.desaysv.workserver.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;


/**
 * sqlite mybatis日期类型转化器
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes({Date.class})
public class UpdateTimeTypeHandler extends BaseTypeHandler<Date> {
    private final DateTimeFormatter dateTimeformatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private Date parse(String dateString) throws SQLException {
        try {
            LocalDateTime localDateTime = LocalDateTime.parse(dateString, dateTimeformatter);
            return java.sql.Timestamp.valueOf(localDateTime);
        } catch (NumberFormatException e) {
            throw new SQLException(String.format("日期转换错误:%s", dateString));
        }
    }

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Date date, JdbcType jdbcType) throws SQLException {
        LocalDateTime localDateTime = new Timestamp(date.getTime()).toLocalDateTime();
        String dateString = dateTimeformatter.format(localDateTime);
        preparedStatement.setString(i, dateString);
    }


    @Override
    public Date getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String dateString = resultSet.getString(s);
        return parse(dateString);
    }

    @Override
    public Date getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String dateString = resultSet.getString(i);
        return parse(dateString);
    }

    @Override
    public Date getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String dateString = callableStatement.getString(i);
        return parse(dateString);
    }


}
