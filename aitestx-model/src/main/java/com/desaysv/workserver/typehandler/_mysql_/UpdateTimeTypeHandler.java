package com.desaysv.workserver.typehandler._mysql_;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.*;
import java.util.Date;


/**
 * <AUTHOR> lwj
 * @date : Created in 2022-5-12 17:14
 * @description :
 * @modified By :
 * @since : 2022-5-12
 */
@MappedJdbcTypes(JdbcType.TIMESTAMP)
@MappedTypes({Date.class})
public class UpdateTimeTypeHandler extends BaseTypeHandler<Date> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Date date, JdbcType jdbcType) throws SQLException {
        preparedStatement.setTimestamp(i, new Timestamp(new Date().getTime()));
    }


    @Override
    public Date getNullableResult(ResultSet resultSet, String s) throws SQLException {
        return resultSet.getDate(s);
    }

    @Override
    public Date getNullableResult(ResultSet resultSet, int i) throws SQLException {
        return resultSet.getDate(i);
    }

    @Override
    public Date getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        return callableStatement.getDate(i);
    }


}
